﻿using NAudio.Wave;
using NAudio.Wave.SampleProviders;
using System.Reflection;
using Xylia.BnsHelper.Models;

namespace Xylia.BnsHelper.Common.Helpers;
internal static class AudioHelper
{
    /// <summary>
    /// Plays an audio file embedded as a resource in the assembly.
    /// </summary>
    /// <remarks>The method retrieves the audio file as a stream from the assembly's embedded resources,
    /// initializes a playback device,  and plays the audio asynchronously. If the specified resource path does not
    /// exist, the method returns without playing any audio.</remarks>
    /// <param name="path">The resource path of the audio file within the assembly.</param>
    /// <param name="volume">The playback volume, ranging from 0.0 (silent) to 1.0 (full volume). The default is 0.4.</param>
    /// <returns>A task that represents the asynchronous operation. The task completes when the audio playback finishes.</returns>
	public static async Task Play(string path, float volume = 0.4f)
	{
		using var stream = Assembly.GetExecutingAssembly().GetManifestResourceStream(path);
		if (stream is null) return;

		// use volumn sample 
		using var audioFile = new AudioSteamReader(stream);
		var _volumeProvider = new VolumeSampleProvider(audioFile) { Volume = volume };

		using var outputDevice = new WaveOutEvent();
		outputDevice.Init(_volumeProvider);
		outputDevice.Play();

		while (outputDevice.PlaybackState == PlaybackState.Playing)
		{
			await Task.Delay(1000);
		}
	}
}
