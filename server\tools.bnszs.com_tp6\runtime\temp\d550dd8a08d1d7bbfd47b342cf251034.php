<?php /*a:1:{s:87:"E:\Build\C++\bnspatch\src\BnsPlugin\server\tools.bnszs.com_tp6\app\common/view/bmg.html";i:1713952980;}*/ ?>
<!DOCTYPE html>
<html lang="cn">
  <head>
    <meta charset="utf-8">
    <title>bmg</title>
    <link rel="stylesheet" type="text/css" href="/res/css/audiojs.css?1" />
  </head>
  <body>
    <audio id="bgm" src="<?php echo $music; ?>" preload="auto" autoplay_="<?php echo $autoplay; ?>" loop="<?php echo $loop; ?>"></audio>
    <script src="/res/js/audiojs/audio.min.js?v=00003"></script>
    <script src="//cdn.staticfile.org/jquery/3.4.1/jquery.min.js"></script>
    <script>
        var ntop = $('.progress_volume_bg').height()
              $(function(){
          var tag = false,oy = 0,height = 0,bgtop = 0;
          $('.progress_volume_btn').mousedown(function(e) {
              oy = e.pageY - ntop;
              tag = true;
          });
          // $('.progress_volume_btn').mouseleave(function() {
          //     tag = false;
          // });
          $(document).mouseup(function() {
              tag = false;
          });
     
          $(document).mousemove(function(e) {
              if (tag) {
                  var ph = $('.progress_volume_bg').height();
                  height = oy + ph - e.pageY;
                  updateVolume(height);
              }
          });
       
          $('.progress_volume_bg').click(function(e) {
               if (!tag) {
                  var ph = $('.progress_volume_bg').height();
                   bgtop = $('.progress_volume_bg').offset().top;
                   height = bgtop + ph - e.pageY;
                   updateVolume(height);
               }
          });
      });
      function updateVolume(height){
        var ph = $('.progress_volume_bg').height();
        if (height <= 0) {
                      height = 0;
                  }else if (height > ph) {
                      height = ph;
                  }
                  // ntop = ph - height;
                  // $('.progress_volume_btn').css('top', ntop - $('.progress_volume_btn').outerHeight()/2);
                  // $('.progress_volume_bar').css('height',height);
                  // $('.progress_volume_bar').css('margin-top', ntop);
                  var volume =  height/ph;
                  //$('.volume').html(parseInt((height/ph)*100) + '%');
                  setVolume(volume);

      }
      function setVolume(num){
        if (num < 0) {
          num = 0;
                  }else if (num > 1) {
                    num = 1;
                  }
        audiojs.instances["audiojs0"].setVolume(num);
        localStorage.setItem('zsAudioVolume',num);
        var ph = $('.progress_volume_bg').height();
        ntop = ph * (1-num);
        height = ph * num;
        $('.progress_volume_btn').css('top', ntop - $('.progress_volume_btn').outerHeight()/2);
                  $('.progress_volume_bar').css('height',height);
                  $('.progress_volume_bar').css('margin-top', ntop);
      }
      audiojs.events.ready(function() {
        audiojs.createAll("<?php echo $msg; ?>");
        var volume =   localStorage.getItem("zsAudioVolume") 
        if(volume ==undefined || volume ==''){
          volume = 0.3;
        }
         setVolume(volume);
      });
      function pause(){
      audiojs.instances["audiojs0"].pause();
      }
      function play(){
      audiojs.instances["audiojs0"].play();
      }

//       document.addEventListener('visibilitychange', function () {
//         var isHidden = document.hidden; 
//         var bmgIframe = $("#bmgIframe")[0].contentWindow; 
//         if (isHidden) {
//             //失去焦点
//             pause();
//         }
//         else {
//             //未失去焦点
//             bplay(); 
//         }
//     });
//     window.onfocus = function () {
//         play(); 
// };
// window.onblur = function () {
//     pause();
// };
    </script>
    <!-- <script>  

      var slider = document.getElementById('sm-mid');
      var scroll = slider.querySelector('.scrubber');
      var mask = slider.querySelector('.progress');

     var bar= slider.querySelector('.thumb');
      var barleft = 0;
      bar.onmousedown = function(event){
        var event = event || window.event;
        var leftVal = event.clientX - this.offsetLeft;
        var that = this;
         // 拖动一定写到 down 里面才可以
        document.onmousemove = function(event){
          var event = event || window.event;
          barleft = event.clientX - leftVal;     
          if(barleft < 0)
            barleft = 0;
          else if(barleft > scroll.offsetWidth - bar.offsetWidth)
            barleft = scroll.offsetWidth - bar.offsetWidth;
          mask.style.width = barleft +'px' ;
          //ptxt.innerHTML = "已经走了" + parseInt(barleft/(scroll.offsetWidth-bar.offsetWidth) * 100) + "%";
   
          //防止选择内容--当拖动鼠标过快时候，弹起鼠标，bar也会移动，修复bug
          window.getSelection ? window.getSelection().removeAllRanges() : document.selection.empty();
        }
   
      }
      document.onmouseup = function(){
        document.onmousemove = null; //弹起鼠标不做任何操作
      }
    </script> -->
  </body>
</html>
