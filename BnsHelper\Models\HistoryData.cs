﻿using System.Collections.ObjectModel;
using System.IO;
using System.Windows;
using CommunityToolkit.Mvvm.Input;
using Newtonsoft.Json;
using Xylia.BnsHelper.Common.Converters;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.BnsHelper.Resources;
using Xylia.Preview.Common.Attributes;
using Xylia.Preview.Common.Extension;

namespace Xylia.BnsHelper.Models;
internal partial class HistoryData(FileInfo file)
{
	#region Properties
	public EventHandler? OnChanged;
	public int Zone { get; set; }
	public DateTime Time { get; set; }
	public FPlayerCollection Data => JsonConvert.DeserializeObject<FPlayerCollection>(File.ReadAllText(file.FullName))!;

	public string? Name => string.Format(SettingHelper.Default.GroupMode ? "{0:yyyy年MM月dd日 HH:mm}" : "{0:MM月dd日 HH:mm} ({1})", 
		Time, NameConverter.Convert(Zone, "zone"));
	#endregion

	#region Methods
	[RelayCommand]
	public void Delete()
	{
		File.Delete(file.FullName);
		OnChanged?.Invoke(this, EventArgs.Empty);
	}

	[RelayCommand]
	void Post()
	{

	}

	public static HistoryData Load(FileInfo file)
	{
		var strs = Path.GetFileNameWithoutExtension(file.FullName).Split('_');
		var zone = int.Parse(strs[1]);
		var time = new DateTime(long.Parse(strs[2]));

		return new HistoryData(file) { Time = time, Zone = zone };
	}
	#endregion
}

internal partial class HistoryGroup : ObservableCollection<HistoryData>
{
	public object? Name { get; private set; }

	[RelayCommand]
	void DeleteAll()
	{
		if (MessageBox.Show(StringHelper.Get("Text.DeleteAll.Ask"), StringHelper.Get("ApplicationName"),
			MessageBoxButton.YesNo, icon: MessageBoxImage.Information) != MessageBoxResult.Yes) return;

		this.ForEach(o =>
		{
			o.OnChanged = null;
			o.Delete();
		});
		Clear();
	}

	public static List<HistoryGroup> GroupBy<TKey>(IEnumerable<HistoryData> datas, Func<HistoryData, TKey> selector)
	{
		var result = new List<HistoryGroup>();

		foreach (var data in datas.OrderByDescending(x => x.Time).GroupBy(selector))
		{
			var group = new HistoryGroup() { Name = data.Key };
			result.Add(group);

			foreach (var o in data)
			{
				group.Add(o);
				o.OnChanged += (_, _) => group.Remove(o);
			}
		}

		return result;
	}
}

public enum HistoryType
{
	Zone,
	[Text("HistoryType.Today")] Today,
	[Text("HistoryType.Week")] Week,
	[Text("HistoryType.Month")] Month,
	[Text("HistoryType.Other")] Other,
}