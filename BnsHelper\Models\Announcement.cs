using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace Xylia.BnsHelper.Models;

/// <summary>
/// 公告模型
/// </summary>
public class Announcement : INotifyPropertyChanged
{
    private string _title = string.Empty;
    private string _content = string.Empty;
    private DateTime _publishTime;
    private AnnouncementType _type;
    private bool _isRead;

    /// <summary>
    /// 公告ID
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// 公告标题
    /// </summary>
    public string Title
    {
        get => _title;
        set
        {
            if (_title != value)
            {
                _title = value;
                OnPropertyChanged();
            }
        }
    }

    /// <summary>
    /// 公告内容
    /// </summary>
    public string Content
    {
        get => _content;
        set
        {
            if (_content != value)
            {
                _content = value;
                OnPropertyChanged();
            }
        }
    }

    /// <summary>
    /// 发布时间
    /// </summary>
    public DateTime PublishTime
    {
        get => _publishTime;
        set
        {
            if (_publishTime != value)
            {
                _publishTime = value;
                OnPropertyChanged();
            }
        }
    }

    /// <summary>
    /// 公告类型
    /// </summary>
    public AnnouncementType Type
    {
        get => _type;
        set
        {
            if (_type != value)
            {
                _type = value;
                OnPropertyChanged();
            }
        }
    }

    /// <summary>
    /// 是否已读
    /// </summary>
    public bool IsRead
    {
        get => _isRead;
        set
        {
            if (_isRead != value)
            {
                _isRead = value;
                OnPropertyChanged();
            }
        }
    }

    /// <summary>
    /// 格式化的发布时间
    /// </summary>
    public string FormattedPublishTime => PublishTime.ToString("yyyy-MM-dd HH:mm");

    /// <summary>
    /// 类型显示文本
    /// </summary>
    public string TypeText => Type switch
    {
        AnnouncementType.Info => "信息",
        AnnouncementType.Warning => "警告",
        AnnouncementType.Update => "更新",
        AnnouncementType.Maintenance => "维护",
        _ => "未知"
    };

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}

/// <summary>
/// 公告类型
/// </summary>
public enum AnnouncementType
{
    /// <summary>
    /// 信息公告
    /// </summary>
    Info = 0,

    /// <summary>
    /// 警告公告
    /// </summary>
    Warning = 1,

    /// <summary>
    /// 更新公告
    /// </summary>
    Update = 2,

    /// <summary>
    /// 维护公告
    /// </summary>
    Maintenance = 3
}
