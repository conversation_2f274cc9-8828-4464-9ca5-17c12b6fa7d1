﻿using CUE4Parse.BNS;
using CUE4Parse.Compression;
using System.IO;
using System.Windows;
using Xylia.BnsHelper.Resources;
using Xylia.BnsHelper.Services;
using Xylia.Preview.Common;
using Xylia.Preview.Common.Extension;
using Xylia.Preview.Data.Client;
using Xylia.Preview.Data.Engine.DatData;
using Xylia.Preview.Data.Engine.Definitions;
using Xylia.Preview.Data.Models;

namespace Xylia.BnsHelper.Common.Helpers;
internal static class DataHelper
{
	private static BnsDatabase? _database;
	private static GameFileProvider? _provider;

	private static async Task<DatafileDefinition?> LoadData(string url, bool overwrite = false)
	{
		ArgumentNullException.ThrowIfNull(url);

		Stream? stream;
		string path = Path.Combine(SettingHelper.Default.DownloadFolder, "data");
		if (!overwrite && File.Exists(path)) stream = File.OpenRead(path);
		else
		{
			stream = await ApiEndpointService.GithubApi.Download(url);
			if (stream is null) return null;

			// create cache file
			await stream.SaveAsync(path);
			stream.Seek(0, SeekOrigin.Begin);
		}

		return new CompressDatafileDefinition(string.Empty, stream, CompressionMethod.Gzip);
	}

	public static void InitializeData()
	{
		// check status
		if (_database is not null) return;
		if (MessageBox.Show(StringHelper.Get("MainWindow_SetupData_Ask"), StringHelper.Get("ApplicationName"), MessageBoxButton.YesNo, MessageBoxImage.Question) != MessageBoxResult.Yes)
			throw new OperationCanceledException();

		// load data
		int retry = 0;
		while (retry++ < 2)
		{
			try
			{
				var definition = LoadData("https://api.github.com/repos/xyliaup/bns-definition/tarball/" +
					SettingHelper.Default.Game.Publisher.ToString().ToUpper(), retry > 0);

				var directory = new DirectoryInfo(SettingHelper.Default.Game.FullPath);
				Globals.GameData = _database ??= new BnsDatabase(DefaultProvider.Load(directory), definition.Result);
				Globals.GameProvider = _provider ??= new GameFileProvider(directory);
				Globals.GameData.Provider.GetTable<Item>();
			}
			catch
			{
				if (retry > 2) throw;
			}
		}

		// clear message - no longer needed since we use dialogs
	}
}
