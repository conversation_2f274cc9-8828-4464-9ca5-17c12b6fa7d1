package utils

import (
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"os/exec"
	"runtime"
	"strings"
)

// DeviceInfo 设备信息
type DeviceInfo struct {
	CPUID      string
	DiskSerial string
	MACAddress string
	QQNumber   string
}

// GetDeviceInfo 获取设备信息
func GetDeviceInfo(qqNumber string) (*DeviceInfo, error) {
	info := &DeviceInfo{
		QQNumber: qqNumber,
	}

	// 获取CPU ID
	if runtime.GOOS == "windows" {
		cmd := exec.Command("wmic", "cpu", "get", "ProcessorId")
		output, err := cmd.Output()
		if err == nil {
			lines := strings.Split(string(output), "\n")
			if len(lines) > 1 {
				info.CPUID = strings.TrimSpace(lines[1])
			}
		}
	}

	// 获取硬盘序列号
	if runtime.GOOS == "windows" {
		cmd := exec.Command("wmic", "diskdrive", "get", "SerialNumber")
		output, err := cmd.Output()
		if err == nil {
			lines := strings.Split(string(output), "\n")
			if len(lines) > 1 {
				info.DiskSerial = strings.TrimSpace(lines[1])
			}
		}
	}

	// 获取MAC地址
	if runtime.GOOS == "windows" {
		cmd := exec.Command("getmac", "/v", "/fo", "csv")
		output, err := cmd.Output()
		if err == nil {
			lines := strings.Split(string(output), "\n")
			if len(lines) > 1 {
				parts := strings.Split(lines[1], ",")
				if len(parts) > 1 {
					info.MACAddress = strings.Trim(strings.TrimSpace(parts[1]), "\"")
				}
			}
		}
	}

	return info, nil
}

// GenerateDeviceFingerprint 生成设备指纹
// 使用多层哈希和标准化处理，提高稳定性和安全性
func GenerateDeviceFingerprint(info *DeviceInfo) string {
	// 标准化处理设备信息，去除空格和特殊字符
	cpuid := normalizeDeviceInfo(info.CPUID)
	diskSerial := normalizeDeviceInfo(info.DiskSerial)
	macAddress := normalizeDeviceInfo(info.MACAddress)
	qqNumber := normalizeDeviceInfo(info.QQNumber)

	// 组合设备信息，使用固定分隔符
	data := fmt.Sprintf("CPU:%s|DISK:%s|MAC:%s|QQ:%s",
		cpuid, diskSerial, macAddress, qqNumber)

	// 第一层哈希：SHA256
	firstHash := sha256.Sum256([]byte(data))

	// 第二层哈希：加盐处理，增加安全性
	salt := "BnsPlugin_Device_Salt_2024"
	saltedData := fmt.Sprintf("%s|%s", hex.EncodeToString(firstHash[:]), salt)
	finalHash := sha256.Sum256([]byte(saltedData))

	return hex.EncodeToString(finalHash[:])
}

// normalizeDeviceInfo 标准化设备信息
func normalizeDeviceInfo(info string) string {
	// 去除空格和换行符
	normalized := strings.ReplaceAll(info, " ", "")
	normalized = strings.ReplaceAll(normalized, "\n", "")
	normalized = strings.ReplaceAll(normalized, "\r", "")
	normalized = strings.ReplaceAll(normalized, "\t", "")

	// 转换为大写，确保一致性
	normalized = strings.ToUpper(normalized)

	// 如果为空，使用默认值
	if normalized == "" {
		normalized = "UNKNOWN"
	}

	return normalized
}

// GenerateDeviceCode 生成设备码（用于签到等功能的设备标识）
// 设备码是基于设备指纹的简化版本，用于缓存键等场景
func GenerateDeviceCode(deviceFingerprint string, qqNumber string) string {
	// 组合设备指纹和QQ号
	data := fmt.Sprintf("DEVICE:%s|QQ:%s", deviceFingerprint, qqNumber)

	// 计算哈希并取前16位作为设备码
	hash := sha256.Sum256([]byte(data))
	deviceCode := hex.EncodeToString(hash[:8]) // 取前8字节，16个字符

	return strings.ToUpper(deviceCode)
}
