<?php

namespace app\ingame\controller\Character;

use think\exception\ClassNotFoundException;
use think\exception\HttpException;
use think\App;
use think\Request;
use think\facade\Db;
use think\facade\Cache;
use think\facade\Cookie;
use think\facade\Session;
use think\facade\Validate;
use think\facade\View;
use voku\helper\HtmlDomParser;
use app\ingame\model\BnsTop as BnsTopModel;
use app\common\model\BnsUserInfo as BnsUserInfoModel;
use app\manage\model\Profile as ProfileModel;
use app\manage\model\ProfileStyle as ProfileStyle;
use app\manage\model\Liar as LiarModel;

class CharacterInfo extends RedirectBase
{
    private $m_sRoleName = '';
    private $m_iServerId = 0;
    
    //图片随机接口定义
    private $imgUrl = '//api.ghser.com/random/api.php';

    public function __construct(App $app, Request $request) {
        parent::__construct($app, $request);
        
        $this->initParams();
        
        //获取自身信息
        $this->m_sRoleName = Session::get('self_roleName', null); //角色名
        $this->m_iServerId = Session::get('self_serverId', null); //服务器ID

        //
        $bg_img = $this->imgUrl;//默认图片
        View::assign('bg_img', $bg_img);
    }

    public function initParams() {
        //获取服务器ID
        $serverId = Cookie::get('world');
        if (empty($serverId)) {
            $serverId = $this->request->get('s', null);
        }
        
        if (empty($serverId)) {
            $serverId = Session::get('s', null);
        }
        
        if ($serverId < 10 || $serverId > 9999) {
            $serverId = 1911; //默认电信一区
        }

        Session::set('s', $serverId);

        if (Cookie::has('world') && Cookie::has('pcName')) {
            Session::set('self_serverId', Cookie::get('world'));//自身服务器ID
            Session::set('self_roleName', Cookie::get('pcName'));//自身角色名
        }

        $this->globalareaId = self::serverId2AreaId($serverId);
        $this->globalserverId = $serverId;
        $this->globalbnsDomain = sprintf('https://%dgate.bns.qq.com', $this->globalareaId);
    }

    public function profile() {
        // 初始化参数
        $this->InitRedirectParm();
        $url_s = $this->request->get('s', null);
        if(empty($url_s)) {
            $url = $_SERVER["REQUEST_URI"] . "&s=" . $this->globalserverId;
            return redirect($url);
        }

        // 初始化 所有人不是骗子 后续查询
        $liarCheck = LiarModel::CheckLiar($this->globalserverId, $this->roleName);
        $liar = LiarModel::GetDetail($this->globalserverId, $this->roleName);

        // 查询自定义信息，返回的对象不会为空
        $user = ProfileModel::find($this->globalserverId, $this->roleName);

        // 准备模板变量 - 基础变量初始化
        $charaterView = "";
        $charaterViewBorderHtml = "";
        $titleImgHtml = "";
        $jobImgHtml = 'class="profileimg"';
        $jobImgBorderHtml = "";
        $otherImgHtml = "";
        $describeHtml = "";
        $labelImgHtml = "";
        $opacityHtml = "";
        $embedHtml = "";
        $bgmHtml = "";
        $bgmTitleHtml = "";
        $textHtml = "";
        $pointer = "";
        $fontStyleHtml = "";
        $mouseStyleHtml = "";
        $extContent = '';

        // 默认角色图片
        if($charaterView == null || $charaterView == ""){
            $charaterView = '//api.btstu.cn/sjbz/api.php?lx=dongman&format=images&method=mobile';
        }

        if(!empty($user->charaterViewBorder)){
            $width = '110%';
            $height = '105%';
            $left = '-5%';
            $top = '-10px';
            $img = $user->charaterViewBorder;
            $pointer = 'auto';
            $isPngToGif = false;
            $this->getImgConfig($user->charaterViewBorder,$img,$width,$height,$left,$top,$pointer,$isPngToGif);
            if(!$isPngToGif){
                $charaterViewBorderHtml = '<div id="charaterViewBorder" style="overflow: hidden;position: absolute;width:'.$width.';height:'.$height.';min-height: 396px;z-index: 10000;top:'.$top.';left:'.$left.'; background-image: url(\''.$img.'\');background-size: 100% 100%;pointer-events:'.$pointer.'; "></div>';
            }else{
                $charaterViewBorderHtml = '<div id="charaterViewBorder" class="pngToGif" style="overflow: hidden;position: absolute;width:'.$width.';height:'.$height.';min-height: 396px;z-index: 10000;top:'.$top.';left:'.$left.'; background-image: url(\''.$img.'\');pointer-events:'.$pointer.'; "></div>';
            }
        }

        if(!empty($user->titleImg)){
            $width = 'auto';
            $height = 'auto';
            $left = 'auto';
            $bottom = '-10px';
            $img = $user->titleImg;
            $pointer = 'auto';
            $isPngToGif = false;
            $this->getImgConfig($user->titleImg,$img,$width,$height,$left,$bottom,$pointer,$isPngToGif);
            if(!$isPngToGif){
                $titleImgHtml = '<div style="position: absolute;width:100%;bottom:'.$bottom.';z-index: 10001;text-align: center;left:'.$left.';"><img src="'.$img.'" style="width:'.$width.';height:'.$height.';max-width: 100%;pointer-events:'.$pointer.'; "></div>';
            }else{
                $titleImgHtml = '<div class="pngToGif" style="position: absolute;width:100%;bottom:'.$bottom.';z-index: 10001;text-align: center;left:'.$left.';width:'.$width.';height:'.$height.';background-image: url(\''.$img.'\');pointer-events:'.$pointer.'; "></div>';
            }
        }

        if(!empty($user->otherImg)){
            $arr = explode(' ',$user->otherImg);
            foreach($arr as $index => $other){
                $width = 'auto';
                $height = 'auto';
                $left = '0';
                $top = '0';
                $img = $other;
                $pointer = 'auto';
                $isPngToGif = false;
                $this->getImgConfig($other,$img,$width,$height,$left,$top,$pointer,$isPngToGif);
                if(!empty($img)){
                    if(!$isPngToGif){
                        $otherImgHtml .= '<img src="'.$img.'" style="z-index:'.(20000+$index).';position: absolute;width:'.$width.';height:'.$height.';left:'.$left.';top:'.$top.';pointer-events:'.$pointer.'; "/>';
                    }else{
                        $otherImgHtml .= '<div class="pngToGif" style="z-index:'.(20000+$index).';position: absolute;width:'.$width.';height:'.$height.';left:'.$left.';top:'.$top.';pointer-events:'.$pointer.'; background-image: url(\''.$img.'\');"></div>';
                    }
                }
            }
        }

        // 显示自定义简介信息
        if(!empty($user->describe)){
            $describeHtml = '<b><span style="position:relative;z-index:9999;margin-left:20px;cursor:pointer;">'.$user->describe.'</span></b>';
        }

        // 简介骗子的简介信息
        if($liar) {
            // 读取骗子的简介信息。若不存在，则显示行骗手段
            $liarMsg = empty($liar['msg']) ? $liar['technique'] : $liar['msg'];
            $describeHtml = '<b><span style="position:relative;z-index:9999;margin-left:20px;color:red;font-size:25px;margin-left:110px;">'.$liarMsg.'</span></b>';
        }

        if(!empty($user->labelImg)){
            $arr = explode(' ',$user->labelImg);
            foreach($arr as $index => $label){
                $width = 'auto';
                $height = '35px';
                $left = 'auto';
                $top = 'auto';
                $img = $label;
                $pointer = 'auto';
                $isPngToGif = false;
                $this->getImgConfig($label,$img,$width,$height,$left,$top,$pointer,$isPngToGif);
                if(!empty($img)){
                    if(!$isPngToGif){
                        $labelImgHtml .= '<img src="'.$img.'" style="margin-left:10px;width:'.$width.';height:'.$height.';left:'.$left.';top:'.$top.';pointer-events:'.$pointer.';"/>';
                    }else{
                        $labelImgHtml .= '<div class="pngToGif" style="margin-left:10px;width:'.$width.';height:'.$height.';left:'.$left.';top:'.$top.';pointer-events:'.$pointer.';background-image: url(\''.$img.'\');"></div>';
                    }
                }
            }
        }

        if(!empty($user->jobImgBorder)){
            $width = '60px';
            $height = '60px';
            $left = '-4px';
            $top = '-4px';
            $img = $user->jobImgBorder;
            $pointer = 'auto';
            $isPngToGif = false;
            $this->getImgConfig($user->jobImgBorder,$img,$width,$height,$left,$top,$pointer,$isPngToGif);
            if(!$isPngToGif){
                $jobImgBorderHtml= '<img src="'.$img.'" style="width:'.$width.';height:'.$height.';left:'.$left.';top:'.$top.';position: absolute;pointer-events:'.$pointer.';"/>';
            }else{
                $jobImgBorderHtml= '<div class="pngToGif" style="width:'.$width.';height:'.$height.';left:'.$left.';top:'.$top.';position: absolute;pointer-events:'.$pointer.';background-image: url(\''.$img.'\');"></div>';
            }
        }

        if(!empty($user->jobImg)) {
            $width = '52px';
            $height = '52px';
            $left = '0';
            $top = '0';
            $img = $user->jobImg;
            $pointer = 'auto';
            $isPngToGif = false;
            $this->getImgConfig($user->jobImg,$img,$width,$height,$left,$top,$pointer,$isPngToGif);

            if(!$isPngToGif){
                $jobImgHtml = 'class="profileimg" style="width:'.$width.';height:'.$height.';background-image: url(\''.$img.'\');background-size: 100% 100%;position: relative;pointer-events:'.$pointer.';"';
            }else{
                $jobImgHtml = 'class="profileimg pngToGif" style="width:'.$width.';height:'.$height.';background-image: url(\''.$img.'\');position: relative;pointer-events:'.$pointer.';"';
            }
        }

        if(!empty($user->embed)) {
            $arr = explode(' ',$user->embed);
            foreach($arr as $index => $other){
                $width = 'auto';
                $height = 'auto';
                $left = '0';
                $top = '0';
                $img = $other;
                $pointer = 'auto';
                $isPngToGif = false;
                $this->getImgConfig($other,$img,$width,$height,$left,$top,$pointer,$isPngToGif);
                if(!empty($img)){
                    if(!$isPngToGif){
                        $embedHtml .= '<embed src="'.$img.'" wmode="transparent" allowNetworking="all" style="position: absolute;width:'.$width.';height:'.$height.';left:'.$left.';top:'.$top.';pointer-events:'.$pointer.'; "></embed>';
                    }else{
                        $embedHtml .= '<div class="pngToGif" style="position: absolute;width:'.$width.';height:'.$height.';left:'.$left.';top:'.$top.';pointer-events:'.$pointer.';background-image: url(\''.$img.'\'); "></div>';
                    }
                }
            }
        }

        //字体
        if(!empty($user->fontFamily) || !empty($user->textColor) || !empty($user->textShadowColor)) {
            $fontFamilyCss ="font-family:".$user->fontFamily." !important;";
            $textColorCss ="color:".$user->textColor." !important;";
            $shadowColorCss ="text-shadow:".$user->textShadowColor." 0px 0px 4px,".$user->textShadowColor." 0px 0px 8px !important;";
            $fontStyleHtml = HtmlDomParser::str_get_html(".gem-wrap input:nth-of-type(1):checked~label:nth-of-type(1), .gem-wrap input:nth-of-type(2):checked~label:nth-of-type(2), .result-count .count, .signature .desc .level,h2,h3,.desc li,a,span,.rank-point,.win-point{".$fontFamilyCss.$textColorCss.$shadowColorCss."}.gem-wrap>label{".$fontFamilyCss.$shadowColorCss."}");
        }

        //透明度
        if(!empty($user->opacity) || $user->opacity=='0') {
            $opacity = $user->opacity;
            $opacityHtml = HtmlDomParser::str_get_html('.loading,.ability-view .stat-description,.ability-view .stat-title,.btn-wrap .btn-more,.ability-view .stat-title.is-active, .ability-view .stat-title.stat-important,.buttons a{background-color: rgba(42,81,119,'.$opacity.') !important;}.background-image{opacity: '.(1-$opacity).' !important;}'.$this->getOpacityCss('.info-ability .point-ability','//down-update.qq.com/bns/static/ingameWeb/ingame/bns/ue4/character/img/bg-point-ability-box.png',$opacity));
        }

        //鼠标指针
        if(!empty($user->mouseIco) || !empty($user->mouseColor)){
            $mouseCss ="<style>";
            if(!empty($user->mouseIco)){
                $mouseCss .="html{cursor:url('".$user->mouseIco."'),auto;}";
            }
            if(!empty($user->mouseColor)){
                $mouseCss .=".stage {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    z-index: 5;
                    pointer-events: none
                }";
            }
            $mouseCss .="</style>";
            $mouseStyleHtml = $mouseCss;
            if(!empty($user->mouseColor)){
                $mouseStyleHtml .= '<canvas id="stage" class="stage"></canvas>
                <script type="text/javascript" src="/res/js/powder.js?color='.$user->mouseColor.'&speed=200"></script>';
            }
            $mouseStyleHtml = HtmlDomParser::str_get_html($mouseStyleHtml);
        }

        $bgmHtml = $this->getBGM($user->bgm, $user->bgmTitle, $pointer);

        // 获取战力信息
        $ranking = BnsTopModel::GetAllRankingByIDName($this->globalserverId, $this->roleName);
        $rankingInfo = null;
        if($ranking) {
            $rankingInfo = '<div class="powerinfo" data-content="战力:'. $ranking['allscore'] . "&#10;TOP:" .$ranking['zbrank'] . '"></div>';
        }

        $customize = [
            "charaterView" => $charaterView ?? null,//角色照片
            "bg_img" => $user->bg_img ?? $this->imgUrl, //背景图
            "charaterViewBorder" => $charaterViewBorderHtml ?? null,//角色边框
            "jobImgHtml" => $jobImgHtml?? null,
            "jobImgBorderHtml" => $jobImgBorderHtml?? null,
            "titleImg" => $titleImgHtml ?? null,//称号图
            "otherImg" => $otherImgHtml ?? null,//其他图片
            "embed" => $embedHtml ?? null,//signature
            "opacity" => $opacityHtml ?? null,//不透明度
            "bgmHtml" => $bgmHtml ?? null,//背景音乐
            "fontStyle" => $fontStyleHtml ?? null, //文体效果
            "mouseStyle" => $mouseStyleHtml ?? null, //鼠标效果
            "textHtml" => $textHtml ?? null,//测试内容
            "liarCheck" => $liarCheck ?? null,//是否为骗子
            "useCharaterview" => $user->usecharaterview, //启用角色图
            "extContent" => $user->extContent ?? null,  //扩展内容
            "ranking"=> $rankingInfo?? null,  //排名
        ];

        return View::fetch('Character/profile', [
            'neo' => $this->request->get('type', null) === 'neo',
            'customize' => $customize,
            'customizeJson' => json_encode($customize)
        ]);
    }

    public function allowed() {
        return json(["allowed" => true]);
    }
    
    public function characters() {
        return $this->ProxyUrl("/ingame/api/characters.json");
    }

    /**
     * 从API获取角色数据
     */
    private function getCharacterDataFromAPI($pcid) {
        try {
            // 这里应该调用实际的API获取角色数据
            // 暂时返回空数组，需要根据实际API实现
            return [];
        } catch (\Exception $e) {
            trace('获取角色数据失败: ' . $e->getMessage(), 'error');
            return null;
        }
    }

    /**
     * 获得背景音乐元素
     */
    public function getBGM($bgm, $bgmTitle, $pointer) {
        $width = '285px';
        $height = '52px';
        $left = '910px';
        $top = '65px';
        $autoplay ="autoplay";
        $loop = "loop";
        $bgmHtml = " ";
        if(!empty($bgm)){
            // 载入自定义BGM
            $img = $bgm;
            $this->getbmgConfig($bgm,$img,$width,$height,$left,$top,$pointer,$autoplay,$loop);
            $bgmHtml = HtmlDomParser::str_get_html('<iframe id="bmgIframe" frameborder="no" border="0" marginwidth="0" marginheight="0" src="/bmg?music='.urlencode($img).(empty($bgm)?'':'&msg='.urlencode($bgmTitle)).'&autoplay='.$autoplay.'&loop='.$loop.'" style="position: absolute;width:'.$width.';height:'.$height.';left:'.$left.';top:'.$top.';z-index: 6;"></iframe>');
        }

        return $bgmHtml;
    }

    /**
     * 获取透明度CSS
     */
    public function getOpacityCss($className, $bgImg, $opacity) {
        $css=$className.'{position: relative;background-image: url() !important;}'.$className.'::before {content:\'\' ;background: url('.$bgImg.');background-repeat: no-repeat; opacity: '.$opacity.';top: 0;left: 0;bottom: 0;right: 0;position: absolute;z-index: 0;}';
        return $css;
    }

    /**
     * 获取图片配置
     */
    public function getImgConfig($configStr, &$img, &$width, &$height, &$left, &$top, &$pointer, &$isPngToGif) {
        $arr = null;
        if(strpos($configStr,"&cfg=") != false){
            $arr = explode('&cfg=',$configStr);
        }else{
            $arr = explode('?',$configStr);
        }

        if(sizeof($arr)>1){
            if(strpos($arr[1],"=")!= false && !strpos($arr[1],",")!= false){
                return;
            }
         $config = explode(',',$arr[1]);
         if(sizeof($config)>1){
            $img = $arr[0];
         }else{
            $img = $configStr;
         }

         if(sizeof($config)>=4){
             if(!empty($config[0])||$config[0]=='0'){
                $width = is_numeric($config[0])?$config[0].'px':$config[0];
             }
             if(!empty($config[1])||$config[1]=='0'){
                $height = is_numeric($config[1])?$config[1].'px':$config[1];
             }
             if(!empty($config[2])||$config[2]=='0'){
                $left = is_numeric($config[2])?$config[2].'px':$config[2];
             }
             if(!empty($config[3])||$config[3]=='0'){
                $top = is_numeric($config[3])?$config[3].'px':$config[3];
             }
             if(!empty($config[4])){
                $pointer = $config[4];
             }
             if(!empty($config[5])){
                $isPngToGif = $config[5] == 'x';
             }
         }
        }else{
            $img = $configStr;
        }
    }

    /**
     * 获取背景音乐配置
     */
    public function getbmgConfig($configStr, &$bmg, &$width, &$height, &$left, &$top, &$pointer, &$autoplay, &$loop) {
        $arr = null;
        if(strpos($configStr,"&cfg=") > 0){
            $arr = explode('&cfg=',$configStr);
        }else{
            $arr = explode('?',$configStr);
        }

        if(sizeof($arr)>1){
         $config = explode(',',$arr[1]);
         if(sizeof($config)>=4){
            $bmg = $arr[0];
             if(!empty($config[0])||$config[0]=='0'){
                $width = is_numeric($config[0])?$config[0].'px':$config[0];
             }
             if(!empty($config[1])||$config[1]=='0'){
                $height = is_numeric($config[1])?$config[1].'px':$config[1];
             }
             if(!empty($config[2])||$config[2]=='0'){
                $left = is_numeric($config[2])?$config[2].'px':$config[2];
             }
             if(!empty($config[3])||$config[3]=='0'){
                $top = is_numeric($config[3])?$config[3].'px':$config[3];
             }
             if(!empty($config[4])){
                $pointer = $config[4];
             }
             if(!empty($config[5])){
                $autoplay = $config[5];
             }
             if(!empty($config[6])){
                $loop = $config[6];
             }
         }
        }else{
            $bmg = $configStr;
        }
    }

    /**
     * 角色搜索
     */
    public function search() {
        $keyword = $this->request->get('keyword', '');
        $serverId = $this->request->get('s', $this->globalserverId);
        
        if (empty($keyword)) {
            return json(['code' => 1, 'msg' => '搜索关键词不能为空']);
        }
        
        $users = BnsUserInfoModel::searchByName($keyword, $serverId);
        
        return json([
            'code' => 0,
            'data' => $users,
            'msg' => 'success'
        ]);
    }

    /**
     * 角色对比
     */
    public function compare() {
        $roleNames = $this->request->get('roles', '');
        $serverId = $this->request->get('server_id', $this->globalserverId);
        
        if (empty($roleNames)) {
            return View::fetch('Character/compare');
        }
        
        $roles = explode(',', $roleNames);
        $compareData = [];
        
        foreach ($roles as $roleName) {
            $roleName = trim($roleName);
            if (!empty($roleName)) {
                $user = BnsUserInfoModel::detail($serverId, $roleName);
                if ($user) {
                    $compareData[] = $user;
                }
            }
        }
        
        return View::fetch('Character/compare', [
            'compare_data' => $compareData,
            'server_id' => $serverId
        ]);
    }

    /**
     * 获取职业列表
     */
    public function jobs() {
        $data = [];
        $data[0] = ["id" => 1 ,"code" => "blademaster","name" => "剑士"];
        $data[1] = ["id" => 2 ,"code" => "kungfufighter","name" => "拳师"];
        $data[2] = ["id" => 3 ,"code" => "forcemaster","name" => "气功师"];
        $data[3] = ["id" => 4 ,"code" => "shooter","name" => "枪手"];
        $data[4] = ["id" => 5 ,"code" => "destroyer","name" => "力士"];
        $data[5] = ["id" => 6 ,"code" => "summoner","name" => "召唤师"];
        $data[6] = ["id" => 7 ,"code" => "assassin","name" => "刺客"];
        $data[7] = ["id" => 8 ,"code" => "bladedancer","name" => "灵剑士"];
        $data[8] = ["id" => 9 ,"code" => "warlock","name" => "咒术师"];
        $data[9] = ["id" => 10 ,"code" => "soulfighter","name" => "气宗"];
        $data[10] = ["id" => 11 ,"code" => "warrior","name" => "斗士"];
        $data[11] = ["id" => 12 ,"code" => "archer","name" => "弓手"];
        $data[12] = ["id" => 14 ,"code" => "thunderer","name" => "星术师"];
        $data[13] = ["id" => 15 ,"code" => "dualblader","name" => "双剑士"];
        $data[14] = ["id" => 16 ,"code" => "bard","name" => "乐师"];
        $data[15] = ["id" => 17 ,"code" => "xxxx","name" => "未定义"];

        return json($data);
    }

    /**
     * 获取角色信息
     */
    public function info() {
        return $this->ProxyUrl("/ingame/api/character/info.json");
    }

    /**
     * 获取角色信息 UE4版本
     */
    public function info_ue4() {
        return $this->ProxyUrl("/ingame/api/character/info_ue4.json");
    }

    /**
     * 获取角色能力
     */
    public function abilities() {
        return $this->ProxyUrl("/ingame/api/character/abilities.json");
    }

    /**
     * 获取能力点效果
     */
    public function pointseffects() {
        return $this->ProxyUrl("/ingame/api/character/abilities/pointseffects.json");
    }

    /**
     * 获取装备信息
     */
    public function equipments() {
        return $this->ProxyUrl("/ingame/api/character/equipments.json");
    }

    /**
     * 用户建议
     */
    public function SuggestUser() {
        $keyword = $this->request->get('q', '');
        
        if (empty($keyword)) {
            return json([]);
        }
        
        $users = BnsUserInfoModel::searchByName($keyword, $this->globalserverId, 10);
        $suggestions = [];
        
        foreach ($users as $user) {
            $suggestions[] = [
                'name' => $user['name'],
                'server_id' => $user['worldid'],
                'level' => $user['level'] ?? 0,
                'job' => $user['job'] ?? 0
            ];
        }
        
        return json($suggestions);
    }

    /**
     * 获取静态的能力点效果数据（备用数据）
     */
    private function getStaticPointsEffects()
    {
        // 这里可以返回一些基础的能力点效果数据
        // 或者从本地文件读取静态数据
        $staticFile = $this->app->getPublicPath() . 'ingame/data/pointseffects.json';

        if (file_exists($staticFile)) {
            $content = file_get_contents($staticFile);
            return json_decode($content, true);
        }

        // 返回基础的空数据结构
        return [
            'success' => true,
            'data' => [],
            'message' => '使用静态数据'
        ];
    }
}
