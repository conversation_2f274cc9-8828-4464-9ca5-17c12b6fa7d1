{layout name="Character/template" /}

<style>
{$opacity}
</style>

{$textHtml|raw}
{$bgmHtml|raw}
{$otherImg|raw}
{$embed|raw}

<!-- 顶部信息 -->
<header id="header" class="summary">
	<!-- 加载用户信息 -->
	{$signature|raw}
	
	<!-- 加载切换按钮 -->
	<div class="button">
	    <span class="wrapTraining">
		    <a href="/ingame/bs/character/profile?c={$roleName}&s={$serverId}" class="btnTraining" onmousedown="try{ _trk_clickTrace('EVT', '/BNS/ingame/角色主页/角色信息');}catch(_e){}">角色信息</a>
	    </span>							
	</div>	
</header>

<!-- 评论组件 -->
<section id="comment">
	<div class="wrap_comments" >
	    <!-- 评论标题 -->
	    <div class="comment_title"><h2><strong>留言（<span id="msg_count">???</span>条）</strong></h2></div><br/>

	    <!-- 评论输入框 -->
	    <div class="comment_input">
	        <div class="input_area">
	            <textarea id="comment_text" placeholder="请输入留言内容..." maxlength="500"></textarea>
	            <div class="input_bottom">
	                <span class="char_count">0/500</span>
	                <button id="submit_comment" class="btn_submit">发表留言</button>
	            </div>
	        </div>
	    </div>

	    <!-- 评论列表 -->
	    <div class="comment_list" id="comment_list">
	        <div class="loading">加载中...</div>
	    </div>

	    <!-- 分页 -->
	    <div class="pagination" id="pagination">
	        <!-- 分页按钮将通过JS动态生成 -->
	    </div>
	</div>
</section>

<script>
$(document).ready(function() {
    var currentPage = 1;
    var roleName = '{$roleName}';
    var serverId = '{$serverId}';
    
    // 加载留言列表
    function loadComments(page) {
        $.ajax({
            url: '/ingame/api/character/board/list',
            type: 'GET',
            data: {
                character: roleName,
                server: serverId,
                page: page
            },
            success: function(response) {
                if (response.code === 0) {
                    renderComments(response.data.list);
                    renderPagination(response.data.pagination);
                    $('#msg_count').text(response.data.total);
                } else {
                    $('#comment_list').html('<div class="error">加载失败：' + response.msg + '</div>');
                }
            },
            error: function() {
                $('#comment_list').html('<div class="error">网络错误，请稍后重试</div>');
            }
        });
    }
    
    // 渲染留言列表
    function renderComments(comments) {
        var html = '';
        if (comments.length === 0) {
            html = '<div class="no_comments">暂无留言</div>';
        } else {
            comments.forEach(function(comment) {
                html += '<div class="comment_item">';
                html += '<div class="comment_header">';
                html += '<span class="author">' + comment.author + '</span>';
                html += '<span class="time">' + comment.create_time + '</span>';
                html += '</div>';
                html += '<div class="comment_content">' + comment.content + '</div>';
                html += '</div>';
            });
        }
        $('#comment_list').html(html);
    }
    
    // 渲染分页
    function renderPagination(pagination) {
        var html = '';
        if (pagination.total_pages > 1) {
            if (pagination.current_page > 1) {
                html += '<button class="page_btn" data-page="' + (pagination.current_page - 1) + '">上一页</button>';
            }
            
            for (var i = 1; i <= pagination.total_pages; i++) {
                if (i === pagination.current_page) {
                    html += '<button class="page_btn active">' + i + '</button>';
                } else {
                    html += '<button class="page_btn" data-page="' + i + '">' + i + '</button>';
                }
            }
            
            if (pagination.current_page < pagination.total_pages) {
                html += '<button class="page_btn" data-page="' + (pagination.current_page + 1) + '">下一页</button>';
            }
        }
        $('#pagination').html(html);
    }
    
    // 提交留言
    $('#submit_comment').click(function() {
        var content = $('#comment_text').val().trim();
        if (!content) {
            alert('请输入留言内容');
            return;
        }
        
        $.ajax({
            url: '/ingame/api/character/board/add',
            type: 'POST',
            data: {
                character: roleName,
                server: serverId,
                content: content
            },
            success: function(response) {
                if (response.code === 0) {
                    $('#comment_text').val('');
                    $('.char_count').text('0/500');
                    loadComments(1); // 重新加载第一页
                    alert('留言发表成功');
                } else {
                    alert('留言发表失败：' + response.msg);
                }
            },
            error: function() {
                alert('网络错误，请稍后重试');
            }
        });
    });
    
    // 字符计数
    $('#comment_text').on('input', function() {
        var length = $(this).val().length;
        $('.char_count').text(length + '/500');
    });
    
    // 分页点击事件
    $(document).on('click', '.page_btn[data-page]', function() {
        var page = parseInt($(this).data('page'));
        currentPage = page;
        loadComments(page);
    });
    
    // 初始加载
    loadComments(1);
});
</script>

<style>
.wrap_comments {
    max-width: 800px;
    margin: 20px auto;
    padding: 20px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
}

.comment_title h2 {
    color: #333;
    margin-bottom: 20px;
}

.comment_input {
    margin-bottom: 30px;
}

.input_area textarea {
    width: 100%;
    height: 100px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    resize: vertical;
    font-family: inherit;
}

.input_bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
}

.char_count {
    color: #999;
    font-size: 12px;
}

.btn_submit {
    background: #007bff;
    color: white;
    border: none;
    padding: 8px 20px;
    border-radius: 5px;
    cursor: pointer;
}

.btn_submit:hover {
    background: #0056b3;
}

.comment_item {
    border-bottom: 1px solid #eee;
    padding: 15px 0;
}

.comment_header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.author {
    font-weight: bold;
    color: #333;
}

.time {
    color: #999;
    font-size: 12px;
}

.comment_content {
    color: #666;
    line-height: 1.5;
}

.pagination {
    text-align: center;
    margin-top: 20px;
}

.page_btn {
    margin: 0 5px;
    padding: 5px 10px;
    border: 1px solid #ddd;
    background: white;
    cursor: pointer;
}

.page_btn.active {
    background: #007bff;
    color: white;
}

.page_btn:hover {
    background: #f8f9fa;
}

.loading, .error, .no_comments {
    text-align: center;
    padding: 20px;
    color: #999;
}

.error {
    color: #dc3545;
}
</style>
