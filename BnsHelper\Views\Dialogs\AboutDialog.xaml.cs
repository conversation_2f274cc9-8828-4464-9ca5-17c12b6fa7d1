﻿using CommunityToolkit.Mvvm.ComponentModel;
using HandyControl.Interactivity;
using System.ComponentModel;
using System.Windows;
using System.Windows.Input;
using Xylia.BnsHelper.ViewModels;

namespace Xylia.Preview.UI.Views.Dialogs;
[ObservableObject]
[DesignTimeVisible(false)]
public partial class AboutDialog 
{
    #region Constructor
    readonly AboutViewModel _viewModel;

    public AboutDialog()
    {
        InitializeComponent();
        CommandBindings.Add(new CommandBinding(ControlCommands.Close, CloseCommand));
        DataContext = _viewModel = new AboutViewModel();
        Loaded += OnLoaded;
    }
    #endregion

    #region Methods
    private async void OnLoaded(object sender, RoutedEventArgs e)
    {
        await _viewModel.Initialize();
    }

    private void Ok_Click(object sender, RoutedEventArgs e)
    {
        _viewModel.CloseAction?.Invoke();
        _viewModel.Result = true;
    }

    private void CloseCommand(object sender, RoutedEventArgs e)
    {
        _viewModel.CloseAction?.Invoke();
    }
    #endregion
}