﻿using Microsoft.Web.WebView2.Core;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Diagnostics;
using System.IO;
using System.Management;
using System.Net.NetworkInformation;
using System.Text;
using System.Web;
using System.Windows;
using Xylia.BnsHelper.Common;
using Xylia.BnsHelper.Common.Extensions;
using Xylia.BnsHelper.Models;
using Xylia.BnsHelper.Resources;
using Xylia.BnsHelper.Services.Network.BinaryProtocol;
using Xylia.BnsHelper.Services.Network.Service;
using Xylia.BnsHelper.ViewModels;
using Xylia.Preview.Common.Extension;

namespace Xylia.BnsHelper.Views;
public partial class UserLogin2
{
    #region Constructor
    //const string Source = "https://xui.ptlogin2.qq.com/cgi-bin/xlogin?proxy_url=http://game.qq.com/comm-htdocs/milo/proxy.html&appid=21000501&s_url=https://bns.qq.com/comm-htdocs/login/logincallback.htm";
    const string Source = "https://xui.ptlogin2.qq.com/cgi-bin/xlogin?pt_disable_pwd=1&appid=715030901&daid=73&s_url=https%3A%2F%2Fqun.qq.com/";

    public UserLogin2()
    {
        InitializeComponent();
        CheckWebView2Runtime();

        // 获取必要参数
        var query = HttpUtility.ParseQueryString(new Uri(Source).Query);
        callback = HttpUtility.UrlDecode(query["s_url"]);
    }
    #endregion

    #region Methods
    private void CheckWebView2Runtime()
    {
        try
        {
            // 尝试获取WebView2运行时版本
            var version = CoreWebView2Environment.GetAvailableBrowserVersionString();
            if (string.IsNullOrEmpty(version))
            {
                ShowWebView2RuntimeError();
                return;
            }

            // 运行时可用，设置WebView2源
            wv.Source = new Uri(Source);
        }
        catch (Exception)
        {
            // 检测失败，显示错误
            ShowWebView2RuntimeError();
        }
    }

    private void ShowWebView2RuntimeError()
    {
        // 隐藏WebView2控件
        wv.Visibility = Visibility.Collapsed;

        // 显示错误信息
        ShowWebView2Error(null);
    }

    private async void CoreWebView2InitializationCompleted(object? sender, CoreWebView2InitializationCompletedEventArgs e)
    {
        if (!e.IsSuccess)
        {
            // WebView2运行时初始化失败，显示错误信息
            ShowWebView2Error(e.InitializationException);
            return;
        }

        wv.CoreWebView2.Settings.AreDevToolsEnabled = wv.CoreWebView2.Settings.AreBrowserAcceleratorKeysEnabled = true;
        wv.CoreWebView2.Settings.UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 bnszs/0.1";
        wv.CoreWebView2.AddWebResourceRequestedFilter("*", CoreWebView2WebResourceContext.All);

        wv.CoreWebView2.ContextMenuRequested += (s, e) => e.Handled = true;
        wv.CoreWebView2.NewWindowRequested += CoreWebView2_NewWindowRequested;
        wv.CoreWebView2.SourceChanged += CoreWebView2_SourceChanged;
        wv.CoreWebView2.WebResourceResponseReceived += CoreWebView2_WebResourceResponseReceived;

        await wv.CoreWebView2.AddScriptToExecuteOnDocumentCreatedAsync($$"""
		document.addEventListener('DOMContentLoaded', function() {
			document.getElementById("close").style.display = 'none';
		});

		window.onload = function() {
			document.getElementById("qr_area").style.display = 'none';
			document.getElementById("bottom_qlogin").style.display = 'none';
		}
		""");
    }

    private void CoreWebView2_NewWindowRequested(object? sender, CoreWebView2NewWindowRequestedEventArgs e)
    {
        e.Handled = true;
        Process.Start("explorer.exe", e.Uri.ToString());
    }

    private void CoreWebView2_SourceChanged(object? sender, CoreWebView2SourceChangedEventArgs e)
    {
        var source = wv.CoreWebView2.Source;
        if (source.StartsWith("https://xui.ptlogin2.qq.com/cgi-bin/xlogin")) uin = 0;
        else if (source == callback)
        {
            // 验证回调页面
        }
    }

    private async void CoreWebView2_WebResourceResponseReceived(object? sender, CoreWebView2WebResourceResponseReceivedEventArgs e)
    {
        var uri = e.Request.Uri;
        if (uri.StartsWith("https://ssl.ptlogin2.qq.com/jump"))
        {
            var query = HttpUtility.ParseQueryString(new Uri(uri).Query);
            uin = query["clientuin"].To<long>();
            wv.Visibility = Visibility.Collapsed;   //隐藏浏览器
            ShowLoading();  //立即显示登录中提示
        }
        else if (uri.Contains("/get_group_list"))
        {
            var content = await e.Response.GetContentAsync();
            var text = new StreamReader(content).ReadToEnd();
            var token = JsonConvert.DeserializeObject<JToken>(text)!;

            void CheckInGroup()
            {
                Groups.Clear();
                token["join"]?.ForEach(o => Groups.Add(o.Value<long>("gc")!));
                token["manage"]?.ForEach(o => Groups.Add(o.Value<long>("gc")!));

                // 检查是否加入了白名单群
                var whitegroup = AppExtensions.FindProperty<string?>("groups") ?? "";
                var flag = Groups.Any(o => whitegroup.Contains(o.ToString()));
                if (!flag) throw new AppException("请先加入剑灵小助手交流群\n群列表可访问 www.bnszs.com 查看");
            }

            try
            {
                CheckInGroup();
                await CheckUin();
            }
            catch (Exception ex)
            {
                ShowError(ex.Message);
            }
        }
    }

    private void ShowWebView2Error(Exception? exception)
    {
        // Hide WebView2 control
        wv.Visibility = Visibility.Collapsed;

        // Hide loading icon and status text, show error container
        LoadingIcon.Visibility = Visibility.Collapsed;
        StatusText.Visibility = Visibility.Collapsed;
        ErrorContainer.Visibility = Visibility.Visible;

        // Update error text
        ErrorText.Text = "浏览器组件初始化失败\n请下载 Microsoft Edge WebView2 组件";

        // Update button text for WebView2 error
        RetryButton.Content = "下载";
        CancelButton.Content = "取消";

        // Show button container
        ButtonContainer.Visibility = Visibility.Visible;

        // Keep overlay visible to show error
        LoadingOverlay.Visibility = Visibility.Visible;
    }

    private void ShowError(string errorMessage)
    {
        // Hide loading icon and status text, show error container
        LoadingIcon.Visibility = Visibility.Collapsed;
        StatusText.Visibility = Visibility.Collapsed;
        ErrorContainer.Visibility = Visibility.Visible;

        // Update error text
        ErrorText.Text = errorMessage;

        // Reset button text for normal errors
        RetryButton.Content = "重试";
        CancelButton.Content = "取消";

        // Show button container
        ButtonContainer.Visibility = Visibility.Visible;

        // Keep overlay visible to show error
        LoadingOverlay.Visibility = Visibility.Visible;
    }

    private void ShowLoading()
    {
        // Show loading icon and status text, hide error elements
        LoadingIcon.Visibility = Visibility.Visible;
        StatusText.Visibility = Visibility.Visible;
        ErrorContainer.Visibility = Visibility.Collapsed;
        ButtonContainer.Visibility = Visibility.Collapsed;

        // Reset status text
        StatusText.Text = "登录中";

        // Show overlay
        LoadingOverlay.Visibility = Visibility.Visible;
    }

    private void RetryButton_Click(object sender, RoutedEventArgs e)
    {
        // Check if this is a WebView2 error (button text is "下载")
        if (RetryButton.Content.ToString() == "下载")
        {
            // Fallback to explorer
            Process.Start("explorer.exe", "https://developer.microsoft.com/zh-cn/microsoft-edge/webview2/consumer/");
        }

        // Normal retry logic
        // Hide error state and show browser again
        LoadingOverlay.Visibility = Visibility.Collapsed;
        wv.Visibility = Visibility.Visible;
        wv.Source = new Uri(Source);
    }

    private void CancelButton_Click(object sender, RoutedEventArgs e)
    {
        // Close the login window
        DialogResult = false;
        Close();
    }

    private async Task CheckUin()
    {
        // 只有快捷登录可以获取到用户QQ
        if (uin == 0)
        {
            LoadingOverlay.Visibility = Visibility.Collapsed;  //隐藏加载提示
            wv.Visibility = Visibility.Visible;  //重新显示浏览器
            wv.Source = new Uri(Source);
            MessageBox.Show(StringHelper.Get("UseLogin_Error"), StringHelper.Get("ApplicationName"), icon: MessageBoxImage.Stop);
            return;
        }

        try
        {
            // Create session
            var session = new BnszsSession();
            var systemInfo = GatherSystemInfo();

            // Generate device fingerprint locally
            var deviceFingerprint = GenerateDeviceFingerprint(systemInfo);

            // Create login packet with device fingerprint (server will generate device code)
            session.SendPacket(new LoginPacket
            {
                QQNumber = uin.ToString(),
                DeviceFingerprint = deviceFingerprint // 传输设备指纹，服务端生成设备码
            }, MessageTypes.Login);

            // Wait for login response specifically, ignoring heartbeat packets
            var response = await session.WaitForResponseWithRetry(MessageTypes.LoginResponse, 3, 3000);
            if (response is LoginPacket login)
            {
                if (login.ErrorCode != 0) throw new AppException(login.ErrorMessage);

                // 验证成功完成登录流程
                var user = new User(session)
                {
                    Uid = login.UserID,
                    Uin = uin,
                    Permission = login.Permission, // 最终权限：服务端计算的结果
                };

                // 设置权限过期时间
                if (login.PermissionExpiration == -1)
                {
                    // 永久权限
                    user.PermissionExpiration = DateTime.MaxValue;
                }
                else if (login.PermissionExpiration > 0)
                {
                    // 具体过期时间（Unix时间戳转换为DateTime）
                    user.PermissionExpiration = DateTimeOffset.FromUnixTimeSeconds(login.PermissionExpiration).DateTime;
                }
                else
                {
                    // 无权限
                    user.PermissionExpiration = null;
                }

                MainWindowViewModel.Instance.User = user;
                DialogResult = true;
            }
        }
        catch (Exception ex)
        {
            ShowError(ex.Message);
            return;
        }
        finally
        {
            // Hide loading animation only on success
            if (DialogResult == true)
            {
                LoadingOverlay.Visibility = Visibility.Collapsed;
            }
        }
    }
    #endregion

    #region Methods
    private static SystemInfo GatherSystemInfo()
    {
        var info = new SystemInfo();

        // 使用更稳定的设备信息收集方法
        info.CPU = GetCPUInfo();
        info.Memory = GetMemoryInfo();
        info.Motherboard = GetMotherboardInfo();
        info.Disk = GetDiskInfo();
        info.MACAddress = GetMACAddress();

        // 最终验证 - 如果所有信息都是默认值，生成唯一标识符
        if (IsAllDefaultValues(info))
        {
            GenerateUniqueDeviceInfo(info);
        }

        return info;
    }

    private static string GetCPUInfo()
    {
        // 方法1: WMI查询
        try
        {
            using var searcher = new ManagementObjectSearcher("SELECT Name, ProcessorId FROM Win32_Processor");
            foreach (ManagementObject obj in searcher.Get())
            {
                var name = obj["Name"]?.ToString();
                var processorId = obj["ProcessorId"]?.ToString();
                if (!string.IsNullOrEmpty(name))
                {
                    // 如果有ProcessorId，添加到名称中增加唯一性
                    return !string.IsNullOrEmpty(processorId) ? $"{name}-{processorId[..8]}" : name;
                }
            }
        }
        catch { }

        // 方法2: 环境变量
        try
        {
            var processorIdentifier = Environment.GetEnvironmentVariable("PROCESSOR_IDENTIFIER");
            var processorArchitecture = Environment.GetEnvironmentVariable("PROCESSOR_ARCHITECTURE");
            if (!string.IsNullOrEmpty(processorIdentifier))
            {
                return $"{processorIdentifier}-{processorArchitecture}";
            }
        }
        catch { }

        // 方法3: Registry查询
        try
        {
            using var key = Microsoft.Win32.Registry.LocalMachine.OpenSubKey(@"HARDWARE\DESCRIPTION\System\CentralProcessor\0");
            var processorName = key?.GetValue("ProcessorNameString")?.ToString();
            var identifier = key?.GetValue("Identifier")?.ToString();
            if (!string.IsNullOrEmpty(processorName))
            {
                return !string.IsNullOrEmpty(identifier) ? $"{processorName}-{identifier}" : processorName;
            }
        }
        catch { }

        // 最后备用方案
        return $"CPU-{Environment.ProcessorCount}Core-{Environment.MachineName}";
    }

    private static string GetMemoryInfo()
    {
        // 方法1: WMI查询
        try
        {
            using var searcher = new ManagementObjectSearcher("SELECT TotalPhysicalMemory FROM Win32_ComputerSystem");
            foreach (ManagementObject obj in searcher.Get())
            {
                if (obj["TotalPhysicalMemory"] != null)
                {
                    var totalMemory = Convert.ToUInt64(obj["TotalPhysicalMemory"]);
                    return $"{totalMemory / (1024 * 1024 * 1024)} GB";
                }
            }
        }
        catch { }

        // 方法2: Performance Counter
        try
        {
            using var pc = new System.Diagnostics.PerformanceCounter("Memory", "Available Bytes");
            var availableMemory = pc.NextValue();
            // 估算总内存（可用内存通常是总内存的一部分）
            var estimatedTotal = (long)(availableMemory * 1.5 / (1024 * 1024 * 1024));
            return $"~{estimatedTotal} GB";
        }
        catch { }

        // 方法3: GC内存信息
        try
        {
            var totalMemory = GC.GetTotalMemory(false) / (1024 * 1024);
            return $"GC-{totalMemory} MB";
        }
        catch { }

        return $"MEM-{Environment.WorkingSet / (1024 * 1024)} MB";
    }

    private static string GetMotherboardInfo()
    {
        // 方法1: WMI查询主板信息
        try
        {
            using var searcher = new ManagementObjectSearcher("SELECT Product, Manufacturer, SerialNumber FROM Win32_BaseBoard");
            foreach (ManagementObject obj in searcher.Get())
            {
                var manufacturer = obj["Manufacturer"]?.ToString();
                var product = obj["Product"]?.ToString();
                var serialNumber = obj["SerialNumber"]?.ToString();

                if (!string.IsNullOrEmpty(manufacturer) && !string.IsNullOrEmpty(product))
                {
                    var result = $"{manufacturer} {product}";
                    if (!string.IsNullOrEmpty(serialNumber) && serialNumber.Length > 4)
                    {
                        result += $"-{serialNumber[..4]}";
                    }
                    return result;
                }
            }
        }
        catch { }

        // 方法2: 计算机系统信息
        try
        {
            using var searcher = new ManagementObjectSearcher("SELECT Model, Manufacturer FROM Win32_ComputerSystem");
            foreach (ManagementObject obj in searcher.Get())
            {
                var manufacturer = obj["Manufacturer"]?.ToString();
                var model = obj["Model"]?.ToString();
                if (!string.IsNullOrEmpty(manufacturer) && !string.IsNullOrEmpty(model))
                {
                    return $"{manufacturer} {model}";
                }
            }
        }
        catch { }

        // 方法3: BIOS信息
        try
        {
            using var searcher = new ManagementObjectSearcher("SELECT Manufacturer, Version FROM Win32_BIOS");
            foreach (ManagementObject obj in searcher.Get())
            {
                var manufacturer = obj["Manufacturer"]?.ToString();
                var version = obj["Version"]?.ToString();
                if (!string.IsNullOrEmpty(manufacturer))
                {
                    return $"BIOS-{manufacturer}-{version}";
                }
            }
        }
        catch { }

        return $"MB-{Environment.MachineName}-{Environment.OSVersion.Platform}";
    }

    private static string GetDiskInfo()
    {
        // 方法1: WMI查询硬盘信息
        try
        {
            using var searcher = new ManagementObjectSearcher("SELECT Model, Size, SerialNumber FROM Win32_DiskDrive WHERE MediaType='Fixed hard disk media'");
            foreach (ManagementObject obj in searcher.Get())
            {
                var model = obj["Model"]?.ToString();
                var serialNumber = obj["SerialNumber"]?.ToString();
                var size = obj["Size"] != null ? Convert.ToUInt64(obj["Size"]) / (1024 * 1024 * 1024) : 0;

                if (!string.IsNullOrEmpty(model))
                {
                    var result = $"{model} ({size} GB)";
                    if (!string.IsNullOrEmpty(serialNumber) && serialNumber.Length > 4)
                    {
                        result += $"-{serialNumber[..4]}";
                    }
                    return result;
                }
            }
        }
        catch { }

        // 方法2: DriveInfo
        try
        {
            var drives = DriveInfo.GetDrives()
                .Where(d => d.DriveType == DriveType.Fixed && d.IsReady)
                .OrderBy(d => d.Name)
                .ToArray();

            if (drives.Length > 0)
            {
                var primaryDrive = drives[0];
                var totalSize = primaryDrive.TotalSize / (1024 * 1024 * 1024);
                var freeSpace = primaryDrive.AvailableFreeSpace / (1024 * 1024 * 1024);
                return $"Drive-{primaryDrive.Name.Replace(":\\", "")} ({totalSize}GB-{freeSpace}GB)";
            }
        }
        catch { }

        // 方法3: 逻辑磁盘信息
        try
        {
            using var searcher = new ManagementObjectSearcher("SELECT Size, FreeSpace, DeviceID FROM Win32_LogicalDisk WHERE DriveType=3");
            foreach (ManagementObject obj in searcher.Get())
            {
                var deviceId = obj["DeviceID"]?.ToString();
                var size = obj["Size"] != null ? Convert.ToUInt64(obj["Size"]) / (1024 * 1024 * 1024) : 0;
                var freeSpace = obj["FreeSpace"] != null ? Convert.ToUInt64(obj["FreeSpace"]) / (1024 * 1024 * 1024) : 0;

                if (!string.IsNullOrEmpty(deviceId))
                {
                    return $"LogicalDisk-{deviceId} ({size}GB-{freeSpace}GB)";
                }
            }
        }
        catch { }

        return $"DISK-{Environment.SystemDirectory[0]}-{Environment.MachineName}";
    }

    private static string GetMACAddress()
    {
        // 方法1: 获取物理网卡MAC地址（排除虚拟网卡）
        try
        {
            var networkInterfaces = NetworkInterface.GetAllNetworkInterfaces()
                .Where(nic => nic.OperationalStatus == OperationalStatus.Up &&
                             nic.NetworkInterfaceType != NetworkInterfaceType.Loopback &&
                             nic.NetworkInterfaceType != NetworkInterfaceType.Tunnel &&
                             !nic.GetPhysicalAddress().Equals(PhysicalAddress.None) &&
                             !nic.Description.ToLower().Contains("virtual") &&
                             !nic.Description.ToLower().Contains("vmware") &&
                             !nic.Description.ToLower().Contains("hyper-v"))
                .OrderBy(nic => nic.Name)
                .ToArray();

            if (networkInterfaces.Length > 0)
            {
                var macAddress = networkInterfaces[0].GetPhysicalAddress().ToString();
                if (macAddress.Length == 12)
                {
                    return string.Join(":", Enumerable.Range(0, 6)
                        .Select(i => macAddress.Substring(i * 2, 2)));
                }
                return macAddress;
            }
        }
        catch { }

        // 方法2: WMI查询网络适配器
        try
        {
            using var searcher = new ManagementObjectSearcher("SELECT MACAddress, AdapterType FROM Win32_NetworkAdapter WHERE MACAddress IS NOT NULL AND AdapterType IS NOT NULL");
            foreach (ManagementObject obj in searcher.Get())
            {
                var macAddress = obj["MACAddress"]?.ToString();
                var adapterType = obj["AdapterType"]?.ToString();

                if (!string.IsNullOrEmpty(macAddress) &&
                    !string.IsNullOrEmpty(adapterType) &&
                    !adapterType.ToLower().Contains("virtual"))
                {
                    return macAddress.Replace("-", ":");
                }
            }
        }
        catch { }

        // 方法3: 生成基于系统信息的伪MAC地址
        try
        {
            var systemInfo = $"{Environment.MachineName}{Environment.UserName}{Environment.OSVersion}{Environment.ProcessorCount}";
            using var sha1 = System.Security.Cryptography.SHA1.Create();
            var hash = sha1.ComputeHash(System.Text.Encoding.UTF8.GetBytes(systemInfo));
            var macBytes = hash.Take(6).ToArray();
            // 设置本地管理位，确保不与真实MAC冲突
            macBytes[0] = (byte)(macBytes[0] | 0x02);
            return string.Join(":", macBytes.Select(b => b.ToString("X2")));
        }
        catch { }

        return "02:00:00:00:00:00"; // 本地管理的MAC地址格式
    }

    private static bool IsAllDefaultValues(SystemInfo info)
    {
        return (info.CPU.StartsWith("Unknown") || info.CPU.StartsWith("CPU-")) &&
               (info.Memory.StartsWith("Unknown") || info.Memory.StartsWith("MEM-") || info.Memory.StartsWith("GC-")) &&
               (info.Motherboard.StartsWith("Unknown") || info.Motherboard.StartsWith("MB-") || info.Motherboard.StartsWith("BIOS-")) &&
               (info.Disk.StartsWith("Unknown") || info.Disk.StartsWith("DISK-") || info.Disk.StartsWith("Drive-") || info.Disk.StartsWith("LogicalDisk-")) &&
               (info.MACAddress == "00:00:00:00:00:00" || info.MACAddress == "02:00:00:00:00:00");
    }

    private static void GenerateUniqueDeviceInfo(SystemInfo info)
    {
        // 生成基于多个系统属性的唯一标识符
        var uniqueId = Guid.NewGuid().ToString("N")[..8];
        var timestamp = DateTimeOffset.Now.ToUnixTimeSeconds().ToString();
        var processId = Environment.ProcessId.ToString();

        // 收集更多系统信息用于生成唯一性
        var systemData = new StringBuilder();
        systemData.Append(Environment.MachineName);
        systemData.Append(Environment.UserName);
        systemData.Append(Environment.OSVersion.ToString());
        systemData.Append(Environment.ProcessorCount);
        systemData.Append(Environment.SystemDirectory);
        systemData.Append(Environment.WorkingSet);
        systemData.Append(uniqueId);
        systemData.Append(timestamp);
        systemData.Append(processId);

        // 尝试获取更多硬件信息
        try
        {
            systemData.Append(Environment.GetEnvironmentVariable("COMPUTERNAME"));
            systemData.Append(Environment.GetEnvironmentVariable("USERDOMAIN"));
            systemData.Append(Environment.GetEnvironmentVariable("PROCESSOR_ARCHITECTURE"));
        }
        catch { }

        var combinedData = systemData.ToString();
        using var sha256 = System.Security.Cryptography.SHA256.Create();
        var hash = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(combinedData));
        var hashString = Convert.ToHexString(hash).ToLower();

        // 生成唯一的设备信息
        info.CPU = $"CPU-{hashString[..8]}-{Environment.ProcessorCount}Core";
        info.Memory = $"MEM-{hashString[8..16]}-{Environment.WorkingSet / (1024 * 1024)}MB";
        info.Motherboard = $"MB-{hashString[16..24]}-{Environment.MachineName}";
        info.Disk = $"DISK-{hashString[24..32]}-{Environment.SystemDirectory[0]}";

        // 生成唯一MAC地址
        var macBytes = hash.Take(6).ToArray();
        macBytes[0] = (byte)(macBytes[0] | 0x02); // 设置本地管理位
        info.MACAddress = string.Join(":", macBytes.Select(b => b.ToString("X2")));
    }

    private static string GenerateDeviceFingerprint(SystemInfo info)
    {
        var combined = $"{info.CPU}|{info.Memory}|{info.Motherboard}|{info.Disk}|{info.MACAddress}";
        using var sha256 = System.Security.Cryptography.SHA256.Create();
        var hash = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(combined));
        return Convert.ToHexString(hash).ToLower();
    }


    private class SystemInfo
    {
        public string CPU { get; set; } = string.Empty;
        public string Memory { get; set; } = string.Empty;
        public string Motherboard { get; set; } = string.Empty;
        public string Disk { get; set; } = string.Empty;
        public string MACAddress { get; set; } = string.Empty;
    }
    #endregion

    #region Fields
    private string? callback;
    private long uin;
    internal string? whitegroup;

    /// <summary>缓存本次登录的群号列表，因为cdkey可能存在白名单限制</summary>
    internal static HashSet<long> Groups = [];
    #endregion
}
