<?php
/**
 * 在线用户统计修复验证脚本
 * 用于验证修复是否成功部署
 */

// 配置数据库连接（请根据实际情况修改）
$config = [
    'host' => 'localhost',
    'username' => 'root',
    'password' => 'your_password',
    'database' => 'your_database_name'
];

echo "=== 在线用户统计修复验证 ===\n\n";

// 1. 检查数据库连接
echo "1. 检查数据库连接...\n";
try {
    $pdo = new PDO(
        "mysql:host={$config['host']};dbname={$config['database']};charset=utf8mb4",
        $config['username'],
        $config['password'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    echo "✓ 数据库连接成功\n";
} catch (PDOException $e) {
    echo "✗ 数据库连接失败: " . $e->getMessage() . "\n";
    exit(1);
}

// 2. 检查统计历史表
echo "\n2. 检查统计历史表...\n";
try {
    $stmt = $pdo->query("DESCRIBE online_stats_history");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "✓ online_stats_history 表存在\n";
    echo "  字段: " . implode(', ', $columns) . "\n";
} catch (PDOException $e) {
    echo "✗ online_stats_history 表不存在或有错误: " . $e->getMessage() . "\n";
    echo "  请执行: mysql -u root -p your_database_name < docs/CREATE_ONLINE_STATS_HISTORY_TABLE.sql\n";
}

// 3. 检查统计数据
echo "\n3. 检查统计数据...\n";
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM online_stats_history");
    $count = $stmt->fetchColumn();
    echo "✓ 统计记录数量: $count\n";
    
    if ($count > 0) {
        $stmt = $pdo->query("
            SELECT online_count, auth_active_users, FROM_UNIXTIME(timestamp) as time 
            FROM online_stats_history 
            ORDER BY timestamp DESC 
            LIMIT 5
        ");
        $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "  最新5条记录:\n";
        foreach ($records as $record) {
            echo "    {$record['time']}: 在线{$record['online_count']}人, 认证{$record['auth_active_users']}人\n";
        }
    } else {
        echo "  暂无统计数据，请等待5-10分钟后再检查\n";
    }
} catch (PDOException $e) {
    echo "✗ 查询统计数据失败: " . $e->getMessage() . "\n";
}

// 4. 检查API接口
echo "\n4. 检查API接口...\n";
$apiUrls = [
    'health' => '/api/onlinestats/health',
    'current' => '/api/onlinestats/current',
    'history' => '/api/onlinestats/history?period=hour&limit=5'
];

foreach ($apiUrls as $name => $url) {
    $fullUrl = "http://localhost" . $url; // 请根据实际域名修改
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 5,
            'ignore_errors' => true
        ]
    ]);
    
    $response = @file_get_contents($fullUrl, false, $context);
    
    if ($response !== false) {
        $data = json_decode($response, true);
        if ($data && isset($data['code']) && $data['code'] == 1) {
            echo "✓ API $name 正常\n";
            if ($name === 'current' && isset($data['data']['online_count'])) {
                echo "  当前在线: {$data['data']['online_count']}人\n";
            }
            if ($name === 'history' && isset($data['data']['count'])) {
                echo "  历史记录: {$data['data']['count']}条\n";
            }
        } else {
            echo "✗ API $name 返回错误: " . ($data['msg'] ?? 'Unknown error') . "\n";
        }
    } else {
        echo "✗ API $name 无法访问 ($fullUrl)\n";
    }
}

// 5. 检查PHP文件语法
echo "\n5. 检查PHP文件语法...\n";
$phpFiles = [
    'www/application/bns/controller/Manage/OnlineDashboard.php',
    'www/application/bns/controller/Api/OnlineStats.php'
];

foreach ($phpFiles as $file) {
    if (file_exists($file)) {
        $output = [];
        $returnCode = 0;
        exec("php -l $file 2>&1", $output, $returnCode);
        
        if ($returnCode === 0) {
            echo "✓ $file 语法正确\n";
        } else {
            echo "✗ $file 语法错误:\n";
            echo "  " . implode("\n  ", $output) . "\n";
        }
    } else {
        echo "✗ $file 文件不存在\n";
    }
}

// 6. 生成测试报告
echo "\n6. 生成测试报告...\n";
$reportFile = 'stats_fix_verification_' . date('Y-m-d_H-i-s') . '.txt';
ob_start();

echo "在线用户统计修复验证报告\n";
echo "生成时间: " . date('Y-m-d H:i:s') . "\n";
echo "数据库: {$config['database']}\n\n";

// 重新查询统计信息
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM online_stats_history");
    $totalRecords = $stmt->fetchColumn();
    
    $stmt = $pdo->query("
        SELECT 
            MIN(FROM_UNIXTIME(timestamp)) as first_record,
            MAX(FROM_UNIXTIME(timestamp)) as last_record,
            AVG(online_count) as avg_online
        FROM online_stats_history
    ");
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "统计数据概览:\n";
    echo "- 总记录数: $totalRecords\n";
    echo "- 首条记录: " . ($stats['first_record'] ?? 'N/A') . "\n";
    echo "- 最新记录: " . ($stats['last_record'] ?? 'N/A') . "\n";
    echo "- 平均在线: " . round($stats['avg_online'] ?? 0, 2) . "人\n\n";
    
    // 按小时统计
    $stmt = $pdo->query("
        SELECT 
            FROM_UNIXTIME(timestamp, '%Y-%m-%d %H:00:00') as hour,
            AVG(online_count) as avg_online,
            COUNT(*) as records
        FROM online_stats_history 
        WHERE timestamp >= UNIX_TIMESTAMP(NOW() - INTERVAL 24 HOUR)
        GROUP BY FROM_UNIXTIME(timestamp, '%Y-%m-%d %H:00:00')
        ORDER BY hour DESC
        LIMIT 10
    ");
    $hourlyStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($hourlyStats)) {
        echo "最近24小时统计:\n";
        foreach ($hourlyStats as $stat) {
            echo "- {$stat['hour']}: 平均{$stat['avg_online']}人在线 ({$stat['records']}条记录)\n";
        }
    }
    
} catch (PDOException $e) {
    echo "查询统计信息失败: " . $e->getMessage() . "\n";
}

$report = ob_get_clean();
file_put_contents($reportFile, $report);
echo "✓ 测试报告已保存到: $reportFile\n";

echo "\n=== 验证完成 ===\n";
echo "\n建议:\n";
echo "1. 如果统计数据为0，请等待5-10分钟后重新运行此脚本\n";
echo "2. 如果API接口无法访问，请检查服务是否正常运行\n";
echo "3. 定期运行此脚本监控系统状态\n";
echo "4. 查看详细报告: cat $reportFile\n";
?>
