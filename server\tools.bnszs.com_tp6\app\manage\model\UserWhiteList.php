<?php

namespace app\manage\model;

use think\Model;
use think\Exception;
use think\facade\Request;

class UserWhiteList extends Model
{
    protected $table = 'bns_whitelist';

    public static function Get($type) {
        $device = request()->header('X-Device-ID');
        if(!$device) return null;

        // 查询权限数据
        $data = UserWhiteList::where(['type'=>$type,'device'=>$device])->find();
        if(!$data) {
            $data = new UserWhiteList();
            $data['type'] = $type;
            $data->device = $device;
            $data->ip     = request()->ip();
            $data->level  = 0;
            $data->save();
        }

        // 返回结果
        if ($data->level > 0) return
        [
            'name'  => null,
            'level' => $data->level,
            'token' => '*********************************************************************************************',
        ];
    }

    public static function GetClient($uid) {
        $type   = "client";
        $device = request()->header('X-Device-ID');
        if(!$device) return null;

        $data = UserWhiteList::where(['type'=>$type,'uid'=>$uid,'device'=>$device])->find();
        if(!$data) {
            $data = new UserWhiteList();
            $data['type'] = $type;
            $data->device = $device;
            $data->uid    = $uid;
            $data->ip     = request()->ip();
            $data->level  = 0;
            $data->save();
        }
    }
}
