using HandyControl.Controls;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Animation;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.BnsHelper.Resources;
using Xylia.BnsHelper.ViewModels;
using Xylia.Preview.Common.Attributes;
using Xylia.Preview.Data.Engine.DatData;

namespace Xylia.BnsHelper.Views.Pages;
public partial class HomePage : Page
{
    #region Fields
    private int _currentPageIndex = 0;
    private readonly IList<ISettingOption> _options;

    // 鼠标手势相关字段
    private bool _isMouseDown = false;
    private Point _mouseDownPosition;
    private const double SWIPE_THRESHOLD = 50; // 滑动阈值（像素）

    // 选项控制相关字段
    private bool _isShowingSelectedOnly = false;
    private IList<ISettingOption>? _originalOptions;

    // GIF 动画相关字段
    private GifImage? _adGifImage;
    #endregion

    #region Constructor
    public HomePage()
    {
        InitializeComponent();

        AllowStatisticsButton.DataContext = new SettingOption<bool>("UseAutoSaved", OnChanged: Reload);
        OptionGroup.ItemsSource = _options =
        [
            new SettingOption<bool>("UseFastDecompose"),
            new SettingOption<bool>("UseFastTransform"),
            new SettingOption<bool>("MaxDecomposeCount"),
            new SettingOption<bool>("UseCameraZoom", true),
            new SettingOption<bool>("UseFatigabilityAlarm") { Publisher = [EPublisher.ZTX, EPublisher.ZNCG] },
            new SettingOption<bool>("UseChannelMove", OnChanged: Reload) { Publisher = [EPublisher.ZTX, EPublisher.ZNCG] },
            new SettingOption<bool>("UseTransitUnlimit", OnChanged: Reload) { Publisher = [EPublisher.ZTX, EPublisher.ZNCG] },
            new SettingOption<bool>("UseSkipCinema", OnChanged: Reload) { Publisher = [EPublisher.ZTX, EPublisher.ZNCG] },
            new SettingOption<bool>("UseQuestQuickSlot"),
            new SettingOption<bool>("UseQuestTracking") { Publisher = [EPublisher.ZTX] },
            new SettingOption<bool>("HideTailMesh"),
            new SettingOption<bool>("EnableNickName"),
            new SettingOption<bool>("EnableFriendNotification", true),
            new SettingOption<bool>("EnableCenter2Notification", true) { Publisher = [EPublisher.ZTX, EPublisher.ZNCG] },
            new SettingOption<bool>("UseZSCharacterInfo") { Publisher = [EPublisher.Tencent] },
            new SettingOption<bool>("UseDifferentServerFriendWhisper"),
            new SettingOption<bool>("UseClientSpammerList"),
            new SettingOption<bool>("UseAppearanceFilter") { Publisher = [EPublisher.ZTX] },
            new SettingOption<bool>("UseMegaphoneMaxCharacter"),
            new SettingOption<bool>("UsePlayerPercentInfo") { Publisher = [EPublisher.ZNCG] },
            new SettingOption<bool>("UseDamageMeter") { Publisher = [EPublisher.Tencent] },
            new SettingOption<bool>("LongClearLogutMember", true),

            //new SettingOption<bool>("UseRaiseItemCap") { Publisher = [EPublisher.Tencent] },
#if DEBUG
            new SettingOption<bool>("UseContextSimpleMode"),
#endif
            new SettingOption<bool>("UseTeamAverageScore"),
            new SettingOption<bool>("EnableJoypad"),
            new SettingOption<bool>("VSync") { Publisher = [EPublisher.ZTX, EPublisher.ZNCG] },
            new SettingOption<bool>("NoUseEffectAutoSort"),
            new SettingOption<bool>("NoMinimizeWindow"),
            new SettingOption<bool>("NoShowClause") { Publisher = [EPublisher.ZTX, EPublisher.ZNCG] },
            new SettingOption<bool>("NoUseIngameNotice"),
            new SettingOption<bool>("NoUseVoiceChat") { Publisher = [EPublisher.ZTX, EPublisher.ZNCG] },
            new SettingOption<bool>("NoUseVoiceChatMute", true) { Publisher = [EPublisher.ZTX, EPublisher.ZNCG] },
            new SettingOption<ShowType>("UseExpTalismanShow", section: "Show"),
            new SettingOption<ShowType>("PcEnterShow", section: "Show"),


            // 运气结束动画
            // 关闭多线程
            // NAGLE算法
            // 关闭自动GCD
            // 旧版社交
            // 最佳性能
            // 增加发包频率
            // 聚灵阁加速
            // 输入等待时间
            // 鼠标响应间隔
            // F键触发间隔
        ];

        // 初始化页面状态
        UpdatePageDisplay();

        // 添加键盘快捷键支持
        this.KeyDown += HomePage_KeyDown;
        this.Focusable = true;

        // status notify
        MainWindowViewModel.Instance.SessionStateChanged += (sender, isLoggedIn) => Refresh(sender, EventArgs.Empty);
        SettingHelper.Default.GameDirectoryChanged += Refresh;

        // 页面生命周期事件
        this.Loaded += HomePage_Loaded;
        this.IsVisibleChanged += HomePage_IsVisibleChanged;
    }
    #endregion

    #region Private Methods

    /// <summary>
    /// 页面指示器1点击
    /// </summary>
    private void Page1Indicator_Click(object sender, MouseButtonEventArgs e)
    {
        NavigateToPage(0);
    }

    /// <summary>
    /// 页面指示器2点击
    /// </summary>
    private void Page2Indicator_Click(object sender, MouseButtonEventArgs e)
    {
        NavigateToPage(1);
    }

    /// <summary>
    /// 键盘快捷键处理
    /// </summary>
    private void HomePage_KeyDown(object sender, KeyEventArgs e)
    {
        switch (e.Key)
        {
            case Key.Left:
            case Key.A:
                NavigateToPage(0);
                e.Handled = true;
                break;
            case Key.Right:
            case Key.D:
                NavigateToPage(1);
                e.Handled = true;
                break;
        }
    }

    /// <summary>
    /// 鼠标按下事件
    /// </summary>
    private void ContentArea_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
    {
        _isMouseDown = true;
        _mouseDownPosition = e.GetPosition((UIElement)sender);
        ((UIElement)sender).CaptureMouse();
    }

    /// <summary>
    /// 鼠标移动事件
    /// </summary>
    private void ContentArea_MouseMove(object sender, MouseEventArgs e)
    {
        if (!_isMouseDown) return;

        var currentPosition = e.GetPosition((UIElement)sender);
        var deltaX = currentPosition.X - _mouseDownPosition.X;

        // 可以在这里添加视觉反馈，比如轻微的内容偏移
    }

    /// <summary>
    /// 鼠标释放事件
    /// </summary>
    private void ContentArea_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
    {
        if (!_isMouseDown) return;

        var currentPosition = e.GetPosition((UIElement)sender);
        var deltaX = currentPosition.X - _mouseDownPosition.X;

        // 检测滑动手势
        if (Math.Abs(deltaX) > SWIPE_THRESHOLD)
        {
            if (deltaX > 0) // 向右滑动
            {
                NavigateToPage(0); // 切换到第一页
            }
            else // 向左滑动
            {
                NavigateToPage(1); // 切换到第二页
            }
        }

        _isMouseDown = false;
        ((UIElement)sender).ReleaseMouseCapture();
    }

    /// <summary>
    /// 导航到指定页面
    /// </summary>
    private void NavigateToPage(int pageIndex)
    {
        if (pageIndex < 0 || pageIndex == _currentPageIndex)
            return;

        _currentPageIndex = pageIndex;
        UpdatePageDisplay();
        PlayPageTransition();
    }

    /// <summary>
    /// 更新页面显示状态
    /// </summary>
    private void UpdatePageDisplay()
    {
        // 更新页面可见性
        HomePageContent.Visibility = _currentPageIndex == 0 ? Visibility.Visible : Visibility.Collapsed;
        CharacterInfoContent.Visibility = _currentPageIndex == 1 ? Visibility.Visible : Visibility.Collapsed;

        // 更新角色信息页面的空状态显示
        if (_currentPageIndex == 1)
        {
            UpdateCharacterListDisplay();
        }

        // 更新页面点样式
        UpdatePageDots();
    }

    /// <summary>
    /// 更新页面指示器样式
    /// </summary>
    private void UpdatePageDots()
    {
        // 更新页面指示器
        Page1Indicator.Fill = _currentPageIndex == 0 ?
            (Brush)FindResource("PrimaryBrush") :
            (Brush)FindResource("BorderBrush");

        Page2Indicator.Fill = _currentPageIndex == 1 ?
            (Brush)FindResource("PrimaryBrush") :
            (Brush)FindResource("BorderBrush");

        // 添加动画效果
        var activeIndicator = _currentPageIndex == 0 ? Page1Indicator : Page2Indicator;
        var inactiveIndicator = _currentPageIndex == 0 ? Page2Indicator : Page1Indicator;

        // 活跃指示器放大动画
        var scaleUpAnimation = new DoubleAnimation(1.0, 1.2, TimeSpan.FromMilliseconds(200));
        var scaleDownAnimation = new DoubleAnimation(1.2, 1.0, TimeSpan.FromMilliseconds(200));

        var scaleTransform = new ScaleTransform(1.0, 1.0);
        activeIndicator.RenderTransform = scaleTransform;
        activeIndicator.RenderTransformOrigin = new Point(0.5, 0.5);

        scaleUpAnimation.Completed += (s, e) =>
        {
            scaleTransform.BeginAnimation(ScaleTransform.ScaleXProperty, scaleDownAnimation);
            scaleTransform.BeginAnimation(ScaleTransform.ScaleYProperty, scaleDownAnimation);
        };

        scaleTransform.BeginAnimation(ScaleTransform.ScaleXProperty, scaleUpAnimation);
        scaleTransform.BeginAnimation(ScaleTransform.ScaleYProperty, scaleUpAnimation);

        // 重置非活跃指示器
        inactiveIndicator.RenderTransform = new ScaleTransform(1.0, 1.0);
    }

    /// <summary>
    /// 更新角色列表显示状态
    /// </summary>
    private void UpdateCharacterListDisplay()
    {
        var viewModel = DataContext as MainWindowViewModel;
        var hasCharacters = viewModel?.AutoSaved?.User?.Count() > 0;

        if (CharacterListContainer != null)
        {
            CharacterListContainer.Visibility = hasCharacters == true ? Visibility.Visible : Visibility.Collapsed;
        }

        if (EmptyState != null)
        {
            EmptyState.Visibility = hasCharacters == true ? Visibility.Collapsed : Visibility.Visible;
        }
    }



    /// <summary>
    /// 播放页面切换动画
    /// </summary>
    private void PlayPageTransition()
    {
        // 简单的淡入淡出动画
        UIElement currentContent = _currentPageIndex == 0 ?
            (UIElement)HomePageContent :
            (UIElement)CharacterInfoContent;

        // 淡出然后淡入
        var fadeOut = new DoubleAnimation(1.0, 0.0, TimeSpan.FromMilliseconds(150));
        var fadeIn = new DoubleAnimation(0.0, 1.0, TimeSpan.FromMilliseconds(150));

        fadeOut.Completed += (s, e) =>
        {
            currentContent.BeginAnimation(UIElement.OpacityProperty, fadeIn);
        };

        currentContent.BeginAnimation(UIElement.OpacityProperty, fadeOut);
    }

    /// <summary>
    /// 页面加载事件处理
    /// </summary>
    private void HomePage_Loaded(object sender, RoutedEventArgs e)
    {
        // 直接获取 GifImage 控件的引用
        _adGifImage = AdGifImage;
    }

    /// <summary>
    /// 页面可见性变化事件处理
    /// </summary>
    private void HomePage_IsVisibleChanged(object sender, DependencyPropertyChangedEventArgs e)
    {
        if (IsVisible)
        {
            // 页面变为可见时，重新启动 GIF 动画
            RestartGifAnimation();
        }
    }

    /// <summary>
    /// 重新启动 GIF 动画
    /// </summary>
    private void RestartGifAnimation()
    {
        try
        {
            if (_adGifImage != null)
            {
                // 通过重新设置 Uri 来重启动画
                var currentUri = _adGifImage.Uri;
                if (currentUri != null)
                {
                    _adGifImage.Uri = null;
                    _adGifImage.Uri = currentUri;
                }
            }
        }
        catch (Exception ex)
        {
            // 忽略重启动画失败的异常
            System.Diagnostics.Debug.WriteLine($"重启 GIF 动画失败: {ex.Message}");
        }
    }

    #endregion

    #region Option Control Methods
    /// <summary>
    /// 全选按钮点击事件
    /// </summary>
    private void SelectAllButton_Click(object sender, RoutedEventArgs e)
    {
        // 获取当前可见的布尔类型选项
        var visibleBoolOptions = _options
            .Where(option => option.Visiable && option is SettingOption<bool>)
            .Cast<SettingOption<bool>>();

        // 全选所有可见的布尔选项
        foreach (var option in visibleBoolOptions)
        {
            option.IsActivate = true;
        }
    }

    /// <summary>
    /// 查看已勾选按钮点击事件
    /// </summary>
    private void ViewSelectedButton_Click(object sender, RoutedEventArgs e)
    {
        if (_isShowingSelectedOnly)
        {
            // 恢复显示所有选项
            RestoreAllOptions();
        }
        else
        {
            // 只显示已勾选的选项
            ShowSelectedOptionsOnly();
        }
    }

    /// <summary>
    /// 只显示已勾选的选项
    /// </summary>
    private void ShowSelectedOptionsOnly()
    {
        _originalOptions ??= _options.ToList();

        // 筛选出已勾选的选项
        var selectedOptions = _options
            .Where(option => option.Visiable && option is SettingOption<bool> boolOption && boolOption.IsActivate)
            .ToList();

        // 更新ItemsSource
        OptionGroup.ItemsSource = selectedOptions;
        _isShowingSelectedOnly = true;

        // 更新按钮文本
        ViewSelectedButton.Content = "全部项";
        ViewSelectedButton.ToolTip = "恢复显示所有选项";
    }

    /// <summary>
    /// 恢复显示所有选项
    /// </summary>
    private void RestoreAllOptions()
    {
        if (_originalOptions != null)
        {
            OptionGroup.ItemsSource = _originalOptions;
            _isShowingSelectedOnly = false;

            // 恢复按钮文本
            ViewSelectedButton.Content = StringHelper.Get("HomePage_ViewSelected") ?? "查看已勾选";
            ViewSelectedButton.ToolTip = StringHelper.Get("HomePage_ViewSelected_Tooltip") ?? "显示当前已勾选的设置选项";
        }
    }
    #endregion

    #region Methods
    void Reload(object? value) => MainWindowViewModel.ReloadConfig();

    public void Refresh(object? sender, EventArgs args)
    {
        // 刷新角色列表显示状态
        if (_currentPageIndex == 1)
        {
            UpdateCharacterListDisplay();
        }

        // 刷新按钮可见性
        foreach (var option in _options)
        {
            option.Refresh();
        }

        // 如果当前正在显示已勾选选项，需要重新筛选
        if (_isShowingSelectedOnly)
        {
            ShowSelectedOptionsOnly();
        }

        // 重新启动 GIF 动画（确保在页面刷新后动画继续播放）
        RestartGifAnimation();
    }
    #endregion

    #region Fields
    public enum ShowType
    {
        [Description("")] ShowType_None,
        [Description("00009805.Legend_Weapon_Buff")] ShowType_Legend,
        [Description("00061144.S_EquipShow_DarkLegend01")] ShowType_DarkLegend,
        [Description("00061144.SS_EquipShow_ButterFly01")] ShowType_ButterFly,
        [Description("/Game/Art/FX/05_BM/EquipShow/Item_EquipShow_ButterFly01_pink.Item_EquipShow_ButterFly01_pink")] ShowType_ButterFly_pink,
        [Description("00061144.Item_EquipShow_Constellation01")] ShowType_Constellation01,
        [Description("/Game/Art/FX/05_BM/EquipShow/SS_EquipShow_Constellation02.SS_EquipShow_Constellation02")] ShowType_Constellation02,
        [Description("/Game/Art/FX/05_BM/EquipShow/Item_EquipShow_Galaxy02.Item_EquipShow_Galaxy02")] ShowType_Galaxy02,
        [Description("00061144.SS_EquipShow_Water01")] ShowType_Water01,
        [Description("00061144.SS_EquipShow_Universe01")] ShowType_Universe01,
        [Description("00061144.SS_EquipShow_Rainbow01")] ShowType_Rainbow01,
        [Description("00061144.S_EquipShow_Rainbow04_WeaponSet432")] ShowType_Rainbow04,
        [Description("00061144.SS_EquipShow_Lotus01")] ShowType_Lotus01,
        [Description("/Game/Art/FX/05_BM/EquipShow/SS_EquipShow_Lotus02.SS_EquipShow_Lotus02")] ShowType_Lotus02,
        [Description("00061144.SS_EquipShow_Ink01")] ShowType_Ink01,
        [Description("00061144.SS_EquipShow_Fox01")] ShowType_Fox01,
        [Description("00061144.SS_EquipShow_Cyber01")] ShowType_Cyber01,
        [Description("/Game/Art/FX/05_BM/EquipShow/SS_EquipShow_FireSpirit.SS_EquipShow_FireSpirit")] ShowType_FireSpirit,
        [Description("/Game/Art/FX/05_BM/EquipShow/Ss_EquipShow_FireMuffler.Ss_EquipShow_FireMuffler")] ShowType_FireMuffler,
        [Description("/Game/Art/FX/05_BM/EquipShow/SS_EquipShow_Lava.SS_EquipShow_Lava")] ShowType_Lava,
        [Description("/Game/Art/FX/05_BM/EquipShow/SS_EquipShow_Leviathan.SS_EquipShow_Leviathan")] ShowType_Leviathan,
        [Description("/Game/Art/FX/05_BM/EquipShow/SS_EquipShow_Net_Weapon.SS_EquipShow_Net_Weapon")] ShowType_NetWeapon,
        [Description("/Game/Art/FX/05_BM/EquipShow/SS_EquipShow_pertal.SS_EquipShow_pertal")] ShowType_Pertal,
        [Description("/Game/Art/FX/05_BM/EquipShow/Item_EquipShow_Matrix.Item_EquipShow_Matrix")] ShowType_Matrix,
        [Description("/Game/Art/FX/05_BM/EquipShow/Item_EquipShow_Matrix_Red.Item_EquipShow_Matrix_Red")] ShowType_Matrix_Red,
        [Description("/Game/Art/FX/05_BM/EquipShow/Item_EquipShow_FireButterfly_SS.Item_EquipShow_FireButterfly_SS")] ShowType_FireButterfly,
        [Description("/Game/Art/FX/05_BM/EquipShow/SS_EquipShow_ThreeHeadDragon.SS_EquipShow_ThreeHeadDragon")] ShowType_ThreeHeadDragon,
        [Description("/Game/Art/FX/05_BM/EquipShow/SS_EquipShow_YellowEye.SS_EquipShow_YellowEye")] ShowType_YellowEye,
        [Description("/Game/Art/FX/05_BM/EquipShow/Item_EquipShow_1475_ROCKET.Item_EquipShow_1475_ROCKET")] ShowType_Rocket,
        [Description("/Game/Art/FX/05_BM/EquipShow/SS_EquipShow_SilverDragon.SS_EquipShow_SilverDragon")] ShowType_SilverDragon,

        // 演出
        [Description("/Game/Art/FX/05_BM/EquipShowKR/Show/KRItem_EquipShow_Normal_2202_DragonRed_StarLoop.KRItem_EquipShow_Normal_2202_DragonRed_StarLoop")] ShowType_DragonRed,
        [Description("/Game/Art/FX/05_BM/EquipShowKR/Show/KRItem_EquipShow_Normal_2210_DragonPurple_StarLoop.KRItem_EquipShow_Normal_2210_DragonPurple_StarLoop")] ShowType_DragonPurple,
        [Description("/Game/Art/FX/05_BM/EquipShowKR/Show/KRItem_EquipShow_Normal_2212_StarLoop.KRItem_EquipShow_Normal_2212_StarLoop")] ShowType_Wind,
        [Description("/Game/Art/FX/05_BM/EquipShowKR/Show/KRItem_EquipShow_Normal_2303_ICE_StartLoop.KRItem_EquipShow_Normal_2303_ICE_StartLoop")] ShowType_ICE,
        [Description("/Game/Art/FX/05_BM/EquipShowKR/Show/KRItem_EquipShow_Normal_2406_ElectricLizard_StartLoop.KRItem_EquipShow_Normal_2406_ElectricLizard_StartLoop")] ShowType_ElectricLizard,
        [Description("/Game/Art/FX/05_BM/EquipShowTX/Show/TXItem_EquipShow_2504_Card_StarLoop.TXItem_EquipShow_2504_Card_StarLoop")] ShowType_2504_Card,
        [Description("/Game/Art/FX/05_BM/EquipShowTX/Show/TXItem_EquipShow_Normal_2505_DragonPink_StarLoop.TXItem_EquipShow_Normal_2505_DragonPink_StarLoop")] ShowType_DragonPink,
        [Description("/Game/Art/FX/05_BM/EquipShowTX/Show/TXItem_EquipShow_2506_Flower01_StarLoop.TXItem_EquipShow_2506_Flower01_StarLoop")] ShowType_Flower01,
        [Description("/Game/Art/FX/05_BM/EquipShowTX/Show/TXItem_EquipShow_2506_AirPlane_StarLoop.TXItem_EquipShow_2506_AirPlane_StarLoop")] ShowType_AirPlane2,

        // 待机特效
        [Description("/Game/Art/Character/Monster/Monster_Show/New_Social_Common_PrimiumSetShow/BM_2021_idle_state_event1.BM_2021_idle_state_event1")] ShowType_IdleEvent1,
        [Description("/Game/Art/Character/Monster/Monster_Show/New_Social_Common_PrimiumSetShow/BM_2022_idle_state_event1.BM_2022_idle_state_event1")] ShowType_IdleEvent3,
        [Description("/Game/Art/FX/05_BM/EventShow/2211_Alice_Idle_EventShow.2211_Alice_Idle_EventShow")] ShowType_IdleEvent4,
        [Description("/Game/Art/Character/Monster/Monster_Show/New_Social_Common_PrimiumSetShow/BM_2023_idle_state_event1.BM_2023_idle_state_event1")] ShowType_IdleEvent5,
        [Description("/Game/Art/Character/Monster/Monster_Show/New_Social_Common_PrimiumSetShow/BM_2023_idle_state_event2.BM_2023_idle_state_event2")] ShowType_IdleEvent6,
        [Description("/Game/Art/Character/Monster/Monster_Show/New_Social_Common_PrimiumSetShow/BM_2023_idle_state_event3.BM_2023_idle_state_event3")] ShowType_IdleEvent7,
        [Description("/Game/Art/Character/Monster/Monster_Show/New_Social_Common_PrimiumSetShow/BM_2023_idle_state_event4.BM_2023_idle_state_event4")] ShowType_IdleEvent8,
        [Description("/Game/Art/Character/Monster/Monster_Show/New_Social_Common_PrimiumSetShow/BM_2024_02_state_envet1.BM_2024_02_state_envet1")] ShowType_IdleEvent9,
        [Description("/Game/Art/Character/Monster/Monster_Show/New_Social_Common_PrimiumSetShow/BM_2024_Idle_state_event3.BM_2024_Idle_state_event3")] ShowType_IdleEvent10,
        [Description("/Game/Art/Character/Monster/Monster_Show/New_Social_Common_PrimiumSetShow/BM_2024_Idle_state_event4.BM_2024_Idle_state_event4")] ShowType_IdleEvent11,
        [Description("/Game/Art/Character/Monster/Monster_Show/New_Social_Common_PrimiumSetShow/BM_2024_Idle_state_event5.BM_2024_Idle_state_event5")] ShowType_IdleEvent12,
        [Description("/Game/Art/Character/Monster/Monster_Show/New_Social_Common_PrimiumSetShow/BM_2025_idle_state_event1.BM_2025_idle_state_event1")] ShowType_IdleEvent13,
        [Description("/Game/Art/Character/Monster/Monster_Show/New_Social_Common_PrimiumSetShow/BM_2025_idle_state_event2.BM_2025_idle_state_event2")] ShowType_Idle_Surfing,

        // 焕彩石
        [ClientType(ClientType.LIVE)][Description("00061518.BadgeEffect_Appearance_02")] ShowType_Hola,
        [ClientType(ClientType.LIVE)][Description("00061144.Item_EquipShow_Card01")] ShowType_Card,
        [ClientType(ClientType.LIVE)][Description("00061144.Item_EquipShow_Flower01")] ShowType_Flower,
        [ClientType(ClientType.LIVE)][Description("00061144.Item_EquipShow_Musicalnote")] ShowType_Melody,
        [ClientType(ClientType.LIVE)][Description("00061144.Item_EquipShow_WeaponMaster")] ShowType_WeaponMaster,
        [ClientType(ClientType.LIVE)][Description("00061144.Item_EquipShow_Dot8bitFrog")] ShowType_Dot8bitFrog,
        [ClientType(ClientType.LIVE)][Description("/Game/Art/FX/05_BM/EquipShow/Item_EquipShow_2108_Airplane.Item_EquipShow_2108_Airplane")] ShowType_Airplane,
        [ClientType(ClientType.LIVE)][Description("/Game/Art/FX/05_BM/EquipShow/SS_EquipShow_Galaxy01.SS_EquipShow_Galaxy01")] ShowType_Space,
        [ClientType(ClientType.LIVE)][Description("/Game/Art/FX/05_BM/EquipShow/Item_EquipShow_Wolf.Item_EquipShow_Wolf")] ShowType_Wolf,
        [ClientType(ClientType.LIVE)][Description("/Game/Art/FX/05_BM/EquipShow/Item_Equipshow_2407_BlackRabbit/Item_EquipShow_2407_BlackRabbit.Item_EquipShow_2407_BlackRabbit")] ShowType_BlackRabbit,
        [ClientType(ClientType.LIVE)][Description("/Game/Art/FX/05_BM/EquipShow/Item_Equipshow_2411_WhiteTurtle/Item_EquipShow_2411_WhiteTurtle.Item_EquipShow_2411_WhiteTurtle")] ShowType_WhiteTurtle,

        // 其他
        [Description("/Game/Neo_Art/Art/FX/03_Show/Common/Common_Neo_LEVELup.Common_Neo_LEVELup")] ShowType_LEVELup,
        [Description("/Game/Art/FX/05_BM/EventShow/DisplayOptionOn/DisplayOptionOn_EventShow_TopRanker_v01_loop.DisplayOptionOn_EventShow_TopRanker_v01_loop")] ShowType_TopRanker1,
        [Description("/Game/Art/FX/03_Show/Dungeon/2202_FirstGonRyun_GWJ/EffectShow/EffectShow_GWJ_Afterimage_on.EffectShow_GWJ_Afterimage_on")] ShowType_AfterImage,
        [Description("/Game/Art/FX/03_Show/Dungeon/2206_BlackSpearTemple_DSA/EffectShow/DSA_B1_DarkCrazyShaman_PCBuff.DSA_B1_DarkCrazyShaman_PCBuff")] ShowType_DarkCrazyShama_PCBuff,
        [Description("/Game/Art/FX/03_Show/Dungeon/2206_BlackSpearTemple_DSA/EffectShow/DSA_B1_DarkCrazyShaman_Mask_Red_Attach.DSA_B1_DarkCrazyShaman_Mask_Red_Attach")] ShowType_DarkCrazyShama_Mask_Red,
        [Description("/Game/Art/FX/03_Show/Dungeon/2206_BlackSpearTemple_DSA/EffectShow/DSA_B1_DarkCrazyShaman_Mask_Blue_Attach.DSA_B1_DarkCrazyShaman_Mask_Blue_Attach")] ShowType_DarkCrazyShama_Mask_Blue,
        [Description("/Game/Art/FX/03_Show/NewCharacter/Skill_NPC_UE4_DunGeon06_Boss1/skill_FireArea_Loop_3m.skill_FireArea_Loop_3m")] ShowType_FireArea,
        [Description("00069237.Thunderer_G1_SummonStar_Lv5_Attach")] ShowType_SummonStar,
        [Description("/Game/Art/FX/03_Show/PC/12_Archer/Core3/Effectshow_Archer_G3_LightningShock_Loop.Effectshow_Archer_G3_LightningShock_Loop")] ShowType_LightningShock,
        [Description("/Game/Art/FX/03_Show/PC/14_Bard/Core1/FxShow_Bard_G1_Angel_Soul_Wing_White_Loop.FxShow_Bard_G1_Angel_Soul_Wing_White_Loop")] ShowType_WhiteSoulWing,
        [Description("/Game/Art/FX/03_Show/PC/14_Bard/Core2/Effectshow_Bard_G2_nimble_fingers_loop.Effectshow_Bard_G2_nimble_fingers_loop")] ShowType_NimbleFingers,
        [Description("/Game/Neo_Art/Art/FX/03_Show/Dungeon/ZNCS_2310_CurrptedStorm_SWH/EffectShow/EffectShow_SWH_Golem_Infection_Loop_Lv02.EffectShow_SWH_Golem_Infection_Loop_Lv02")] ShowType_Infection,
    }
    #endregion
}
