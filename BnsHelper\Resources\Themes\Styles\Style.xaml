﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
					xmlns:hc="https://handyorg.github.io/handycontrol"
					xmlns:system="clr-namespace:System;assembly=mscorlib"
					xmlns:helper="clr-namespace:Xylia.BnsHelper.Common.Helpers">

	<Style TargetType="TextBlock">
		<Setter Property="FontSize" Value="{Binding FontSize,Source={x:Static helper:SettingHelper.Default}}" />
	</Style>

	
	<Style x:Key="ToggleButtonTransparent" TargetType="ToggleButton">
		<Setter Property="BorderThickness" Value="1.5" />
		<Setter Property="FocusVisualStyle" Value="{x:Null}" />
		<Setter Property="Foreground" Value="{DynamicResource PrimaryTextBrush}" />
		<Setter Property="OverridesDefaultStyle" Value="True" />
		<Setter Property="HorizontalContentAlignment" Value="Center" />
		<Setter Property="VerticalContentAlignment" Value="Center" />
		<Setter Property="MinWidth" Value="30" />
		<Setter Property="Template">
			<Setter.Value>
				<ControlTemplate TargetType="{x:Type ToggleButton}">
					<Border x:Name="PART_Border" Width="{TemplateBinding Width}" Height="{TemplateBinding Height}" Background="{TemplateBinding Background}" BorderThickness="{TemplateBinding BorderThickness}">
						<ContentPresenter Content="{TemplateBinding Content}" HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}" />
					</Border>
					<ControlTemplate.Triggers>
						<Trigger Property="IsMouseOver" Value="True">
							<Setter Property="Opacity" Value="0.7"/>
						</Trigger>
						<Trigger Property="IsPressed" Value="True">
							<Setter Property="Opacity" Value="0.5"/>
						</Trigger>
						<Trigger Property="IsEnabled" Value="False">
							<Setter Property="Foreground" Value="{DynamicResource SecondaryTextBrush}"/>
						</Trigger>
						<Trigger Property="IsChecked" Value="True">
							<Setter Property="Foreground" Value="{DynamicResource PrimaryBrush}"/>
						</Trigger>
					</ControlTemplate.Triggers>
				</ControlTemplate>
			</Setter.Value>
		</Setter>
	</Style>

	<Style x:Key="ProgressBarNoAnimation" TargetType="ProgressBar">
		<Setter Property="hc:VisualElement.Text">
			<Setter.Value>
				<MultiBinding Converter="{StaticResource Number2PercentageConverter}" StringFormat="{}{0:F0} %">
					<Binding Path="Value" RelativeSource="{RelativeSource Self}" />
					<Binding Path="Maximum" RelativeSource="{RelativeSource Self}" />
				</MultiBinding>
			</Setter.Value>
		</Setter>
		<Setter Property="Height" Value="20" />
		<Setter Property="hc:BorderElement.CornerRadius" Value="{StaticResource DefaultCornerRadius}" />
		<Setter Property="Background" Value="{DynamicResource SecondaryRegionBrush}" />
		<Setter Property="Foreground" Value="{DynamicResource PrimaryBrush}" />
		<Setter Property="BorderBrush" Value="{DynamicResource BorderBrush}" />
		<Setter Property="BorderThickness" Value="0" />
		<Setter Property="Template">
			<Setter.Value>
				<ControlTemplate TargetType="ProgressBar">
					<Border x:Name="TemplateRoot" CornerRadius="{Binding Path=(hc:BorderElement.CornerRadius),RelativeSource={RelativeSource TemplatedParent}}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" Background="{TemplateBinding Background}">
						<hc:SimplePanel>
							<Rectangle x:Name="PART_Track" />
							<TextBlock HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="{TemplateBinding Foreground}" Text="{Binding Path=(hc:VisualElement.Text),RelativeSource={RelativeSource TemplatedParent}}" />
							<Border Style="{StaticResource BorderClip}" CornerRadius="{Binding Path=(hc:BorderElement.CornerRadius),RelativeSource={RelativeSource TemplatedParent}}" x:Name="PART_Indicator" HorizontalAlignment="Left">
								<hc:SimplePanel>
									<Border x:Name="Indicator" Background="{TemplateBinding Foreground}">
										<Border Width="{Binding ActualWidth,ElementName=TemplateRoot}">
											<TextBlock HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="{DynamicResource TextIconBrush}" Text="{Binding Path=(hc:VisualElement.Text),RelativeSource={RelativeSource TemplatedParent}}" />
										</Border>
									</Border>
									<Rectangle x:Name="PART_GlowRect" Fill="{StaticResource ProgressBarIndicatorAnimatedFill}" HorizontalAlignment="Left" Margin="-100,0,0,0" Width="100" Visibility="Collapsed" />
								</hc:SimplePanel>
							</Border>
						</hc:SimplePanel>
					</Border>
				</ControlTemplate>
			</Setter.Value>
		</Setter>
	</Style>

</ResourceDictionary>