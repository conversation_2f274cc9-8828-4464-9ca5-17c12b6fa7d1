package middleware

import (
	"fmt"
	"log"
	"runtime/debug"
)

// RecoverMiddleware 错误恢复中间件
func RecoverMiddleware(next func()) {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("Recovered from panic: %v\nStack trace:\n%s", r, debug.Stack())
		}
	}()
	next()
}

// RecoverWithError 带错误返回的恢复中间件
func RecoverWithError(next func() error) (err error) {
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("recovered from panic: %v", r)
			log.Printf("Panic recovered: %v\nStack trace:\n%s", r, debug.Stack())
		}
	}()
	return next()
}
