﻿using System.ComponentModel;
using System.Diagnostics;
using System.Windows;
using System.Windows.Media;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.Preview.Common.Extension;

namespace Xylia.BnsHelper.Resources;
internal static class SkinHelpers
{
	internal static void UpdateSkin(SkinType skin, int night)
	{
		var skins0 = Application.Current.Resources.MergedDictionaries[0];
		skins0.MergedDictionaries.Clear();
		skins0.MergedDictionaries.Add(new ResourceDictionary { Source = new Uri("pack://application:,,,/HandyControl;component/Themes/SkinDefault.xaml") });
		skins0.MergedDictionaries.Add(GetDayNight(night));
		skins0.MergedDictionaries.Add(GetSkin(skin));

		var skins1 = Application.Current.Resources.MergedDictionaries[1];
		skins1.MergedDictionaries.Clear();
		skins1.MergedDictionaries.Add(new ResourceDictionary { Source = new Uri("pack://application:,,,/HandyControl;component/Themes/Theme.xaml") });
		skins1.MergedDictionaries.Add(new ResourceDictionary { Source = new Uri("pack://application:,,,/;component/Resources/Themes/Theme.xaml") });

		Application.Current.MainWindow?.OnApplyTemplate();
	}

	private static ResourceDictionary GetDayNight(int status)
	{
        return new ResourceDictionary
		{
			Source = new Uri($"pack://application:,,,/;component/Resources/Themes/Skins/Basic/{(IsNight(status) ? "Dark" : "Day")}.xaml")
		};
	}

	private static ResourceDictionary GetSkin(SkinType skin)
	{
		try
		{
			return new ResourceDictionary
			{
				Source = new Uri($"pack://application:,,,/;component/Resources/Themes/Skins/{skin}.xaml")
			};
		}
		catch
		{
			Debug.Fail("fail to get skin.");
			if (skin == SkinType.Blue) throw;

			return GetSkin(SkinType.Blue);
		}
	}


    public static bool IsNight(int status) => status != 1 && (status == 2 || DateTime.Now.Hour < 6 || DateTime.Now.Hour >= 18);

    public static bool IsNight() => IsNight(SettingHelper.Default.NightMode);
}

internal struct SkinInfo(SkinType type)	
{
	public readonly SkinType Type => type;

	public readonly Brush Brush => type.GetDescription().To<SolidColorBrush>();

	public override string ToString() => type.GetDescription();
}

internal enum SkinType
{
	[Description("#9DA3DD")] Blue,
	[Description("#E49457")] Chocolate,
	[Description("#ED5372")] Crimson,
	[Description("#FFD700")] Gold,
	[Description("#09C909")] Green,
	[Description("#CBCB02")] Olive,
	[Description("#DD8BDA")] Orchid,
	[Description("#FFC0CB")] Pink,
}
