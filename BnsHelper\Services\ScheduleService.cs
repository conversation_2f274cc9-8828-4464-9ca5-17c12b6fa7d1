﻿using Quartz;
using Quartz.Impl;
using System.Diagnostics;
using Xylia.BnsHelper.ViewModels;

namespace Xylia.BnsHelper.Services;
/// <summary>
/// Provides functionality for managing and scheduling tasks using Quartz.NET.
/// </summary>
/// <remarks>This service is a singleton and provides methods to register and manage scheduled tasks. It uses
/// Quartz.NET to define and execute jobs based on specified schedules.</remarks>
internal class ScheduleService : IService
{
	#region Constructor 
	private static ScheduleService? _instance;
	public static ScheduleService Instance => _instance ??= new ScheduleService();

	private readonly StdSchedulerFactory ScheduleFactory;
	private IScheduler? _scheduler;

	private ScheduleService()
	{
		ScheduleFactory = new StdSchedulerFactory();
	}
	#endregion

	#region Methods
	// https://www.cnblogs.com/youring2/p/quartz_net.html
	public async void Register()
	{
		try
		{
			// 统一方案：通过Schedules属性获取数据
			var schedules = ApiEndpointService.BnszsApi.Schedules;
			Debug.WriteLine($"[INFO] ScheduleService通过Schedules属性获取到 {schedules.Length} 条数据");

			if (schedules.Length == 0)
			{
				Debug.WriteLine("[INFO] 当前Schedules为空，跳过通知任务注册");
				return;
			}

			_scheduler = await ScheduleFactory.GetScheduler();
			await _scheduler.Start();

			// 加载用户的通知设置
			foreach (var schedule in schedules)
			{
				schedule.LoadNotificationSettings();
			}

			await RegisterNotifications(schedules);
		}
		catch (Exception ex)
		{
			Debug.WriteLine($"[ERROR] ScheduleService注册失败: {ex.Message}");
		}
	}

	/// <summary>
	/// 刷新通知任务 - 统一通过Schedules属性
	/// </summary>
	public async void RefreshNotifications()
	{
		try
		{
			if (_scheduler == null) return;

			// 统一方案：通过Schedules属性获取数据
			var schedules = ApiEndpointService.BnszsApi.Schedules;
			Debug.WriteLine($"[INFO] RefreshNotifications通过Schedules属性获取到 {schedules.Length} 条数据");

			if (schedules.Length == 0)
			{
				Debug.WriteLine("[INFO] 当前Schedules为空，跳过通知任务刷新");
				return;
			}

			// 清除所有现有的任务
			await _scheduler.Clear();
			Debug.WriteLine("[INFO] 已清除所有现有的通知任务");

			// 重新注册通知任务
			await RegisterNotifications(schedules);
			Debug.WriteLine("[INFO] 已重新注册通知任务");
		}
		catch (Exception ex)
		{
			Debug.WriteLine($"[ERROR] 刷新通知任务失败: {ex.Message}");
		}
	}

	/// <summary>
	/// 注册通知任务
	/// </summary>
	private async Task RegisterNotifications(IEnumerable<Models.Schedule> schedules)
	{
		if (_scheduler == null) return;

		foreach (var schedule in schedules)
		{
			async Task AddSchedule(int minute, string minuteText)
			{
				var job = JobBuilder.Create<ToastNotifyJob>()
					.WithIdentity($"schedule_{schedule.UniqueId}_{Math.Abs(minute)}")
					.SetJobData([
						new("Title", $"{schedule} - {minuteText}分钟后开始"),
						new("Content", schedule.Zone)
					])
					.Build();

				var notifyTime = schedule.DateTime.AddMinutes(minute);

				// 只为未来的时间创建触发器
				if (notifyTime > DateTime.Now)
				{
					var trigger = TriggerBuilder.Create()
						.WithIdentity($"trigger_{schedule.UniqueId}_{Math.Abs(minute)}")
						.WithSchedule(CronScheduleBuilder.WeeklyOnDayAndHourAndMinute(
							schedule.DateTime.DayOfWeek,
							notifyTime.Hour,
							notifyTime.Minute))
						.Build();

					await _scheduler.ScheduleJob(job, trigger);
					Debug.WriteLine($"[INFO] 已注册通知任务: {schedule} - {minuteText}分钟前通知，时间: {notifyTime:yyyy-MM-dd HH:mm}");
				}
			}

			if (schedule.Notify15) await AddSchedule(-15, "15");
			if (schedule.Notify5) await AddSchedule(-5, "5");
			if (schedule.Notify3) await AddSchedule(-3, "3");
		}
	}
	#endregion

	public class ToastNotifyJob : IJob
	{
		public string? Title { get; set; }
		public string? Content { get; set; }

		public Task Execute(IJobExecutionContext context) => Task.Factory.StartNew(() =>
		{
			MainWindowViewModel.SendBalloonTip(Title, Content);
		});
	}
}
