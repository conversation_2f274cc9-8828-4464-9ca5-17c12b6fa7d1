﻿using System.Collections.ObjectModel;
using System.Diagnostics;

namespace Xylia.BnsHelper.Services;
public interface IService
{
	/// <summary>
	/// Initiaze service
	/// </summary>
	/// <returns>regist result</returns>
	void Register();
}

public class ServiceManager : Collection<IService>
{
	public ServiceManager(params IService[] services)
	{
		foreach (var s in services)
		{
			try
			{
				s.Register();
			}
			catch (Exception ex)
			{
				Debug.Fail(string.Format("{0} register failed.", s.GetType()), ex.Message);
			}
		}
	}
}