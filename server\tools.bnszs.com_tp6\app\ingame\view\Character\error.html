{layout name="Character/template" /}
<link rel="stylesheet" href="//down-update.qq.com/bns/static/ingameWeb/ingame/bns/ue4/character/css/error.1.css?v=20210630" media="screen" />
<body class="error-page">
    <div id="container" class="container">
		
	    <div id="contents" class="contents">
		    <!-- (s)컨텐츠영역 -->
		    <header class="title">
			    <h1>暂时无法使用服务 </h1>
		    </header>
		    
	    	<section class="contents">
			    <section class="message">
				你好，这里是剑灵小助手<br>暂时无法使用角色服务<br>非常抱歉给您带来不便，请稍后再试<br><br><br>Tips: 目标角色不存在时也会导致此提示，请检查输入
			    </section>
	    	</section>
		    <!-- (e)컨텐츠영역 -->
		    
		    
		    <!-- (s)推荐角色区域 -->
		    <section class="recommend">
		    	<p class="TopPlayer" >顶部玩家角色推荐：
		            {$jobTop|raw}
		        </p>
		        
		    	<p class="Team" >团队部分游戏角色：
		            <span data-server="1219" data-name="堕络乄" job="forcemaster" ><img> 堕络</span> | 
		            <span data-server="1911" data-name="三千问乀" job="warrior" ><img> 兔子</span> | 
		            <span data-server="3211" data-name="国服刺客绿了策划" job="assassin" ><img> 熊猫</span> | 
		            <span data-server="2021" data-name="库洛丶里多" job="summoner" ><img> 库洛</span> | 
	    	        <span data-server="1219" data-name="川凌哥哥" job="shooter" ><img> 川凌哥哥</span> | 
		            <span data-server="2021" data-name="莫若长风" job="forcemaster" ><img> 长风酱</span> | 
		        </p>
		        

		        <script>
		            //此处是为了随机显示 CorePlayer / Team 层，减少页面单次显示的文字
		            //N 就表示 1/n 概率显示为团队角色
	       	        var Flag = Math.floor(Math.random() * 20);
		            if(Flag == 0) $(".TopPlayer").hide();
		            else $(".Team").hide();

	  	
	  	            //绑定推荐玩家点击事件
	  	            $(".recommend span").click(function() {
	  	                var RoleName = $(this).data("name");
	  	                var ServerId = $(this).data("server");

	  	                if(RoleName != null && ServerId != null) 
	  	                    window.location.href = '/ingame/bs/character/profile?c=' + RoleName + '&s=' + ServerId;	  
	  	                else {
	  	                     console.log('信息异常，查看失败');
	  	                }
                    });
		
		
		            //职业图标自动加载
		           $("span img").each(function(){
		              if($(this).attr('src') == null) {
		                  //$(this).attr('src', '//down.qq.com/bns/static/ingameWeb/ingame/bns/character_v2/profile/' + $(this).parent().attr('job') + '.png');
		                  $(this).attr('src', 'https://wstatic-cdn.plaync.com/ingame/bns/ue4/character/img/job/' + $(this).parent().attr('job') + '.png');

		              } else {
		                  var ConvertSrc = $(this).attr('src').replace('tencent://','//down.qq.com/bns/static/ingameWeb/ingame/bns/character_v2/profile/');
		                  $(this).attr('src', ConvertSrc); 
		              }
                   });
                   //window.onblur = function () { document.getElementsByTagName('audio')[0].pause()}
                   //window.onfocus = function (){document.getElementsByTagName('audio')[0].play()}
		        </script>
	        </section>
	        <!-- (e)推荐角色区域 -->
	        
	    </div>
    </div>
    <!-- <script src="https://wstatic-cdn.plaync.com/ingame/bns/common/js/util.js"></script> -->
</body>