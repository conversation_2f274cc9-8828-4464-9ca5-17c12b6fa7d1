<?php

namespace app\ingame\controller\Character;

use app\common\controller\BaseController;
use app\ingame\model\Like as LikeModel;
use think\App;
use think\Request;
use think\facade\Session;
use think\facade\Validate;

class Like extends BaseController
{
    private $m_bIsLogin = false;
    private $m_sRoleName = '';
    private $m_iServerId = 0;

    public function __construct(App $app, Request $request)
    {
        parent::__construct($app, $request);
        $this->checkLogin();
    }

    private function checkLogin()
    {
        //获取自身信息
        $this->m_sRoleName = Session::get('self_roleName', null);
        $this->m_iServerId = Session::get('self_serverId', null);

        if (!empty($this->m_sRoleName) && !empty($this->m_iServerId)) {
            $this->m_bIsLogin = true;
        }
    }

    /**
     * 获取点赞信息
     */
    public function info()
    {
        $targetRoleName = $this->request->get('c', '');
        $targetServerId = $this->request->get('s', 0);

        if (empty($targetRoleName) || empty($targetServerId)) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }

        // 使用模型获取总点赞数
        $totalLikes = LikeModel::getVoteCount($targetRoleName, $targetServerId);

        $isLiked = false;
        if ($this->m_bIsLogin) {
            // 使用模型检查当前用户是否已点赞
            $isLiked = LikeModel::hasLiked($this->m_sRoleName, $this->m_iServerId, $targetRoleName, $targetServerId);
        }

        return json([
            'code' => 0,
            'data' => [
                'total_likes' => $totalLikes,
                'is_liked' => $isLiked,
                'can_like' => $this->m_bIsLogin
            ]
        ]);
    }

    /**
     * 点赞操作
     */
    public function vote()
    {
        if (!$this->m_bIsLogin) {
            return json(['code' => 1, 'msg' => '请先登录']);
        }

        $targetRoleName = $this->request->post('c', '');
        $targetServerId = $this->request->post('s', 0);

        if (empty($targetRoleName) || empty($targetServerId)) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }

        // 不能给自己点赞
        if ($targetRoleName == $this->m_sRoleName && $targetServerId == $this->m_iServerId) {
            return json(['code' => 1, 'msg' => '不能给自己点赞']);
        }

        // 使用模型检查是否已经点过赞
        if (LikeModel::hasLiked($this->m_sRoleName, $this->m_iServerId, $targetRoleName, $targetServerId)) {
            return json(['code' => 1, 'msg' => '您已经点过赞了']);
        }

        // 检查今日点赞次数限制（这个逻辑需要在模型中添加）
        $todayCount = $this->getTodayLikeCount($this->m_sRoleName, $this->m_iServerId);
        if ($todayCount >= 10) {
            return json(['code' => 1, 'msg' => '今日点赞次数已达上限']);
        }

        // 使用模型设置点赞
        try {
            LikeModel::setVote($this->m_sRoleName, $this->m_iServerId, $targetRoleName, $targetServerId, 1);
            return json(['code' => 0, 'msg' => '点赞成功']);
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '点赞失败']);
        }
    }

    /**
     * 取消点赞
     */
    public function cancel()
    {
        if (!$this->m_bIsLogin) {
            return json(['code' => 1, 'msg' => '请先登录']);
        }

        $targetRoleName = $this->request->post('c', '');
        $targetServerId = $this->request->post('s', 0);

        if (empty($targetRoleName) || empty($targetServerId)) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }

        // 使用模型取消点赞
        try {
            LikeModel::cancelVote($this->m_sRoleName, $this->m_iServerId, $targetRoleName, $targetServerId);
            return json(['code' => 0, 'msg' => '取消点赞成功']);
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '取消点赞失败']);
        }
    }

    /**
     * 获取点赞账户列表
     */
    public function accounts()
    {
        $targetRoleName = $this->request->get('c', '');
        $targetServerId = $this->request->get('s', 0);
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 20);

        if (empty($targetRoleName) || empty($targetServerId)) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }

        // 使用模型获取粉丝列表
        $likes = LikeModel::getCharacterFans($targetRoleName, $targetServerId, $limit);

        // 获取总数
        $totalLikes = LikeModel::getVoteCount($targetRoleName, $targetServerId);

        return json([
            'code' => 0,
            'data' => [
                'list' => $likes,
                'total' => $totalLikes,
                'page' => $page,
                'limit' => $limit
            ]
        ]);
    }

    /**
     * 删除点赞记录（取消自己的点赞）
     */
    public function delete()
    {
        if (!$this->m_bIsLogin) {
            return json(['code' => 1, 'msg' => '请先登录']);
        }

        $targetRoleName = $this->request->post('target_role_name', '');
        $targetServerId = $this->request->post('target_server_id', 0);

        if (empty($targetRoleName) || empty($targetServerId)) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }

        // 使用模型取消点赞
        try {
            LikeModel::cancelVote($this->m_sRoleName, $this->m_iServerId, $targetRoleName, $targetServerId);
            return json(['code' => 0, 'msg' => '删除成功']);
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '删除失败']);
        }
    }

    /**
     * 获取今日点赞次数
     */
    private function getTodayLikeCount($roleName, $serverId)
    {
        return LikeModel::getTodayLikeCount($roleName, $serverId);
    }
}
