﻿using System.Collections;
using System.Globalization;
using System.Reflection;
using System.Resources;
using System.Windows;
using HandyControl.Tools;
using Xylia.Preview.Common;
using Xylia.Preview.Common.Attributes;
using Xylia.Preview.Common.Extension;
using Xylia.Preview.Data.Common.Abstractions;
using Xylia.Preview.Data.Engine.DatData;

namespace Xylia.BnsHelper.Resources;
/// <summary>
/// Text Controller
/// </summary>
public class StringHelper : ResourceDictionary, ITextProvider
{
	#region Constructor
	public static StringHelper? Current { get; private set; }

	public StringHelper()
	{
		Globals.TextProvider = Current = this;
		Language = ELanguage.ChineseS;
	}
	#endregion

	#region CultureInfo
	private CultureInfo CultureInfo = CultureInfo.CurrentCulture;

	protected virtual string BasePath => "Xylia.BnsHelper.Resources.Strings.Strings";

	public ELanguage Language
	{
		get => EnumerateLanguages().FirstOrDefault(x => x.GetAttribute<NameAttribute>().Name == CultureInfo.Name);
		set
		{
			// culture
			if (value != ELanguage.None)
			{
				var name = value.GetAttribute<NameAttribute>()?.Name;
				if (name != null) CultureInfo = new CultureInfo(name);
			}

			// resource
			ConfigHelper.Instance.SetLang(CultureInfo.Name);
			var manager = new ResourceManager(BasePath, Assembly.GetEntryAssembly()!);
			foreach (DictionaryEntry entry in manager.GetResourceSet(CultureInfo, true, true)!)
			{
				var resourceKey = entry.Key.ToString()!;
				var resource = entry.Value;

				this[resourceKey] = resource;
			}
		}
	}

	string? ITextProvider.this[string key] => this[key] as string;
	#endregion

	#region Static Methods
	internal static IEnumerable<ELanguage> EnumerateLanguages() => Enum.GetValues<ELanguage>().Where(x => x.GetAttribute<NameAttribute>() != null);

	/// <summary>
	/// Gets text and replaces the format item in a specified string with the string representation of a corresponding object in a specified array.
	/// </summary>
	/// <param name="key">Target text resource key</param>
	/// <param name="args">An object array that contains zero or more objects to format.</param>
	/// <returns></returns>
	public static string? Get(string? key, params object?[] args)
	{
		if (string.IsNullOrEmpty(key)) return string.Empty;
		if (Current?[key] is string s) return string.Format(s, args);

		return null;
	}
	#endregion
}