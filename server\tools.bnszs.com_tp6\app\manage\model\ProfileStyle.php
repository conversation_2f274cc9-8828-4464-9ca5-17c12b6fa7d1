<?php
namespace app\manage\model;

use think\Model;
use think\facade\Db;

class ProfileStyle extends Model
{
    protected $pk = 'id';
    protected $table = 'bns_profile_style';

    /**
     * 获取样式详情
     */
    static public function detail($id) {
        return static::find($id);
    }

    /**
     * 随机获取一个样式
     */
    static public function random() {
        // 使用更高效的随机查询方式
        $count = static::where('isAction', 1)->count();
        if ($count == 0) {
            return null;
        }
        
        $offset = mt_rand(0, $count - 1);
        return static::where('isAction', 1)
            ->limit(1)
            ->offset($offset)
            ->find();
    }
    
    /**
     * 获取所有可用样式
     */
    public static function getAvailableStyles() {
        return static::where('isAction', 1)
            ->order('sort DESC, id ASC')
            ->select();
    }
    
    /**
     * 根据分类获取样式
     */
    public static function getStylesByCategory($category) {
        return static::where('isAction', 1)
            ->where('category', $category)
            ->order('sort DESC, id ASC')
            ->select();
    }
    
    /**
     * 获取热门样式
     */
    public static function getPopularStyles($limit = 10) {
        return static::where('isAction', 1)
            ->order('use_count DESC, id ASC')
            ->limit($limit)
            ->select();
    }
    
    /**
     * 获取最新样式
     */
    public static function getLatestStyles($limit = 10) {
        return static::where('isAction', 1)
            ->order('create_time DESC')
            ->limit($limit)
            ->select();
    }
    
    /**
     * 搜索样式
     */
    public static function searchStyles($keyword, $limit = 20) {
        return static::where('isAction', 1)
            ->where(function($query) use ($keyword) {
                $query->where('name', 'like', '%' . $keyword . '%')
                      ->whereOr('description', 'like', '%' . $keyword . '%')
                      ->whereOr('tags', 'like', '%' . $keyword . '%');
            })
            ->order('use_count DESC')
            ->limit($limit)
            ->select();
    }
    
    /**
     * 增加使用次数
     */
    public function incrementUseCount() {
        $this->use_count = ($this->use_count ?? 0) + 1;
        return $this->save();
    }
    
    /**
     * 创建新样式
     */
    public static function createStyle($data) {
        $style = new static();
        $style->name = $data['name'];
        $style->description = $data['description'] ?? '';
        $style->category = $data['category'] ?? 'default';
        $style->css_content = $data['css_content'] ?? '';
        $style->js_content = $data['js_content'] ?? '';
        $style->html_content = $data['html_content'] ?? '';
        $style->preview_image = $data['preview_image'] ?? '';
        $style->tags = $data['tags'] ?? '';
        $style->sort = $data['sort'] ?? 0;
        $style->isAction = $data['isAction'] ?? 1;
        $style->use_count = 0;
        $style->create_time = time();
        
        return $style->save();
    }
    
    /**
     * 更新样式
     */
    public function updateStyle($data) {
        foreach ($data as $key => $value) {
            if (in_array($key, ['name', 'description', 'category', 'css_content', 'js_content', 'html_content', 'preview_image', 'tags', 'sort', 'isAction'])) {
                $this->$key = $value;
            }
        }
        
        $this->update_time = time();
        return $this->save();
    }
    
    /**
     * 启用样式
     */
    public function enable() {
        $this->isAction = 1;
        return $this->save();
    }
    
    /**
     * 禁用样式
     */
    public function disable() {
        $this->isAction = 0;
        return $this->save();
    }
    
    /**
     * 获取样式分类列表
     */
    public static function getCategories() {
        return static::where('isAction', 1)
            ->group('category')
            ->column('category');
    }
    
    /**
     * 获取样式统计信息
     */
    public static function getStats() {
        return [
            'total' => static::count(),
            'active' => static::where('isAction', 1)->count(),
            'categories' => count(static::getCategories()),
            'total_uses' => static::sum('use_count')
        ];
    }
    
    /**
     * 复制样式
     */
    public function duplicate($newName = null) {
        $newStyle = new static();
        $newStyle->name = $newName ?: ($this->name . ' (副本)');
        $newStyle->description = $this->description;
        $newStyle->category = $this->category;
        $newStyle->css_content = $this->css_content;
        $newStyle->js_content = $this->js_content;
        $newStyle->html_content = $this->html_content;
        $newStyle->preview_image = $this->preview_image;
        $newStyle->tags = $this->tags;
        $newStyle->sort = $this->sort;
        $newStyle->isAction = 0; // 副本默认禁用
        $newStyle->use_count = 0;
        $newStyle->create_time = time();
        
        return $newStyle->save();
    }
    
    /**
     * 获取样式的完整CSS内容
     */
    public function getFullCssContent() {
        $css = $this->css_content ?: '';
        
        // 可以在这里添加一些默认的CSS
        $defaultCss = "
        /* 样式: {$this->name} */
        .profile-style-{$this->id} {
            /* 自定义样式内容 */
        }
        ";
        
        return $defaultCss . "\n" . $css;
    }
    
    /**
     * 获取样式的完整JS内容
     */
    public function getFullJsContent() {
        $js = $this->js_content ?: '';
        
        // 包装在函数中，避免全局污染
        return "
        (function() {
            // 样式: {$this->name}
            {$js}
        })();
        ";
    }
}
