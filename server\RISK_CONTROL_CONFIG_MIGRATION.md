# 风控配置迁移说明

## 概述

已将风控配置从文件配置迁移到数据库配置，实现Go服务端和PHP管理后台的配置统一。

## 变更内容

### 1. 数据库表结构

使用 `risk_control_config` 表存储配置：

```sql
CREATE TABLE `risk_control_config` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` varchar(500) NOT NULL COMMENT '配置值',
  `description` varchar(200) DEFAULT NULL COMMENT '配置描述',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 2. 配置项说明

| 配置键 | 说明 | 默认值 |
|--------|------|--------|
| `max_qq_per_device_per_day` | 同设备每日最大登录QQ数量 | 3 |
| `max_qq_per_ip_per_day` | 同IP每日最大登录QQ数量 | 3 |
| `max_login_attempts_per_hour` | 每小时最大登录尝试次数 | 20 |
| `enable_new_device_check` | 是否启用新设备检查 | true |
| `enable_location_check` | 是否启用异地登录检查 | false |
| `enable_risk_control` | 是否启用风控系统 | true |

### 3. Go服务端变更

- **启动时读取配置**：服务启动时从数据库加载配置到内存
- **定时更新配置**：每小时在统计任务中更新配置（避免频繁查询数据库）
- **线程安全**：使用读写锁保证配置读取的线程安全
- **降级机制**：数据库读取失败时使用默认配置

### 4. PHP管理后台变更

- **读取配置**：从 `risk_control_config` 表读取配置
- **保存配置**：直接更新数据库表，不再写入PHP配置文件
- **兼容性**：保持原有的管理界面不变

### 5. 删除的文件

以下配置文件已删除：
- `server/config/risk_control.yaml`
- `server/tools.bnszs.com_tp6/config/risk_control.php`

## 部署说明

### 1. 数据库初始化

执行 SQL 文件初始化配置表：
```bash
mysql -u username -p database_name < server/risk_control_config.sql
```

### 2. 重启服务

重启Go服务端以加载新的配置逻辑：
```bash
# 停止服务
sudo systemctl stop bnszs-server

# 重新编译（如果需要）
cd server && go build -o bnszs-server cmd/main.go

# 启动服务
sudo systemctl start bnszs-server
```

### 3. 验证配置

可以使用测试脚本验证配置是否正常工作：
```bash
cd server && go run test_risk_config.go
```

## 配置更新机制

1. **PHP管理后台**：管理员在后台修改配置，直接更新数据库
2. **Go服务端**：每小时自动从数据库重新加载配置
3. **实时生效**：配置更新后最多1小时内生效（可通过重启服务立即生效）

## 注意事项

1. **数据库连接**：确保Go服务端和PHP都能正常连接数据库
2. **配置缓存**：Go服务端会缓存配置30分钟，避免频繁查询数据库
3. **默认值**：如果数据库中没有配置或读取失败，会使用代码中的默认值
4. **日志监控**：关注服务日志中的配置加载和更新信息

## 故障排除

### 配置未生效
1. 检查数据库连接是否正常
2. 查看服务日志中的配置加载信息
3. 重启Go服务端强制重新加载配置

### 数据库错误
1. 确认 `risk_control_config` 表已正确创建
2. 检查数据库用户权限
3. 验证配置数据格式是否正确
