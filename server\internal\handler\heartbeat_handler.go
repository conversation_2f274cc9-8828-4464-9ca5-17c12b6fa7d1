package handler

import (
	"fmt"
	"log"
	"udp-server/server/internal/model"
	"udp-server/server/internal/service"
)

// HeartbeatHandler 心跳处理器
type HeartbeatHandler struct {
	authService      *service.AuthService
	heartbeatService *service.HeartbeatService
}

// NewHeartbeatHandler 创建新的心跳处理器
func NewHeartbeatHandler(authService *service.AuthService, heartbeatService *service.HeartbeatService) *HeartbeatHandler {
	return &HeartbeatHandler{
		authService:      authService,
		heartbeatService: heartbeatService,
	}
}

// HandleHeartbeatDirect 直接处理心跳请求（用于二进制协议）
func (h *HeartbeatHandler) HandleHeartbeatDirect(token string) (*model.User, int, error) {
	// 打印日志，便于排查
	log.Printf("[DEBUG] 收到心跳请求: Token=%s", token)

	// 验证token
	user, err := h.authService.ValidateToken(token)
	if err != nil {
		log.Printf("[ERROR] Token验证失败: %v, Token=%s", err, token)
		return nil, 0, fmt.Errorf("token验证失败: %v", err)
	}

	// 使用用户的UIN作为设备标识，只更新HeartbeatService中的心跳时间（纯内存操作）
	deviceID := fmt.Sprintf("%d", user.Uin)
	if h.heartbeatService != nil {
		if err := h.heartbeatService.UpdateHeartbeat(deviceID); err != nil {
			return nil, 0, fmt.Errorf("更新心跳时间失败: %v", err)
		}
	} else {
		return nil, 0, fmt.Errorf("心跳服务未初始化")
	}

	// 获取当前在线用户数量
	onlineCount := 0
	if h.heartbeatService != nil {
		onlineCount = h.heartbeatService.GetOnlineUserCount()
	}

	log.Printf("[INFO] 心跳成功: QQ号=%d, Token=%s, 在线用户数: %d", user.Uin, user.Token, onlineCount)
	return user, onlineCount, nil
}
