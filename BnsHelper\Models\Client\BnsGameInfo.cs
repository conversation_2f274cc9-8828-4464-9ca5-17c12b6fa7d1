﻿using System.IO;
using Xylia.Preview.Data.Engine.DatData;

namespace Xylia.BnsHelper.Models;
internal struct BnsGameInfo(string fullPath) : IComparable<BnsGameInfo>
{
    public readonly string FullPath => fullPath;

    private Locale? locale;
    public Locale Locale => locale ??= new Locale(new DirectoryInfo(FullPath));
    public EPublisher Publisher => Locale.ClientPublisher;

    public string? Drive => Path.GetPathRoot(fullPath)?.TrimEnd('\\');

    public string Name => Publisher switch
    {
        EPublisher.Tencent => "正式服",
        EPublisher.ZTX => "怀旧服",
        EPublisher.ZNCG => "巅峰服",
        _ => "不支持的客户端"
    };

    public string Source => fullPath.Contains("_prod") ? "登录器" : "WeGame";

    public bool IsEmpty => string.IsNullOrEmpty(FullPath) || !Directory.Exists(FullPath);

    #region IComparable
    public readonly bool Equals(BnsGameInfo other) => string.Equals(FullPath , other.FullPath , StringComparison.OrdinalIgnoreCase);
    public readonly override bool Equals(object obj) => obj is BnsGameInfo other && Equals(other);
    public readonly override int GetHashCode() => HashCode.Combine(FullPath);
    public readonly int CompareTo(BnsGameInfo other) => this.FullPath.CompareTo(other.FullPath);
    public readonly override string ToString() => FullPath;
    #endregion
}
