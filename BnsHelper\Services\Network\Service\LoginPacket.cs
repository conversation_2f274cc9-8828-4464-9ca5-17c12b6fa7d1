using System.Text;
using Xylia.Preview.Data.Engine;

namespace Xylia.BnsHelper.Services.Network.Service;

/// <summary>
/// 登录包，包含登录请求和响应的处理逻辑
/// </summary>
internal class LoginPacket : BasePacket
{
    #region Response Fields
    /// <summary>
    /// 用户ID
    /// </summary>
    public ulong UserID { get; set; }

    /// <summary>
    /// 用户权限：0-普通用户，1-高级用户，2-会员用户
    /// </summary>
    public byte Permission { get; set; } = 0;

    /// <summary>
    /// 权限过期时间：-1=永久，0=无权限，>0=具体时间戳
    /// </summary>
    public long PermissionExpiration { get; set; } = 0;
    #endregion

    #region Request Fields
    /// <summary>
    /// QQ号码
    /// </summary>
    public string QQNumber { get; set; }

    /// <summary>
    /// 设备指纹（服务端根据此生成设备码）
    /// </summary>
    public string DeviceFingerprint { get; set; }
    #endregion

    #region Methods
    public override DataArchiveWriter Create()
    {
        // 登录请求包不存在Token信息
        using var writer = new DataArchiveWriter();
        writer.WriteString(QQNumber, Encoding.UTF8);
        writer.WriteString(DeviceFingerprint, Encoding.UTF8);
        return writer;
    }

    /// <summary>
    /// 解析登录响应数据
    /// </summary>
    /// <param name="reader">数据读取器</param>
    protected override void ReadResponse(DataArchive reader)
    {
        Token = reader.ReadString();
        UserID = reader.Read<ulong>();

        // 跳过暂时不需要的字段
        reader.ReadString(); // UIN
        reader.ReadString(); // Name
        reader.ReadString(); // Email
        reader.Read<uint>(); // Status
        reader.Read<bool>(); // Beta

        // 读取权限相关字段
        Permission = reader.Read<byte>();  // 最终权限（服务端计算结果）
        PermissionExpiration = reader.Read<long>(); // 权限过期时间
    }
    #endregion
}
