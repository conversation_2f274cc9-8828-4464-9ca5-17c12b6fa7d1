﻿using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Win32;
using Newtonsoft.Json;
using System.IO;
using Xylia.BnsHelper.Models;
using Xylia.BnsHelper.Resources;
using Xylia.Preview.UI.Views.Dialogs;

namespace Xylia.BnsHelper.ViewModels.Pages;
internal partial class HUDPageViewModel : ObservableObject
{
	#region Properties		 
	[ObservableProperty] HUDCollection data = HUDCollection.Load();
	#endregion

	#region Methods
	[RelayCommand]
	async Task Import()
	{
		var dialog = new OpenFileDialog()
		{
			FileName = "hud",
			Filter = "JavaScript Object Notation|*.json",
		};
		if (dialog.ShowDialog() != true) return;

		Data.Clear();
		Data = JsonConvert.DeserializeObject<HUDCollection>(File.ReadAllText(dialog.FileName))!;
		await MessageDialog.ShowDialog(StringHelper.Get("EffectPage_ImportMessage"));
	}

	[RelayCommand]
	async Task Export()
	{
		var dialog = new SaveFileDialog()
		{
			FileName = "hud",
			Filter = "JavaScript Object Notation|*.json",
		};
		if (dialog.ShowDialog() != true) return;

		File.WriteAllText(dialog.FileName, JsonConvert.SerializeObject(Data, Formatting.Indented));
		await MessageDialog.ShowDialog(StringHelper.Get("EffectPage_ExportMessage"));
	}
	#endregion
}