﻿<hc:Window x:Class="Xylia.BnsHelper.Views.UserLogin2"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
		xmlns:hc="https://handyorg.github.io/handycontrol"
		xmlns:helper="clr-namespace:Xylia.BnsHelper.Common.Helpers"
		xmlns:WebView="clr-namespace:Microsoft.Web.WebView2.Wpf;assembly=Microsoft.Web.WebView2.Wpf"
		Title="{DynamicResource UserLogin_Name}" Background="{StaticResource RegionBrush}"
		WindowStartupLocation="CenterScreen" ResizeMode="NoResize" SizeToContent="WidthAndHeight">

	<Grid Width="450" Height="300">
		<WebView:WebView2 Name="wv" Width="450" Height="300" CreationProperties="{StaticResource EvergreenWebView2CreationProperties}" CoreWebView2InitializationCompleted="CoreWebView2InitializationCompleted" />

		<!-- Loading Animation Overlay -->
        <Grid Name="LoadingOverlay" Background="{DynamicResource DarkOpacityBrush}" Visibility="Collapsed">
			<Border Background="{DynamicResource RegionBrush}" CornerRadius="12" MinWidth="340" MaxWidth="420" MinHeight="180" MaxHeight="220"
					HorizontalAlignment="Center" VerticalAlignment="Center"
					BorderBrush="{DynamicResource BorderBrush}" BorderThickness="1">
				<Border.Effect>
					<DropShadowEffect Color="Black" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="8"/>
				</Border.Effect>

				<StackPanel Margin="25,25" HorizontalAlignment="Center" VerticalAlignment="Center">
					<!-- Loading Icon -->
					<hc:CircleProgressBar Name="LoadingIcon" IsIndeterminate="True" Width="48" Height="48"
										  Foreground="{DynamicResource PrimaryBrush}" Margin="0,0,0,15"/>

					<!-- Error State Container -->
					<StackPanel Name="ErrorContainer" Orientation="Horizontal" HorizontalAlignment="Center"
								Margin="0,0,0,15" Visibility="Collapsed">
						<!-- Error Icon Container -->
						<Border Name="ErrorIconContainer" Width="36" Height="36" CornerRadius="18"
								Background="{DynamicResource LightDangerBrush}" BorderBrush="{DynamicResource DangerBrush}" BorderThickness="2"
								Margin="0,0,12,0" VerticalAlignment="Center">
							<TextBlock Name="ErrorIcon" Text="&#xE711;" FontFamily="{DynamicResource SegoeAssets}"
									   FontSize="18" Foreground="{DynamicResource DangerBrush}" HorizontalAlignment="Center" VerticalAlignment="Center"/>
						</Border>

						<!-- Error Text -->
						<TextBlock Name="ErrorText" FontSize="15" FontWeight="Medium"
								   VerticalAlignment="Center" TextAlignment="Left"
								   Foreground="{DynamicResource PrimaryTextBrush}" TextWrapping="Wrap" TextTrimming="CharacterEllipsis"
								   MaxWidth="300"/>
					</StackPanel>

					<!-- Loading Status Text -->
					<TextBlock Name="StatusText" Text="{DynamicResource UserLogin_LoggingIn}" FontSize="16" FontWeight="Medium"
							   HorizontalAlignment="Center" TextAlignment="Center"
							   Foreground="{DynamicResource PrimaryTextBrush}" MaxWidth="320"/>

					<!-- Button Container (Hidden by default) -->
					<StackPanel Name="ButtonContainer" Orientation="Horizontal" HorizontalAlignment="Center"
								Margin="0,15,0,0" Visibility="Collapsed">
						<!-- Retry Button -->
						<Button Name="RetryButton" Content="{DynamicResource Button_Retry}" Padding="18,8" Margin="0,0,10,0"
								Background="{DynamicResource PrimaryBrush}" Foreground="{DynamicResource TextIconBrush}"
								BorderThickness="0" FontSize="14" FontWeight="Medium"
								Click="RetryButton_Click">
							<Button.Style>
								<Style TargetType="Button">
									<Setter Property="Cursor" Value="Hand"/>
									<Setter Property="Template">
										<Setter.Value>
											<ControlTemplate TargetType="Button">
												<Border Background="{TemplateBinding Background}"
														CornerRadius="6"
														BorderBrush="{TemplateBinding BorderBrush}"
														BorderThickness="{TemplateBinding BorderThickness}">
													<ContentPresenter HorizontalAlignment="Center"
																	  VerticalAlignment="Center"
																	  Margin="{TemplateBinding Padding}"/>
												</Border>
												<ControlTemplate.Triggers>
													<Trigger Property="IsMouseOver" Value="True">
														<Setter Property="Background" Value="{DynamicResource DarkPrimaryBrush}"/>
													</Trigger>
													<Trigger Property="IsPressed" Value="True">
														<Setter Property="Background" Value="{DynamicResource DarkPrimaryBrush}"/>
														<Setter Property="Opacity" Value="0.8"/>
													</Trigger>
												</ControlTemplate.Triggers>
											</ControlTemplate>
										</Setter.Value>
									</Setter>
								</Style>
							</Button.Style>
						</Button>

						<!-- Cancel Button -->
						<Button Name="CancelButton" Content="{DynamicResource Button_Cancel}" Padding="18,8"
								Background="{DynamicResource SecondaryRegionBrush}" Foreground="{DynamicResource PrimaryTextBrush}"
								BorderBrush="{DynamicResource BorderBrush}" BorderThickness="1" FontSize="14" FontWeight="Medium"
								Click="CancelButton_Click">
							<Button.Style>
								<Style TargetType="Button">
									<Setter Property="Cursor" Value="Hand"/>
									<Setter Property="Template">
										<Setter.Value>
											<ControlTemplate TargetType="Button">
												<Border Background="{TemplateBinding Background}"
														CornerRadius="6"
														BorderBrush="{TemplateBinding BorderBrush}"
														BorderThickness="{TemplateBinding BorderThickness}">
													<ContentPresenter HorizontalAlignment="Center"
																	  VerticalAlignment="Center"
																	  Margin="{TemplateBinding Padding}"/>
												</Border>
												<ControlTemplate.Triggers>
													<Trigger Property="IsMouseOver" Value="True">
														<Setter Property="Background" Value="{DynamicResource ThirdlyRegionBrush}"/>
														<Setter Property="BorderBrush" Value="{DynamicResource SecondaryBorderBrush}"/>
													</Trigger>
													<Trigger Property="IsPressed" Value="True">
														<Setter Property="Background" Value="{DynamicResource ThirdlyRegionBrush}"/>
														<Setter Property="Opacity" Value="0.8"/>
													</Trigger>
												</ControlTemplate.Triggers>
											</ControlTemplate>
										</Setter.Value>
									</Setter>
								</Style>
							</Button.Style>
						</Button>
					</StackPanel>
				</StackPanel>
			</Border>
		</Grid>
	</Grid>
</hc:Window>