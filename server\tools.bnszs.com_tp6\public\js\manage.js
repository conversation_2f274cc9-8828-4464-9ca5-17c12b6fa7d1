$.each($("textarea"), function(i, n){
	$(n).css("height", (n.scrollHeight + 10)  + "px");
})

$(".pimg").click(function(){
	imgShow("#outerdiv", "#innerdiv", "#bigimg", $(this));
});

 //点击菜单内容后隐藏菜单栏
$(".am-dropdown-content li").click(function(){
	$(this).parent().hide();
});



function isValidURL(url){
	var urlRegExp=/^((https|http)?:\/\/)+[A-Za-z0-9]+\.[A-Za-z0-9]+[\/=\?%\-&_~`@[\]\':+!]*([^<>\"\"])*$/;
	return urlRegExp.test(url);
}

function getQueryVariable(variable) {
	var query = window.location.search.substring(1);
	var vars = query.split("&");
	
	for (var i=0;i<vars.length;i++) {
		var pair = vars[i].split("=");
		if(pair[0] == variable) return decodeURI(pair[1]);
	}
	   
	return null;
}

function changeQueryVariable(variable,value,url = window.location.href){
	var pattern = variable+'=([^&]*)';
	var replaceText = variable+'='+value; 
	
	if(url.match(pattern)){
		var tmp='/('+ variable+'=)([^&]*)/gi';
		tmp=url.replace(eval(tmp),replaceText);
		
		return tmp;
	}else{ 
		if(url.match('[\?]')){ 
			return url+'&'+replaceText; 
		}else{ 
			return url+'?'+replaceText; 
		} 
	}
}

function getCookie(name) {
	var cookieArr = document.cookie.split(";"); 
	for (var i = 0; i < cookieArr.length; i++) {
		var cookiePair = cookieArr[i].split("=");
		if (name == cookiePair[0].trim()) return decodeURIComponent(cookiePair[1]);
	}
	return null;
}

function logout() {
	if(!confirm('确认退出吗')){ 
	   window.location.href='/'; 
	} else{ 
	   window.location.href='/manage/logout';
	}
}

function gotoTop($Sel) {
	$($Sel).animate({ scrollTop: 0 }, 300);
}

function imgShow(outerdiv, innerdiv, bigimg, _this){
	var src = _this.attr("src");//获取当前点击的pimg元素中的src属性
	$(bigimg).attr("src", src);//设置#bigimg元素的src属性
 
        /*获取当前点击图片的真实大小，并显示弹出层及大图*/
	$("<img/>").attr("src", src).load(function(){
		var windowW = $(window).width();//获取当前窗口宽度
		var windowH = $(window).height();//获取当前窗口高度
		var realWidth = this.width;//获取图片真实宽度
		var realHeight = this.height;//获取图片真实高度
		var imgWidth, imgHeight;
		var scale = 0.8;//缩放尺寸，当图片真实宽度和高度大于窗口宽度和高度时进行缩放
		
		if(realHeight>windowH*scale) 
		{
			//判断图片高度
			imgHeight = windowH*scale;//如大于窗口高度，图片高度进行缩放
			imgWidth = imgHeight/realHeight*realWidth;//等比例缩放宽度
			if(imgWidth>windowW*scale) 
			{//如宽度仍大于窗口宽度
				imgWidth = windowW*scale;//再对宽度进行缩放
			}
		} 
		else if(realWidth>windowW*scale) 
		{//如图片高度合适，判断图片宽度
			imgWidth = windowW*scale;//如大于窗口宽度，图片宽度进行缩放
            imgHeight = imgWidth/realWidth*realHeight;//等比例缩放高度
		} 
		else {//如果图片真实高度和宽度都符合要求，高宽不变
			imgWidth = realWidth;
			imgHeight = realHeight;
		}
        $(bigimg).css("width",imgWidth);//以最终的宽度对图片缩放
		
		var w = (windowW-imgWidth)/2;//计算图片与窗口左边距
		var h = (windowH-imgHeight)/2;//计算图片与窗口上边距
		$(innerdiv).css({"top":h, "left":w});//设置#innerdiv的top和left属性
		$(outerdiv).fadeIn("fast");//淡入显示#outerdiv及.pimg
	});
	
	$(outerdiv).click(function(){//再次点击淡出消失弹出层
		$(this).fadeOut("fast");
	});
}