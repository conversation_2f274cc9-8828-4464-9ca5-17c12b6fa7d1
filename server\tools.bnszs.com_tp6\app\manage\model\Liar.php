<?php
namespace app\manage\model;

use think\Model;

class Liar extends Model
{
    protected $pk = 'id';
    protected $table = 'bns_liar';

    /**
     * 检查是否是骗子
     */
    public static function CheckLiar($serverId, $roleName) {
        if (empty($serverId) || empty($roleName)) return false;

        $user = static::where(['area' => $serverId, 'rolename' => $roleName, 'is_del' => 0, 'is_shenhe' => 1])->find();
        return !empty($user);
    }

    /**
     * 获取骗子详情
     */
    public static function GetDetail($serverId, $roleName) {   
        $user = static::where(['area' => $serverId, 'rolename' => $roleName, 'is_del' => 0, 'is_shenhe' => 1])->find();
        return $user;		
    }
    
    /**
     * 获取骗子信息
     */
    public static function GetInfo($serverId, $roleName) {   
        if (empty($serverId) || empty($roleName)) return null;

        $roleName = urldecode($roleName);
        return static::where(['area' => $serverId, 'rolename' => $roleName, 'is_del' => 0, 'is_shenhe' => 1])->find();	
    }

    /**
     * 获取骗子列表
     */
    public static function Gets() {
        return static::where('is_del', 0)->order('id', 'desc');
    }
    
    /**
     * 举报骗子
     */
    public static function reportLiar($data) {
        $liar = new static();
        $liar->area = $data['area'];
        $liar->rolename = $data['rolename'];
        $liar->reason = $data['reason'] ?? '';
        $liar->evidence = $data['evidence'] ?? '';
        $liar->reporter_name = $data['reporter_name'] ?? '';
        $liar->reporter_contact = $data['reporter_contact'] ?? '';
        $liar->is_del = 0;
        $liar->is_shenhe = 0; // 待审核
        $liar->create_time = time();
        
        return $liar->save();
    }
    
    /**
     * 审核通过
     */
    public function approve() {
        $this->is_shenhe = 1;
        $this->shenhe_time = time();
        return $this->save();
    }
    
    /**
     * 审核拒绝
     */
    public function reject($reason = '') {
        $this->is_shenhe = 2;
        $this->reject_reason = $reason;
        $this->shenhe_time = time();
        return $this->save();
    }
    
    /**
     * 删除举报
     */
    public function deleteLiar() {
        $this->is_del = 1;
        $this->delete_time = time();
        return $this->save();
    }
    
    /**
     * 获取待审核列表
     */
    public static function getPendingList($limit = 20) {
        return static::where('is_del', 0)
            ->where('is_shenhe', 0)
            ->order('create_time DESC')
            ->limit($limit)
            ->select();
    }
    
    /**
     * 获取已审核列表
     */
    public static function getApprovedList($limit = 50) {
        return static::where('is_del', 0)
            ->where('is_shenhe', 1)
            ->order('shenhe_time DESC')
            ->limit($limit)
            ->select();
    }
    
    /**
     * 搜索骗子
     */
    public static function searchLiar($keyword, $limit = 20) {
        return static::where('is_del', 0)
            ->where('is_shenhe', 1)
            ->where('rolename', 'like', '%' . $keyword . '%')
            ->order('create_time DESC')
            ->limit($limit)
            ->select();
    }
    
    /**
     * 根据服务器获取骗子列表
     */
    public static function getLiarsByServer($serverId, $limit = 50) {
        return static::where('is_del', 0)
            ->where('is_shenhe', 1)
            ->where('area', $serverId)
            ->order('create_time DESC')
            ->limit($limit)
            ->select();
    }
    
    /**
     * 获取统计信息
     */
    public static function getStats() {
        return [
            'total' => static::where('is_del', 0)->count(),
            'approved' => static::where('is_del', 0)->where('is_shenhe', 1)->count(),
            'pending' => static::where('is_del', 0)->where('is_shenhe', 0)->count(),
            'rejected' => static::where('is_del', 0)->where('is_shenhe', 2)->count(),
            'today' => static::where('create_time', '>', strtotime('today'))->count()
        ];
    }
    
    /**
     * 检查重复举报
     */
    public static function checkDuplicate($area, $rolename, $reporterContact) {
        return static::where('area', $area)
            ->where('rolename', $rolename)
            ->where('reporter_contact', $reporterContact)
            ->where('is_del', 0)
            ->where('create_time', '>', time() - 86400) // 24小时内
            ->find();
    }
}
