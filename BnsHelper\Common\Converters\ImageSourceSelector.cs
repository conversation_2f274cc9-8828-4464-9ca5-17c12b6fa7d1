﻿using Xylia.Preview.Data.Models.Sequence;

namespace Xylia.BnsHelper.Common.Converters;
public class ImageSourceSelector : ImageSelector
{
	const string Prefix = "/Resources/Images/";

	protected override string? GetUri(object value) => value switch
	{
		JobSeq.검사 => Prefix + "job_blademaster.png",
		JobSeq.권사 => Prefix + "job_kungfufighter.png",
		JobSeq.기공사 => Prefix + "job_forcemaster.png",
		JobSeq.격사 => Prefix + "job_shooter.png",
		JobSeq.역사 => Prefix + "job_destroyer.png",
		JobSeq.소환사 => Prefix + "job_summoner.png",
		JobSeq.암살자 => Prefix + "job_assassin.png",
		JobSeq.귀검사 => Prefix + "job_swordmaster.png",
		JobSeq.주술사 => Prefix + "job_warlock.png",
		JobSeq.기권사 => Prefix + "job_soulfighter.png",
		JobSeq.투사 => Prefix + "job_warrior.png",
		JobSeq.궁사 => Prefix + "job_archer.png",
		JobSeq.창술사 => Prefix + "job_blademaster.png",
		JobSeq.뇌전술사 => Prefix + "job_thunderer.png",
		JobSeq.쌍검사 => Prefix + "job_dualblade.png",
		JobSeq.악사 => Prefix + "job_bard.png",
		JobSeq.소환수루키 => Prefix + "race_cat.png",
		_ => null
	};
}