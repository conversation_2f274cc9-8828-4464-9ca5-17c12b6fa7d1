﻿using Microsoft.Web.WebView2.Core;
using System.Diagnostics;
using Xylia.BnsHelper.Resources;
using Xylia.BnsHelper.ViewModels.Pages;

namespace Xylia.BnsHelper.Views.Pages;
public partial class ActivityPage
{
	#region Constructor
	readonly ActivityPageViewModel _viewModel;

	public ActivityPage()
	{
		InitializeComponent();
		DataContext = _viewModel = new ActivityPageViewModel(WebView);
		WebView.CoreWebView2InitializationCompleted += WebView_CoreWebView2InitializationCompleted;
        WebView.Source = new Uri("https://tools.bnszs.com/activity?style=" + (SkinHelpers.IsNight() ? "dark" : "light"));

    }
	#endregion

	#region Methods
	private void WebView_CoreWebView2InitializationCompleted(object? sender, CoreWebView2InitializationCompletedEventArgs args)
	{
		if (!args.IsSuccess) return;

		WebView.CoreWebView2.Settings.AreDevToolsEnabled = false;
		//WebView.CoreWebView2.Settings.AreDefaultContextMenusEnabled = false;
		WebView.CoreWebView2.Settings.UserAgent = "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2272.104 Safari/537.36 BnsIngameBrowser";
		WebView.CoreWebView2.ContextMenuRequested += WebView_CoreWebView2ContextMenuRequested;
		WebView.CoreWebView2.NewWindowRequested += WebView_CoreWebView2NewWindowRequested;
	}

	private void WebView_CoreWebView2NewWindowRequested(object? sender, CoreWebView2NewWindowRequestedEventArgs args)
	{
		args.Handled = true;
		Process.Start("explorer.exe", args.Uri.ToString());
	}

	private void WebView_CoreWebView2ContextMenuRequested(object? sender, CoreWebView2ContextMenuRequestedEventArgs args)
	{
		args.MenuItems.Clear();

#if DEBUG
		var item0 = WebView.CoreWebView2.Environment.CreateContextMenuItem("测试", null, CoreWebView2ContextMenuItemKind.Command);
		item0.CustomItemSelected += (s, e) => _viewModel.Test();
		args.MenuItems.Insert(args.MenuItems.Count, item0);
#endif
	}
	#endregion
}
