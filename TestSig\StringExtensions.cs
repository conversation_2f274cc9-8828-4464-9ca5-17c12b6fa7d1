﻿using System.Security.Cryptography;
using System.Text;

namespace ConsoleApp1;
internal static class StringExtensions
{
    internal static string? Hash(this string source, Encoding? encoding = null)
	{
		ArgumentNullException.ThrowIfNull(source);
		encoding ??= Encoding.UTF8;

		var data = MD5.HashData(encoding.GetBytes(source));
		var str = new StringBuilder();

		for (int i = 0; i < data.Length; i++)
		{
			str.Append(data[i].ToString("x2"));
		}

		return str.ToString();
	}

    internal static string? HashW(this string source) => source.Hash(Encoding.Unicode);

    // HMAC-SHA256签名方法
    internal static string? HMACSign(this string source, string secretKey)
    {
        ArgumentNullException.ThrowIfNull(source);
        ArgumentNullException.ThrowIfNull(secretKey);

        try
        {
            using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(secretKey));
            var hashBytes = hmac.ComputeHash(Encoding.UTF8.GetBytes(source));
            return Convert.ToBase64String(hashBytes);
        }
        catch
        {
            return null;
        }
    }

    // 生成安全密钥（与C++端保持一致）
    internal static string GenerateSecretKey(string version, string clientId = "")
    {
        // 使用每日轮换的密钥（与C++端逻辑一致）
        var now = DateTime.UtcNow;
        var dayKey = (long)(now - DateTime.UnixEpoch).TotalHours / 24;

        var baseKey = $"BnsZs_HMAC_SecureKey_{version}_2025_{dayKey}";
        if (!string.IsNullOrEmpty(clientId))
        {
            baseKey += $"_{clientId}";
        }

        // 转换为Unicode字节（与C++端一致）
        var unicodeBytes = Encoding.Unicode.GetBytes(baseKey);

        // 计算SHA256
        using var sha256 = SHA256.Create();
        var keyBytes = sha256.ComputeHash(unicodeBytes);

        return Convert.ToBase64String(keyBytes);
    }
}