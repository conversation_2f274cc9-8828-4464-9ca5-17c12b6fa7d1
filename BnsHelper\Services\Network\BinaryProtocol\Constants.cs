namespace Xylia.BnsHelper.Services.Network.BinaryProtocol;

// Binary protocol constants
internal static class Constants
{
    // Protocol magic and version
    public const byte ProtocolMagic = 0xDA;
    public const byte ProtocolVersion = 0x01;

    // Message header size
    public const int HeaderSize = 16; // Fixed 16 bytes

    // Flag definitions
    public const byte FlagNeedResponse = 0x01; // Need response
    public const byte FlagCompressed = 0x02;   // Compressed
    public const byte FlagEncrypted = 0x04;    // Encrypted

    // Limits
    public const int MaxMessageSize = 65536;   // Max message size 64KB
}

// Message type definitions
internal static class MessageTypes
{
    public const byte Login = 0x01;                        // Login request
    public const byte LoginResponse = 0x81;                // Login response
    public const byte Heartbeat = 0x02;                    // Heartbeat request
    public const byte HeartbeatResponse = 0x82;            // Heartbeat response
    public const byte Logout = 0x03;                       // Logout request
    public const byte LogoutResponse = 0x83;               // Logout response
    public const byte GetDeviceHistory = 0x04;             // Get device history
    public const byte GetDeviceHistoryResponse = 0x84;     // Device history response
    public const byte GetActiveDevices = 0x05;             // Get active devices
    public const byte GetActiveDevicesResponse = 0x85;     // Active devices response
    public const byte LuckyDraw = 0x06;                    // Lucky draw (sign-in) request
    public const byte LuckyDrawResponse = 0x86;            // Lucky draw response
    public const byte LuckyStatus = 0x07;                  // Get sign-in status request
    public const byte LuckyStatusResponse = 0x87;          // Sign-in status response
    public const byte CDKeyActivate = 0x08;                // CDKEY activate request
    public const byte CDKeyActivateResponse = 0x88;        // CDKEY activate response
    public const byte GetActivityInfo = 0x09;              // Get activity info request
    public const byte GetActivityInfoResponse = 0x89;      // Get activity info response
    public const byte Error = 0xFF;                        // Error response
}
