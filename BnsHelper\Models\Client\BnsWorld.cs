﻿namespace Xylia.BnsHelper.Models;
internal class BnsWorld(short id, string name)
{
	public short Id { get; } = id;
	public string Name { get; } = name;


	#region Methods
	private static BnsWorld[] Worlds { get; } =
	[
		new(1001, "大漠孤烟"),
		new(1002, "长河剑气灵光"),
		new(1006, "武林浑天白雾"),
		new(1101, "永恒续章"),
		new(1201, "重逢无限"),
		new(1301, "北方雪原"),
		new(1302, "有你才灵"),

        new(2001, "绝代风华"),
        new(2003, "爱欧妮娅"),
        new(2004, "巅峰无界"),
        new(2006, "登峰极镜"),
        new(2007, "新无日峰"),
        new(2009, "一剑霜华"),
        new(2013, "归去来兮"),
    ];

	public static string GetName(int world) => Worlds.FirstOrDefault(x => x.Id == world)?.Name ?? world.ToString();
	#endregion
}
