<?php
namespace app\guidebook\model;

use think\Model;

class Clock
{
    /**
     * 静态时钟数据表 - 持久化存储
     */
    private static $table = [
            ['day'=>1,'time'=>'21:00:00','type'=>0,'zone'=>['烈沙地带远古巨龙','巨岩谷玄武帝']],
            ['day'=>2,'time'=>'21:00:00','type'=>0,'zone'=>['风之平原神武龙','巨岩谷玄武帝']],
            ['day'=>3,'time'=>'21:00:00','type'=>0,'zone'=>['风之平原神武龙','烈沙地带远古巨龙']],
            ['day'=>4,'time'=>'21:00:00','type'=>0,'zone'=>['烈沙地带远古巨龙','巨岩谷玄武帝']],
            ['day'=>5,'time'=>'21:00:00','type'=>0,'zone'=>['风之平原神武龙','巨岩谷玄武帝']],
            ['day'=>6,'time'=>'15:00:00','type'=>0,'zone'=>['风之平原神武龙','烈沙地带远古巨龙']],
            ['day'=>6,'time'=>'21:00:00','type'=>0,'zone'=>['烈沙地带远古巨龙','巨岩谷玄武帝']],
            ['day'=>0,'time'=>'15:00:00','type'=>0,'zone'=>['风之平原神武龙','烈沙地带远古巨龙']],
            ['day'=>0,'time'=>'21:00:00','type'=>0,'zone'=>['风之平原神武龙','巨岩谷玄武帝']],

            // 浊魔灵
            ['day'=>1,'time'=>'13:30:00','type'=>1,'zone'=>['烈沙地带','土门阵','红叶山庄','白桦林','北方雪原']],
            ['day'=>1,'time'=>'19:30:00','type'=>1,'zone'=>['半月湖','巨岩谷','五彩岩岛','风之平原','北方雪原']],
            ['day'=>1,'time'=>'22:30:00','type'=>1,'zone'=>['土门阵','陶土林','白雾森林','白桦林','北方雪原']],
            ['day'=>2,'time'=>'13:30:00','type'=>1,'zone'=>['黑森林','陶土林','白雾森林','风之平原','白桦林']],
            ['day'=>2,'time'=>'19:30:00','type'=>1,'zone'=>['烈沙地带','黑森林','红叶山庄','风之平原','白桦林']],
            ['day'=>2,'time'=>'22:30:00','type'=>1,'zone'=>['天狼陵','黑森林','五彩岩岛','风之平原','北方雪原']],
            ['day'=>3,'time'=>'13:30:00','type'=>1,'zone'=>['烈沙地带','天狼陵','巨岩谷','风之平原','北方雪原']],
            ['day'=>3,'time'=>'19:30:00','type'=>1,'zone'=>['黑森林','陶土林','白雾森林','白桦林','北方雪原']],
            ['day'=>3,'time'=>'22:30:00','type'=>1,'zone'=>['烈沙地带','巨岩谷','鬼都','风之平原','白桦林']],
            ['day'=>4,'time'=>'13:30:00','type'=>1,'zone'=>['黑森林','五彩岩岛','鬼都','风之平原','北方雪原']],
            ['day'=>4,'time'=>'19:30:00','type'=>1,'zone'=>['烈沙地带','土门阵','鬼都','风之平原','白桦林']],
            ['day'=>4,'time'=>'22:30:00','type'=>1,'zone'=>['黑森林','五彩岩岛','红叶山庄','风之平原','北方雪原']],
            ['day'=>5,'time'=>'13:30:00','type'=>1,'zone'=>['巨岩谷','陶土林','白雾森林','白桦林','北方雪原']],
            ['day'=>5,'time'=>'19:30:00','type'=>1,'zone'=>['烈沙地带','土门阵','鬼都','风之平原','白桦林']],
            ['day'=>5,'time'=>'22:30:00','type'=>1,'zone'=>['黑森林','五彩岩岛','红叶山庄','风之平原','北方雪原']],
            ['day'=>6,'time'=>'13:30:00','type'=>1,'zone'=>['烈沙地带','半月湖','巨岩谷','白桦林','北方雪原']],
            ['day'=>6,'time'=>'19:30:00','type'=>1,'zone'=>['半月湖','黑森林','陶土林','白桦林','北方雪原']],
            ['day'=>6,'time'=>'22:30:00','type'=>1,'zone'=>['烈沙地带','巨岩谷','白雾森林','风之平原','白桦林']],
            ['day'=>0,'time'=>'13:30:00','type'=>1,'zone'=>['土门阵','陶土林','鬼都','风之平原','白桦林']],
            ['day'=>0,'time'=>'19:30:00','type'=>1,'zone'=>['巨岩谷','五彩岩岛','红叶山庄','风之平原','北方雪原']],
            ['day'=>0,'time'=>'22:30:00','type'=>1,'zone'=>['天狼陵','土门阵','五彩岩岛','白桦林','北方雪原']],
        ];

    /**
     * 获取时钟数据
     */
    public static function getClockData($type) {
        // 根据服务器返回不同信息
        $ret = [];
        if ($type !== "ZTX") return $ret;

        $current = time();
        $data = self::$table;
        $currentDay = (int)date('w', $current); // 当前星期几 (0=周日, 1=周一...)

        // 防止无限循环：限制处理的记录数量
        $maxRecords = 50;
        $processedCount = 0;

        foreach ($data as $record) {
            // 防止死循环保护
            if (++$processedCount > $maxRecords) {
                break;
            }

            // 只处理今天的活动
            if ($record['day'] !== $currentDay) {
                continue;
            }

            $zone = $record['zone'] ?? [];

            // 修复时间计算：使用今天的日期 + 活动时间
            $todayDate = date('Y-m-d', $current);
            $activityTime = strtotime($todayDate . ' ' . $record['time']);

            // 防止无效时间
            if ($activityTime === false) {
                continue;
            }

            $sec = $current - $activityTime;

            $ret[] = [
                'type' => $record['type'],
                'time' => $activityTime,
                'zone' => !empty($zone) ? implode(" / ", $zone) : '暂未出现',
                'pass' => $sec > 0,
            ];
        }

        return $ret;
    }
}