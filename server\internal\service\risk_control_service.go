package service

import (
	"fmt"
	"log"
	"strconv"
	"strings"
	"sync"
	"time"
	"udp-server/server/internal/model"
	"udp-server/server/pkg/cache"

	"gorm.io/gorm"
)

// RiskControlService 风控服务
type RiskControlService struct {
	db               *gorm.DB
	cache            cache.Cache
	config           *RiskControlConfig
	configMutex      sync.RWMutex
	lastConfigUpdate time.Time
}

// RiskControlConfig 风控配置
type RiskControlConfig struct {
	// 同设备登录不同QQ号的阈值
	MaxQQPerDevicePerDay int `json:"max_qq_per_device_per_day"` // 默认5个

	// 同IP登录不同QQ号的阈值
	MaxQQPerIPPerDay int `json:"max_qq_per_ip_per_day"` // 默认10个

	// 短时间内频繁登录的阈值
	MaxLoginAttemptsPerHour int `json:"max_login_attempts_per_hour"` // 默认20次

	// 新设备登录检查
	EnableNewDeviceCheck bool `json:"enable_new_device_check"` // 默认true

	// 异地登录检查
	EnableLocationCheck bool `json:"enable_location_check"` // 默认false

	// 是否跳过问题设备码的风控检查
	SkipProblematicDevices bool `json:"skip_problematic_devices"` // 默认true

	// 问题设备码列表（逗号分隔）
	ProblematicDeviceIDs string `json:"problematic_device_ids"` // 默认包含已知问题设备码
}

// DefaultRiskControlConfig 默认风控配置
func DefaultRiskControlConfig() *RiskControlConfig {
	return &RiskControlConfig{
		MaxQQPerDevicePerDay:    3,  // 降低到3个，更严格的设备限制
		MaxQQPerIPPerDay:        5,  // 降低到5个，更严格的IP限制
		MaxLoginAttemptsPerHour: 20, // 降低到10次，防止频繁登录
		EnableNewDeviceCheck:    true,
		EnableLocationCheck:     false,
		SkipProblematicDevices:  true,                                                               // 默认跳过问题设备码
		ProblematicDeviceIDs:    "1ef745e604f98b9176d4b8d4f7f525cedc6c1f6e38adc1ed7c7cd35352106c9f", // 默认问题设备码
	}
}

// NewRiskControlService 创建风控服务实例
func NewRiskControlService(db *gorm.DB, cache cache.Cache) *RiskControlService {
	service := &RiskControlService{
		db:    db,
		cache: cache,
	}

	// 自动迁移风险事件表和配置表
	if err := db.AutoMigrate(&model.RiskEvent{}, &model.RiskControlConfig{}); err != nil {
		log.Printf("[ERROR] 风控服务表迁移失败: %v", err)
	}

	// 初始化配置
	if err := service.loadConfigFromDB(); err != nil {
		log.Printf("[ERROR] 加载风控配置失败，使用默认配置: %v", err)
		service.config = DefaultRiskControlConfig()
	}

	return service
}

// CheckLoginRisk 检查登录风险
func (s *RiskControlService) CheckLoginRisk(qqNumber int64, deviceID, ipAddress string) error {
	config := s.getConfig()

	// 1. 检查同设备登录不同QQ号
	if err := s.checkDeviceQQLimit(deviceID, qqNumber, config.MaxQQPerDevicePerDay); err != nil {
		return err
	}

	// 2. 检查同IP登录不同QQ号
	if err := s.checkIPQQLimit(ipAddress, qqNumber, config.MaxQQPerIPPerDay); err != nil {
		return err
	}

	// 3. 检查频繁登录
	if err := s.checkFrequentLogin(qqNumber, deviceID, ipAddress, config.MaxLoginAttemptsPerHour); err != nil {
		return err
	}

	return nil
}

// checkDeviceQQLimit 检查同设备登录不同QQ号的限制
func (s *RiskControlService) checkDeviceQQLimit(deviceID string, currentQQ int64, maxLimit int) error {
	// 检查是否为已知的问题设备码（设备信息收集失败导致的通用设备码）
	if s.isProblematicDeviceID(deviceID) {
		log.Printf("[WARN] 检测到问题设备码，跳过设备风控检查: DeviceID=%s, QQ=%d", deviceID, currentQQ)
		return nil // 跳过设备风控检查，但不阻止登录
	}

	// 获取今天的开始时间
	today := time.Now().Truncate(24 * time.Hour)
	todayUnix := today.Unix()

	// 查询今天该设备登录的所有不同QQ号
	var histories []model.DeviceHistory
	err := s.db.Where("device = ? AND login_time >= ?", deviceID, todayUnix).
		Select("DISTINCT uin").
		Find(&histories).Error

	if err != nil {
		log.Printf("[ERROR] 查询设备登录历史失败: %v", err)
		return nil // 查询失败不阻止登录
	}

	// 统计不同的QQ号
	qqSet := make(map[int64]bool)
	for _, history := range histories {
		qqSet[history.Uin] = true
	}

	// 如果当前QQ不在列表中，添加进去
	qqSet[currentQQ] = true

	uniqueQQCount := len(qqSet)

	log.Printf("[DEBUG] 设备 %s 今日登录QQ数量: %d/%d", deviceID, uniqueQQCount, maxLimit)

	// 如果超过限制，触发风控
	if uniqueQQCount > maxLimit {
		// 记录风险事件
		s.recordRiskEvent("device_qq_limit", deviceID, "", qqSet, uniqueQQCount, "high",
			fmt.Sprintf("设备 %s 今日登录QQ数量超限: %d/%d", deviceID, uniqueQQCount, maxLimit))

		// 将涉及的所有QQ号设置为审核状态
		s.setUsersToAuditStatus(qqSet, fmt.Sprintf("设备异常登录检测：设备 %s 今日登录QQ数量超限", deviceID))

		return fmt.Errorf("设备异常登录检测：该设备今日登录账号数量过多，相关账号已进入审核状态")
	}

	return nil
}

// isProblematicDeviceID 检查是否为已知的问题设备码
// 这些设备码是由于客户端设备信息收集失败而产生的通用设备码
func (s *RiskControlService) isProblematicDeviceID(deviceID string) bool {
	config := s.getConfig()

	// 如果配置中禁用了跳过问题设备码，则不跳过
	if !config.SkipProblematicDevices {
		return false
	}

	// 从配置中获取问题设备码列表
	if config.ProblematicDeviceIDs == "" {
		return false
	}

	// 解析逗号分隔的设备码列表
	problematicIDs := strings.Split(config.ProblematicDeviceIDs, ",")
	for _, problematicID := range problematicIDs {
		if strings.TrimSpace(problematicID) == deviceID {
			return true
		}
	}

	return false
}

// checkIPQQLimit 检查同IP登录不同QQ号的限制
func (s *RiskControlService) checkIPQQLimit(ipAddress string, currentQQ int64, maxLimit int) error {
	// 获取今天的开始时间
	today := time.Now().Truncate(24 * time.Hour)
	todayUnix := today.Unix()

	// 查询今天该IP登录的所有不同QQ号
	var histories []model.DeviceHistory
	err := s.db.Where("ip_address = ? AND login_time >= ?", ipAddress, todayUnix).
		Select("DISTINCT uin").
		Find(&histories).Error

	if err != nil {
		log.Printf("[ERROR] 查询IP登录历史失败: %v", err)
		return nil // 查询失败不阻止登录
	}

	// 统计不同的QQ号
	qqSet := make(map[int64]bool)
	for _, history := range histories {
		qqSet[history.Uin] = true
	}

	// 如果当前QQ不在列表中，添加进去
	qqSet[currentQQ] = true

	uniqueQQCount := len(qqSet)

	log.Printf("[DEBUG] IP %s 今日登录QQ数量: %d/%d", ipAddress, uniqueQQCount, maxLimit)

	// 如果超过限制，触发风控
	if uniqueQQCount > maxLimit {
		// 记录风险事件
		s.recordRiskEvent("ip_qq_limit", "", ipAddress, qqSet, uniqueQQCount, "medium",
			fmt.Sprintf("IP %s 今日登录QQ数量超限: %d/%d", ipAddress, uniqueQQCount, maxLimit))

		// 将涉及的所有QQ号设置为审核状态
		s.setUsersToAuditStatus(qqSet, fmt.Sprintf("IP异常登录检测：IP %s 今日登录QQ数量超限", ipAddress))

		return fmt.Errorf("IP异常登录检测：该IP今日登录账号数量过多，相关账号已进入审核状态")
	}

	return nil
}

// checkFrequentLogin 检查频繁登录
func (s *RiskControlService) checkFrequentLogin(qqNumber int64, deviceID, ipAddress string, maxLimit int) error {
	// 获取一小时前的时间
	oneHourAgo := time.Now().Add(-time.Hour).Unix()

	// 查询该QQ号在过去一小时内的登录次数
	var count int64
	err := s.db.Model(&model.DeviceHistory{}).
		Where("uin = ? AND login_time >= ?", qqNumber, oneHourAgo).
		Count(&count).Error

	if err != nil {
		log.Printf("[ERROR] 查询频繁登录失败: %v", err)
		return nil // 查询失败不阻止登录
	}

	log.Printf("[DEBUG] QQ %d 过去1小时登录次数: %d/%d", qqNumber, count, maxLimit)

	// 如果超过限制，触发风控
	if int(count) > maxLimit {
		// 记录风险事件
		qqSet := map[int64]bool{qqNumber: true}
		s.recordRiskEvent("frequent_login", deviceID, ipAddress, qqSet, int(count), "medium",
			fmt.Sprintf("QQ %d 频繁登录: %d次/小时", qqNumber, count))

		// 将该QQ号设置为审核状态
		s.setUsersToAuditStatus(qqSet, fmt.Sprintf("频繁登录检测：QQ %d 过去1小时登录次数过多", qqNumber))

		return fmt.Errorf("频繁登录检测：该账号登录过于频繁，已进入审核状态")
	}

	return nil
}

// recordRiskEvent 记录风险事件
func (s *RiskControlService) recordRiskEvent(eventType, deviceID, ipAddress string, qqSet map[int64]bool, count int, severity, description string) {
	// 将QQ号集合转换为逗号分隔的字符串
	var qqNumbers []string
	for qq := range qqSet {
		qqNumbers = append(qqNumbers, fmt.Sprintf("%d", qq))
	}
	qqNumbersStr := ""
	if len(qqNumbers) > 0 {
		qqNumbersStr = fmt.Sprintf("[%s]", fmt.Sprintf("%v", qqNumbers))
	}

	riskEvent := &model.RiskEvent{
		EventType:   eventType,
		DeviceID:    deviceID,
		IPAddress:   ipAddress,
		QQNumbers:   qqNumbersStr,
		Count:       count,
		Severity:    severity,
		Status:      "pending",
		Description: description,
	}

	// 记录到数据库（Go和PHP共享同一个数据库）
	if err := s.db.Create(riskEvent).Error; err != nil {
		log.Printf("[ERROR] 记录风险事件失败: %v", err)
	} else {
		log.Printf("[RISK] 风险事件已记录: %s - %s", eventType, description)
	}
}

// setUsersToAuditStatus 将用户设置为审核状态
func (s *RiskControlService) setUsersToAuditStatus(qqSet map[int64]bool, reason string) {
	for qq := range qqSet {
		var user model.User
		if err := s.db.Where("uin = ?", qq).First(&user).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				log.Printf("[WARN] 用户不存在，跳过状态更新: QQ=%d", qq)
				continue
			}
			log.Printf("[ERROR] 查询用户失败: QQ=%d, Error=%v", qq, err)
			continue
		}

		// 如果用户状态已经是审核状态或更严重的状态，跳过
		if user.Status >= 3 {
			log.Printf("[INFO] 用户状态已是审核或更严重状态，跳过: QQ=%d, Status=%d", qq, user.Status)
			continue
		}

		// 更新用户状态为审核状态
		user.Status = 3
		if err := s.db.Save(&user).Error; err != nil {
			log.Printf("[ERROR] 更新用户状态失败: QQ=%d, Error=%v", qq, err)
		} else {
			log.Printf("[RISK] 用户已设置为审核状态: QQ=%d, Reason=%s", qq, reason)

			// 记录用户日志
			userLog := &model.UserLog{
				UID:   user.UID,
				Type:  "risk_control",
				Extra: reason,
				Time:  time.Now().Format("2006-01-02 15:04:05"),
			}
			if err := s.db.Create(userLog).Error; err != nil {
				log.Printf("[ERROR] 记录用户日志失败: QQ=%d, Error=%v", qq, err)
			}
		}
	}
}

// GetRiskEvents 获取风险事件列表
func (s *RiskControlService) GetRiskEvents(limit, offset int, status string) ([]model.RiskEvent, int64, error) {
	var events []model.RiskEvent
	var total int64

	query := s.db.Model(&model.RiskEvent{})
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	if err := query.Order("created_at DESC").
		Limit(limit).Offset(offset).
		Find(&events).Error; err != nil {
		return nil, 0, err
	}

	return events, total, nil
}

// ProcessRiskEvent 处理风险事件
func (s *RiskControlService) ProcessRiskEvent(eventID uint, action string, adminNote string) error {
	var event model.RiskEvent
	if err := s.db.First(&event, eventID).Error; err != nil {
		return fmt.Errorf("风险事件不存在: %v", err)
	}

	switch action {
	case "approve":
		// 批准：恢复用户正常状态
		event.Status = "processed"
		if err := s.restoreUsersFromAudit(event.QQNumbers, "管理员批准"); err != nil {
			log.Printf("[ERROR] 恢复用户状态失败: %v", err)
		}
	case "reject":
		// 拒绝：保持审核状态或进一步处理
		event.Status = "processed"
	case "ignore":
		// 忽略：标记为已忽略
		event.Status = "ignored"
	default:
		return fmt.Errorf("无效的处理动作: %s", action)
	}

	// 更新事件状态
	if err := s.db.Save(&event).Error; err != nil {
		return fmt.Errorf("更新风险事件失败: %v", err)
	}

	log.Printf("[RISK] 风险事件已处理: ID=%d, Action=%s, Admin=%s", eventID, action, adminNote)
	return nil
}

// restoreUsersFromAudit 从审核状态恢复用户
func (s *RiskControlService) restoreUsersFromAudit(qqNumbersStr, reason string) error {
	// 解析QQ号字符串（格式：[123456,789012]）
	// 这里简化处理，实际应该用更严格的解析
	if qqNumbersStr == "" {
		return nil
	}

	// 简单的字符串解析（实际项目中应该用JSON解析）
	qqNumbersStr = qqNumbersStr[1 : len(qqNumbersStr)-1] // 去掉方括号
	if qqNumbersStr == "" {
		return nil
	}

	// 这里需要更完善的解析逻辑
	log.Printf("[INFO] 需要恢复的用户: %s, 原因: %s", qqNumbersStr, reason)

	return nil
}

// GetDailyStats 获取每日风控统计
func (s *RiskControlService) GetDailyStats() (map[string]interface{}, error) {
	today := time.Now().Truncate(24 * time.Hour)

	var stats struct {
		TotalEvents    int64 `json:"total_events"`
		PendingEvents  int64 `json:"pending_events"`
		HighSeverity   int64 `json:"high_severity"`
		MediumSeverity int64 `json:"medium_severity"`
		LowSeverity    int64 `json:"low_severity"`
	}

	// 今日总事件数
	s.db.Model(&model.RiskEvent{}).Where("created_at >= ?", today).Count(&stats.TotalEvents)

	// 待处理事件数
	s.db.Model(&model.RiskEvent{}).Where("created_at >= ? AND status = ?", today, "pending").Count(&stats.PendingEvents)

	// 按严重程度统计
	s.db.Model(&model.RiskEvent{}).Where("created_at >= ? AND severity = ?", today, "high").Count(&stats.HighSeverity)
	s.db.Model(&model.RiskEvent{}).Where("created_at >= ? AND severity = ?", today, "medium").Count(&stats.MediumSeverity)
	s.db.Model(&model.RiskEvent{}).Where("created_at >= ? AND severity = ?", today, "low").Count(&stats.LowSeverity)

	return map[string]interface{}{
		"date":            today.Format("2006-01-02"),
		"total_events":    stats.TotalEvents,
		"pending_events":  stats.PendingEvents,
		"high_severity":   stats.HighSeverity,
		"medium_severity": stats.MediumSeverity,
		"low_severity":    stats.LowSeverity,
	}, nil
}

// loadConfigFromDB 从数据库加载配置
func (s *RiskControlService) loadConfigFromDB() error {
	s.configMutex.Lock()
	defer s.configMutex.Unlock()

	var configs []model.RiskControlConfig
	if err := s.db.Find(&configs).Error; err != nil {
		return fmt.Errorf("查询配置失败: %v", err)
	}

	// 创建配置映射
	configMap := make(map[string]string)
	for _, config := range configs {
		configMap[config.ConfigKey] = config.ConfigValue
	}

	// 解析配置
	riskConfig := &RiskControlConfig{}

	if val, ok := configMap["max_qq_per_device_per_day"]; ok {
		if intVal, err := strconv.Atoi(val); err == nil {
			riskConfig.MaxQQPerDevicePerDay = intVal
		}
	}

	if val, ok := configMap["max_qq_per_ip_per_day"]; ok {
		if intVal, err := strconv.Atoi(val); err == nil {
			riskConfig.MaxQQPerIPPerDay = intVal
		}
	}

	if val, ok := configMap["max_login_attempts_per_hour"]; ok {
		if intVal, err := strconv.Atoi(val); err == nil {
			riskConfig.MaxLoginAttemptsPerHour = intVal
		}
	}

	if val, ok := configMap["enable_new_device_check"]; ok {
		riskConfig.EnableNewDeviceCheck = val == "true" || val == "1"
	}

	if val, ok := configMap["enable_location_check"]; ok {
		riskConfig.EnableLocationCheck = val == "true" || val == "1"
	}

	if val, ok := configMap["skip_problematic_devices"]; ok {
		riskConfig.SkipProblematicDevices = val == "true" || val == "1"
	}

	if val, ok := configMap["problematic_device_ids"]; ok {
		riskConfig.ProblematicDeviceIDs = val
	}

	// 设置默认值
	if riskConfig.MaxQQPerDevicePerDay == 0 {
		riskConfig.MaxQQPerDevicePerDay = 3
	}
	if riskConfig.MaxQQPerIPPerDay == 0 {
		riskConfig.MaxQQPerIPPerDay = 3
	}
	if riskConfig.MaxLoginAttemptsPerHour == 0 {
		riskConfig.MaxLoginAttemptsPerHour = 20
	}

	s.config = riskConfig
	s.lastConfigUpdate = time.Now()

	log.Printf("[INFO] 风控配置已从数据库加载: 设备限制=%d, IP限制=%d, 登录限制=%d",
		riskConfig.MaxQQPerDevicePerDay, riskConfig.MaxQQPerIPPerDay, riskConfig.MaxLoginAttemptsPerHour)

	return nil
}

// getConfig 获取当前配置（线程安全）
func (s *RiskControlService) getConfig() *RiskControlConfig {
	s.configMutex.RLock()
	defer s.configMutex.RUnlock()

	if s.config == nil {
		return DefaultRiskControlConfig()
	}

	return s.config
}

// UpdateConfig 更新配置（每小时调用）
func (s *RiskControlService) UpdateConfig() error {
	// 检查是否需要更新（避免频繁查询数据库）
	s.configMutex.RLock()
	lastUpdate := s.lastConfigUpdate
	s.configMutex.RUnlock()

	if time.Since(lastUpdate) < 30*time.Minute {
		return nil // 30分钟内不重复更新
	}

	log.Printf("[INFO] 开始更新风控配置...")
	return s.loadConfigFromDB()
}
