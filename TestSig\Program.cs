﻿using ConsoleApp1;
using Microsoft.Win32;
using System.Diagnostics;

Console.WriteLine("请输入要检查的目录路径:");
string? inputPath = Console.ReadLine();

if (string.IsNullOrWhiteSpace(inputPath))
{
    Console.WriteLine("输入的路径为空，程序退出。");
    return;
}

// 检查目录是否存在
if (!Directory.Exists(inputPath))
{
    Console.WriteLine($"目录不存在: {inputPath}");
    return;
}

// 检查libiconv2017_cl64.dll文件是否存在
string dllPath = Path.Combine(inputPath, "libiconv2017_cl64.dll");
var ClientPath = Path.Combine(inputPath, "BNSR.exe");
if (!File.Exists(dllPath))
{
    Console.WriteLine($"在目录 {inputPath} 中未找到 libiconv2017_cl64.dll 文件。");
    return;
}

FileVersionInfo versionInfo = FileVersionInfo.GetVersionInfo(dllPath);
using RegistryKey hklm = Registry.LocalMachine;
using RegistryKey directory = hklm.CreateSubKey($@"Software\Xylia\bns-plugins\directory", true);

// 生成HMAC-SHA256签名而不是MD5哈希
var secretKey = StringExtensions.GenerateSecretKey("0.1.2506.3");
var hmacSignature = ClientPath.HMACSign(secretKey);

if (string.IsNullOrEmpty(hmacSignature))
{
    Console.WriteLine("生成HMAC签名失败");
    return;
}

directory.SetValue(ClientPath, hmacSignature, RegistryValueKind.String);

Console.WriteLine($"成功生成HMAC-SHA256签名");
Console.WriteLine($"路径: {ClientPath}");
Console.WriteLine($"签名: {hmacSignature}");
