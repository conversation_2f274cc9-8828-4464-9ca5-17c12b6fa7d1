package model

import (
	"time"
)

// RiskEvent 风险事件模型
type RiskEvent struct {
	ID          uint      `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	EventType   string    `gorm:"column:event_type;size:50;index" json:"event_type"`   // 事件类型
	DeviceID    string    `gorm:"column:device_id;size:64;index" json:"device_id"`     // 设备ID
	IPAddress   string    `gorm:"column:ip_address;size:45;index" json:"ip_address"`   // IP地址
	QQNumbers   string    `gorm:"column:qq_numbers;size:1000" json:"qq_numbers"`       // 涉及的QQ号列表（逗号分隔）
	Count       int       `gorm:"column:count" json:"count"`                           // 触发次数
	Severity    string    `gorm:"column:severity;size:20" json:"severity"`             // 严重程度：low, medium, high
	Status      string    `gorm:"column:status;size:20;default:pending" json:"status"` // 处理状态：pending, processed, ignored
	Description string    `gorm:"column:description;size:500" json:"description"`      // 事件描述
	CreatedAt   time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`  // 创建时间
	UpdatedAt   time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`  // 更新时间
}

// TableName 指定表名
func (RiskEvent) TableName() string {
	return "risk_events"
}

// RiskControlConfig 风控配置模型
type RiskControlConfig struct {
	ID          uint64    `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	ConfigKey   string    `gorm:"column:config_key;size:100;uniqueIndex:uk_config_key;not null" json:"config_key"`
	ConfigValue string    `gorm:"column:config_value;size:500;not null" json:"config_value"`
	Description string    `gorm:"column:description;size:200" json:"description"`
	CreatedAt   time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt   time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
}

// TableName 指定表名
func (RiskControlConfig) TableName() string {
	return "risk_control_config"
}

// AdminLog 管理员操作日志模型
type AdminLog struct {
	ID            uint64    `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	AdminUID      int       `gorm:"column:admin_uid;not null" json:"admin_uid"`
	AdminUsername string    `gorm:"column:admin_username;size:50;not null" json:"admin_username"`
	Action        string    `gorm:"column:action;size:100;not null" json:"action"`
	Data          string    `gorm:"column:data;type:text" json:"data"`
	IP            string    `gorm:"column:ip;size:45;not null" json:"ip"`
	CreatedAt     time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
}

// TableName 指定表名
func (AdminLog) TableName() string {
	return "bns_useradminlog"
}
