﻿using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.Preview.Common.Extension;
using static Xylia.Preview.Data.Models.Effect;

namespace Xylia.BnsHelper.Models;
internal partial class EffectConfig(string alias) : ObservableObject
{
	#region Properties
	[ObservableProperty] string alias = alias;
	[ObservableProperty] UiSlotSeq _uiSlot;
	[ObservableProperty] UiCategorySeq _uiCategory;
    [ObservableProperty] bool _battleMessage;
	[ObservableProperty] long _name2;

	public override bool Equals(object? obj) => obj is EffectConfig other && string.Equals(Alias, other.Alias, StringComparison.OrdinalIgnoreCase);
	public override int GetHashCode() => Alias.GetHashCode(StringComparison.OrdinalIgnoreCase);
	#endregion

	#region ViewModel
	public static IEnumerable<UiSlotSeq> UiSlots => Enum.GetValues<UiSlotSeq>().Where(x => x < UiSlotSeq.COUNT);
	public static IEnumerable<UiCategorySeq> UiCategorys => Enum.GetValues<UiCategorySeq>().Where(x => x < UiCategorySeq.COUNT);

	internal string LastKey = alias;
	internal event EventHandler? Deleted;

	[RelayCommand]
    [property: Newtonsoft.Json.JsonIgnore]
	void Delete() => Deleted?.Invoke(this, EventArgs.Empty);

	internal void Save()
	{
		var value = $"{(int)UiSlot},{(int)UiCategory},{(BattleMessage ? 7 : 0)},{Name2}";
		SettingHelper.Default.SetValue(value, LastKey = Alias, EffectCollection.SECTION);
	}

	internal void Remove()
	{
		SettingHelper.Default.RemoveKey(LastKey, EffectCollection.SECTION);
	}
	#endregion
}

internal class EffectCollection : ObservableCollection<EffectConfig>
{
	public const string SECTION = "Effect";

	protected override void InsertItem(int index, EffectConfig item)
	{
		// invoke changed
		item.Save();
		item.PropertyChanged += (s, e) => SetItem(IndexOf(item), item);
		item.Deleted += (s, e) => RemoveItem(IndexOf(item));
		base.InsertItem(index, item);
	}

	protected override void SetItem(int index, EffectConfig item)
	{
		item.Remove();
		item.Save();
		base.SetItem(index, item);
	}

	protected override void RemoveItem(int index)
	{
		var item = this[index];
		item.Remove();
		base.RemoveItem(index);
	}

	protected override void ClearItems()
	{
		foreach (var item in this) item.Remove();
		base.ClearItems();
	}


	/// <summary>
	/// Load config from <see cref="SettingHelper"/>.
	/// </summary>
	/// <returns></returns>
	public static EffectCollection Load()
	{
		var collection = new EffectCollection();

		var keys = SettingHelper.Default[SECTION];
		if (keys.Count != 0)
		{
			foreach (var item in keys)
			{
				var data = item.Value.Split(',');

				collection.Add(new EffectConfig(item.KeyName)
				{
					UiSlot = data.ElementAtOrDefault(0).To<UiSlotSeq>(),
					UiCategory = data.ElementAtOrDefault(1).To<UiCategorySeq>(),
					BattleMessage = data.ElementAtOrDefault(2).To<byte>() > 0,
					Name2 = data.ElementAtOrDefault(3).To<int>(),
				});
			}
		}
		else
		{
			// 默认配置 会心一击+汲取
			collection.Add(new EffectConfig("CardCollection_New_Red_Effect3_.*"));
			collection.Add(new EffectConfig("CardCollection_New_Purple_Effect1_.*_attach"));
		}

		return collection;
	}
}
