# 管理员日志表部署说明

## 概述

为了解决风控页面中`admin_logs`表不存在的问题，已创建新的管理员日志表`bns_useradminlog`。

## 部署步骤

### 1. 执行SQL脚本

在MySQL数据库中执行以下SQL脚本来创建管理员日志表：

```bash
mysql -u username -p database_name < server/bns_useradmin_log.sql
```

或者直接在MySQL客户端中执行：

```sql
-- 管理员操作日志表
CREATE TABLE IF NOT EXISTS `bns_useradminlog` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `admin_uid` int(11) NOT NULL COMMENT '管理员UID',
  `admin_username` varchar(50) NOT NULL COMMENT '管理员用户名',
  `action` varchar(100) NOT NULL COMMENT '操作类型',
  `data` text COMMENT '操作数据(JSON格式)',
  `ip` varchar(45) NOT NULL COMMENT '操作IP地址',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_admin_uid` (`admin_uid`),
  KEY `idx_action` (`action`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员操作日志表';
```

### 2. 验证表创建

执行以下SQL确认表已正确创建：

```sql
DESCRIBE bns_useradminlog;
```

应该看到以下字段：
- `id` - 主键，自增
- `admin_uid` - 管理员UID
- `admin_username` - 管理员用户名
- `action` - 操作类型
- `data` - 操作数据(JSON格式)
- `ip` - 操作IP地址
- `created_at` - 创建时间

### 3. 重启服务

重启PHP和Go服务以使更改生效：

```bash
# 重启PHP服务（根据实际部署方式）
sudo systemctl restart php-fpm
sudo systemctl restart nginx

# 重启Go服务
sudo systemctl restart bnszs-server
```

## 功能说明

### 记录的操作类型

管理员日志表会记录以下操作：

1. **风控事件处理** (`process_risk_event`)
   - 解封用户
   - 封禁用户
   - 忽略事件

2. **批量用户处理** (`batch_process_users`)
   - 批量解封
   - 批量封禁

3. **风控配置更新** (`update_risk_config`)
   - 修改风控参数

4. **缓存清理** (`clear_signin_cache`)
   - 清理签到缓存

### 日志数据格式

每条日志记录包含：
- **admin_uid**: 操作管理员的UID
- **admin_username**: 操作管理员的用户名
- **action**: 操作类型标识
- **data**: 操作详细数据（JSON格式）
- **ip**: 操作来源IP地址
- **created_at**: 操作时间

### 示例日志记录

```json
{
  "admin_uid": 1,
  "admin_username": "admin",
  "action": "process_risk_event",
  "data": "{\"event_id\":123,\"action\":\"approve\",\"admin_note\":\"用户申诉通过\",\"event_type\":\"device_qq_limit\"}",
  "ip": "*************",
  "created_at": "2025-01-01 12:00:00"
}
```

## 故障排除

### 表创建失败
1. 检查数据库连接权限
2. 确认数据库用户有CREATE TABLE权限
3. 检查字符集和排序规则是否支持

### 日志记录失败
1. 检查表是否存在：`SHOW TABLES LIKE 'bns_useradminlog';`
2. 检查表结构是否正确：`DESCRIBE bns_useradminlog;`
3. 查看PHP错误日志：`tail -f /var/log/php/error.log`
4. 检查备用文件日志：`ls -la runtime/log/admin_risk_control.log`

### 权限问题
确保数据库用户对`bns_useradminlog`表有以下权限：
- SELECT
- INSERT
- UPDATE（如果需要）

## 维护建议

1. **定期清理**: 建议定期清理过期的日志记录
2. **索引优化**: 根据查询需求添加适当的索引
3. **备份**: 定期备份日志数据
4. **监控**: 监控日志表大小，避免过度增长

## 相关文件

- `server/bns_useradmin_log.sql` - 表创建脚本
- `server/tools.bnszs.com_tp6/app/manage/controller/manage/RiskControl.php` - PHP控制器
- `server/internal/model/risk.go` - Go模型定义
