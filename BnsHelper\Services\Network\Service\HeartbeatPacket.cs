using System.Windows;
using Xylia.Preview.Data.Engine;

namespace Xylia.BnsHelper.Services.Network.Service;
internal class HeartbeatPacket : BasePacket
{
    #region Response Fields
    /// <summary>
    /// 当前在线用户数量
    /// </summary>
    public uint OnlineUserCount { get; set; }
    #endregion

    #region Methods
    protected override void ReadResponse(DataArchive reader)
    {
        OnlineUserCount = reader.Read<uint>();
        
        // 更新应用程序属性中的用户数量
        Application.Current.Properties["UserCount"] = OnlineUserCount;
    }
    #endregion
}
