package main

import (
	"fmt"
	"log"
	"net"
	"os"
	"path/filepath"
	"strconv"
	"time"
	"udp-server/server/internal/database"
	"udp-server/server/internal/handler"
	"udp-server/server/internal/service"
	"udp-server/server/pkg/binary"
	"udp-server/server/pkg/cache"
	"udp-server/server/pkg/config"
	"udp-server/server/pkg/logger"
	"udp-server/server/pkg/metrics"

	"github.com/spf13/viper"
)

func main() {
	// 初始化配置
	initConfig()

	// 初始化数据库
	if err := database.InitDB(); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	// 创建 metrics 实例
	metricsInstance := metrics.NewMetrics()

	// 创建配置实例
	configInstance := config.NewConfig()
	if err := configInstance.Load(); err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化日志系统
	logConfig := &logger.Config{
		Level:    configInstance.GetString("log.level"),
		Console:  configInstance.GetBool("log.console"),
		File:     configInstance.GetBool("log.file"),
		FilePath: configInstance.GetString("log.file_path"),
	}
	if err := logger.Init(logConfig); err != nil {
		log.Fatalf("Failed to initialize logger: %v", err)
	}
	defer logger.Close()

	logger.Info("服务器启动中...")

	// 创建Redis缓存实例（强制使用Redis）
	redisHost := viper.GetString("redis.host")
	redisPort := viper.GetInt("redis.port")
	redisPassword := viper.GetString("redis.password")
	redisDB := viper.GetInt("redis.db")

	logger.Info("正在连接Redis缓存服务器 %s:%d...", redisHost, redisPort)

	// 强制使用Redis缓存，连接失败则退出
	cacheInstance, err := cache.NewRedisCache(redisHost, redisPort, redisPassword, redisDB)
	if err != nil {
		logger.Error("Redis缓存服务器连接失败: %v", err)
		logger.Error("请检查以下配置:")
		logger.Error("  - Redis服务器地址: %s:%d", redisHost, redisPort)
		logger.Error("  - Redis数据库: %d", redisDB)
		logger.Error("  - Redis服务是否正常运行")
		logger.Error("  - 网络连接是否正常")
		if redisPassword != "" {
			logger.Error("  - Redis密码是否正确")
		}
		logger.Error("服务器启动失败")
		os.Exit(1)
	}

	// 执行Redis功能测试
	logger.Info("正在测试Redis缓存功能...")
	testKey := fmt.Sprintf("server_startup_test_%d", time.Now().Unix())
	testValue := "startup_test_value"

	// 测试写入
	if err := cacheInstance.Set(testKey, testValue, 30*time.Second); err != nil {
		logger.Error("Redis缓存写入测试失败: %v", err)
		logger.Error("服务器启动失败，请检查Redis服务权限配置")
		os.Exit(1)
	}

	// 测试读取
	var readValue string
	if err := cacheInstance.Get(testKey, &readValue); err != nil {
		logger.Error("Redis缓存读取测试失败: %v", err)
		logger.Error("服务器启动失败，请检查Redis服务配置")
		os.Exit(1)
	}

	// 验证数据一致性
	if readValue != testValue {
		logger.Error("Redis缓存数据一致性测试失败: 期望 '%s', 实际 '%s'", testValue, readValue)
		logger.Error("服务器启动失败，请检查Redis服务配置")
		os.Exit(1)
	}

	// 测试删除
	if err := cacheInstance.Delete(testKey); err != nil {
		logger.Error("Redis缓存删除测试失败: %v", err)
		logger.Error("服务器启动失败，请检查Redis服务权限配置")
		os.Exit(1)
	}

	logger.Info("Redis缓存功能测试通过")
	logger.Info("成功连接到Redis缓存服务器 %s:%d (数据库: %d)", redisHost, redisPort, redisDB)

	// 创建服务实例
	authService := service.NewAuthService(
		database.GetDB(),
		cacheInstance,
		configInstance,
	)

	// 创建权限服务实例
	permissionService := service.NewPermissionService(database.GetDB(), cacheInstance)

	// 启动Redis健康检查
	go startRedisHealthCheck(cacheInstance)

	// 创建心跳配置
	heartbeatConfig := config.DefaultHeartbeatConfig()
	if err := heartbeatConfig.Validate(); err != nil {
		logger.Fatal("心跳配置验证失败: %v", err)
	}
	logger.Info("心跳配置: 客户端间隔=%v, 服务器超时=%v, 容错时间=%v, 清理间隔=%v",
		heartbeatConfig.ClientInterval,
		heartbeatConfig.ServerTimeout,
		heartbeatConfig.GetToleranceTime(),
		heartbeatConfig.CleanupInterval)

	// 创建心跳服务实例
	heartbeatService := service.NewHeartbeatService(
		heartbeatConfig.ServerTimeout,
		heartbeatConfig.CleanupInterval,
		metricsInstance,
		cacheInstance,
	)

	// 创建签到抽奖服务实例（使用Hash缓存）
	redisAddr := fmt.Sprintf("%s:%d", redisHost, redisPort)
	luckyService := service.NewLuckyService(cacheInstance, permissionService, redisAddr, redisPassword, redisDB)

	// 创建风控服务实例
	riskService := service.NewRiskControlService(database.GetDB(), cacheInstance)

	// 创建统计服务实例
	statsService := service.NewStatsService(database.GetDB(), cacheInstance, authService, heartbeatService, riskService)

	// 启动统计数据收集器和清理任务
	statsService.StartStatsCollector()
	statsService.StartStatsCleanup()

	// 创建处理器实例
	authHandler := handler.NewAuthHandler(authService, permissionService)
	heartbeatHandler := handler.NewHeartbeatHandler(authService, heartbeatService)
	luckyHandler := handler.NewLuckyHandler(luckyService, authService)

	// 创建UDP服务器
	addr := net.UDPAddr{
		Port: viper.GetInt("server.port"),
		IP:   net.ParseIP(viper.GetString("server.host")),
	}

	conn, err := net.ListenUDP("udp", &addr)
	if err != nil {
		logger.Fatal("Failed to start UDP server: %v", err)
	}
	defer conn.Close()

	logger.Info("UDP server started on %s:%d", viper.GetString("server.host"), viper.GetInt("server.port"))

	// 创建网络处理器
	networkHandler := binary.NewNetworkHandler()

	// 处理消息
	buffer := make([]byte, 65536) // 增大缓冲区以支持更大的消息
	for {
		n, remoteAddr, err := conn.ReadFromUDP(buffer)
		if err != nil {
			log.Printf("Error reading from UDP: %v", err)
			continue
		}

		// 验证是否为二进制协议
		if err := networkHandler.ValidateMessage(buffer[:n]); err != nil {
			log.Printf("Invalid message from %s: %v", remoteAddr.IP.String(), err)
			continue
		}

		// 处理二进制协议消息
		binaryMsg, parseErr := networkHandler.ProcessBinaryMessage(buffer[:n])
		if parseErr != nil {
			log.Printf("Error parsing binary message from %s: %v", remoteAddr.IP.String(), parseErr)
			continue
		}

		// 根据消息类型处理
		var responseData []byte
		var handleErr error

		switch binaryMsg.Header.MsgType {
		case binary.MsgTypeLogin:
			responseData, handleErr = handleLoginRequest(binaryMsg, authHandler, permissionService, riskService, remoteAddr)
		case binary.MsgTypeHeartbeat:
			responseData, handleErr = handleHeartbeatRequest(binaryMsg, heartbeatHandler)
		case binary.MsgTypeLogout:
			responseData, handleErr = handleLogoutRequest(binaryMsg, authHandler, remoteAddr)
		case binary.MsgTypeGetDeviceHistory:
			responseData, handleErr = handleGetDeviceHistoryRequest(binaryMsg, authHandler, remoteAddr)
		case binary.MsgTypeGetActiveDevices:
			responseData, handleErr = handleGetActiveDevicesRequest(binaryMsg, authHandler, remoteAddr)
		case binary.MsgTypeLuckyDraw:
			responseData, handleErr = handleLuckyDrawRequest(binaryMsg, luckyHandler, remoteAddr)
		case binary.MsgTypeLuckyStatus:
			responseData, handleErr = handleLuckyStatusRequest(binaryMsg, luckyHandler, remoteAddr)
		case binary.MsgTypeCDKeyActivate:
			responseData, handleErr = handleCDKeyActivateRequest(binaryMsg, luckyHandler, remoteAddr)
		default:
			logger.Warn("Unknown message type: 0x%02X from %s", binaryMsg.Header.MsgType, remoteAddr.IP.String())
			// 发送错误响应
			responseData, _ = networkHandler.CreateErrorResponse(binary.ErrorCodeInvalidFormat, "Unknown message type")
		}

		if handleErr != nil {
			logger.Error("Error handling message: %v", handleErr)
			// 发送错误响应
			responseData, _ = networkHandler.CreateErrorResponse(binary.ErrorCodeServerError, handleErr.Error())
		}

		if responseData != nil {
			_, err = conn.WriteToUDP(responseData, remoteAddr)
			if err != nil {
				logger.Error("Error sending response: %v", err)
			}
		}
	}
}

// handleLuckyDrawRequest 处理签到抽奖请求
func handleLuckyDrawRequest(binaryMsg *binary.Message, luckyHandler *handler.LuckyHandler, remoteAddr *net.UDPAddr) ([]byte, error) {
	// 解析签到抽奖请求
	luckyReq, err := binaryMsg.GetLuckyDrawRequest()
	if err != nil {
		return nil, fmt.Errorf("failed to parse lucky draw request: %w", err)
	}

	// 直接调用签到抽奖处理器
	return luckyHandler.HandleLuckyDrawDirect(luckyReq.Token, remoteAddr.IP.String(), "")
}

// handleLuckyStatusRequest 处理获取签到状态请求
func handleLuckyStatusRequest(binaryMsg *binary.Message, luckyHandler *handler.LuckyHandler, remoteAddr *net.UDPAddr) ([]byte, error) {
	// 解析签到状态请求
	statusReq, err := binaryMsg.GetLuckyStatusRequest()
	if err != nil {
		return nil, fmt.Errorf("failed to parse lucky status request: %w", err)
	}

	// 直接调用签到状态处理器
	return luckyHandler.HandleLuckyStatusDirect(statusReq.Token)
}

// handleLoginRequest 处理登录请求
func handleLoginRequest(binaryMsg *binary.Message, authHandler *handler.AuthHandler, permissionService *service.PermissionService, riskService *service.RiskControlService, remoteAddr *net.UDPAddr) ([]byte, error) {
	// 解析登录请求
	loginReq, err := binaryMsg.GetLoginRequest()
	if err != nil {
		return nil, fmt.Errorf("failed to parse login request: %w", err)
	}

	// 转换QQ号为int64进行风控检查
	qqNumberInt, err := strconv.ParseInt(loginReq.QQNumber, 10, 64)
	if err != nil {
		networkHandler := binary.NewNetworkHandler()
		return networkHandler.CreateErrorResponse(binary.ErrorCodeInvalidFormat, "无效的QQ号格式")
	}

	// 执行风控检查
	clientIP := remoteAddr.IP.String()
	if err := riskService.CheckLoginRisk(qqNumberInt, loginReq.DeviceFingerprint, clientIP); err != nil {
		logger.Warn("风控检查失败: QQ=%d, Device=%s, IP=%s, Error=%v",
			qqNumberInt, loginReq.DeviceFingerprint, clientIP, err)

		// 创建风控错误响应
		networkHandler := binary.NewNetworkHandler()
		return networkHandler.CreateErrorResponse(binary.ErrorCodeUnauthorized, err.Error())
	}

	// 风控检查通过，调用认证服务进行登录
	user, err := authHandler.LoginDirect(loginReq.QQNumber, loginReq.DeviceFingerprint, loginReq, clientIP)
	if err != nil {
		networkHandler := binary.NewNetworkHandler()
		return networkHandler.CreateErrorResponse(binary.ErrorCodeServerError, err.Error())
	}

	// 获取权限过期时间（使用统一的权限计算方法）
	permissionExpiration, err := permissionService.GetUserPermissionExpiration(user.UID, "client")
	if err != nil {
		log.Printf("[WARN] 获取用户权限过期时间失败: UID=%d, Error=%v", user.UID, err)
		permissionExpiration = 0 // 无权限
	}
	log.Printf("[DEBUG] 登录时获取权限过期时间: UID=%d, Expiration=%d", user.UID, permissionExpiration)

	// 创建成功响应数据（包含权限过期时间）
	responseData := map[string]interface{}{
		"uid":                   user.UID,
		"uin":                   user.Uin,
		"name":                  user.Name,
		"token":                 user.Token,
		"login_time":            user.LoginTime,
		"token_time":            user.TokenTime,
		"status":                user.Status,
		"email":                 user.Email,
		"beta":                  user.Beta,
		"permission":            user.Permission,      // 最终权限：服务端计算的结果
		"permission_expiration": permissionExpiration, // 权限过期时间：-1=永久，0=无权限，>0=具体时间戳
	}

	networkHandler := binary.NewNetworkHandler()
	return networkHandler.CreateSuccessResponse(binary.MsgTypeLoginResponse, responseData)
}

// handleHeartbeatRequest 处理心跳请求
func handleHeartbeatRequest(binaryMsg *binary.Message, heartbeatHandler *handler.HeartbeatHandler) ([]byte, error) {
	// 解析心跳请求
	heartbeatReq, err := binaryMsg.GetHeartbeatRequest()
	if err != nil {
		return nil, fmt.Errorf("failed to parse heartbeat request: %w", err)
	}

	// 直接调用心跳处理器
	_, onlineCount, err := heartbeatHandler.HandleHeartbeatDirect(heartbeatReq.Token)
	if err != nil {
		// 创建错误响应
		networkHandler := binary.NewNetworkHandler()
		return networkHandler.CreateErrorResponse(binary.ErrorCodeServerError, err.Error())
	}

	// 创建包含在线用户数量的心跳响应
	networkHandler := binary.NewNetworkHandler()
	return networkHandler.CreateHeartbeatResponse(onlineCount)
}

// handleLogoutRequest 处理登出请求
func handleLogoutRequest(binaryMsg *binary.Message, authHandler *handler.AuthHandler, remoteAddr *net.UDPAddr) ([]byte, error) {
	// 解析注销请求
	logoutReq, err := binaryMsg.GetLogoutRequest()
	if err != nil {
		networkHandler := binary.NewNetworkHandler()
		return networkHandler.CreateLogoutResponse(false, binary.ErrorCodeServerError, fmt.Sprintf("failed to parse logout request: %v", err))
	}

	logger.Debug("收到注销请求: Token=%s, 客户端IP=%s", logoutReq.Token, remoteAddr.IP.String())

	// 调用认证服务进行注销
	err = authHandler.LogoutDirect(logoutReq.Token)
	if err != nil {
		logger.Error("注销失败: Token=%s, 错误=%v", logoutReq.Token, err)
		networkHandler := binary.NewNetworkHandler()
		return networkHandler.CreateLogoutResponse(false, binary.ErrorCodeServerError, fmt.Sprintf("logout failed: %v", err))
	}

	logger.Info("注销成功: Token=%s, 客户端IP=%s", logoutReq.Token, remoteAddr.IP.String())

	// 创建成功响应
	networkHandler := binary.NewNetworkHandler()
	return networkHandler.CreateLogoutResponse(true, 0, "")
}

// handleGetDeviceHistoryRequest 处理获取设备历史请求 - 暂时不支持
func handleGetDeviceHistoryRequest(binaryMsg *binary.Message, authHandler *handler.AuthHandler, remoteAddr *net.UDPAddr) ([]byte, error) {
	networkHandler := binary.NewNetworkHandler()
	return networkHandler.CreateErrorResponse(binary.ErrorCodeServerError, "GetDeviceHistory not implemented in direct value protocol")
}

// handleGetActiveDevicesRequest 处理获取活跃设备请求 - 暂时不支持
func handleGetActiveDevicesRequest(binaryMsg *binary.Message, authHandler *handler.AuthHandler, remoteAddr *net.UDPAddr) ([]byte, error) {
	networkHandler := binary.NewNetworkHandler()
	return networkHandler.CreateErrorResponse(binary.ErrorCodeServerError, "GetActiveDevices not implemented in direct value protocol")
}

// initConfig 初始化配置
func initConfig() {
	// 设置配置文件路径
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("config")
	viper.AddConfigPath(".")

	// 读取环境变量
	viper.AutomaticEnv()

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			// 配置文件不存在，创建默认配置
			createDefaultConfig()
		} else {
			log.Fatalf("Error reading config file: %v", err)
		}
	}
}

// createDefaultConfig 创建默认配置文件
func createDefaultConfig() {
	// 确保配置目录存在
	if err := os.MkdirAll("config", 0755); err != nil {
		log.Fatalf("Error creating config directory: %v", err)
	}

	// 创建默认配置
	viper.SetDefault("database.driver", "mysql")
	viper.SetDefault("database.host", "localhost")
	viper.SetDefault("database.port", 3306)
	viper.SetDefault("database.username", "root")
	viper.SetDefault("database.password", "root")
	viper.SetDefault("database.dbname", "bns")
	viper.SetDefault("database.charset", "utf8mb4")
	viper.SetDefault("database.parseTime", true)
	viper.SetDefault("database.loc", "Local")
	viper.SetDefault("database.maxIdleConns", 10)
	viper.SetDefault("database.maxOpenConns", 100)
	viper.SetDefault("database.connMaxLifetime", 3600)

	viper.SetDefault("server.host", "0.0.0.0")
	viper.SetDefault("server.port", 8081)

	// Redis 配置
	viper.SetDefault("redis.host", "localhost")
	viper.SetDefault("redis.port", 6379)
	viper.SetDefault("redis.password", "")
	viper.SetDefault("redis.db", 0)

	// 保存配置文件
	if err := viper.WriteConfigAs(filepath.Join("config", "config.yaml")); err != nil {
		log.Fatalf("Error writing config file: %v", err)
	}
}

// handleCDKeyActivateRequest 处理CDKEY激活请求
func handleCDKeyActivateRequest(binaryMsg *binary.Message, luckyHandler *handler.LuckyHandler, remoteAddr *net.UDPAddr) ([]byte, error) {
	// 解析CDKEY激活请求
	cdkeyReq, err := binaryMsg.GetCDKeyActivateRequest()
	if err != nil {
		return nil, fmt.Errorf("failed to parse cdkey activate request: %w", err)
	}

	// 调用CDKEY激活处理器
	return luckyHandler.HandleCDKeyActivate(cdkeyReq.Token, cdkeyReq.CDKey, cdkeyReq.IsVerified, remoteAddr.IP.String())
}

// startRedisHealthCheck 启动Redis健康检查
func startRedisHealthCheck(cacheInstance cache.Cache) {
	ticker := time.NewTicker(30 * time.Second) // 每30秒检查一次
	defer ticker.Stop()

	log.Printf("[INFO] Redis健康检查已启动，检查间隔: 30秒")

	for {
		select {
		case <-ticker.C:
			// 执行Redis健康检查
			healthCheckKey := fmt.Sprintf("health_check_%d", time.Now().Unix())
			healthCheckValue := "ok"

			// 测试写入
			if err := cacheInstance.Set(healthCheckKey, healthCheckValue, 10*time.Second); err != nil {
				logger.Error("Redis健康检查失败 - 写入测试: %v", err)
				log.Printf("[ERROR] Redis健康检查失败 - 写入测试: %v", err)
				continue
			}

			// 测试读取
			var readValue string
			if err := cacheInstance.Get(healthCheckKey, &readValue); err != nil {
				logger.Error("Redis健康检查失败 - 读取测试: %v", err)
				log.Printf("[ERROR] Redis健康检查失败 - 读取测试: %v", err)
				continue
			}

			// 验证数据一致性
			if readValue != healthCheckValue {
				logger.Error("Redis健康检查失败 - 数据不一致: 期望 '%s', 实际 '%s'", healthCheckValue, readValue)
				log.Printf("[ERROR] Redis健康检查失败 - 数据不一致: 期望 '%s', 实际 '%s'", healthCheckValue, readValue)
				continue
			}

			// 清理测试数据
			if err := cacheInstance.Delete(healthCheckKey); err != nil {
				logger.Warn("Redis健康检查 - 清理测试数据失败: %v", err)
				log.Printf("[WARN] Redis健康检查 - 清理测试数据失败: %v", err)
			}

			// 健康检查通过（只在第一次或错误恢复后记录）
			// log.Printf("[DEBUG] Redis健康检查通过")
		}
	}
}
