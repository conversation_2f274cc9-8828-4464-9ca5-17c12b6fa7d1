var areaSelect;
var serverSelect;
var areaTarget;
var serverTarget; 

function initServer(a,s,at,st) {
	areaSelect = a;
	serverSelect = s;   
	areaTarget = at;
	serverTarget = st;
	
	getArea();
	bind();
}

function getArea() {
    $.ajax({
        dataType:'script',
        scriptCharset:'gb2312',
      	url:'//gameact.qq.com/comm-htdocs/js/game_area/bns_server_select.js',
  		success:function() {
	  		server = eval("BNSServerSelect");
	  			
      		if(server.STD_DATA[0].opt_data_array==undefined)
      		{
		       	$('.cc-select-role dd').eq(0).hide();
		        $("#serverSelect1 option").remove();
		      	$("#areaSelect1 option").remove();
		        return;
	  		}
	  			
		   	$('.cc-select-role dd').eq(0).show();
			$("#"+ areaSelect + " option").remove();
			$("#"+ areaSelect).append('<option value="">请选择大区</option>');
			    	  
			var serverList = new Array();
			    	  
			//按大区显示（不重复）
		    for (var i=0;i< server.STD_DATA.length;i++) {
		      	var STD = server.STD_DATA[i];

				// 判断服务器是否在数组里
		        var firstServer = STD.opt_data_array[0].v;
		        if (serverList.indexOf(firstServer) == -1){
		         	$("#"+ areaSelect).append('<option value="'+ STD.v +'">'+ STD.t+'</option>');

					for (var h=0; h< STD.opt_data_array.length; h++) {
						var v = STD.opt_data_array[h].v;
						serverList.push(v);

						// 如果未传递大区id时，匹配服务器id
						if (areaTarget == null && serverTarget != null) {
							if(v == serverTarget) areaTarget = STD.v;
						}
					}
		        }
	     	}

        	$("#"+ areaSelect).find("option").each(function(){
                if($(this).val()==areaTarget || $(this).text()==areaTarget){
                    $("#"+ areaSelect).val($(this).val()).trigger('change');
                    return;
            }});
  	}});
}

function bind() {
    $("#" + areaSelect).off();
    $("#" + areaSelect).bind("change",function() {
        var index = $("#" + areaSelect).val();
        if (index==9211) var area =[{t: "义薄云天",v: "9211",status:"1", display:"1", opt_data_array:[]}];
        else if(index==9111) var area =[{t: "特邀嘉宾",v: "9111",status:"1", display:"1", opt_data_array:[]}];
        else {
        	for(var i=0;i<server.STD_DATA.length;i++){
		   	    var STD = server.STD_DATA[i];
		        if (STD.v == index){
		    	    var area = STD.opt_data_array;
		        	break;
		        }
		    }    
        }
    
        $("#"+serverSelect+" option").remove();
        $("#"+serverSelect).append('<option value="">请选择服务器</option>');
        
        if(area == undefined){
        	return;
        }
        
        for(var i=0;i<area.length;i++){
            $("#"+serverSelect).append('<option value="'+area[i].v+'">'+area[i].t+'</option>');
        }

        $("#"+serverSelect).find("option").each(function(){
            if($(this).val() == serverTarget || $(this).text() == serverTarget) {
                $("#"+serverSelect).val($(this).val()).trigger('change');
                return;
            }
        });
    })  
}