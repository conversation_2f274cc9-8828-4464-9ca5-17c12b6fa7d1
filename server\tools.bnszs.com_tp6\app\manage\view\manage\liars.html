{layout name="manage/template" /}

<div class="admin-content">
	<div class="admin-content-body">
		<div class="am-cf am-padding">
			<div class="am-fl am-cf"><strong class="am-text-primary am-text-lg">首页</strong> / <small>骗子列表</small></div>
		</div>
		<hr>
		<div class="am-g">
			<div class="am-u-sm-12 am-u-md-3">
				<div class="am-input-group am-input-group-sm">
					<input type="text" class="am-form-field" id="search_value" placeholder="输入需要查询的信息">
					<span class="am-input-group-btn">
						<button class="am-btn am-btn-default" type="button" id="search" onclick="search();">查询</button>
					</span>
					<!-- <span class="am-input-group-btn">
            <button class="am-btn am-btn-default" type="button" id="search" onclick="">新增</button>
          </span> -->
				</div>
			</div>
		</div>
		<div class="am-g">
			<div class="am-u-sm-12">
				{$data|raw}
				<table class="am-table am-table-bd am-table-striped admin-content-table">
					<thead>
						<tr>
							<th>ID</th>
							<th>大区</th>
							<th>服务器</th>
							<th>游戏昵称</th>
							<th>证据</th>
							{if session("admin")}
							<th>操作</th>
							{/if}
						</tr>
					</thead>
					<tbody>
						{volist name='data' id='item'}
						<tr data-t="{$item.id}" data-s="{$item.area}" data-c="{$item.rolename}"></tr>
							<td>{$item.id}</td>
							<td><a title="点击可搜索同大区的数据" href="?search={$item.area}">{$item.area}</a></td>
							<td><a title="点击可搜索同服务器的数据" href="?search={$item.area}">{$item.area}</a></td>
							<td><a title="点击可查看装备详情" target="_blank" href="/ingame/bs/character/profile?s={$item.area}&c={$item.rolename}">{$item.rolename}</a></td>
							<td><a title="点击可查看证据" target="_blank" href="{$item.url}">查看证据</a></td>
							{if session("admin")}
						    <td style="width:5%;text-align: center;">
                                <select class="action option">
                                    <option select>请选择操作</option>                             
                                    <option value="edit">编辑</option>
							        <option value="delete">删除</option>
                                </select>
                            </td>
							{/if}
						</tr>
						{/volist}
              		</tbody>
				</table>
			</div>
		</div>
  </div>
</div>

<script>
	//回车事件绑定
	$('#search_value').bind('keypress', function(event) {
	  if (event.keyCode == "13") {            
		event.preventDefault(); 
		//回车执行查询
		search();
	  }
	});
	
	function search(){
		var search_value = $("#search_value").val();
		if (search_value == null || search_value == undefined || search_value == '') {
		  alert("请输入查询条件 如服务器:无日峰 或角色名称 以及任意你知道的信息");return false;
	  }else{
		  window.location.href = "?search=" + search_value;
	  }
		
	}
	
	$(".action").change(function(){
	  var action = $(this).val();
	  var id = $(this).data("id");
	  
	  $(this).find("option").first().attr("selected", true);
		
		if(action=='Delete') {		
			if(!confirm("即将删除骗子数据，是否确认？")) return;
			
			$.ajax({
				type: "POST",
				dataType: "json",
				url: window.location.href,
				data: "mode=del&id=" + id,
				success: function (result) {
					alert(result.msg);
					if(result.code == 0) location.reload();
				},
				error : function() {
					alert("删除失败,未知错误");
				}
		  });
		}
		
		else if(action=='Edit') window.location.href = 'post.php?id=' + id;
	});
	
	$("tbody tr").each(function() {
	  $.ajax({
	      type: "GET",
	      url: '//tools.bnszs.com/api/roleIs302',
	      data: { s: $(this).data('s'), c: $(this).data('c'), t: $(this).data('t') },
	      dataType: "jsonp", 
	      success: function (result) {
	          console.log(111);
	      }
	  });    
	});
</script>