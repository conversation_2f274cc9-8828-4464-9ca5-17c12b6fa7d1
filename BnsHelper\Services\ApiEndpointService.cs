﻿using RestSharp;
using RestSharp.Authenticators.OAuth2;
using System.Net;
using Xylia.BnsHelper.Services.ApiEndpoints;

namespace Xylia.BnsHelper.Services;
internal class ApiEndpointService
{
	public static BnszsApiEndpoint BnszsApi { get; } = new(new(new RestClientOptions
	{
		Proxy = new WebProxy(),
		Timeout = TimeSpan.FromSeconds(5),
		UserAgent = "bnszs",
	}, configureSerialization: s => s.UseSerializer<JsonNetSerializer>()));

	public static GithubEndpoint GithubApi { get; } = new(new(new RestClientOptions
	{
		Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator("*********************************************************************************************", "Bearer"),
		Proxy = new WebProxy(),
		Timeout = TimeSpan.FromSeconds(5),
		UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0",
	}, configureSerialization: s => s.UseSerializer<JsonNetSerializer>()));

	public static YoudaoApiEndpoint YoudaoApi { get; } = new(new(new RestClientOptions
	{
		Proxy = new WebProxy(),
		Timeout = TimeSpan.FromSeconds(5),
		UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0",
	}, configureSerialization: s => s.UseSerializer<JsonNetSerializer>()));
}
