package binary

import (
	"fmt"
)

// ProtocolType 协议类型
type ProtocolType int

const (
	ProtocolTypeUnknown ProtocolType = iota
	ProtocolTypeBinary               // 二进制协议
)

// String 返回协议类型的字符串表示
func (pt ProtocolType) String() string {
	switch pt {
	case ProtocolTypeBinary:
		return "Binary"
	default:
		return "Unknown"
	}
}

// ProtocolDetector 协议检测器
type ProtocolDetector struct{}

// NewProtocolDetector 创建协议检测器
func NewProtocolDetector() *ProtocolDetector {
	return &ProtocolDetector{}
}

// DetectProtocol 检测数据包的协议类型
func (pd *ProtocolDetector) DetectProtocol(data []byte) ProtocolType {
	if len(data) == 0 {
		return ProtocolTypeUnknown
	}

	// 检查是否为二进制协议
	if data[0] == ProtocolMagic {
		// 进一步验证二进制协议格式
		if pd.isBinaryProtocol(data) {
			return ProtocolTypeBinary
		}
	}

	return ProtocolTypeUnknown
}

// isBinaryProtocol 验证是否为有效的二进制协议
func (pd *ProtocolDetector) isBinaryProtocol(data []byte) bool {
	if len(data) < HeaderSize {
		return false
	}

	// 检查魔数
	if data[0] != ProtocolMagic {
		return false
	}

	// 检查版本
	if data[1] != ProtocolVersion {
		return false
	}

	// 检查消息类型是否有效
	msgType := data[2]
	if _, exists := MsgTypeNames[msgType]; !exists {
		return false
	}

	// 检查消息长度是否合理
	if len(data) >= 8 {
		// 简单检查长度字段（大端序）
		length := uint32(data[4])<<24 | uint32(data[5])<<16 | uint32(data[6])<<8 | uint32(data[7])
		if length < HeaderSize || length > MaxMessageSize {
			return false
		}

		// 如果数据足够长，检查长度是否匹配
		if len(data) >= int(length) && len(data) > int(length) {
			// 数据长度超过消息长度，可能不是有效的二进制协议
			return false
		}
	}

	return true
}

// ValidateProtocolData 验证协议数据的完整性
func (pd *ProtocolDetector) ValidateProtocolData(data []byte, protocolType ProtocolType) error {
	switch protocolType {
	case ProtocolTypeBinary:
		return pd.validateBinaryData(data)
	default:
		return fmt.Errorf("unsupported protocol type: %s", protocolType)
	}
}

// validateBinaryData 验证二进制协议数据
func (pd *ProtocolDetector) validateBinaryData(data []byte) error {
	if len(data) < HeaderSize {
		return fmt.Errorf("binary data too short: need at least %d bytes, got %d", HeaderSize, len(data))
	}

	// 解码并验证消息头
	header, err := DecodeHeader(data)
	if err != nil {
		return fmt.Errorf("invalid binary header: %w", err)
	}

	// 验证数据长度
	if len(data) < int(header.Length) {
		return fmt.Errorf("incomplete binary message: need %d bytes, got %d", header.Length, len(data))
	}

	return nil
}

// GetProtocolInfo 获取协议信息
func (pd *ProtocolDetector) GetProtocolInfo(data []byte) (ProtocolType, map[string]interface{}, error) {
	protocolType := pd.DetectProtocol(data)
	info := make(map[string]interface{})

	switch protocolType {
	case ProtocolTypeBinary:
		if len(data) >= HeaderSize {
			header, err := DecodeHeader(data)
			if err == nil {
				info["magic"] = fmt.Sprintf("0x%02X", header.Magic)
				info["version"] = fmt.Sprintf("0x%02X", header.Version)
				info["message_type"] = fmt.Sprintf("0x%02X", header.MsgType)
				info["message_type_name"] = MsgTypeNames[header.MsgType]
				info["flags"] = fmt.Sprintf("0x%02X", header.Flags)
				info["length"] = header.Length
				info["timestamp"] = header.Timestamp
			}
		}
	case ProtocolTypeUnknown:
		if len(data) > 0 {
			info["first_byte"] = fmt.Sprintf("0x%02X", data[0])
			info["length"] = len(data)
		}
	}

	return protocolType, info, nil
}
