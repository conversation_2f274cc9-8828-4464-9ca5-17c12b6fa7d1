{layout name="manage/template" /} 
<div class="admin-content">
    <div class="admin-content-body">
        <div class="am-cf am-padding am-padding-bottom-0">
            <div class="am-fl am-cf">
                <strong class="am-text-primary am-text-lg">签到管理</strong> / <small>Sign-in Management</small>
            </div>
        </div>
        <hr>
        
        <!-- 缓存统计 -->
        <div class="am-panel am-panel-default">
            <div class="am-panel-hd">缓存统计信息</div>
            <div class="am-panel-bd">
                <div class="am-g" id="cache-stats">
                    <div class="am-u-sm-12 am-u-md-3">
                        <div class="stats-card">
                            <div class="stats-number" id="activity-rewards-count">-</div>
                            <div class="stats-label">活动奖励缓存</div>
                        </div>
                    </div>
                    <div class="am-u-sm-12 am-u-md-3">
                        <div class="stats-card">
                            <div class="stats-number" id="user-draw-count">-</div>
                            <div class="stats-label">用户签到缓存</div>
                        </div>
                    </div>
                    <div class="am-u-sm-12 am-u-md-3">
                        <div class="stats-card">
                            <div class="stats-number" id="device-signin-count">-</div>
                            <div class="stats-label">设备签到缓存</div>
                        </div>
                    </div>
                    <div class="am-u-sm-12 am-u-md-3">
                        <div class="stats-card">
                            <div class="stats-number" id="signin-unavailable-count">-</div>
                            <div class="stats-label">签到不可用缓存</div>
                        </div>
                    </div>
                </div>
                <div class="am-margin-top">
                    <button type="button" class="am-btn am-btn-primary" onclick="refreshCacheStats()">
                        <i class="am-icon-refresh"></i> 刷新统计
                    </button>
                    <button type="button" class="am-btn am-btn-secondary" onclick="testRedisConnection()">
                        <i class="am-icon-cog"></i> 测试Redis连接
                    </button>
                </div>
            </div>
        </div>

        <!-- 缓存管理 -->
        <div class="am-panel am-panel-default">
            <div class="am-panel-hd">缓存管理操作</div>
            <div class="am-panel-bd">
                <form class="am-form am-form-horizontal" id="cache-clear-form">
                    <div class="am-form-group">
                        <label for="cache-type" class="am-u-sm-3 am-form-label">缓存类型</label>
                        <div class="am-u-sm-9">
                            <select id="cache-type" name="cache_type" class="am-form-field">
                                <option value="all">全部签到相关缓存</option>
                                <option value="activity_rewards">活动奖励缓存</option>
                                <option value="user_draw">用户签到数据缓存</option>
                                <option value="device_signin">设备签到状态缓存</option>
                                <option value="signin_unavailable">签到不可用状态缓存</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="am-form-group">
                        <label for="activity-id" class="am-u-sm-3 am-form-label">活动ID</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="activity-id" name="activity_id" class="am-form-field" 
                                   placeholder="可选，指定活动ID清理特定活动的缓存">
                            <small class="am-text-xs">留空则清理所有活动的相关缓存</small>
                        </div>
                    </div>
                    
                    <div class="am-form-group">
                        <div class="am-u-sm-9 am-u-sm-push-3">
                            <button type="button" class="am-btn am-btn-danger" onclick="clearCache()">
                                <i class="am-icon-trash"></i> 清除缓存
                            </button>
                            <button type="button" class="am-btn am-btn-warning" onclick="clearAllCache()">
                                <i class="am-icon-warning"></i> 清除所有签到缓存
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- 操作说明 -->
        <div class="am-panel am-panel-default">
            <div class="am-panel-hd">操作说明</div>
            <div class="am-panel-bd">
                <ul class="am-list">
                    <li><strong>活动奖励缓存：</strong>缓存活动的奖励配置信息，清除后会重新从数据库加载</li>
                    <li><strong>用户签到数据缓存：</strong>缓存用户的签到记录和状态，清除后会重新计算</li>
                    <li><strong>设备签到状态缓存：</strong>缓存设备的签到状态，防止同一设备重复签到</li>
                    <li><strong>签到不可用状态缓存：</strong>缓存用户签到失败的状态，避免频繁重试</li>
                </ul>
                <div class="am-alert am-alert-warning">
                    <strong>注意：</strong>清除缓存可能会导致短时间内服务器负载增加，请谨慎操作。
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.stats-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
    margin-bottom: 15px;
}

.stats-number {
    font-size: 2em;
    font-weight: bold;
    color: #0e90d2;
    margin-bottom: 5px;
}

.stats-label {
    color: #666;
    font-size: 0.9em;
}

.status-pending {
    color: #f37b1d;
}

.severity-high {
    color: #dd514c;
}
</style>

<script>
// 页面加载时获取缓存统计
$(document).ready(function() {
    refreshCacheStats();
});

// 刷新缓存统计
function refreshCacheStats() {
    $.ajax({
        url: '/manage/admin/risk/getCacheStats',
        type: 'POST',
        dataType: 'json',
        success: function(response) {
            if (response.code === 1) {
                $('#activity-rewards-count').text(response.data.activity_rewards);
                $('#user-draw-count').text(response.data.user_draw);
                $('#device-signin-count').text(response.data.device_signin);
                $('#signin-unavailable-count').text(response.data.signin_unavailable);
            } else {
                layer.msg('获取缓存统计失败: ' + response.msg, {icon: 2});
            }
        },
        error: function() {
            layer.msg('网络错误，获取缓存统计失败', {icon: 2});
        }
    });
}

// 测试Redis连接
function testRedisConnection() {
    $.ajax({
        url: '/manage/admin/risk/testRedis',
        type: 'POST',
        dataType: 'json',
        success: function(response) {
            if (response.code === 1) {
                layer.msg(response.msg, {icon: 1});
            } else {
                layer.msg('Redis连接测试失败: ' + response.msg, {icon: 2});
            }
        },
        error: function() {
            layer.msg('网络错误，无法测试Redis连接', {icon: 2});
        }
    });
}

// 清除缓存
function clearCache() {
    var cacheType = $('#cache-type').val();
    var activityId = $('#activity-id').val();
    
    var confirmMsg = '确定要清除 ';
    switch(cacheType) {
        case 'all':
            confirmMsg += '所有签到相关缓存';
            break;
        case 'activity_rewards':
            confirmMsg += '活动奖励缓存';
            break;
        case 'user_draw':
            confirmMsg += '用户签到数据缓存';
            break;
        case 'device_signin':
            confirmMsg += '设备签到状态缓存';
            break;
        case 'signin_unavailable':
            confirmMsg += '签到不可用状态缓存';
            break;
    }
    
    if (activityId) {
        confirmMsg += '（活动ID: ' + activityId + '）';
    }
    confirmMsg += ' 吗？';
    
    layer.confirm(confirmMsg, {icon: 3, title: '确认清除缓存'}, function(index) {
        $.ajax({
            url: '/manage/admin/risk/clearSigninCache',
            type: 'POST',
            data: {
                cache_type: cacheType,
                activity_id: activityId
            },
            dataType: 'json',
            success: function(response) {
                if (response.code === 1) {
                    layer.msg(response.msg, {icon: 1});
                    refreshCacheStats(); // 刷新统计
                } else {
                    layer.msg('清除失败: ' + response.msg, {icon: 2});
                }
            },
            error: function() {
                layer.msg('网络错误，清除缓存失败', {icon: 2});
            }
        });
        layer.close(index);
    });
}

// 清除所有缓存
function clearAllCache() {
    layer.confirm('确定要清除所有签到相关缓存吗？这将影响所有用户的签到状态！', {icon: 3, title: '危险操作确认'}, function(index) {
        $.ajax({
            url: '/manage/admin/risk/clearSigninCache',
            type: 'POST',
            data: {
                cache_type: 'all'
            },
            dataType: 'json',
            success: function(response) {
                if (response.code === 1) {
                    layer.msg(response.msg, {icon: 1});
                    refreshCacheStats(); // 刷新统计
                } else {
                    layer.msg('清除失败: ' + response.msg, {icon: 2});
                }
            },
            error: function() {
                layer.msg('网络错误，清除缓存失败', {icon: 2});
            }
        });
        layer.close(index);
    });
}
</script>
