package binary

import (
	"fmt"
)

// XOR加密密钥
var encryptionKey = []byte{
	92, 62, 100, 99, 255, 94, 254, 99, 33, 8,
	246, 154, 15, 194, 179, 148, 252, 85, 170, 16,
}

// Encryptor XOR加密器
type Encryptor struct {
	key []byte
}

// NewEncryptor 创建新的加密器
func NewEncryptor() *Encryptor {
	// 复制密钥以避免外部修改
	key := make([]byte, len(encryptionKey))
	copy(key, encryptionKey)

	return &Encryptor{
		key: key,
	}
}

// NewEncryptorWithKey 使用自定义密钥创建加密器
func NewEncryptorWithKey(key []byte) (*Encryptor, error) {
	if len(key) == 0 {
		return nil, fmt.Errorf("encryption key cannot be empty")
	}

	// 复制密钥
	keyCopy := make([]byte, len(key))
	copy(keyCopy, key)

	return &Encryptor{
		key: keyCopy,
	}, nil
}

// Encrypt 加密数据
func (e *Encryptor) Encrypt(data []byte) []byte {
	if len(data) == 0 {
		return []byte{}
	}

	encrypted := make([]byte, len(data))
	keyLen := len(e.key)

	for i, b := range data {
		encrypted[i] = b ^ e.key[i%keyLen]
	}

	return encrypted
}

// Decrypt 解密数据（XOR加密是对称的）
func (e *Encryptor) Decrypt(data []byte) []byte {
	// XOR加密是对称的，解密和加密使用相同的操作
	return e.Encrypt(data)
}

// EncryptMessage 加密消息体（不包括消息头）
func (e *Encryptor) EncryptMessage(msg *Message) (*Message, error) {
	if msg == nil {
		return nil, fmt.Errorf("message cannot be nil")
	}

	// 如果已经加密，直接返回
	if msg.Header.HasFlag(FlagEncrypted) {
		return msg, nil
	}

	// 克隆消息以避免修改原始消息
	encryptedMsg := msg.Clone()

	// 加密消息体
	if len(encryptedMsg.Body) > 0 {
		encryptedMsg.Body = e.Encrypt(encryptedMsg.Body)
	}

	// 设置加密标志
	encryptedMsg.Header.SetFlag(FlagEncrypted)

	return encryptedMsg, nil
}

// DecryptMessage 解密消息体
func (e *Encryptor) DecryptMessage(msg *Message) (*Message, error) {
	if msg == nil {
		return nil, fmt.Errorf("message cannot be nil")
	}

	// 检查是否设置了加密标志
	if !msg.Header.HasFlag(FlagEncrypted) {
		return msg, nil // 消息未加密，直接返回
	}

	// 克隆消息
	decryptedMsg := msg.Clone()

	// 解密消息体
	if len(decryptedMsg.Body) > 0 {
		decryptedMsg.Body = e.Decrypt(decryptedMsg.Body)
	}

	// 清除加密标志
	decryptedMsg.Header.ClearFlag(FlagEncrypted)

	return decryptedMsg, nil
}

// EncryptData 加密原始数据
func (e *Encryptor) EncryptData(data []byte) []byte {
	return e.Encrypt(data)
}

// DecryptData 解密原始数据
func (e *Encryptor) DecryptData(data []byte) []byte {
	return e.Decrypt(data)
}

// GetKeyLength 获取密钥长度
func (e *Encryptor) GetKeyLength() int {
	return len(e.key)
}

// ValidateKey 验证密钥有效性
func (e *Encryptor) ValidateKey() error {
	if len(e.key) == 0 {
		return fmt.Errorf("encryption key is empty")
	}

	if len(e.key) > 256 {
		return fmt.Errorf("encryption key too long: %d bytes (max: 256)", len(e.key))
	}

	return nil
}

// GetKeyInfo 获取密钥信息
func (e *Encryptor) GetKeyInfo() map[string]interface{} {
	info := make(map[string]interface{})
	info["length"] = len(e.key)
	info["algorithm"] = "XOR"

	// 计算密钥的简单校验和
	checksum := uint32(0)
	for _, b := range e.key {
		checksum += uint32(b)
	}
	info["checksum"] = checksum

	return info
}

// TestEncryption 测试加密/解密功能
func (e *Encryptor) TestEncryption(testData []byte) error {
	if len(testData) == 0 {
		testData = []byte("Hello, World! 测试数据 123456")
	}

	// 加密
	encrypted := e.Encrypt(testData)

	// 解密
	decrypted := e.Decrypt(encrypted)

	// 验证结果
	if len(testData) != len(decrypted) {
		return fmt.Errorf("length mismatch: original %d, decrypted %d", len(testData), len(decrypted))
	}

	for i, b := range testData {
		if b != decrypted[i] {
			return fmt.Errorf("data mismatch at position %d: expected 0x%02X, got 0x%02X", i, b, decrypted[i])
		}
	}

	return nil
}

// 全局加密器实例
var defaultEncryptor = NewEncryptor()

// GetDefaultEncryptor 获取默认加密器
func GetDefaultEncryptor() *Encryptor {
	return defaultEncryptor
}

// EncryptMessageData 使用默认加密器加密消息数据
func EncryptMessageData(data []byte) []byte {
	return defaultEncryptor.EncryptData(data)
}

// DecryptMessageData 使用默认加密器解密消息数据
func DecryptMessageData(data []byte) []byte {
	return defaultEncryptor.DecryptData(data)
}

// EncryptMessageBody 加密消息体（跳过消息头）
func EncryptMessageBody(data []byte) ([]byte, error) {
	if len(data) < HeaderSize {
		return nil, fmt.Errorf("data too short for message header: %d bytes", len(data))
	}

	// 复制数据
	result := make([]byte, len(data))
	copy(result, data)

	// 只加密消息体部分（跳过消息头）
	if len(data) > HeaderSize {
		bodyData := result[HeaderSize:]
		encryptedBody := defaultEncryptor.Encrypt(bodyData)
		copy(result[HeaderSize:], encryptedBody)

		// 设置加密标志位
		result[3] |= FlagEncrypted
	}

	return result, nil
}

// DecryptMessageBody 解密消息体（跳过消息头）
func DecryptMessageBody(data []byte) ([]byte, error) {
	if len(data) < HeaderSize {
		return nil, fmt.Errorf("data too short for message header: %d bytes", len(data))
	}

	// 检查加密标志位
	if (data[3] & FlagEncrypted) == 0 {
		return data, nil // 未加密，直接返回
	}

	// 复制数据
	result := make([]byte, len(data))
	copy(result, data)

	// 只解密消息体部分（跳过消息头）
	if len(data) > HeaderSize {
		bodyData := result[HeaderSize:]
		decryptedBody := defaultEncryptor.Decrypt(bodyData)
		copy(result[HeaderSize:], decryptedBody)

		// 清除加密标志位
		result[3] &= ^uint8(FlagEncrypted)
	}

	return result, nil
}

// IsEncrypted 检查数据是否已加密
func IsEncrypted(data []byte) bool {
	if len(data) < HeaderSize {
		return false
	}

	return (data[3] & FlagEncrypted) != 0
}
