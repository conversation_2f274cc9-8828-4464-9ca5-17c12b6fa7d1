package model

import (
	"time"
)

// User 用户模型
type User struct {
	UID        uint64 `gorm:"column:uid;primaryKey" json:"uid"`              // 站点账号
	Uin        uint64 `gorm:"column:uin" json:"uin"`                         // 用户绑定的qq号
	Name       string `gorm:"column:name;size:255" json:"name"`              // 用户名
	Status     int    `gorm:"column:status;default:0" json:"status"`         // 用户状态
	Email      string `gorm:"column:email;size:255" json:"email"`            // 邮箱
	Password   string `gorm:"column:password;size:255" json:"-"`             // 密码
	Token      string `gorm:"column:token;size:255" json:"token"`            // 令牌
	TokenTime  int64  `gorm:"column:token_time" json:"token_time"`           // Token有效期（Unix时间戳）
	LoginTime  int64  `gorm:"column:login_time" json:"login_time"`           // 登录时间（Unix时间戳）
	Beta       bool   `gorm:"column:beta;default:0" json:"beta"`             // 是否测试用户
	Permission uint8  `gorm:"column:permission;default:0" json:"permission"` // 用户权限：0-普通用户，1-高级用户，2-会员用户（优化为1字节）
	Modify     int    `gorm:"column:modify" json:"modify"`                   // 修改时间
}

// TableName 指定表名
func (User) TableName() string {
	return "user"
}

// GetTokenTime 获取Token有效期
func (u *User) GetTokenTime() time.Time {
	return time.Unix(u.TokenTime, 0)
}

// SetTokenTime 设置Token有效期
func (u *User) SetTokenTime(t time.Time) {
	u.TokenTime = t.Unix()
}

// GetLoginTime 获取登录时间
func (u *User) GetLoginTime() time.Time {
	return time.Unix(u.LoginTime, 0)
}

// SetLoginTime 设置登录时间
func (u *User) SetLoginTime(t time.Time) {
	u.LoginTime = t.Unix()
}

// UserLog 用户日志模型
type UserLog struct {
	ID    uint   `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	UID   uint64 `gorm:"column:uid;index" json:"uid"`           // 用户ID
	Type  string `gorm:"column:type;size:50;index" json:"type"` // 日志类型
	Extra string `gorm:"column:extra;size:255" json:"extra"`    // 额外信息
	Time  string `gorm:"column:time;size:50" json:"time"`       // 时间
}

// TableName 指定表名
func (UserLog) TableName() string {
	return "user_log"
}
