package handler

import (
	"log"
	"strconv"
	"udp-server/server/internal/model"
	"udp-server/server/internal/protocol"
	"udp-server/server/internal/service"
	"udp-server/server/pkg/binary"
)

// AuthHandler 处理认证相关的请求
type AuthHandler struct {
	authService       *service.AuthService
	permissionService *service.PermissionService
}

// NewAuthHandler 创建新的认证处理器
func NewAuthHandler(authService *service.AuthService, permissionService *service.PermissionService) *AuthHandler {
	return &AuthHandler{
		authService:       authService,
		permissionService: permissionService,
	}
}

// LoginDirect 直接处理登录请求（用于二进制协议）
func (h *AuthHandler) LoginDirect(qqNumber, deviceFingerprint string, loginReq *binary.LoginRequest, clientIP string) (*model.User, error) {
	log.Printf("[DEBUG] 收到登录请求，客户端IP: %s", clientIP)
	log.Printf("[DEBUG] 登录请求详情: QQ号=%s, 设备指纹=%s",
		loginReq.QQNumber,
		loginReq.DeviceFingerprint,
	)

	// 转换QQ号为int64
	qqNumberInt, err := strconv.ParseInt(qqNumber, 10, 64)
	if err != nil {
		log.Printf("[ERROR] QQ号格式错误: %s", qqNumber)
		return nil, protocol.NewError(protocol.ErrorCodeInvalidQQ, "无效的QQ号格式")
	}

	// 调用认证服务
	user, err := h.authService.Login(qqNumberInt, deviceFingerprint, loginReq, clientIP)
	if err != nil {
		log.Printf("[ERROR] 登录失败: QQ号=%s, 错误=%v", qqNumber, err)
		return nil, err
	}

	log.Printf("[INFO] 登录成功: QQ号=%s, Token=%s", qqNumber, user.Token)
	return user, nil
}

// LogoutDirect 直接处理注销请求（用于二进制协议）
func (h *AuthHandler) LogoutDirect(token string) error {
	// 调用认证服务进行注销
	err := h.authService.Logout(token)
	if err != nil {
		return err
	}

	return nil
}

// GetActivityInfoForUser 获取特定用户的活动信息
func (h *AuthHandler) GetActivityInfoForUser(userPermission uint8) map[string]interface{} {
	if h.permissionService != nil {
		return h.permissionService.GetActivityInfoForUser(userPermission)
	}

	// 如果权限服务不可用，返回默认信息
	return map[string]interface{}{
		"is_active":   false,
		"title":       "战斗统计功能",
		"description": "权限服务不可用",
		"message":     "无法获取活动信息",
	}
}
