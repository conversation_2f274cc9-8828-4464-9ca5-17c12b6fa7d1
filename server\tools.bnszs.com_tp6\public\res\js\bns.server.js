var areaSelect;     //选择大区
var serverSelect;   //选择服务器
var areaTarget;     //目标大区
var serverTarget;   //目标服务器



function initServer(a,s,at,st)
{
	areaSelect = a;
	serverSelect = s;   
	areaTarget = at;
	serverTarget = st;
	
	getArea();
	
	//绑定事件
  	Bind();
}



function getArea()
{
    $.ajax({
        dataType:'script',
        scriptCharset:'gb2312',
      	url:'//gameact.qq.com/comm-htdocs/js/game_area/bns_server_select.js',
  		success:function()
  		{
	  		server = eval("BNSServerSelect");
	  			
      		if(server.STD_DATA[0].opt_data_array==undefined)
      		{
		       	$('.cc-select-role dd').eq(0).hide();
		        $("#serverSelect1 option").remove();
		      	$("#areaSelect1 option").remove();
		        return;
	  		}
	  			
		   	$('.cc-select-role dd').eq(0).show();
		   	//$("#serverSelect1 option").remove();
			$("#"+ areaSelect + " option").remove();
			$("#"+ areaSelect).append('<option value="">请选择大区</option>');
			    	  
			var serverList = new Array();
			    	  
			//按大区显示（不重复）
		    for(var i=0;i< server.STD_DATA.length;i++)
		    {
		      	var STD = server.STD_DATA[i];
		        var firstServer = STD.opt_data_array[0].v;
		         	
		       if (serverList.indexOf(firstServer) == -1){
		         	serverList.push(firstServer);
		         	$("#"+ areaSelect).append('<option value="'+ firstServer +'">'+ STD.t+'</option>');
		       }
		         	
		         	
		        //判断所属大区
		        for(var h=0;h<STD.opt_data_array.length;h++){
		         	if(areaTarget==STD.opt_data_array[h].v){
		         	 	 $("#"+ areaSelect).val(firstServer).trigger('change');
		         	 	 break;
		         	}
		        }
	     	}

			$("#"+ areaSelect).append('<option value="9211">南天国实验室</option>');
		    $("#"+ areaSelect).append('<option value="9111">波拉国研究室</option>');
		        	
        	$("#"+ areaSelect).find("option").each(function(){
                if($(this).val()==areaTarget || $(this).text()==areaTarget){
                    $("#"+ areaSelect).val($(this).val()).trigger('change');
                    return;
            }});
  	}});
}


function Bind()
{
    $("#" + areaSelect).off();
    $("#" + areaSelect).bind("change",function()
    {
        var index = $("#" + areaSelect).val();

        if(index==9211) var area =[{t: "义薄云天",v: "9211",status:"1", display:"1", opt_data_array:[]}];
        else if(index==9111) var area =[{t: "特邀嘉宾",v: "9111",status:"1", display:"1", opt_data_array:[]}];
        else {
        	for(var i=0;i<server.STD_DATA.length;i++){
		   	    var STD = server.STD_DATA[i];
		   	    
		        if(STD.opt_data_array[0].v == index){
		    	    var area = STD.opt_data_array;
		        	break;
		        }
		    }    
        }
    
	
        $("#"+serverSelect+" option").remove();
        $("#"+serverSelect).append('<option value="">请选择服务器</option>');
        
        
        if(area == undefined){
        	return;
        }
        
        for(var i=0;i<area.length;i++){
            $("#"+serverSelect).append('<option value="'+area[i].v+'">'+area[i].t+'</option>');
        }
        
    
        $("#"+serverSelect).find("option").each(function(){
            if($(this).text() == serverTarget){
                $("#"+serverSelect).val($(this).val()).trigger('change');
                return;
            }
        });
    })  
    
    
}
