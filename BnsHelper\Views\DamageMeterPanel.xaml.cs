﻿using HandyControl.Data;
using System.ComponentModel;
using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Input;
using System.Windows.Interop;
using System.Windows.Media;
using Vanara.PInvoke;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.BnsHelper.Models;
using Xylia.BnsHelper.Resources;
using Xylia.BnsHelper.ViewModels;
using static Xylia.BnsHelper.ViewModels.DamageMeterViewModel;

namespace Xylia.BnsHelper.Views;
public partial class DamageMeterPanel
{
    #region Constructor
    readonly DamageMeterViewModel _viewModel;

    private DamageMeterPanel()
    {
        _instance = this;
        DataContext = _viewModel = new DamageMeterViewModel();

        InitializeComponent();
        Loaded += OnLoaded;
        DamageMeterViewModel.OnRefresh += OnRefresh;

        // 检查并显示活动提醒
        CheckAndShowActivityNotice();
    }
    #endregion

    #region Properties	 
    static DamageMeterPanel? _instance;
    internal static DamageMeterPanel Instance
    {
        get
        {
            // 检查插件版本
            MainWindowViewModel.CheckPlugin(new Version(0, 1, 2506, 1));

            // 检查权限：需要登录且有高级权限
            var user = MainWindowViewModel.Instance.User;
            if (user == null || !user.IsLoggedIn || user.Permission < 1) throw new UnauthorizedAccessException("战斗统计功能需要登录并拥有高级权限");

            return _instance ??= new DamageMeterPanel();
        }
    }
    #endregion

    #region Methods
    private static nint _hwnd;
    private static User32.SafeHHOOK? _hookId;
    private static DamageMeterPanel? _currentInstance;
    private bool _isTransparent = false;
    private bool _lastHitTestVisible = true;
    private static double _cachedDpi = 1.0;
    private static DateTime _lastDpiUpdate = DateTime.MinValue;
    private bool _isDisposed = false;

    // 保持Hook委托的强引用，防止被垃圾回收
    private static readonly User32.HookProc? _hookDelegate;

    protected override void OnSourceInitialized(EventArgs e)
    {
        base.OnSourceInitialized(e);
        _hwnd = new WindowInteropHelper(this).EnsureHandle();
        _currentInstance = this;

        // 应用初始的OBS capture设置
        UpdateCaptureSettings();

        var source = HwndSource.FromHwnd(_hwnd);
        source.AddHook(WndProc);

        // 监听IsHitTestVisible属性变化
        _viewModel.PropertyChanged += OnViewModelPropertyChanged;

        // 监听AllowCapture设置变化
        SettingHelper.Default.PropertyChanged += OnSettingChanged;
    }

    protected override void OnClosed(EventArgs e)
    {
        _isDisposed = true;
        DamageMeterViewModel.OnRefresh -= OnRefresh;
        _viewModel.PropertyChanged -= OnViewModelPropertyChanged;
        SettingHelper.Default.PropertyChanged -= OnSettingChanged;
        UninstallHook();
        _currentInstance = null;
        _viewModel.Dispose();
        _instance = null;
        base.OnClosed(e);
    }

    private nint WndProc(nint hwnd, int msg, nint wParam, nint lParam, ref bool handled)
    {
        switch ((User32.WindowMessage)msg)
        {
            case User32.WindowMessage.WM_WINDOWPOSCHANGING when !_viewModel.IsHitTestVisible:
                var wp = Marshal.PtrToStructure<User32.WINDOWPOS>(lParam);
                wp.flags |= User32.SetWindowPosFlags.SWP_NOMOVE;
                Marshal.StructureToPtr(wp, lParam, false);
                handled = true;
                break;

            case User32.WindowMessage.WM_STYLECHANGING when wParam == (long)User32.WindowLongFlags.GWL_EXSTYLE:
                var style = Marshal.PtrToStructure<User32.STYLESTRUCT>(lParam);
                style.styleNew |= (uint)User32.WindowStylesEx.WS_EX_LAYERED;
                Marshal.StructureToPtr(style, lParam, false);
                handled = true;
                break;
        }

        return nint.Zero;
    }

    private static nint Hook(int nCode, nint wParam, nint lParam)
    {
        try
        {
            // 检查窗口实例是否存在
            if (_currentInstance == null || _currentInstance._isDisposed || _hookId == null)
            {
                return User32.CallNextHookEx(_hookId, nCode, wParam, lParam);
            }

            // Hook只在锁定状态下运行，所以不需要检查IsHitTestVisible
            if (nCode >= 0)
            {
                // 只在鼠标移动时检查位置，减少性能开销
                if (wParam == (nint)User32.WindowMessage.WM_MOUSEMOVE)
                {
                    // check position
                    var info = Marshal.PtrToStructure<User32.MSLLHOOKSTRUCT>(lParam);

                    // 使用缓存的DPI值，每秒最多更新一次
                    var now = DateTime.Now;
                    if ((now - _lastDpiUpdate).TotalSeconds > 1.0)
                    {
                        _lastDpiUpdate = now;
                        // 异步更新DPI，避免阻塞Hook线程
                        try
                        {
                            var instance = _currentInstance;
                            if (instance != null && !instance._isDisposed)
                            {
                                var dispatcher = instance.Dispatcher;
                                if (dispatcher != null)
                                {
                                    dispatcher.BeginInvoke(() =>
                                    {
                                        try
                                        {
                                            if (instance != null && !instance._isDisposed)
                                            {
                                                _cachedDpi = WpfScreenHelper.Screen.FromWindow(instance).ScaleFactor;
                                            }
                                        }
                                        catch
                                        {
                                            _cachedDpi = 1.0;
                                        }
                                    });
                                }
                            }
                        }
                        catch
                        {
                            _cachedDpi = 1.0;
                        }
                    }

                    // 异步处理HitTest，避免阻塞Hook线程
                    try
                    {
                        var instance = _currentInstance;
                        if (instance != null && !instance._isDisposed)
                        {
                            var dispatcher = instance.Dispatcher;
                            if (dispatcher != null)
                            {
                                dispatcher.BeginInvoke(() =>
                                {
                                    try
                                    {
                                        if (instance._isDisposed) return;

                                        var point = new Point(info.pt.X / _cachedDpi - instance.Left, info.pt.Y / _cachedDpi - instance.Top);

                                        // 检查鼠标是否在窗口范围内
                                        if (point.X >= 0 && point.Y >= 0 && point.X <= instance.ActualWidth && point.Y <= instance.ActualHeight)
                                        {
                                            var result = VisualTreeHelper.HitTest(instance, point)?.VisualHit as FrameworkElement;
                                            bool shouldBeTransparent = result?.Tag is null;
                                            instance.SetWindowTransparency(shouldBeTransparent);
                                        }
                                        else
                                        {
                                            // 鼠标在窗口外，设置为透明
                                            instance.SetWindowTransparency(true);
                                        }
                                    }
                                    catch
                                    {
                                        // 如果HitTest失败，设置为透明
                                        if (!instance._isDisposed)
                                        {
                                            instance.SetWindowTransparency(true);
                                        }
                                    }
                                });
                            }
                        }
                    }
                    catch
                    {
                        // 如果Dispatcher调用失败，设置为透明
                        _currentInstance?.SetWindowTransparency(true);
                    }
                }
            }
        }
        catch
        {
            // 如果Hook处理出现异常，确保不会崩溃
            // 设置为透明状态作为安全回退
            try
            {
                var instance = _currentInstance;
                if (instance != null && !instance._isDisposed)
                {
                    instance.SetWindowTransparency(true);
                }
            }
            catch
            {
                // 忽略设置透明状态的异常，防止级联崩溃
            }
        }

        // 安全地调用下一个Hook
        try
        {
            return User32.CallNextHookEx(_hookId, nCode, wParam, lParam);
        }
        catch
        {
            // 如果CallNextHookEx失败，返回0
            return nint.Zero;
        }
    }

    private void SetWindowTransparency(bool transparent)
    {
        // 检查窗口是否已经被销毁
        if (_isDisposed || _hwnd == nint.Zero)
        {
            return;
        }

        // 只在状态改变时修改窗口样式
        if (transparent != _isTransparent)
        {
            _isTransparent = transparent;

            try
            {
                if (_isTransparent)
                {
                    var style = User32.GetWindowLongPtr(_hwnd, User32.WindowLongFlags.GWL_EXSTYLE) | (nint)User32.WindowStylesEx.WS_EX_TRANSPARENT;
                    User32.SetWindowLong(_hwnd, User32.WindowLongFlags.GWL_EXSTYLE, style);
                }
                else
                {
                    var style = User32.GetWindowLongPtr(_hwnd, User32.WindowLongFlags.GWL_EXSTYLE) & ~(nint)User32.WindowStylesEx.WS_EX_TRANSPARENT;
                    User32.SetWindowLong(_hwnd, User32.WindowLongFlags.GWL_EXSTYLE, style);
                }
            }
            catch
            {
                // 如果设置窗口样式失败，记录但不抛出异常
                System.Diagnostics.Debug.WriteLine("Failed to set window transparency");
            }
        }
    }

    private static void InstallHook()
    {
        // 如果Hook已经安装，先卸载
        if (_hookId != null && !_hookId.IsInvalid)
        {
            UninstallHook();
        }

        try
        {
            var moduleHandle = Kernel32.GetModuleHandle();

            _hookId = User32.SetWindowsHookEx(User32.HookType.WH_MOUSE_LL, Hook, moduleHandle, 0);
            if (_hookId == nint.Zero) throw new Win32Exception(Marshal.GetLastWin32Error());

            System.Diagnostics.Debug.WriteLine("Mouse hook installed successfully");
        }
        catch (Exception ex)
        {
            // 如果Hook安装失败，记录错误但不阻止程序运行
            System.Diagnostics.Debug.WriteLine($"Failed to install mouse hook: {ex.Message}");
        }
    }

    private static void UninstallHook()
    {
        if (_hookId != null && !_hookId.IsInvalid)
        {
            try
            {
                _hookId.Close();
                _hookId = null;
                System.Diagnostics.Debug.WriteLine("Mouse hook uninstalled successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to uninstall mouse hook: {ex.Message}");
            }
        }
    }

    private void OnViewModelPropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        if (_isDisposed) return;

        if (e.PropertyName == nameof(DamageMeterViewModel.IsHitTestVisible))
        {
            // 当IsHitTestVisible改变时，立即更新窗口透明状态和Hook状态
            if (_viewModel.IsHitTestVisible != _lastHitTestVisible)
            {
                _lastHitTestVisible = _viewModel.IsHitTestVisible;

                try
                {
                    if (!_viewModel.IsHitTestVisible)
                    {
                        // 锁定状态：安装Hook并设置为透明
                        InstallHook();
                        SetWindowTransparency(true);
                    }
                    else
                    {
                        // 解锁状态：卸载Hook并移除透明
                        UninstallHook();
                        SetWindowTransparency(false);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error in OnViewModelPropertyChanged: {ex.Message}");
                }
            }
        }
    }

    private void OnSettingChanged(object? sender, PropertyChangedEventArgs e)
    {
        if (_isDisposed) return;

        if (e.PropertyName == nameof(SettingHelper.AllowCapture))
        {
            // 当AllowCapture设置改变时，更新窗口样式
            UpdateCaptureSettings();
        }
    }

    private void UpdateCaptureSettings()
    {
        if (_hwnd == nint.Zero) return;

        try
        {
            var currentExStyle = User32.GetWindowLong(_hwnd, User32.WindowLongFlags.GWL_EXSTYLE);

            if (!SettingHelper.Default.AllowCapture)
            {
                // 不允许捕获：添加WS_EX_TOOLWINDOW标志，隐藏任务栏图标
                var newExStyle = currentExStyle | (nint)User32.WindowStylesEx.WS_EX_TOOLWINDOW;
                User32.SetWindowLong(_hwnd, User32.WindowLongFlags.GWL_EXSTYLE, newExStyle);
            }
            else
            {
                // 允许捕获：移除WS_EX_TOOLWINDOW标志，显示任务栏图标
                var newExStyle = currentExStyle & ~(nint)User32.WindowStylesEx.WS_EX_TOOLWINDOW;
                User32.SetWindowLong(_hwnd, User32.WindowLongFlags.GWL_EXSTYLE, newExStyle);
            }

            System.Diagnostics.Debug.WriteLine($"Updated capture settings: AllowCapture={SettingHelper.Default.AllowCapture}");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Failed to update capture settings: {ex.Message}");
        }
    }


    private void OnLoaded(object? sender, EventArgs e)
    {
        Init:
        try
        {
            _viewModel.Initialize();
        }
        catch (Exception ex)
        {
#if DEBUG
            _viewModel.Parse("测试被Player7的测试命中，受到X了7点伤害。");
            _viewModel.Parse("测试被Player3的测试命中，受到了3点伤害。");
            _viewModel.Parse("测试被Player4的测试命中，受到了4点伤害。");
            _viewModel.Parse("测试被Player5的测试命中，受到了5点伤害。");
            _viewModel.Parse("测试被Player6的测试命中，受到了6点伤害。");
            _viewModel.Parse("喵～命中绿色木偶，造成了9点伤害。");
#endif

            if (MessageBox.Show(ex.Message, StringHelper.Get("ApplicationName"), MessageBoxButton.OKCancel, MessageBoxImage.Error) == MessageBoxResult.OK) goto Init;
        }

        // Hook将根据IsHitTestVisible状态动态安装/卸载
        // 初始状态下不安装Hook，只有在锁定时才安装
    }

    private void OnExit(object sender, RoutedEventArgs e)
    {
        Close();
    }

    private void OnTargetButtonClick(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.ContextMenu != null)
        {
            button.ContextMenu.PlacementTarget = button;
            button.ContextMenu.Placement = PlacementMode.Bottom;
            button.ContextMenu.IsOpen = true;
        }
    }


    private void OnRefresh(object? sender, EventArgs e)
    {
        // Tooltip cannot be opened in refresh
        if (!PlayerHolder.IsMouseOver) _viewModel.Players.Refresh();
    }

    private void OnHistoryDoubleClick(object sender, MouseButtonEventArgs e)
    {
        if (HistoryHolder.SelectedItem is not HistoryData history) return;

        _viewModel.Status = StatusType.Pause;
        _viewModel.Players = history.Data;
        _viewModel.Page = 0;
    }

    /// <summary>
    /// 检查并显示活动提醒
    /// </summary>
    private void CheckAndShowActivityNotice()
    {
        var user = MainWindowViewModel.Instance.User;
        if (user?.ShowActivityNotice == true)
        {
            var activity = ActivityInfo.Instance;
            HandyControl.Controls.Growl.Info(new GrowlInfo
            {
                Message = activity.Description,
                WaitTime = 5,
                Token = "DamageMeterGrowl",
                ShowDateTime = false
            });
        }
    }

    /// <summary>
    /// 清除单个BOSS倒计时器
    /// </summary>
    private void OnClearSingleBossTimer(object sender, RoutedEventArgs e)
    {
        if (sender is MenuItem menuItem && menuItem.DataContext is BossTimer timer)
        {
            _viewModel.ClearSingleBossTimer(timer.Channel);
        }
    }

    /// <summary>
    /// 清除所有BOSS倒计时器
    /// </summary>
    private void OnClearAllBossTimers(object sender, RoutedEventArgs e)
    {
        _viewModel.ClearBossTimers();
    }
    #endregion
}
