{layout name="manage/template" /}

<div class="admin-content">
	<div class="admin-content-body">
		<div class="am-cf am-padding am-padding-bottom-0">
			<div class="am-fl am-cf">
				<strong class="am-text-primary am-text-lg">风控配置</strong> / <small>Risk Control Configuration</small>
			</div>
		</div>
		<hr>
		<!-- 导航栏 -->
		<div class="am-btn-toolbar am-margin-bottom">
			<a href="/manage/admin/risk/dashboard" class="am-btn am-btn-secondary">
				<i class="am-icon-dashboard"></i> 仪表板 </a>
			<a href="/manage/admin/risk/events" class="am-btn am-btn-secondary">
				<i class="am-icon-list"></i> 风险事件 </a>
			<span class="am-btn am-btn-primary am-disabled">
				<i class="am-icon-cog"></i> 风控配置 </span>
		</div>
		<!-- 配置表单 -->
		<div class="am-panel am-panel-default">
			<div class="am-panel-hd">风控规则配置</div>
			<div class="am-panel-bd">
				<form class="am-form am-form-horizontal" method="post">
					<div class="am-form-group">
						<label for="max_qq_per_device_per_day" class="am-u-sm-3 am-form-label"> 同设备每日最大QQ数量: </label>
						<div class="am-u-sm-9">
							<input type="number" id="max_qq_per_device_per_day" name="max_qq_per_device_per_day" value="{$config.max_qq_per_device_per_day}" class="am-form-field" min="1" max="50" required>
							<small class="am-text-muted"> 当同一设备在一天内登录不同QQ号超过此数量时触发风控 </small>
						</div>
					</div>
					<div class="am-form-group">
						<label for="max_qq_per_ip_per_day" class="am-u-sm-3 am-form-label"> 同IP每日最大QQ数量: </label>
						<div class="am-u-sm-9">
							<input type="number" id="max_qq_per_ip_per_day" name="max_qq_per_ip_per_day" value="{$config.max_qq_per_ip_per_day}" class="am-form-field" min="1" max="100" required>
							<small class="am-text-muted"> 当同一IP在一天内登录不同QQ号超过此数量时触发风控 </small>
						</div>
					</div>
					<div class="am-form-group">
						<label for="max_login_attempts_per_hour" class="am-u-sm-3 am-form-label"> 每小时最大登录次数: </label>
						<div class="am-u-sm-9">
							<input type="number" id="max_login_attempts_per_hour" name="max_login_attempts_per_hour" value="{$config.max_login_attempts_per_hour}" class="am-form-field" min="1" max="100" required>
							<small class="am-text-muted"> 当同一QQ号在一小时内登录次数超过此数量时触发风控 </small>
						</div>
					</div>
					<div class="am-form-group">
						<label class="am-u-sm-3 am-form-label">新设备检查:</label>
						<div class="am-u-sm-9">
							<label class="am-radio-inline">
								<input type="radio" name="enable_new_device_check" value="1" {if condition="$config.enable_new_device_check == 1" }checked{/if}> 启用 </label>
							<label class="am-radio-inline">
								<input type="radio" name="enable_new_device_check" value="0" {if condition="$config.enable_new_device_check == 0" }checked{/if}> 禁用 </label>
							<small class="am-text-muted"> 是否对新设备登录进行额外检查 </small>
						</div>
					</div>
					<div class="am-form-group">
						<label class="am-u-sm-3 am-form-label">异地登录检查:</label>
						<div class="am-u-sm-9">
							<label class="am-radio-inline">
								<input type="radio" name="enable_location_check" value="1" {if condition="$config.enable_location_check == 1" }checked{/if}> 启用 </label>
							<label class="am-radio-inline">
								<input type="radio" name="enable_location_check" value="0" {if condition="$config.enable_location_check == 0" }checked{/if}> 禁用 </label>
							<small class="am-text-muted"> 是否检查异地登录行为（基于IP地理位置） </small>
						</div>
					</div>
					<div class="am-form-group">
						<div class="am-u-sm-9 am-u-sm-push-3">
							<button type="submit" class="am-btn am-btn-primary">
								<i class="am-icon-save"></i> 保存配置 </button>
							<button type="reset" class="am-btn am-btn-default">
								<i class="am-icon-refresh"></i> 重置 </button>
						</div>
					</div>
				</form>
			</div>
		</div>
		<!-- 配置说明 -->
		<div class="am-panel am-panel-default">
			<div class="am-panel-hd">配置说明</div>
			<div class="am-panel-bd">
				<div class="am-alert am-alert-secondary">
					<h4>风控规则说明:</h4>
					<ul>
						<li><strong>设备QQ数量限制:</strong> 防止同一设备被用于大量不同账号的登录，可能的工作室行为</li>
						<li><strong>IP QQ数量限制:</strong> 防止同一IP地址下大量不同账号登录，可能的代理或工作室行为</li>
						<li><strong>频繁登录限制:</strong> 防止单个账号异常频繁的登录行为，可能的自动化工具</li>
						<li><strong>新设备检查:</strong> 对首次使用的设备进行额外的安全检查</li>
						<li><strong>异地登录检查:</strong> 检测账号在不同地理位置的登录行为</li>
					</ul>
				</div>
				<div class="am-alert am-alert-warning">
					<h4>注意事项:</h4>
					<ul>
						<li>修改配置后需要重启Go风控服务才能生效</li>
						<li>过于严格的配置可能影响正常用户的使用体验</li>
						<li>建议根据实际情况逐步调整配置参数</li>
						<li>所有配置修改都会记录在管理员操作日志中</li>
					</ul>
				</div>
				<div class="am-alert am-alert-success">
					<h4>当前管理员:</h4>
					<p>
						<strong>{$admin_info.username}</strong> {if condition="$admin_info.super == 1"} <span class="am-badge am-badge-danger">超级管理员</span> {else/} <span class="am-badge am-badge-primary">普通管理员</span> {/if}
					</p>
				</div>
			</div>
		</div>
	</div>
</div>
<script>
	$(document).ready(function() {
	    // 表单验证
	    $('form').on('submit', function(e) {
	        var deviceLimit = parseInt($('#max_qq_per_device_per_day').val());
	        var ipLimit = parseInt($('#max_qq_per_ip_per_day').val());
	        var loginLimit = parseInt($('#max_login_attempts_per_hour').val());
	
	        if (deviceLimit < 1 || deviceLimit > 50) {
	            alert('设备QQ数量限制必须在1-50之间');
	            e.preventDefault();
	            return false;
	        }
	
	        if (ipLimit < 1 || ipLimit > 100) {
	            alert('IP QQ数量限制必须在1-100之间');
	            e.preventDefault();
	            return false;
	        }
	
	        if (loginLimit < 1 || loginLimit > 100) {
	            alert('登录次数限制必须在1-100之间');
	            e.preventDefault();
	            return false;
	        }
	
	        return confirm('确定要保存这些配置吗？配置修改后需要重启Go风控服务才能生效。');
	    });
	});
</script>