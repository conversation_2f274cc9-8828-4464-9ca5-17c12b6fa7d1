<?php
namespace app\manage\model;

use think\Exception;
use think\Model;
use think\facade\Cache;
use app\manage\model\CDkey as CDkey;
use app\manage\model\UserBind as UserBind;
use app\manage\model\UserDraw as UserDraw;

class Lucky extends Model
{
    protected $table = 'bns_lucky';

    /**
     * 公示抽奖池
     */
    public static function announce() {
        return static::where('is_activity', 1)
            ->where('startTime', '<=', time())
            ->where('endTime', '>=', time())
            ->find();
    }
 
    /**
     * 执行抽奖
     */
    public static function draw() {
        $ip = request()->ip();
        $uid = session('user');
        
        try {
            $redis = Cache::store('redis')->handler();
        } catch (\Exception $e) {
            throw new Exception("缓存服务不可用");
        }

        // 获取当前活动
        $data = static::where('is_activity', 1)
            ->where('startTime', '<=', time())
            ->where('endTime', '>=', time())
            ->find();
            
        if (!$data) {
            throw new Exception("没有正在进行中的活动");
        }

        // 检查IP限制
        $hash = 'user_draw_' . $data->id;
        if ($redis->hExists($hash, $ip) && static::isToday($redis->hGet($hash, $ip))) {
            throw new Exception("当前IP今天已经签到过啦~");
        }

        // 获取用户抽奖数据
        $user = UserDraw::Get($uid, $data->id);
        $last = date('Y-m-d', $user->time);
        
        if ($last === date('Y-m-d', time())) {
            // 判断免费次数是否使用完了
            if ($user->today >= $data->free) {
                if ($user->extra == 0) {
                    $redis->hSet($hash, $ip, time());
                    throw new Exception("今天已经签到过啦~");
                }
                $user->extra--;
            }
            $user->today++;
        } else {
            // 新的一天，重置今日次数
            $user->today = 1;
        }

        // 执行抽奖逻辑
        $result = static::performDraw($data);
        
        // 更新用户数据
        $user->time = time();
        $user->total++;
        $user->save();
        
        // 记录IP
        $redis->hSet($hash, $ip, time());
        
        return $result;
    }

    /**
     * 执行抽奖逻辑
     */
    private static function performDraw($activity) {
        // 解析奖品配置
        $prizes = json_decode($activity->prizes, true);
        if (!$prizes) {
            throw new Exception("奖品配置错误");
        }

        // 计算总权重
        $totalWeight = array_sum(array_column($prizes, 'weight'));
        
        // 生成随机数
        $random = mt_rand(1, $totalWeight);
        
        // 确定中奖奖品
        $currentWeight = 0;
        foreach ($prizes as $prize) {
            $currentWeight += $prize['weight'];
            if ($random <= $currentWeight) {
                return static::awardPrize($prize);
            }
        }
        
        // 默认返回最后一个奖品（保底）
        return static::awardPrize(end($prizes));
    }

    /**
     * 发放奖品
     */
    private static function awardPrize($prize) {
        $result = [
            'name' => $prize['name'],
            'type' => $prize['type'],
            'value' => $prize['value'] ?? '',
            'description' => $prize['description'] ?? ''
        ];

        // 根据奖品类型处理
        switch ($prize['type']) {
            case 'cdkey':
                // 生成CDKey
                $cdkey = CDkey::Random($prize['prefix'] ?? 'LUCKY', 8);
                CDkey::create([
                    'cdkey' => $cdkey,
                    'type' => $prize['cdkey_type'] ?? 1,
                    'value' => $prize['cdkey_value'] ?? 0,
                    'status' => 0,
                    'create_time' => time()
                ]);
                $result['cdkey'] = $cdkey;
                break;
                
            case 'points':
                // 积分奖励（这里可以扩展积分系统）
                $result['points'] = $prize['points'] ?? 0;
                break;
                
            case 'virtual':
                // 虚拟奖品（谢谢参与等）
                break;
        }

        return $result;
    }

    /**
     * 检查是否是今天
     */
    private static function isToday($timestamp) {
        return date('Y-m-d', $timestamp) === date('Y-m-d', time());
    }

    /**
     * 获取活动列表
     */
    public static function getActivityList($status = null) {
        $query = static::order('create_time DESC');
        
        if ($status !== null) {
            $query->where('is_activity', $status);
        }
        
        return $query->select();
    }

    /**
     * 创建活动
     */
    public static function createActivity($data) {
        $activity = new static();
        $activity->name = $data['name'];
        $activity->description = $data['description'] ?? '';
        $activity->startTime = strtotime($data['start_time']);
        $activity->endTime = strtotime($data['end_time']);
        $activity->free = $data['free'] ?? 1;
        $activity->prizes = json_encode($data['prizes']);
        $activity->is_activity = $data['is_activity'] ?? 0;
        $activity->create_time = time();
        
        return $activity->save();
    }

    /**
     * 更新活动
     */
    public function updateActivity($data) {
        foreach ($data as $key => $value) {
            if (in_array($key, ['name', 'description', 'free', 'is_activity'])) {
                $this->$key = $value;
            }
        }
        
        if (isset($data['start_time'])) {
            $this->startTime = strtotime($data['start_time']);
        }
        
        if (isset($data['end_time'])) {
            $this->endTime = strtotime($data['end_time']);
        }
        
        if (isset($data['prizes'])) {
            $this->prizes = json_encode($data['prizes']);
        }
        
        $this->update_time = time();
        return $this->save();
    }

    /**
     * 获取活动统计
     */
    public static function getActivityStats($activityId) {
        $activity = static::find($activityId);
        if (!$activity) {
            return null;
        }

        $totalDraws = UserDraw::where('activity_id', $activityId)->sum('total');
        $totalUsers = UserDraw::where('activity_id', $activityId)->count();
        
        return [
            'activity' => $activity,
            'total_draws' => $totalDraws,
            'total_users' => $totalUsers,
            'avg_draws' => $totalUsers > 0 ? round($totalDraws / $totalUsers, 2) : 0
        ];
    }

    /**
     * 检查活动状态
     */
    public function getStatus() {
        $now = time();
        
        if (!$this->is_activity) {
            return 'disabled';
        }
        
        if ($now < $this->startTime) {
            return 'pending';
        }
        
        if ($now > $this->endTime) {
            return 'ended';
        }
        
        return 'active';
    }

    /**
     * 获取奖品列表
     */
    public function getPrizeList() {
        return json_decode($this->prizes, true) ?: [];
    }
}
