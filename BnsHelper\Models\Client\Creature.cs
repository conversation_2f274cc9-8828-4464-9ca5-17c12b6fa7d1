﻿using CommunityToolkit.Mvvm.Input;
using System.Xml.Serialization;
using Xylia.BnsHelper.Properties;
using Xylia.BnsHelper.Views;
using Xylia.Preview.Data.Engine;
using Xylia.Preview.Data.Models;
using Xylia.Preview.Data.Models.Sequence;

namespace Xylia.BnsHelper.Models;
public class Creature
{
    #region Fields
    // Stats
    [XmlAttribute("name")] public string? Name { get; set; }
    [XmlAttribute] public short world;
    [XmlAttribute] public byte race;
    [XmlAttribute] public byte sex;
    [XmlAttribute] public byte job;
    [XmlAttribute] public int level;
    [XmlAttribute("geo-zone")] public int GeoZone;
    [XmlAttribute] public short x;
    [XmlAttribute] public short y;
    [XmlAttribute] public short z;
    [XmlAttribute] public short yaw;

    [XmlAttribute("exp")] public long Exp { get; set; }
    [XmlAttribute("mastery-level")] public int MasteryLevel { get; set; }
    [XmlAttribute("mastery-exp")] public long MasteryExp { get; set; }
    [XmlAttribute("max-hp")] public long MaxHp { get; set; }
    [XmlAttribute("max-hp-equip")] public int MaxHpEquip { get; set; }
    [XmlAttribute("hp-regen")] public long HpRegen { get; set; }
    [XmlAttribute("hp-regen-equip")] public int HpRegenEquip { get; set; }
    [XmlAttribute("hp-regen-combat")] public long HpRegenCombat { get; set; }
    [XmlAttribute("hp-regen-combat-equip")] public int HpRegenCombatEquip { get; set; }
    [XmlAttribute("attack-hit-value")] public short AttackHitValue { get; set; }
    [XmlAttribute("attack-hit-value-equip")] public short AttackHitValueEquip { get; set; }
    [XmlAttribute("attack-pierce-value")] public short AttackPierceValue { get; set; }
    [XmlAttribute("attack-pierce-value-equip")] public short AttackPierceValueEquip { get; set; }
    [XmlAttribute("attack-critical-value")] public int AttackCriticalValue { get; set; }
    [XmlAttribute("attack-critical-value-equip")] public int AttackCriticalValueEquip { get; set; }
    [XmlAttribute("attack-critical-damage-value")] public int AttackCriticalDamageValue { get; set; }
    [XmlAttribute("attack-critical-damage-value-equip")] public int AttackCriticalDamageValueEquip { get; set; }
    [XmlAttribute("defend-critical-value")] public int DefendCriticalValue { get; set; }
    [XmlAttribute("defend-critical-vlue-equip")] public int DefendCriticalValueEquip { get; set; }
    [XmlAttribute("defend-dodge-value")] public short DefendDodgeValue { get; set; }
    [XmlAttribute("defend-dodge-value-equip")] public short DefendDodgeValueEquip { get; set; }
    [XmlAttribute("attack-power-creature-min")] public int AttackPowerCreatureMin { get; set; }
    [XmlAttribute("attack-power-creature-max")] public int AttackPowerCreatureMax { get; set; }
    [XmlAttribute("attack-power-equip-min")] public int AttackPowerEquipMin { get; set; }
    [XmlAttribute("attack-power-equip-max")] public int AttackPowerEquipMax { get; set; }
    [XmlAttribute("defend-power-creature-value")] public int DefendPowerCreatureValue { get; set; }
    [XmlAttribute("defend-power-equip-value")] public short DefendPowerEquipValue { get; set; }

    // Equipments
    [XmlElement("hand")] public Equipment? Hand { get; set; }
    [XmlElement("body")] public Equipment? Body { get; set; }
    [XmlElement("ear")] public Equipment? Ear { get; set; }
    [XmlElement("eye")] public Equipment? Eye { get; set; }
    [XmlElement("head")] public Equipment? Head { get; set; }
    [XmlElement("finger")] public Equipment? Finger { get; set; }
    [XmlElement("neck")] public Equipment? Neck { get; set; }
    [XmlElement("gem-1")] public Equipment? Gem1 { get; set; }
    [XmlElement("gem-2")] public Equipment? Gem2 { get; set; }
    [XmlElement("gem-3")] public Equipment? Gem3 { get; set; }
    [XmlElement("gem-4")] public Equipment? Gem4 { get; set; }
    [XmlElement("gem-5")] public Equipment? Gem5 { get; set; }
    [XmlElement("gem-6")] public Equipment? Gem6 { get; set; }
    [XmlElement("gem-7")] public Equipment? Gem7 { get; set; }
    [XmlElement("gem-8")] public Equipment? Gem8 { get; set; }
    [XmlElement("wrist")] public Equipment? Wrist { get; set; }
    [XmlElement("waist")] public Equipment? Waist { get; set; }
    [XmlElement("body-attach")] public Equipment? BodyAttach { get; set; }
    [XmlElement("summoned-body")] public Equipment? SummonedBody { get; set; }
    [XmlElement("summoned-head")] public Equipment? SummonedHead { get; set; }
    [XmlElement("summoned-attach")] public Equipment? SummonedAttach { get; set; }
    [XmlElement("soul")] public Equipment? Soul { get; set; }
    [XmlElement("preset-gem-1")] public Equipment? PresetGem1 { get; set; }
    [XmlElement("preset-gem-2")] public Equipment? PresetGem2 { get; set; }
    [XmlElement("preset-gem-3")] public Equipment? PresetGem3 { get; set; }
    [XmlElement("preset-gem-4")] public Equipment? PresetGem4 { get; set; }
    [XmlElement("preset-gem-5")] public Equipment? PresetGem5 { get; set; }
    [XmlElement("preset-gem-6")] public Equipment? PresetGem6 { get; set; }
    [XmlElement("preset-gem-7")] public Equipment? PresetGem7 { get; set; }
    [XmlElement("preset-gem-8")] public Equipment? PresetGem8 { get; set; }
    [XmlElement("soul-2")] public Equipment? Soul2 { get; set; }
    [XmlElement("gloves")] public Equipment? Gloves { get; set; }
    [XmlElement("pet-1")] public Equipment? Pet1 { get; set; }
    [XmlElement("pet-2")] public Equipment? Pet2 { get; set; }
    [XmlElement("rune-1")] public Equipment? Rune1 { get; set; }
    [XmlElement("rune-2")] public Equipment? Rune2 { get; set; }
    [XmlElement("nova")] public Equipment? Nova { get; set; }
    [XmlElement("vehicle")] public Equipment? Vehicle { get; set; }
    [XmlElement("preset-hand")] public Equipment? PresetHand { get; set; }
    [XmlElement("preset-ear")] public Equipment? PresetEar { get; set; }
    [XmlElement("preset-finger")] public Equipment? PresetFinger { get; set; }
    [XmlElement("preset-neck")] public Equipment? PresetNeck { get; set; }
    [XmlElement("preset-wrist")] public Equipment? PresetWrist { get; set; }
    [XmlElement("preset-waist")] public Equipment? PresetWaist { get; set; }
    [XmlElement("preset-soul")] public Equipment? PresetSoul { get; set; }
    [XmlElement("preset-soul-2")] public Equipment? PresetSoul2 { get; set; }
    [XmlElement("preset-gloves")] public Equipment? PresetGloves { get; set; }
    [XmlElement("preset-pet-1")] public Equipment? PresetPet1 { get; set; }
    [XmlElement("preset-pet-2")] public Equipment? PresetPet2 { get; set; }
    [XmlElement("preset-rune-1")] public Equipment? PresetRune1 { get; set; }
    [XmlElement("preset-rune-2")] public Equipment? PresetRune2 { get; set; }
    [XmlElement("preset-nova")] public Equipment? PresetNova { get; set; }
    [XmlElement("preset-vehicle")] public Equipment? PresetVehicle { get; set; }
    [XmlElement("normal-state-appearance")] public Equipment? NormalStateAppearance { get; set; }
    [XmlElement("idle-state-appearance")] public Equipment? IdleStateAppearance { get; set; }
    [XmlElement("chatting-symbol")] public Equipment? ChattingSymbol { get; set; }
    [XmlElement("portrait-appearance")] public Equipment? PortraitAppearance { get; set; }
    [XmlElement("hypermove-appearance")] public Equipment? HypermoveAppearance { get; set; }
    [XmlElement("hand-appearance")] public Equipment? HandAppearance { get; set; }
    [XmlElement("pet-1-appearance")] public Equipment? Pet1Appearance { get; set; }
    [XmlElement("name-plate-appearance")] public Equipment? NamePlateAppearance { get; set; }
    [XmlElement("speech-bubble-appearance")] public Equipment? SpeechBubbleAppearance { get; set; }
    [XmlElement("talk-social")] public Equipment? TalkSocial { get; set; }
    [XmlElement("armlet-1")] public Equipment? Armlet1 { get; set; }
    [XmlElement("armlet-2")] public Equipment? Armlet2 { get; set; }
    [XmlElement("preset-armlet-1")] public Equipment? PresetArmlet1 { get; set; }
    [XmlElement("preset-armlet-2")] public Equipment? PresetArmlet2 { get; set; }
    #endregion

    #region Constructor
    public Creature()
    {

    }

    internal Creature(DataArchive reader)
    {
        var id = reader.Read<long>();
        world = reader.Read<short>();
        race = reader.Read<byte>();
        sex = reader.Read<byte>();
        job = reader.Read<byte>();
        Name = reader.ReadString();
        GeoZone = reader.Read<int>();
        x = reader.Read<short>();
        y = reader.Read<short>();
        z = reader.Read<short>();
        yaw = reader.Read<short>();
    }
    #endregion

    #region Properties
    public string? World => BnsWorld.GetName(world);

    public string? Level => string.Format(MasteryLevel == 0 ? "{0}级" : "{0}级{1}星", level, MasteryLevel);

    public JobSeq Job => (JobSeq)job;

    public string? NeedExp
    {
        get
        {
            if (MasteryLevel == 0)
            {
                var record1 = ResourceProvider.Instance.Provider.GetTable<Level>()[level];
                var record2 = ResourceProvider.Instance.Provider.GetTable<Level>()[level + 1];
                if (record1 is null || record2 is null) throw new Exception("未定义数据");

                var full = record2.Exp - record1.Exp;
                var expect = record2.Exp - Exp;
                var current = Exp - record1.Exp;
                return string.Format("当前经验进度{0:P0}，距离升级还需要{1:###,###}经验", (double)current / full, expect);
            }
            else
            {
                var record1 = ResourceProvider.Instance.Provider.GetTable<MasteryLevel>()[MasteryLevel];
                var record2 = ResourceProvider.Instance.Provider.GetTable<MasteryLevel>()[MasteryLevel + 1];
                if (record1 is null || record2 is null) throw new Exception("未定义数据");
                if (record1.CannotMasteryExpUpInMinExp) return string.Format("已达到最高星级");

                var full = record2.MasteryExp - record1.MasteryExp;
                var expect = record2.MasteryExp - MasteryExp;
                var current = MasteryExp - record1.MasteryExp;
                return string.Format("当前经验进度{0:P0}，距离升级还需要{1:###,###}经验", (double)current / full, expect);
            }
        }
    }

    public int AttackPowerCreature => (AttackPowerCreatureMin + AttackPowerCreatureMax) / 2;
    public int AttackPowerEquip => (AttackPowerEquipMin + AttackPowerEquipMax) / 2;
    #endregion

    #region Methods
    public IRelayCommand ViewCommand => new RelayCommand(View);

    private void View() => new CharacterInfoPanel(this).Show();
    #endregion
}
