* {
  box-sizing: border-box;
}

div.search {padding: 10px 0;}
form {
  position: relative;
  width:95%;
  margin: 0 auto;
}

input, button {
  border: none;
  outline: none;
}

input {
  width: 100%;
  height: 37px;
  padding-left: 13px;
}

.button {
  height: 42px;
  width: 42px;
  cursor: pointer;
  position: absolute;
}
/*搜索框2*/
.bar2 {
  background: #ffffff;
  width:100%;
  margin:0 auto;}

.bar2 input, .bar2 button {
  border-radius: 3px;
}
.bar2 input {
  background: #f2f3f5;
}
.bar2 button {
  height: 26px;
  width: 26px;
  top: 8px;
  right: 8px;
  background: #F15B42;
}
.bar2 button:before {
  content: "\f105";
  font-family: FontAwesome;
  color: #F9F0DA;
  font-size: 20px;
  font-weight: bold;
}

/*播放器*/
/*video-box样式*/

.video-box {
	width: 100%;
	padding-right: 4px;
	padding-left: 4px;
	position: relative;
}

.video-box>.container {
	padding-right: 0px;
	padding-left: 0px;
}

#video {
	width: 100%;
	height: 75%;
    margin-top:15px;
}
/*屏幕最小768px匹配样式*/

@media (min-width: 768px) {
  /*video-box样式*/
	.video-box {
		width: 100%;
	}
	/*palybox视频窗口*/
	#video {
      width:70%;
      height:80%;
      margin-top:20px;
	}
  .bar2 {
    background: #ffffff;
    width:70%;
    margin:0 auto;
  }
}
