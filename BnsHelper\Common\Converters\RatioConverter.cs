﻿using System.Globalization;
using System.Windows;
using System.Windows.Data;
using System.Windows.Markup;

namespace Xylia.BnsHelper.Common.Converters;
public class RatioConverter : MarkupExtension, IValueConverter, IMultiValueConverter
{
	public override object ProvideValue(IServiceProvider serviceProvider) => this;

	public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
	{
		return System.Convert.ToDouble(value) * System.Convert.ToDouble(parameter, culture);
	}

	public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
	{
		if (values.Length < 2 || values.Any(e => e == DependencyProperty.UnsetValue)) return DependencyProperty.UnsetValue;

		return values.Select(System.Convert.ToDouble).Aggregate(1d, (s, i) => s * (double.IsNaN(i) ? 1 : i));
	}

	public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture) => throw new NotImplementedException();

	public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture) => throw new NotImplementedException();
}