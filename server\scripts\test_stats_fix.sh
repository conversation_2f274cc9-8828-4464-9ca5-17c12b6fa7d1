#!/bin/bash

# 在线用户统计修复验证脚本

echo "=== 在线用户统计修复验证 ==="
echo

# 1. 检查数据库表是否存在
echo "1. 检查数据库表..."
mysql -u root -p -e "USE your_database_name; DESCRIBE online_stats_history;" 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✓ online_stats_history 表已存在"
else
    echo "✗ online_stats_history 表不存在，请先执行数据库迁移"
    echo "  执行: mysql -u root -p your_database_name < docs/CREATE_ONLINE_STATS_HISTORY_TABLE.sql"
fi
echo

# 2. 检查Go代码编译
echo "2. 检查Go代码编译..."
cd "$(dirname "$0")/.."
go build -o /tmp/test_server ./cmd/main.go 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✓ Go代码编译成功"
    rm -f /tmp/test_server
else
    echo "✗ Go代码编译失败"
    go build ./cmd/main.go
fi
echo

# 3. 检查PHP语法
echo "3. 检查PHP语法..."
php -l www/application/bns/controller/Manage/OnlineDashboard.php >/dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✓ OnlineDashboard.php 语法正确"
else
    echo "✗ OnlineDashboard.php 语法错误"
    php -l www/application/bns/controller/Manage/OnlineDashboard.php
fi

php -l www/application/bns/controller/Api/OnlineStats.php >/dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✓ OnlineStats.php 语法正确"
else
    echo "✗ OnlineStats.php 语法错误"
    php -l www/application/bns/controller/Api/OnlineStats.php
fi
echo

# 4. 检查新增文件
echo "4. 检查新增文件..."
files=(
    "internal/model/stats.go"
    "internal/service/stats_service.go"
    "docs/CREATE_ONLINE_STATS_HISTORY_TABLE.sql"
    "docs/ONLINE_STATS_FIX.md"
)

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "✓ $file 存在"
    else
        echo "✗ $file 不存在"
    fi
done
echo

# 5. 检查API接口（需要服务运行）
echo "5. 检查API接口（需要服务运行）..."
echo "请手动测试以下API接口："
echo "  GET /api/onlinestats/current"
echo "  GET /api/onlinestats/history?period=hour&limit=24"
echo "  GET /api/onlinestats/health"
echo

echo "=== 验证完成 ==="
echo
echo "部署步骤："
echo "1. 执行数据库迁移: mysql -u root -p your_database_name < docs/CREATE_ONLINE_STATS_HISTORY_TABLE.sql"
echo "2. 重启Go服务"
echo "3. 等待1小时后检查统计数据"
echo "4. 访问管理后台查看在线统计图表"
