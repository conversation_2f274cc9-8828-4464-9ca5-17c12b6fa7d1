<?php
namespace app\guidebook\model;

use think\Model;

class Article extends Model
{
    protected $pk = 'id';
    protected $table = 'guidebook_page';  // 使用TP5兼容的表名

    /**
     * 获取文章列表
     */
    public static function getList($page = 1, $limit = 20, $category = null) {
        $query = static::where('status', 1)->order('sort DESC, create_time DESC');
        
        if ($category) {
            $query->where('category', $category);
        }
        
        return $query->paginate($limit);
    }
    
    /**
     * 获取首页文章列表 - 兼容TP5版本
     */
    public static function GetHome() {
        return static::where('draft', 0)->select();
    }
    
    /**
     * 获取文章详情
     */
    public static function getDetail($id) {
        $article = static::where('id', $id)->where('status', 1)->find();
        
        if ($article) {
            // 增加浏览次数
            $article->view_count = ($article->view_count ?? 0) + 1;
            $article->save();
        }
        
        return $article;
    }
    
    /**
     * 根据标题获取文章
     */
    public static function getByTitle($title) {
        return static::where('title', $title)->where('draft', 0)->find();
    }
    
    /**
     * 获取文章分类
     */
    public static function getCategories() {
        return static::where('draft', 0)
            ->group('category')
            ->column('category');
    }
    
    /**
     * 获取文章统计
     */
    public static function getStats() {
        return [
            'total' => static::count(),
            'published' => static::where('draft', 0)->count(),
            'draft' => static::where('draft', 1)->count(),
            'total_views' => static::sum('view_count'),
        ];
    }
    
    /**
     * 获取文章摘要
     */
    public function getSummary($length = 200) {
        if ($this->summary) {
            return $this->summary;
        }
        
        // 从内容中提取摘要
        $content = strip_tags($this->content);
        return mb_substr($content, 0, $length) . (mb_strlen($content) > $length ? '...' : '');
    }
    
    /**
     * 检查文章是否可以访问
     */
    public function canAccess() {
        return $this->draft == 0;
    }
}
