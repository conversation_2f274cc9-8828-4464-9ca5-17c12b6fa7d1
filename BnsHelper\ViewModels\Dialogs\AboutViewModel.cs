﻿using CommunityToolkit.Mvvm.ComponentModel;
using HandyControl.Tools.Extension;
using Xylia.BnsHelper.Models;
using Xylia.BnsHelper.Services;

namespace Xylia.BnsHelper.ViewModels;
internal partial class AboutViewModel : ObservableObject, IDialogResultable<bool>
{
    #region Properties
    [ObservableProperty] Team _team;
    [ObservableProperty] string? _referencesLabel;

    public bool Result { get; set; }
    public Action? CloseAction { get; set; }
    #endregion

    #region Methods
    public async Task Initialize()
    {
        await Task.Run(() =>
        {
            ReferencesLabel = string.Join(", ",
                "BnsModPolice", "CUE4Parse", "HandyControl", "AutoUpdater.NET",
                "ini-parser-netstandard", "Oodle.NET", "RestSharp",
                "Serilog", "SkiaSharp", "Vanara.PInvoke", "WindowsShortcutFactory");
            Team = ApiEndpointService.BnszsApi.Team;

        }).ConfigureAwait(false);
    }
    #endregion
}
