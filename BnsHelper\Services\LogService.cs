﻿using System;
using System.IO;
using Serilog;
using Serilog.Events;

namespace Xylia.BnsHelper.Services;
/// <summary>
/// Provides logging services for the application, including log file management and configuration.
/// </summary>
/// <remarks>This service initializes the logging system, configures log output destinations, and manages log file
/// retention. Log files are stored in the "logs" directory within the application's base directory, and files older
/// than a predefined retention period are automatically deleted.</remarks>
internal class LogService : IService
{
	const int KEEP_DAY = 3;

	public void Register()
	{
		var logs = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
		var dir = Directory.CreateDirectory(logs);

		// clear logs
		var today = DateTime.Now;
		foreach (var file in dir.GetFiles("*.log"))
		{
			var name = Path.GetFileNameWithoutExtension(file.FullName);
			if (DateTime.TryParse(name, out var time) && (today - time).Days > KEEP_DAY) file.Delete();
		}

		// register the service
		string template = "{Timestamp:yyyy-MM-dd HH:mm:ss}|{Level}|{Message:lj}{NewLine}{Exception}";
		Log.Logger = new LoggerConfiguration()
			.WriteTo.Debug(LogEventLevel.Information, outputTemplate: template)
			.WriteTo.File(Path.Combine(logs, $"{DateTime.Now:yyyy-MM-dd}.log"), outputTemplate: template)
			.CreateLogger();
	}
}
