<?php

namespace app\ingame\controller\Character;

use app\common\controller\BaseController;
use think\App;
use think\Request;
use think\facade\Db;
use app\ingame\model\Server as ServerModel;
use think\facade\Validate;
use think\facade\Session;
use think\facade\View;

class Board extends BaseController
{
    private $m_bIsLogin = false;
    private $m_sRoleName = '';
    private $m_iServerId = 0;
    private $m_sTargetRoleName = '';
    private $m_iTargetServerId = 0;

    public function __construct(App $app, Request $request)
    {
        parent::__construct($app, $request);
        $this->checkAuthData();
    }

    public function checkAuthData()
    {
        $params = $this->request->param();
        $rule = [
            'c' => 'require',
            's' => 'require|number'
        ];

        $validate = Validate::make($rule);
        if (!$validate->check($params)) {
            $this->result_like("INVALID", array());
            return;
        }

        //获取目标信息
        $this->m_sTargetRoleName = $this->request->get('c'); //目标角色名
        $this->m_iTargetServerId = $this->request->get('s'); //目标服务器ID

        //获取自身信息
        $this->m_sRoleName = Session::get('self_roleName', null); //角色名
        $this->m_iServerId = Session::get('self_serverId', null); //服务器ID

        //判断一些需要本人操作的方法
        if (!empty($this->m_sRoleName) && !empty($this->m_iServerId)) {
            $this->m_bIsLogin = true;
        }
    }

    /**
     * 留言板首页
     */
    public function index()
    {
        $messages = BoardModel::getMessages($this->m_iTargetServerId, $this->m_sTargetRoleName);
        
        return View::fetch('Character/board', [
            'messages' => $messages,
            'target_role_name' => $this->m_sTargetRoleName,
            'target_server_id' => $this->m_iTargetServerId,
            'is_login' => $this->m_bIsLogin,
            'self_role_name' => $this->m_sRoleName,
            'self_server_id' => $this->m_iServerId
        ]);
    }

    /**
     * 获取评论数据(数组)
     */
    public function info()
    {
        //获得评论数据
        $msgs = BoardModel::GetMsgs($this->m_sTargetRoleName, $this->m_iTargetServerId);
             
        $Msg_array = array();

        // 向数组写入信息
        foreach ($msgs as $msg) {
        
            if($msg['state'] != 0){
                $msg['Content'] = '<span style="color:#C0C0C0">当前留言因违反社区管理规定，已被系统删除。主人可将其设置为不再显示。</span>';
            }
            
            //获取服务器状态
            $state = ServerModel::GetServerState($msg['serverId']);
            
            //获取权限
            $access = 0;
            if($this->m_bIsLogin){
                //管理账号可以删除
                if($this->m_iServerId < 10 && $this->m_iServerId >0){
                    $access = 1;
                //消息发出的本人可以删除	
                }else if($this->m_sRoleName == $msg['roleName'] && $this->m_iServerId == $msg['serverId']) {  
                    $access = 1;
                //评论板主人可以删除    
                } else if($this->m_sRoleName == $this->m_sTargetRoleName && $this->m_iServerId == $this->m_iTargetServerId) {  
                    $access = 1;
                }
            }
            
            //获取用户头像
            $head = BoardModel::GetHead($msg['roleName'], $msg['serverId']);

            //如果为空则应用默认
            $msgInfo = [
                "id"      =>$msg['id'],
                "name"    =>$msg['roleName'],
                "head"    => $head,
                "area"    => ServerModel::GetServerName($msg['serverId']),
                "area_id" => $msg['serverId'],
                "area_state" => $state,
                "content" =>$msg['Content'],
                "access"  =>$access,
                "time"    => self::get_last_time($msg['time']),
            ];

            array_push($Msg_array, $msgInfo);
        }

        // 返回json
        $this->result_like("SUCCESS", ["ingame"=> !empty($this->m_sRoleName),"Msg" => $Msg_array]);
        return;
    }

    /**
     * 新评论
     */
    public function comment()
    {
        //获得评论文本
        $Content = urldecode($this->request->get('Content'));
        
        //如果评论为空，返回错误
        if(empty($Content)){		
            $this->result_like("NO_CONTENT", array());
            return;
        }
        
        //判断账号是否为黑名单
        $Black = BoardModel::isBlackAccount($this->m_sRoleName,$this->m_iServerId);
        if(!empty($Black) && sizeof($Black)!=0){	
            $this->result_like("LIMITED CHAR", $Black);
            return;
        }
        
        //非白名单账号
        if(!BoardModel::isWhiteAccount($this->m_sRoleName,$this->m_iServerId)){
            //判断角色等级 60级才允许评论（白名单不受限制）
            if(BoardModel::GetLevel($this->m_sRoleName,$this->m_iServerId)){		
                $this->result_like("LIMITED LEVEL", array());
                return;
            }
        
            //判断当日评论是否达到上限（白名单不受限制）
            $comment = BoardModel::GetMySendMsg($this->m_sRoleName, $this->m_iServerId);
            if (!empty($comment) && sizeof($comment) >= 5) {
                $this->result_like("LIMITED COUNT", array());
                return;
            }
            
            //判断是否不文明	
            if($this->Check($Content)){				
                // 发送不文明语句的列入黑名单			
                BoardModel::ToBlackAccount([
                    "roleName" => $this->m_sRoleName,
                    "serverId" => $this->m_iServerId,  
                    "reason" => "使用不文明语言", 
                    "endTime" => date('Y-m-d H:i:s',strtotime('+1day')),  //时间暂定
                ]);
                
                $this->result_like("LIMITED LANGUAGE", array());
               return;
            }
        }

        // 判断是否xss、sql注入
        if(xss($Content)||strpos($Content,'</') !== false || strpos($Content,'/>') !== false){
            BoardModel::ToBlackAccount([
                "roleName" => $this->m_sRoleName,
                "serverId" => $this->m_iServerId,  
                "reason" => "尝试进行非法操作", 
                "endTime" => date('Y-m-d H:i:s',strtotime('+10year')),   //时间暂定
            ]);

            $this->result_like("ILLICITE", array());
            return;		
        }
        
        // 发送评论
        BoardModel::SendMsg([
            "roleName" => $this->m_sRoleName,
            "serverId" => $this->m_iServerId,  
            "target_roleName" => $this->m_sTargetRoleName, 
            "target_serverId" => $this->m_iTargetServerId,
            "Content" => $Content, 
            "Is_del" => 0, 
        ]);

        // 返回json
        $this->result_like("SUCCESS", array());
        return;
    }

    /**
     * 删除评论
     */
    public function delete()
    {
        $id = $this->request->get('id');
        
        // 如果id为空，返回错误
        if(empty($id)){		
            $this->result_like("NONE_ID", array());
            return;
        }
                
        // 判断是否存在指定留言
        $info = BoardModel::GetMsg2($id);
        
        if(empty($info)){
            $this->result_like("INVALID", array());
            return;
        }
        
        //管理账号可以删除
        if($this->m_iServerId < 10 && $this->m_iServerId >0){
            BoardModel::DeleteMsg($id,"管理账号("+ $this->m_sRoleName + ")删除");
        //消息发出的本人可以删除	
        }else if($this->m_sRoleName == $info['roleName'] && $this->m_iServerId == $info['serverId']) {  
            BoardModel::DeleteMsg($id,"本人删除"); 
        //评论板主人可以删除    
        } else if($this->m_sRoleName == $this->m_sTargetRoleName && $this->m_iServerId == $this->m_iTargetServerId) {  
            BoardModel::DeleteMsg($id,"主人删除"); 
        //返回权限不足提示    
        }else{
            $this->result_like("NO_RIGHT", array());
            return;
        }
        
        // 返回json
        $this->result_like("SUCCESS", array());
        return;
    }

    /**
     * 检查敏感词
     */
    static function Check($Str)
    {
        return false;
    }

    /**
     * 获取时间差
     */
    static function get_last_time($time)
    {
        $time = strtotime($time);
        $now = time();
        $diff = $now - $time;
        
        if ($diff < 60) {
            return '刚刚';
        } elseif ($diff < 3600) {
            return floor($diff / 60) . '分钟前';
        } elseif ($diff < 86400) {
            return floor($diff / 3600) . '小时前';
        } elseif ($diff < 2592000) {
            return floor($diff / 86400) . '天前';
        } else {
            return date('Y-m-d', $time);
        }
    }

    /**
     * 返回统一格式的结果
     */
    private function result_like($status, $data)
    {
        $messages = [
            'SUCCESS' => '操作成功',
            'FAILED' => '操作失败',
            'INVALID' => '参数错误',
            'NOT_LOGIN' => '请先登录',
            'NO_CONTENT' => '留言内容不能为空',
            'LIMITED CHAR' => '账号受限',
            'LIMITED LEVEL' => '等级不足',
            'LIMITED COUNT' => '今日评论次数已达上限',
            'LIMITED LANGUAGE' => '包含不文明语言',
            'ILLICITE' => '非法操作',
            'NONE_ID' => '缺少ID参数',
            'NO_RIGHT' => '权限不足'
        ];

        return json([
            'status' => $status,
            'message' => $messages[$status] ?? '未知状态',
            'data' => $data
        ]);
    }
}
