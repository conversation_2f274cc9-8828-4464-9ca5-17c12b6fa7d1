<?php
namespace app\ingame\model;

use think\Model;

class Server extends Model
{
    protected $pk = 'area_id';
    protected $table = 'bns_server';

    /**
     * 获取服务器名称
     */
    static public function GetServerName($serverId)
    {   
        $info = static::where('area_id', $serverId)->find();
        
        if(empty($info) || empty($info["area_name"])) {
            return $serverId;
        }
        
        return $info["area_name"];		
    }
    
    /**
     * 获取服务器列表
     */
    public static function getList() {
        return static::field('area_id, area_name, server_id')
            ->order('area_id ASC')
            ->select()
            ->toArray();
    }
    
    /**
     * 根据大区ID获取服务器列表
     */
    public static function getServersByArea($areaId) {
        return static::where('server_id', $areaId)
            ->field('area_id, area_name')
            ->order('area_id ASC')
            ->select()
            ->toArray();
    }
    
    /**
     * 获取大区列表
     */
    public static function getAreaList() {
        return static::field('server_id, COUNT(*) as server_count')
            ->group('server_id')
            ->order('server_id ASC')
            ->select()
            ->toArray();
    }
}
