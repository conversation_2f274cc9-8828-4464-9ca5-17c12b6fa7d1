﻿using Xylia.BnsHelper.Resources;

namespace Xylia.BnsHelper.Common;
internal class AppException : Exception
{
	#region Constructors
	public AppException(string message) : base(message)
	{

	}

	public AppException(string message, Exception innerException) : base(message, innerException)
	{
	}

	public AppException(ExceptionCode code, params object[] param) : this(code, null, param)
	{

	}

	public AppException(ExceptionCode code, Exception? innerException, params object[] param) :
		base(StringHelper.Get("Exception_" + code, param), innerException)
	{

	}
	#endregion
}

public enum ExceptionCode
{
	None,
	InvalidGame,
	InvalidPublisher,
	InvalidPublisher2,
	InvalidPacket,
	InvalidPath,
	InvalidPlugin,
	InvalidPluginVersion,
	InvalidUser,
}
