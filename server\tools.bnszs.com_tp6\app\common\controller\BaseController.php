<?php
namespace app\common\controller;

use think\App;
use think\Request;

/**
 * TP6基础控制器 - 包含从TP5.1迁移的统一基类功能
 */
class BaseController
{
    /**
     * Request实例
     * @var Request
     */
    protected $request;

    /**
     * 应用实例
     * @var App
     */
    protected $app;

    /**
     * 构造方法
     * @param App $app
     * @param Request $request
     */
    public function __construct(App $app, Request $request)
    {
        $this->app = $app;
        $this->request = $request;

        // 控制器初始化
        $this->initialize();
    }

    /**
     * 初始化
     */
    protected function initialize()
    {
        // 子类可以重写此方法
    }

    /**
     * 返回JSON数据
     * @param mixed $data
     * @param int $code
     * @param string $msg
     * @return \think\Response
     */
    protected function json($data = [], $code = 1, $msg = 'success')
    {
        return json([
            'code' => $code,
            'msg' => $msg,
            'data' => $data
        ]);
    }

    /**
     * CURL GET请求 - 从TP5.1 Base类迁移
     * @param string $url
     * @param int|null $stateCode
     * @param string|null $cookie
     * @return string|false
     */
    public static function curlGetForm($url, &$stateCode = null, $cookie = null)
    {
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "GET");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');

        if ($cookie) {
            curl_setopt($ch, CURLOPT_COOKIE, $cookie);
        }

        $result = curl_exec($ch);
        $stateCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        return $result;
    }

    /**
     * CURL POST请求 - 从TP5.1 Base类迁移
     * @param string $url
     * @param mixed $parameter
     * @return string|false
     */
    public static function curlPostForm($url, $parameter)
    {
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $parameter);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');

        $result = curl_exec($ch);
        curl_close($ch);

        return $result;
    }

    /**
     * 服务器ID转大区ID - 从TP5.1 Base类迁移
     * @param int $serverId
     * @return string
     */
    public static function serverId2AreaId($serverId)
    {
        return substr($serverId, 0, 2);
    }

    /**
     * 代理请求到外部API并返回结果 - 解决CORS问题
     * @param string $targetUrl
     * @param array $headers
     * @return \think\Response|string
     */
    protected function proxyRequest($targetUrl, $headers = [])
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $targetUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');

        // 传递Cookie
        if ($this->request->header('Cookie')) {
            curl_setopt($ch, CURLOPT_COOKIE, $this->request->header('Cookie'));
        }

        // 设置自定义头部
        if (!empty($headers)) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        }

        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);

        // 记录调试信息
        trace("ProxyRequest - URL: " . $targetUrl, 'info');
        trace("ProxyRequest - HTTP Code: " . $httpCode, 'info');
        trace("ProxyRequest - Curl Error: " . $curlError, 'info');

        // 设置CORS响应头
        $corsHeaders = [
            'Access-Control-Allow-Origin' => '*',
            'Access-Control-Allow-Methods' => 'GET, POST, OPTIONS',
            'Access-Control-Allow-Headers' => 'Content-Type',
            'Content-Type' => 'application/json; charset=utf-8'
        ];

        if ($result === false || $httpCode !== 200) {
            trace("ProxyRequest - Failed: " . $curlError, 'error');
            return response(json_encode(['error' => 'Failed to fetch data from external API', 'details' => $curlError, 'http_code' => $httpCode]), 503, $corsHeaders);
        }

        // 直接返回原始响应内容，设置正确的响应头
        return response($result, 200, $corsHeaders);
    }


}
