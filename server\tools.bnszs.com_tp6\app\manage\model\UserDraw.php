<?php
namespace app\manage\model;

use think\Exception;
use think\Model;

class UserDraw extends Model
{
    protected $table = 'user_draw';

    /**
     * 获取用户抽奖数据
     */
    public static function Get($uid, $activityId) {
        // 如果不存在则创建
        $data = static::where(['uid' => $uid, 'activity_id' => $activityId])->find();
        if (!$data) {
            $data = new static();
            $data->uid = $uid;
            $data->activity_id = $activityId;
            $data->extra = 0;
            $data->day = 1;
            $data->point = 1;
            $data->total = 0;
            $data->today = 0;
            $data->time = time();
            $data->save();
        }

        return $data;
    }
    
    /**
     * 获取用户抽奖历史
     */
    public static function getUserHistory($uid, $limit = 20) {
        return static::where('uid', $uid)
            ->order('time DESC')
            ->limit($limit)
            ->select();
    }
    
    /**
     * 获取活动参与用户列表
     */
    public static function getActivityUsers($activityId, $limit = 50) {
        return static::where('activity_id', $activityId)
            ->order('total DESC, time DESC')
            ->limit($limit)
            ->select();
    }
    
    /**
     * 增加额外抽奖次数
     */
    public function addExtraDraws($count) {
        $this->extra += $count;
        return $this->save();
    }
    
    /**
     * 重置今日抽奖次数
     */
    public function resetTodayDraws() {
        $this->today = 0;
        return $this->save();
    }
    
    /**
     * 检查是否可以抽奖
     */
    public function canDraw($activity) {
        $today = date('Y-m-d', time());
        $lastDrawDay = date('Y-m-d', $this->time);
        
        // 如果是新的一天，重置今日次数
        if ($today !== $lastDrawDay) {
            $this->today = 0;
        }
        
        // 检查今日免费次数
        if ($this->today < $activity->free) {
            return true;
        }
        
        // 检查额外次数
        if ($this->extra > 0) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 获取用户统计信息
     */
    public static function getUserStats($uid) {
        $stats = static::where('uid', $uid)->select();
        
        $totalDraws = 0;
        $totalActivities = count($stats);
        
        foreach ($stats as $stat) {
            $totalDraws += $stat->total;
        }
        
        return [
            'total_draws' => $totalDraws,
            'total_activities' => $totalActivities,
            'avg_draws' => $totalActivities > 0 ? round($totalDraws / $totalActivities, 2) : 0
        ];
    }
    
    /**
     * 获取活动排行榜
     */
    public static function getActivityRanking($activityId, $limit = 10) {
        return static::where('activity_id', $activityId)
            ->order('total DESC')
            ->limit($limit)
            ->select();
    }
    
    /**
     * 更新连续签到天数
     */
    public function updateConsecutiveDays() {
        $today = date('Y-m-d', time());
        $yesterday = date('Y-m-d', strtotime('-1 day'));
        $lastDrawDay = date('Y-m-d', $this->time);
        
        if ($lastDrawDay === $yesterday) {
            // 连续签到
            $this->day += 1;
        } elseif ($lastDrawDay !== $today) {
            // 中断了，重新开始
            $this->day = 1;
        }
        
        return $this->save();
    }
    
    /**
     * 获取连续签到奖励
     */
    public function getConsecutiveReward() {
        $rewards = [
            7 => ['type' => 'extra', 'value' => 1, 'name' => '额外抽奖次数+1'],
            15 => ['type' => 'extra', 'value' => 2, 'name' => '额外抽奖次数+2'],
            30 => ['type' => 'extra', 'value' => 5, 'name' => '额外抽奖次数+5']
        ];
        
        foreach ($rewards as $days => $reward) {
            if ($this->day >= $days && $this->day % $days === 0) {
                return $reward;
            }
        }
        
        return null;
    }
    
    /**
     * 应用连续签到奖励
     */
    public function applyConsecutiveReward() {
        $reward = $this->getConsecutiveReward();
        if ($reward) {
            switch ($reward['type']) {
                case 'extra':
                    $this->extra += $reward['value'];
                    break;
            }
            $this->save();
            return $reward;
        }
        return null;
    }
}
