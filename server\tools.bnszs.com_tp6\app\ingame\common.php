<?php
// Ingame应用公共文件

/**
 * XSS检查函数
 */
function xss($arr) {
    $a = Array('/script/','/javascript/','/vbscript/','/expression/','/applet/','/meta/','/xml/','/blink/','/link/','/style/','/embed/','/object/','/frame/','/layer/','/title/','/bgsound/','/base/','/onload/','/onunload/','/onchange/','/onsubmit/','/onreset/','/onselect/','/onblur/','/onfocus/','/onabort/','/onkeydown/','/onkeypress/','/onkeyup/','/onclick/','/ondblclick/','/onmousedown/','/onmousemove/','/onmouseout/','/onmouseover/','/onmouseup/','/onunload/','/alert/','/img/','/">/');
    if (is_array($arr)) {
        foreach ($arr as $key => $value) {
            if (!is_array($value)) {
                if (!get_magic_quotes_gpc()) {
                    $value = addslashes($value);
                }
                foreach ($a as $k => $v) {
                    if (preg_match($v, $value)) {
                        return true;
                    }
                }
            } else {
                return xss($arr[$key]);
            }
        }
    }
    return false;
}

/**
 * 检查是否为JSON格式
 */
function is_json($string) {
    json_decode($string);
    return (json_last_error() == JSON_ERROR_NONE);
}

/**
 * 清理XSS
 */
function clean_xss($str) {
    $str = preg_replace('/([\x00-\x08][\x0b-\x0c][\x0e-\x20])/', '', $str);
    $search = 'abcdefghijklmnopqrstuvwxyz';
    $search .= 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $search .= '1234567890!@#$%^&*()';
    $search .= '~`";:?+/={}[]-_|\'\\';
    for ($i = 0; $i < strlen($search); $i++) {
        $str = preg_replace('/(&#[x|X]0{0,8}'.dechex(ord($search[$i])).';?)/i', $search[$i], $str);
        $str = preg_replace('/(&#0{0,8}'.ord($search[$i]).';?)/', $search[$i], $str);
    }
    return $str;
}
