package config

import (
	"fmt"
	"time"

	"github.com/spf13/viper"
)

// HeartbeatConfig 心跳配置
type HeartbeatConfig struct {
	// ClientInterval 客户端心跳间隔
	ClientInterval time.Duration

	// ServerTimeout 服务器端心跳超时时间
	// 应该比客户端间隔长，给客户端留出容错时间
	ServerTimeout time.Duration

	// CleanupInterval 清理过期连接的间隔
	CleanupInterval time.Duration
}

// DefaultHeartbeatConfig 默认心跳配置
func DefaultHeartbeatConfig() *HeartbeatConfig {
	// 尝试从配置文件读取，如果失败则使用默认值
	clientInterval := getConfigDuration("heartbeat.client_interval", 25*time.Minute)
	serverTimeout := getConfigDuration("heartbeat.server_timeout", 30*time.Minute)
	cleanupInterval := getConfigDuration("heartbeat.cleanup_interval", 5*time.Minute)

	return &HeartbeatConfig{
		ClientInterval:  clientInterval,  // 客户端心跳间隔
		ServerTimeout:   serverTimeout,   // 服务器超时时间
		CleanupInterval: cleanupInterval, // 清理间隔
	}
}

// getConfigDuration 从配置文件获取时间间隔，如果失败则返回默认值
func getConfigDuration(key string, defaultValue time.Duration) time.Duration {
	if viper.IsSet(key) {
		if duration, err := time.ParseDuration(viper.GetString(key)); err == nil {
			return duration
		}
	}
	return defaultValue
}

// GetToleranceTime 获取容错时间
func (c *HeartbeatConfig) GetToleranceTime() time.Duration {
	return c.ServerTimeout - c.ClientInterval
}

// Validate 验证配置的合理性
func (c *HeartbeatConfig) Validate() error {
	if c.ServerTimeout <= c.ClientInterval {
		return fmt.Errorf("服务器超时时间(%v)必须大于客户端心跳间隔(%v)", c.ServerTimeout, c.ClientInterval)
	}

	if c.CleanupInterval <= 0 {
		return fmt.Errorf("清理间隔(%v)必须大于0", c.CleanupInterval)
	}

	// 建议容错时间至少为客户端间隔的20%
	minTolerance := c.ClientInterval / 5
	if c.GetToleranceTime() < minTolerance {
		return fmt.Errorf("容错时间(%v)建议至少为客户端间隔的20%(%v)", c.GetToleranceTime(), minTolerance)
	}

	return nil
}
