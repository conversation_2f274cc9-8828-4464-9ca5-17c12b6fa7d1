<?php
namespace app\manage\model;

use think\facade\Db;
use think\Exception;
use think\Model;
use think\facade\Cache;
use app\manage\model\User as User;
use app\manage\model\UserBind as UserBind;
use app\manage\model\UserDraw as UserDraw;

class CDkey extends Model
{
    protected $table = 'bns_cdkey';

    /**
     * 生成随机CDKey
     */
    public static function Random($discern, $n) {
        try {
            // 生成随机字符串
            $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
            $randomString = '';
            
            // 生成8位随机字符
            for ($i = 0; $i < 8; $i++) {
                $randomString .= $characters[rand(0, strlen($characters) - 1)];
            }
            
            // 组合最终的CDKey
            $cdkey = $discern . $randomString;
            
            // 验证CDKey是否已存在
            $exists = Db::name('bns_cdkey')->where('cdkey', $cdkey)->find();
            if ($exists) {
                // 如果存在，递归重新生成
                return static::Random($discern, $n);
            }
            
            return $cdkey;
        } catch (\Exception $e) {
            // 记录错误日志
            $logFile = app()->getRuntimePath() . 'log/cdkey_logs/' . date('Y/m') . '/' . date('Y-m-d') . '.log';
            $logDir = dirname($logFile);
            if (!is_dir($logDir)) {
                mkdir($logDir, 0755, true);
            }
            
            $logContent = "[" . date('Y-m-d H:i:s') . "] CDkey::Random 方法错误: " . $e->getMessage() . "\n";
            $logContent .= "[" . date('Y-m-d H:i:s') . "] 堆栈: " . $e->getTraceAsString() . "\n";
            file_put_contents($logFile, $logContent, FILE_APPEND);
            
            throw $e;
        }
    }

    /**
     * 激活CDKey
     */
    public static function Activate($cdkey, $uid, $admin = 0) {
        try {
            // 查找CDKey
            $cdkeyInfo = static::where('cdkey', $cdkey)->find();
            if (!$cdkeyInfo) {
                throw new Exception('CDKey不存在');
            }
            
            if ($cdkeyInfo->status == 1) {
                throw new Exception('CDKey已被使用');
            }
            
            if ($cdkeyInfo->expire_time && $cdkeyInfo->expire_time < time()) {
                throw new Exception('CDKey已过期');
            }
            
            // 激活CDKey
            $cdkeyInfo->status = 1;
            $cdkeyInfo->user_id = $uid;
            $cdkeyInfo->use_time = time();
            $cdkeyInfo->save();
            
            // 记录激活日志
            static::addActivateLog($cdkey, $uid, $admin);
            
            return $cdkeyInfo;
            
        } catch (\Exception $e) {
            // 记录错误日志
            static::logError('Activate', $e, ['cdkey' => $cdkey, 'uid' => $uid]);
            throw $e;
        }
    }

    /**
     * 批量生成CDKey
     */
    public static function batchGenerate($prefix, $count, $type = 1, $value = 0, $expireDays = 0) {
        $cdkeys = [];
        $expireTime = $expireDays > 0 ? time() + ($expireDays * 86400) : 0;
        
        for ($i = 0; $i < $count; $i++) {
            $cdkey = static::Random($prefix, 8);
            
            $data = [
                'cdkey' => $cdkey,
                'type' => $type,
                'value' => $value,
                'status' => 0,
                'expire_time' => $expireTime,
                'create_time' => time()
            ];
            
            static::create($data);
            $cdkeys[] = $cdkey;
        }
        
        return $cdkeys;
    }

    /**
     * 检查CDKey状态
     */
    public static function checkStatus($cdkey) {
        $cdkeyInfo = static::where('cdkey', $cdkey)->find();
        
        if (!$cdkeyInfo) {
            return ['status' => 'not_found', 'message' => 'CDKey不存在'];
        }
        
        if ($cdkeyInfo->status == 1) {
            return ['status' => 'used', 'message' => 'CDKey已被使用', 'use_time' => $cdkeyInfo->use_time];
        }
        
        if ($cdkeyInfo->expire_time && $cdkeyInfo->expire_time < time()) {
            return ['status' => 'expired', 'message' => 'CDKey已过期', 'expire_time' => $cdkeyInfo->expire_time];
        }
        
        return ['status' => 'valid', 'message' => 'CDKey有效', 'data' => $cdkeyInfo];
    }

    /**
     * 获取CDKey列表
     */
    public static function getList($page = 1, $limit = 20, $filters = []) {
        $query = static::order('create_time DESC');
        
        if (isset($filters['type'])) {
            $query->where('type', $filters['type']);
        }
        
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }
        
        if (isset($filters['cdkey'])) {
            $query->where('cdkey', 'like', '%' . $filters['cdkey'] . '%');
        }
        
        return $query->paginate($limit);
    }

    /**
     * 删除过期CDKey
     */
    public static function deleteExpired() {
        return static::where('expire_time', '<', time())
            ->where('expire_time', '>', 0)
            ->where('status', 0)
            ->delete();
    }

    /**
     * 记录激活日志
     */
    private static function addActivateLog($cdkey, $uid, $admin) {
        $logData = [
            'cdkey' => $cdkey,
            'user_id' => $uid,
            'admin' => $admin,
            'ip' => request()->ip(),
            'create_time' => time()
        ];
        
        // 这里可以添加到专门的日志表
        // CdkeyLog::create($logData);
    }

    /**
     * 记录错误日志
     */
    private static function logError($method, $exception, $data = []) {
        $logFile = app()->getRuntimePath() . 'log/cdkey_errors/' . date('Y/m') . '/' . date('Y-m-d') . '.log';
        $logDir = dirname($logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $logContent = "[" . date('Y-m-d H:i:s') . "] CDkey::$method 错误: " . $exception->getMessage() . "\n";
        $logContent .= "[" . date('Y-m-d H:i:s') . "] 数据: " . json_encode($data) . "\n";
        $logContent .= "[" . date('Y-m-d H:i:s') . "] 堆栈: " . $exception->getTraceAsString() . "\n\n";
        
        file_put_contents($logFile, $logContent, FILE_APPEND);
    }

    /**
     * 获取统计信息
     */
    public static function getStats() {
        return [
            'total' => static::count(),
            'used' => static::where('status', 1)->count(),
            'unused' => static::where('status', 0)->count(),
            'expired' => static::where('expire_time', '<', time())
                ->where('expire_time', '>', 0)
                ->where('status', 0)
                ->count()
        ];
    }
}
