<?php
//加载公共头部
require_once('../public/header.php');
require_once('../public/common.php');
require_once('../public/admincommon.php');
if(!$token){
	msg('请登录后再来操作','../admin/login.php'); 
	exit; 
}


//获取不想审核的信息编号
$skips = "0";
if($_REQUEST["skip"] != null) {
   foreach(explode(",",$_REQUEST["skip"]) as $skip){
       //如果值是数字
       if(is_numeric($skip)) $skips .= "," . $skip;
   }
}

//筛选符合条件的数据
//排除管理员已经跳过的角色
$waitView = $DB->get_row("SELECT * FROM `character_view` `view` 
                          WHERE `view`.status = 0 AND `view`.`id` not in ($skips)
                          ORDER BY `UploadTime` LIMIT 1");    
if($waitView) {
    
    $ViewId = $waitView['id'];
?>
        <div class="modal-content" style="display:inline-block;width:300px;right:30px;top:60px;position: absolute;z-index:999">
            <div class="modal-body">
                <div>
                    <label for="txt_departmentname">审核操作</label>
                    <button type="button" class="btn-default" style="float:right" data-dismiss="modal" onclick="Skip();">
                        <span class=" glyphicon glyphicon-refresh" aria-hidden="true"></span> 跳过
                    </button>
                            
                    <input type="text" class="form-control" id="banReason" placeholder="未通过原因，不填时系统提示默认原因" value=<?=$waitView['banReason']?>>
                </div>
            </div>
            
            <div class="modal-footer">
                <button type="button" id="btn_submit" class="btn btn-primary" data-dismiss="modal" onclick="adopt();" style ="float:left;">
                    <span class="glyphicon glyphicon-ok" aria-hidden="true"></span> 通过审核
                </button>
                    
                <button type="button" class="btn btn-default" data-dismiss="modal" onclick="Ban();">
                    <span class="glyphicon glyphicon-remove" aria-hidden="true"></span> 退回
                </button>
            </div>
        </div> 
<?php } ?> 



    
  <!-- content start -->
  <div class="admin-content">
    <div class="admin-content-body">
      <div class="am-cf am-padding">
        <div class="am-fl am-cf"><strong class="am-text-primary am-text-lg">首页</strong> / <small>自定义审核</small></div>
      </div>
      
      <div class="am-cf" >
        <div class="am-fl am-cf">当前共有未审核<?=$DB->count("SELECT count(*) FROM `character_view` WHERE status=0")?>份，退回<?=$DB->count("SELECT count(*) FROM `character_view` WHERE status=2")?>份</div>
        <div class="am-fl am-cf" style="margin-left:30px;">最近30天内审核<?=$DB->count("SELECT count(*) FROM `character_view` WHERE status<>0 AND uin is not null AND time >= DATE_SUB(CURDATE(),INTERVAL 30 DAY);")?>份，
                                 系统内共有<?=$DB->count("SELECT count(*) FROM `character_view`")?>份（过期 <?=$DB->count("SELECT count(*) FROM `character_view` WHERE timediff(expiration,NOW()) < 0 AND expiration != '0000-00-00 00:00:00'")?>份）
        
        </div>
      </div>
	  <hr>
	  
	  
<?php if($waitView) { ?>
      <div class="am-g">
        <div class="am-u-sm-12">
        
        <iframe id="userView" frameborder="0" src="https://tools.bnszs.com/ingame/bs/character/profile?c=<?=$waitView['roleName']?>&s=<?=$waitView['serverId']?>&examine=1" style="width: 100%; height:  765px; border: none; margin: 0px;"></iframe>
          
        </div>
      </div>
<?php } else { ?> 
    
    <span style="padding-left:70px">已经到底啦 ~</span>      
                
<?php } ?>   
     


<script>
  document.onkeydown=function(event){
    var e = event || window.event || arguments.callee.caller.arguments[0];
    if(e && e.keyCode==13){ 
        event.preventDefault(); 
        adopt();  //回车执行
    }
  }; 


  
  function adopt(id = <?=$ViewId?>) {
	var r = $("#banReason").val(); 
    
    if(r != "" && r != null && r != "undefined"){
        if(!confirm("已填写退回原因，是否确认通过?")) return;
    }
      
    $.ajax({
	    type: "POST",//方法类型
	    dataType: "json",//预期服务器返回的数据类型
	    url: "./character_view.php" ,//url
	    data: $(window.parent.document).find("#userView").attr("src")+ "&id="+ id +"&adopt",
	    success: function (result) {
	        //alert(result.msg);
	        if(result.code == 0){
	        	location.reload();
	        }else{
	        	alert("审核失败" + result.msg);
	        }
	    },
        error : function(event, XMLHttpRequest, ajaxOptions, thrownError) {
            console.log(event)
	        alert("发生未知错误，编辑资料失败");
		}
	});
  }	


  function Ban(id = <?=$ViewId?>) {
	var r = $("#banReason").val();
	
    $.ajax({
	    type: "POST",
	    dataType: "json",
	    url: "./character_view.php", 
	    data: $(window.parent.document).find("#userView").attr("src") + "&id="+id+"&ban&reason=" + r,
	    success: function (result) {
	        if(result.code == 0) location.reload();
	        else alert("退回失败，" + result.msg);
	    },
	    error: function() {
 	    	alert("发生未知错误，退回失败");
        }
	});
  }	
  
  //跳过
  function Skip(id = <?=$ViewId?>) {
      
      var LastSkip = getQueryVariable("skip");
      if(LastSkip != "" && LastSkip != null && LastSkip != "undefined") LastSkip += ","
      
      window.location.href = changeQueryVariable("skip", LastSkip + id);
  }	
  
  
  
  document.getElementById('userView').onload = function() {
	//here doc
  }
  
  
　$(function() {
      //audiojs.instances["audiojs0"].playPause();
　});
  
</script>

<?php require_once('../public/footer.php'); //加载公共尾部 ?>



