﻿<Application x:Class="Xylia.BnsHelper.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
			 xmlns:wv="clr-namespace:Microsoft.Web.WebView2.Wpf;assembly=Microsoft.Web.WebView2.Wpf"
             DispatcherUnhandledException="OnUnhandledException">
	<Application.Resources>
		<ResourceDictionary>
			<ResourceDictionary.MergedDictionaries>
				
				<ResourceDictionary>
					<ResourceDictionary.MergedDictionaries>
						<ResourceDictionary Source="pack://application:,,,/HandyControl;component/Themes/SkinDefault.xaml"/>
						<ResourceDictionary Source="Resources/Themes/Skins/Blue.xaml"/>
						<ResourceDictionary Source="Resources/Themes/Skins/Basic/Day.xaml"/>
					</ResourceDictionary.MergedDictionaries>
				</ResourceDictionary>

				<ResourceDictionary>
					<ResourceDictionary.MergedDictionaries>
						<ResourceDictionary Source="pack://application:,,,/HandyControl;component/Themes/Theme.xaml"/>
						<ResourceDictionary Source="Resources/Themes/Theme.xaml"/>
					</ResourceDictionary.MergedDictionaries>
				</ResourceDictionary>

				<ResourceDictionary Source="pack://application:,,,/Preview.UI.Common;component/Themes/Styles.xaml"/>
				<local:StringHelper xmlns:local="clr-namespace:Xylia.BnsHelper.Resources"/>
			</ResourceDictionary.MergedDictionaries>



			<!-- Global ToolTip Style - Must be after all merged dictionaries -->
			<Style TargetType="ToolTip">
				<Setter Property="Padding" Value="5 4" />
				<Setter Property="Background" Value="{DynamicResource SecondaryRegionBrush}" />
				<Setter Property="BorderBrush" Value="{DynamicResource BorderBrush}" />
				<Setter Property="BorderThickness" Value="1" />
				<Setter Property="Foreground" Value="{DynamicResource PrimaryTextBrush}" />
				<Setter Property="FontSize" Value="13.5" />
				<Setter Property="HasDropShadow" Value="True" />
				<Setter Property="Template">
					<Setter.Value>
						<ControlTemplate TargetType="ToolTip">
							<Border Background="{TemplateBinding Background}"
									BorderBrush="{TemplateBinding BorderBrush}"
									BorderThickness="{TemplateBinding BorderThickness}"
									Padding="{TemplateBinding Padding}"
									CornerRadius="4">
								<ContentPresenter Content="{TemplateBinding Content}"
												  ContentTemplate="{TemplateBinding ContentTemplate}"
												  ContentTemplateSelector="{TemplateBinding ContentTemplateSelector}" />
							</Border>
						</ControlTemplate>
					</Setter.Value>
				</Setter>
			</Style>

			<!-- Global ToolTipService Settings -->
			<Style TargetType="FrameworkElement">
				<Style.Setters>
					<Setter Property="ToolTipService.InitialShowDelay" Value="500" />
					<Setter Property="ToolTipService.ShowDuration" Value="10000" />
					<Setter Property="ToolTipService.BetweenShowDelay" Value="100" />
				</Style.Setters>
			</Style>

            <wv:CoreWebView2CreationProperties x:Key="EvergreenWebView2CreationProperties" />
        </ResourceDictionary>
	</Application.Resources>
</Application>
