﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.1.32228.430
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Bns<PERSON>elper", "B<PERSON><PERSON>el<PERSON>\BnsHelper.csproj", "{3F365550-8CE0-47AA-B98E-675080BE0382}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Updater", "Updater\Updater.csproj", "{E31F986C-9D42-427F-A15D-9C960DD2949E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Preview.UI.Common", "..\..\..\..\DotNet\Xylia\bns-preview-tools\Preview.UI.Common\Preview.UI.Common.csproj", "{54B2704C-289B-EF3A-35FE-EFE78F0321C7}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Exe|Any CPU = Exe|Any CPU
		Exe|x64 = Exe|x64
		Exe|x86 = Exe|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{3F365550-8CE0-47AA-B98E-675080BE0382}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3F365550-8CE0-47AA-B98E-675080BE0382}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3F365550-8CE0-47AA-B98E-675080BE0382}.Debug|x64.ActiveCfg = Debug|Any CPU
		{3F365550-8CE0-47AA-B98E-675080BE0382}.Debug|x64.Build.0 = Debug|Any CPU
		{3F365550-8CE0-47AA-B98E-675080BE0382}.Debug|x86.ActiveCfg = Debug|Any CPU
		{3F365550-8CE0-47AA-B98E-675080BE0382}.Debug|x86.Build.0 = Debug|Any CPU
		{3F365550-8CE0-47AA-B98E-675080BE0382}.Exe|Any CPU.ActiveCfg = Release|Any CPU
		{3F365550-8CE0-47AA-B98E-675080BE0382}.Exe|Any CPU.Build.0 = Release|Any CPU
		{3F365550-8CE0-47AA-B98E-675080BE0382}.Exe|x64.ActiveCfg = Release|Any CPU
		{3F365550-8CE0-47AA-B98E-675080BE0382}.Exe|x64.Build.0 = Release|Any CPU
		{3F365550-8CE0-47AA-B98E-675080BE0382}.Exe|x86.ActiveCfg = Release|Any CPU
		{3F365550-8CE0-47AA-B98E-675080BE0382}.Exe|x86.Build.0 = Release|Any CPU
		{3F365550-8CE0-47AA-B98E-675080BE0382}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3F365550-8CE0-47AA-B98E-675080BE0382}.Release|Any CPU.Build.0 = Release|Any CPU
		{3F365550-8CE0-47AA-B98E-675080BE0382}.Release|x64.ActiveCfg = Release|Any CPU
		{3F365550-8CE0-47AA-B98E-675080BE0382}.Release|x64.Build.0 = Release|Any CPU
		{3F365550-8CE0-47AA-B98E-675080BE0382}.Release|x86.ActiveCfg = Release|Any CPU
		{3F365550-8CE0-47AA-B98E-675080BE0382}.Release|x86.Build.0 = Release|Any CPU
		{E31F986C-9D42-427F-A15D-9C960DD2949E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E31F986C-9D42-427F-A15D-9C960DD2949E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E31F986C-9D42-427F-A15D-9C960DD2949E}.Debug|x64.ActiveCfg = Debug|Any CPU
		{E31F986C-9D42-427F-A15D-9C960DD2949E}.Debug|x64.Build.0 = Debug|Any CPU
		{E31F986C-9D42-427F-A15D-9C960DD2949E}.Debug|x86.ActiveCfg = Debug|Any CPU
		{E31F986C-9D42-427F-A15D-9C960DD2949E}.Debug|x86.Build.0 = Debug|Any CPU
		{E31F986C-9D42-427F-A15D-9C960DD2949E}.Exe|Any CPU.ActiveCfg = Exe|Any CPU
		{E31F986C-9D42-427F-A15D-9C960DD2949E}.Exe|Any CPU.Build.0 = Exe|Any CPU
		{E31F986C-9D42-427F-A15D-9C960DD2949E}.Exe|x64.ActiveCfg = Exe|Any CPU
		{E31F986C-9D42-427F-A15D-9C960DD2949E}.Exe|x64.Build.0 = Exe|Any CPU
		{E31F986C-9D42-427F-A15D-9C960DD2949E}.Exe|x86.ActiveCfg = Exe|Any CPU
		{E31F986C-9D42-427F-A15D-9C960DD2949E}.Exe|x86.Build.0 = Exe|Any CPU
		{E31F986C-9D42-427F-A15D-9C960DD2949E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E31F986C-9D42-427F-A15D-9C960DD2949E}.Release|Any CPU.Build.0 = Release|Any CPU
		{E31F986C-9D42-427F-A15D-9C960DD2949E}.Release|x64.ActiveCfg = Release|Any CPU
		{E31F986C-9D42-427F-A15D-9C960DD2949E}.Release|x64.Build.0 = Release|Any CPU
		{E31F986C-9D42-427F-A15D-9C960DD2949E}.Release|x86.ActiveCfg = Release|Any CPU
		{E31F986C-9D42-427F-A15D-9C960DD2949E}.Release|x86.Build.0 = Release|Any CPU
		{54B2704C-289B-EF3A-35FE-EFE78F0321C7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{54B2704C-289B-EF3A-35FE-EFE78F0321C7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{54B2704C-289B-EF3A-35FE-EFE78F0321C7}.Debug|x64.ActiveCfg = Debug|Any CPU
		{54B2704C-289B-EF3A-35FE-EFE78F0321C7}.Debug|x64.Build.0 = Debug|Any CPU
		{54B2704C-289B-EF3A-35FE-EFE78F0321C7}.Debug|x86.ActiveCfg = Debug|Any CPU
		{54B2704C-289B-EF3A-35FE-EFE78F0321C7}.Debug|x86.Build.0 = Debug|Any CPU
		{54B2704C-289B-EF3A-35FE-EFE78F0321C7}.Exe|Any CPU.ActiveCfg = Debug|Any CPU
		{54B2704C-289B-EF3A-35FE-EFE78F0321C7}.Exe|Any CPU.Build.0 = Debug|Any CPU
		{54B2704C-289B-EF3A-35FE-EFE78F0321C7}.Exe|x64.ActiveCfg = Debug|Any CPU
		{54B2704C-289B-EF3A-35FE-EFE78F0321C7}.Exe|x64.Build.0 = Debug|Any CPU
		{54B2704C-289B-EF3A-35FE-EFE78F0321C7}.Exe|x86.ActiveCfg = Debug|Any CPU
		{54B2704C-289B-EF3A-35FE-EFE78F0321C7}.Exe|x86.Build.0 = Debug|Any CPU
		{54B2704C-289B-EF3A-35FE-EFE78F0321C7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{54B2704C-289B-EF3A-35FE-EFE78F0321C7}.Release|Any CPU.Build.0 = Release|Any CPU
		{54B2704C-289B-EF3A-35FE-EFE78F0321C7}.Release|x64.ActiveCfg = Release|Any CPU
		{54B2704C-289B-EF3A-35FE-EFE78F0321C7}.Release|x64.Build.0 = Release|Any CPU
		{54B2704C-289B-EF3A-35FE-EFE78F0321C7}.Release|x86.ActiveCfg = Release|Any CPU
		{54B2704C-289B-EF3A-35FE-EFE78F0321C7}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {48C2D002-4BB3-418D-BBA5-8D41E74DEC2E}
	EndGlobalSection
EndGlobal
