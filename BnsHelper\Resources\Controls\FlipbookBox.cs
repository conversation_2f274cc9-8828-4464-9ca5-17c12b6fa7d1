﻿using CUE4Parse.UE4.Objects.Core.Math;
using SkiaSharp;
using System.Windows;
using System.Windows.Controls;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.Preview.Data.Engine.DatData;
using Xylia.Preview.UI.Controls;

namespace Xylia.BnsHelper.Resources;
internal class FlipbookBox : Canvas
{
    const string FlipbookL = $"pack://application:,,,/Resources/Images/Flipbook.png";
    const string FlipbookN = $"pack://application:,,,/Resources/Images/Neo_Flipbook.png";

    public FlipbookBox()
    {
        var info = Application.GetResourceStream(new Uri(SettingHelper.Default.Publisher >= EPublisher.ZNCS ? FlipbookN : FlipbookL));
        using var source = SKBitmap.Decode(info?.Stream ?? throw new EntryPointNotFoundException("FlipbookBox 资源读取失败"));

        var animation = BnsCustomImageWidget.Flipbook(source, new FVector2D(256, 128), 5);
        ApplyAnimationClock(BackgroundProperty, animation.CreateClock());
    }
}
