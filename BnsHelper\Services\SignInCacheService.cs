using System.IO;
using System.Text.Json;

namespace Xylia.BnsHelper.Services;

/// <summary>
/// 签到状态缓存服务
/// 用于本地缓存签到不可用状态，避免频繁请求服务器
/// </summary>
public class SignInCacheService
{
    private static readonly Lazy<SignInCacheService> _instance = new(() => new SignInCacheService());
    public static SignInCacheService Instance => _instance.Value;

    private readonly string _cacheFilePath;
    private readonly Dictionary<string, SignInCacheItem> _cache;
    private readonly object _lockObject = new();

    private SignInCacheService()
    {
        var appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "BnsHelper");
        Directory.CreateDirectory(appDataPath);
        _cacheFilePath = Path.Combine(appDataPath, "signin_cache.json");
        _cache = new Dictionary<string, SignInCacheItem>();
        LoadCache();
    }

    /// <summary>
    /// 检查用户今天是否已经签到不可用（已缓存）
    /// </summary>
    /// <param name="uid">用户ID</param>
    /// <returns>是否已缓存为不可用状态</returns>
    public bool IsSignInUnavailableCached(ulong uid)
    {
        lock (_lockObject)
        {
            var key = GetCacheKey(uid);
            if (_cache.TryGetValue(key, out var item))
            {
                // 检查是否为今天的缓存
                if (item.Date.Date == DateTime.Today)
                {
                    return item.IsUnavailable;
                }
                else
                {
                    // 过期缓存，删除
                    _cache.Remove(key);
                }
            }
            return false;
        }
    }

    /// <summary>
    /// 缓存用户签到不可用状态
    /// </summary>
    /// <param name="uid">用户ID</param>
    /// <param name="reason">不可用原因</param>
    public void CacheSignInUnavailable(ulong uid, string reason)
    {
        lock (_lockObject)
        {
            var key = GetCacheKey(uid);
            _cache[key] = new SignInCacheItem
            {
                Date = DateTime.Today,
                IsUnavailable = true,
                Reason = reason,
                CachedAt = DateTime.Now
            };
            SaveCacheAsync();
        }
    }

    /// <summary>
    /// 缓存用户签到可用状态
    /// </summary>
    /// <param name="uid">用户ID</param>
    public void CacheSignInAvailable(ulong uid)
    {
        lock (_lockObject)
        {
            var key = GetCacheKey(uid);
            _cache[key] = new SignInCacheItem
            {
                Date = DateTime.Today,
                IsUnavailable = false,
                Reason = string.Empty,
                CachedAt = DateTime.Now
            };
            SaveCacheAsync();
        }
    }

    /// <summary>
    /// 获取缓存的不可用原因
    /// </summary>
    /// <param name="uid">用户ID</param>
    /// <returns>不可用原因，如果没有缓存或可用则返回null</returns>
    public string? GetCachedUnavailableReason(ulong uid)
    {
        lock (_lockObject)
        {
            var key = GetCacheKey(uid);
            if (_cache.TryGetValue(key, out var item))
            {
                // 检查是否为今天的缓存且不可用
                if (item.Date.Date == DateTime.Today && item.IsUnavailable)
                {
                    return item.Reason;
                }
            }
            return null;
        }
    }

    /// <summary>
    /// 清除用户的签到缓存
    /// </summary>
    /// <param name="uid">用户ID</param>
    public void ClearSignInCache(ulong uid)
    {
        lock (_lockObject)
        {
            var key = GetCacheKey(uid);
            _cache.Remove(key);
            SaveCacheAsync();
        }
    }

    /// <summary>
    /// 清除所有过期的缓存
    /// </summary>
    public void ClearExpiredCache()
    {
        lock (_lockObject)
        {
            var today = DateTime.Today;
            var keysToRemove = new List<string>();

            foreach (var kvp in _cache)
            {
                if (kvp.Value.Date.Date != today)
                {
                    keysToRemove.Add(kvp.Key);
                }
            }

            foreach (var key in keysToRemove)
            {
                _cache.Remove(key);
            }

            if (keysToRemove.Count > 0)
            {
                SaveCacheAsync();
            }
        }
    }

    /// <summary>
    /// 获取缓存统计信息
    /// </summary>
    /// <returns>缓存统计信息</returns>
    public SignInCacheStats GetCacheStats()
    {
        lock (_lockObject)
        {
            var today = DateTime.Today;
            int todayCount = 0;
            int unavailableCount = 0;

            foreach (var item in _cache.Values)
            {
                if (item.Date.Date == today)
                {
                    todayCount++;
                    if (item.IsUnavailable)
                    {
                        unavailableCount++;
                    }
                }
            }

            return new SignInCacheStats
            {
                TotalCacheCount = _cache.Count,
                TodayCacheCount = todayCount,
                TodayUnavailableCount = unavailableCount
            };
        }
    }

    private string GetCacheKey(ulong uid)
    {
        return $"signin_{uid}";
    }

    private void LoadCache()
    {
        try
        {
            if (File.Exists(_cacheFilePath))
            {
                var json = File.ReadAllText(_cacheFilePath);
                var cacheData = JsonSerializer.Deserialize<Dictionary<string, SignInCacheItem>>(json);
                if (cacheData != null)
                {
                    foreach (var kvp in cacheData)
                    {
                        _cache[kvp.Key] = kvp.Value;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"加载签到缓存失败: {ex.Message}");
            // 加载失败时清空缓存
            _cache.Clear();
        }
    }

    private async void SaveCacheAsync()
    {
        try
        {
            await Task.Run(() =>
            {
                var json = JsonSerializer.Serialize(_cache, new JsonSerializerOptions
                {
                    WriteIndented = true
                });
                File.WriteAllText(_cacheFilePath, json);
            });
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"保存签到缓存失败: {ex.Message}");
        }
    }
}

/// <summary>
/// 签到缓存项
/// </summary>
public class SignInCacheItem
{
    /// <summary>
    /// 缓存日期
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// 是否不可用
    /// </summary>
    public bool IsUnavailable { get; set; }

    /// <summary>
    /// 不可用原因
    /// </summary>
    public string Reason { get; set; } = string.Empty;

    /// <summary>
    /// 缓存时间
    /// </summary>
    public DateTime CachedAt { get; set; }
}

/// <summary>
/// 签到缓存统计信息
/// </summary>
public class SignInCacheStats
{
    /// <summary>
    /// 总缓存数量
    /// </summary>
    public int TotalCacheCount { get; set; }

    /// <summary>
    /// 今日缓存数量
    /// </summary>
    public int TodayCacheCount { get; set; }

    /// <summary>
    /// 今日不可用缓存数量
    /// </summary>
    public int TodayUnavailableCount { get; set; }
}
