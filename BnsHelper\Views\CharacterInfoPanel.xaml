﻿<hc:Window x:Class="Xylia.BnsHelper.Views.CharacterInfoPanel"
		xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
		xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
		xmlns:hc="https://handyorg.github.io/handycontrol"
		xmlns:WebView="clr-namespace:Microsoft.Web.WebView2.Wpf;assembly=Microsoft.Web.WebView2.Wpf"
		Title="{DynamicResource CharacterInfoPanel_Name}"
		SizeToContent="WidthAndHeight" ResizeMode="CanMinimize">

	<WebView:WebView2 Name="wv" CreationProperties="{StaticResource EvergreenWebView2CreationProperties}" Width="1200" Height="765" />
</hc:Window>