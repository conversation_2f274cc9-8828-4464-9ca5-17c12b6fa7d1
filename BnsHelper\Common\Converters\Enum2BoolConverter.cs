﻿using System.Globalization;
using System.Windows.Data;

namespace Xylia.BnsHelper.Common.Converters;
internal class Enum2BoolConverter : IValueConverter
{
	public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
	{
		var current = System.Convert.ToInt32(value);
		var target = System.Convert.ToInt32(parameter);

		return current == target;
	}

	public object? ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
	{
		return value is true ? parameter : 0;
	}
}