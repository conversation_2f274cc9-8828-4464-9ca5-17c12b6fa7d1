<?php
// +----------------------------------------------------------------------
// | 会话设置
// +----------------------------------------------------------------------

return [
    // session name
    'name'           => 'PHPSESSID',
    // SESSION_ID的提交变量,解决flash上传跨域
    'var_session_id' => '',
    // 驱动方式 支持file cache
    'type'           => env('SESSION_TYPE', 'file'),
    // 存储连接标识 当type使用cache的时候有效
    'store'          => null,
    // 过期时间
    'expire'         => env('SESSION_EXPIRE', 1440),
    // 前缀
    'prefix'         => env('SESSION_PREFIX', ''),
    // 自动启动
    'auto_start'     => env('SESSION_AUTO_START', true),
    // cookie设置
    'cookie'         => [
        'lifetime' => 0,
        'path'     => env('SESSION_COOKIE_PATH', '/'),
        'domain'   => env('SESSION_COOKIE_DOMAIN', ''),
        'secure'   => env('SESSION_COOKIE_SECURE', false),
        'httponly' => env('SESSION_COOKIE_HTTPONLY', true),
        'samesite' => env('SESSION_COOKIE_SAMESITE', 'Lax'),
    ],
];
