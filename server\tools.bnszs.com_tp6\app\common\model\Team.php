<?php
namespace app\common\model;

use think\Model;

class Team extends Model
{
    protected $pk = 'id';
    protected $table = 'bns_team';

    /**
     * 获取团队列表
     */
    static public function GetTeams() {
        $Model = new static();
        return $Model->select()->toArray();
    }
    
    /**
     * 获取活跃团队
     */
    public static function getActiveTeams($limit = 20) {
        return static::where('status', 1)
            ->order('update_time DESC')
            ->limit($limit)
            ->select()
            ->toArray();
    }
    
    /**
     * 根据服务器获取团队
     */
    public static function getTeamsByServer($serverId, $limit = 50) {
        return static::where('server_id', $serverId)
            ->where('status', 1)
            ->order('create_time DESC')
            ->limit($limit)
            ->select()
            ->toArray();
    }
    
    /**
     * 搜索团队
     */
    public static function searchTeams($keyword, $limit = 20) {
        return static::where('status', 1)
            ->where(function($query) use ($keyword) {
                $query->where('name', 'like', '%' . $keyword . '%')
                      ->whereOr('description', 'like', '%' . $keyword . '%');
            })
            ->order('update_time DESC')
            ->limit($limit)
            ->select()
            ->toArray();
    }
    
    /**
     * 获取团队详情
     */
    public static function getTeamDetail($teamId) {
        return static::where('id', $teamId)
            ->where('status', 1)
            ->find();
    }
    
    /**
     * 创建团队
     */
    public static function createTeam($data) {
        $team = new static();
        $team->name = $data['name'];
        $team->description = $data['description'] ?? '';
        $team->server_id = $data['server_id'];
        $team->leader_name = $data['leader_name'];
        $team->contact = $data['contact'] ?? '';
        $team->requirements = $data['requirements'] ?? '';
        $team->status = 1;
        $team->create_time = time();
        $team->update_time = time();
        
        return $team->save();
    }
    
    /**
     * 更新团队信息
     */
    public function updateTeam($data) {
        foreach ($data as $key => $value) {
            if (in_array($key, ['name', 'description', 'contact', 'requirements'])) {
                $this->$key = $value;
            }
        }
        
        $this->update_time = time();
        return $this->save();
    }
    
    /**
     * 删除团队
     */
    public function deleteTeam() {
        $this->status = 0;
        $this->update_time = time();
        return $this->save();
    }
    
    /**
     * 获取团队统计信息
     */
    public static function getStats() {
        return [
            'total' => static::count(),
            'active' => static::where('status', 1)->count(),
            'today' => static::where('create_time', '>', strtotime('today'))->count()
        ];
    }
    
    /**
     * 获取热门团队
     */
    public static function getPopularTeams($limit = 10) {
        return static::where('status', 1)
            ->order('view_count DESC, update_time DESC')
            ->limit($limit)
            ->select()
            ->toArray();
    }
    
    /**
     * 增加浏览次数
     */
    public function incrementViewCount() {
        $this->view_count = ($this->view_count ?? 0) + 1;
        return $this->save();
    }
}
