<?php
namespace app\manage\model;

use think\Model;
use think\Exception;
use think\facade\Request;

class Profile extends Model
{
    protected $table = 'bns_profile';

    /**
     * 查找角色资料
     */
    public static function Find($server, $name) {
        $data = static::Query($server, $name);

        // 预览模式
        if (request()->get('preview', null)) {
            $data = new static();
            $data->status = 0;
            $data->bg_img = request()->post('bg_img', "");
            $data->charaterView = request()->post('charaterView', "");
            $data->charaterViewBorder = request()->post('charaterViewBorder', "");
            $data->titleImg = request()->post('titleImg', "");
            $data->jobImg = request()->post('jobImg', "");
            $data->jobImgBorder = request()->post('jobImgBorder', "");
            $data->otherImg = request()->post('otherImg', "");
            $data->describe = request()->post('describe', "");
            $data->labelImg = request()->post('labelImg', "");
            $data->opacity = request()->post('opacity', "");
            $data->embed = request()->post('embed', "");
            $data->bgm = request()->post('bgm', "");
            $data->bgmTitle = request()->post('bgmTitle', "");
            $data->fontFamily = request()->post('selectFontFamily', "");
            $data->textColor = request()->post('selectTextColor', "");
            $data->textShadowColor = request()->post('selectTextShadowColor', "");
            $data->mouseIco = request()->post('mouseIco', "");
            $data->mouseColor = request()->post('mouseColor', "");
            $data->usecharaterview = request()->post('usecharaterview', "false") != "false";
            $data->extContent = request()->post('extContent', "");
        // 默认结果
        } elseif(!$data || $data->status != 1) {
            $data = new static();
            $data->status = 0;
            $data->bg_img = "";
            $data->charaterView = "";
            $data->charaterViewBorder = "";
            $data->titleImg = "";
            $data->jobImg = "";
            $data->jobImgBorder = "";
            $data->otherImg = "";
            $data->describe = "";
            $data->labelImg = "";
            $data->opacity = "";
            $data->embed = "";
            $data->bgm = "";
            $data->bgmTitle = "";
            $data->fontFamily = "";
            $data->textColor = "";
            $data->textShadowColor = "";
            $data->mouseIco = "";
            $data->mouseColor = "";
            $data->usecharaterview = false;
            $data->extContent = "";
        }

        return $data;
    }

    /**
     * 查询角色资料
     */
    public static function Query($server, $name) {
        return static::where(['server' => $server, 'name' => $name])->find();
    }

    /**
     * 保存角色资料
     */
    public static function SaveProfile($server, $name, $data) {
        $profile = static::where(['server' => $server, 'name' => $name])->find();

        if (!$profile) {
            $profile = new static();
            $profile->server = $server;
            $profile->name = $name;
        }

        // 更新字段
        foreach ($data as $key => $value) {
            if (property_exists($profile, $key)) {
                $profile->$key = $value;
            }
        }

        $profile->update_time = time();
        return $profile->save();
    }

    /**
     * 设置资料状态
     */
    public function setStatus($status) {
        $this->status = $status;
        $this->update_time = time();
        return $this->save();
    }

    /**
     * 获取用户的所有资料
     */
    public static function getUserProfiles($uid, $limit = 10) {
        return static::where('uid', $uid)
            ->order('update_time DESC')
            ->limit($limit)
            ->select();
    }

    /**
     * 获取热门资料
     */
    public static function getPopularProfiles($limit = 20) {
        return static::where('status', 1)
            ->order('view_count DESC, update_time DESC')
            ->limit($limit)
            ->select();
    }

    /**
     * 增加浏览次数
     */
    public function incrementViewCount() {
        $this->view_count = ($this->view_count ?? 0) + 1;
        return $this->save();
    }

    /**
     * 检查资料是否可以编辑
     */
    public function canEdit($uid) {
        return $this->uid == $uid || $this->isAdmin($uid);
    }

    /**
     * 检查是否是管理员
     */
    private function isAdmin($uid) {
        // 这里可以添加管理员检查逻辑
        return false;
    }

    /**
     * 获取资料统计信息
     */
    public static function getStats() {
        return [
            'total' => static::count(),
            'published' => static::where('status', 1)->count(),
            'pending' => static::where('status', 0)->count(),
            'today' => static::where('create_time', '>', strtotime('today'))->count()
        ];
    }

    /**
     * 搜索资料
     */
    public static function search($keyword, $limit = 20) {
        return static::where('status', 1)
            ->where(function($query) use ($keyword) {
                $query->where('name', 'like', '%' . $keyword . '%')
                      ->whereOr('describe', 'like', '%' . $keyword . '%');
            })
            ->order('update_time DESC')
            ->limit($limit)
            ->select();
    }

    /**
     * 获取最新资料
     */
    public static function getLatest($limit = 10) {
        return static::where('status', 1)
            ->order('update_time DESC')
            ->limit($limit)
            ->select();
    }

    /**
     * 删除资料
     */
    public static function deleteProfile($server, $name, $uid) {
        $profile = static::where(['server' => $server, 'name' => $name])->find();
        
        if (!$profile) {
            throw new Exception('资料不存在');
        }
        
        if (!$profile->canEdit($uid)) {
            throw new Exception('没有权限删除此资料');
        }
        
        return $profile->delete();
    }

    /**
     * 复制资料
     */
    public function copyTo($newServer, $newName, $uid) {
        $newProfile = new static();
        $newProfile->server = $newServer;
        $newProfile->name = $newName;
        $newProfile->uid = $uid;
        $newProfile->status = 0; // 新复制的资料需要审核
        
        // 复制所有样式字段
        $fields = [
            'bg_img', 'charaterView', 'charaterViewBorder', 'titleImg',
            'jobImg', 'jobImgBorder', 'otherImg', 'describe', 'labelImg',
            'opacity', 'embed', 'bgm', 'bgmTitle', 'fontFamily',
            'textColor', 'textShadowColor', 'mouseIco', 'mouseColor',
            'usecharaterview', 'extContent'
        ];
        
        foreach ($fields as $field) {
            if (isset($this->$field)) {
                $newProfile->$field = $this->$field;
            }
        }
        
        $newProfile->create_time = time();
        $newProfile->update_time = time();
        
        return $newProfile->save();
    }

    /**
     * 验证资料数据
     */
    public static function validateData($data) {
        $errors = [];
        
        // 检查必填字段
        if (empty($data['name'])) {
            $errors[] = '角色名称不能为空';
        }
        
        if (empty($data['server'])) {
            $errors[] = '服务器不能为空';
        }
        
        // 检查描述长度
        if (isset($data['describe']) && mb_strlen($data['describe']) > 500) {
            $errors[] = '描述不能超过500个字符';
        }
        
        // 检查URL格式
        $urlFields = ['bg_img', 'charaterView', 'titleImg', 'jobImg', 'otherImg', 'labelImg'];
        foreach ($urlFields as $field) {
            if (isset($data[$field]) && !empty($data[$field]) && !filter_var($data[$field], FILTER_VALIDATE_URL)) {
                $errors[] = $field . ' 必须是有效的URL';
            }
        }
        
        return $errors;
    }
}
