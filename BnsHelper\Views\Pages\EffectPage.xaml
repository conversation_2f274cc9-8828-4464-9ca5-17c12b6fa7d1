﻿<Page x:Class="Xylia.BnsHelper.Views.Pages.EffectPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
	  xmlns:hc="https://handyorg.github.io/handycontrol">

	<Grid Margin="3 5">
		<Grid.Resources>
			<!-- Data Templates -->
			<DataTemplate x:Key="EnumTemplate">
				<TextBlock Text="{Binding Converter={StaticResource NameConverter}}" />
			</DataTemplate>

			<!-- Effect Item Style -->
			<Style x:Key="EffectItemStyle" TargetType="Border">
				<Setter Property="Background" Value="{DynamicResource RegionBrush}" />
				<Setter Property="BorderBrush" Value="{DynamicResource BorderBrush}" />
				<Setter Property="BorderThickness" Value="1" />
				<Setter Property="CornerRadius" Value="6" />
				<Setter Property="Margin" Value="0,0,0,8" />
				<Setter Property="Padding" Value="15,12" />
				<Setter Property="Effect">
					<Setter.Value>
						<DropShadowEffect Color="{DynamicResource EffectShadowColor}" Direction="270" ShadowDepth="1" Opacity="0.1" BlurRadius="3"/>
					</Setter.Value>
				</Setter>
			</Style>

			<!-- Label Style -->
			<Style x:Key="FieldLabelStyle" TargetType="TextBlock">
				<Setter Property="FontSize" Value="12" />
				<Setter Property="FontWeight" Value="Medium" />
				<Setter Property="Foreground" Value="{DynamicResource PrimaryTextBrush}" />
				<Setter Property="VerticalAlignment" Value="Center" />
				<Setter Property="Margin" Value="0,0,0,4" />
			</Style>
		</Grid.Resources>

		<Grid.RowDefinitions>
			<RowDefinition Height="Auto" />
			<RowDefinition Height="*" />
		</Grid.RowDefinitions>

		<!-- Header Section -->
		<Border Grid.Row="0" Background="{DynamicResource SecondaryRegionBrush}"
				BorderBrush="{DynamicResource BorderBrush}" BorderThickness="0,0,0,1"
				Padding="15,10" Margin="-3,-5,-3,0">
			<Grid>
				<Grid.ColumnDefinitions>
					<ColumnDefinition Width="*" />
					<ColumnDefinition Width="Auto" />
				</Grid.ColumnDefinitions>

				<StackPanel Grid.Column="0" Orientation="Horizontal">
					<TextBlock Text="特效配置管理" FontSize="16" FontWeight="SemiBold"
							   Foreground="{DynamicResource PrimaryTextBrush}" VerticalAlignment="Center" />
                    <Border Background="Transparent" Margin="7,0,0,0" VerticalAlignment="Center" ToolTip="管理游戏特效的显示配置和过滤规则">
                        <Viewbox Width="16" Height="16">
                            <Path Data="{StaticResource QuestionCircleGeometry}" Fill="{DynamicResource SecondaryTextBrush}" />
                        </Viewbox>
                    </Border>
				</StackPanel>

				<hc:ButtonGroup Grid.Column="1" Margin="0">
					<Button Content="{DynamicResource Text.Reload}" Command="{Binding ReloadCommand}"
							Style="{StaticResource ButtonDefault}" Padding="12,6">
						<Button.ToolTip>
							<ToolTip Content="重新加载配置" />
						</Button.ToolTip>
					</Button>
					<Button Content="{DynamicResource Text.Add}" Command="{Binding AddCommand}"
							Style="{StaticResource ButtonPrimary}" Padding="12,6">
						<Button.ToolTip>
							<ToolTip Content="添加新的特效配置" />
						</Button.ToolTip>
					</Button>
					<Button Content="{DynamicResource Text.Import}" Command="{Binding ImportCommand}"
							Style="{StaticResource ButtonDefault}" Padding="12,6">
						<Button.ToolTip>
							<ToolTip Content="从文件导入配置" />
						</Button.ToolTip>
					</Button>
					<Button Content="{DynamicResource Text.Export}" Command="{Binding ExportCommand}"
							Style="{StaticResource ButtonDefault}" Padding="12,6">
						<Button.ToolTip>
							<ToolTip Content="导出配置到文件" />
						</Button.ToolTip>
					</Button>
				</hc:ButtonGroup>
			</Grid>
		</Border>

		<!-- Content Section -->
		<Grid Grid.Row="1" Margin="0,15,0,0">
			<Grid.RowDefinitions>
				<RowDefinition Height="Auto" />
				<RowDefinition Height="*" />
			</Grid.RowDefinitions>

			<!-- Table Header -->
			<Border Grid.Row="0" Background="{DynamicResource ThirdlyRegionBrush}"
					BorderBrush="{DynamicResource BorderBrush}" BorderThickness="1"
					CornerRadius="6,6,0,0" Padding="15,10">
				<Grid>
					<Grid.ColumnDefinitions>
						<ColumnDefinition Width="250" />
						<ColumnDefinition Width="*" />
						<ColumnDefinition Width="*" />
						<ColumnDefinition Width="*" />
					</Grid.ColumnDefinitions>

					<TextBlock Grid.Column="0" Text="{DynamicResource EffectPage_Alias}" HorizontalAlignment="Center"
							   Style="{StaticResource FieldLabelStyle}" />
                    <TextBlock Grid.Column="1" Text="{DynamicResource EffectPage_Message}" 
							   Style="{StaticResource FieldLabelStyle}" />
                    <TextBlock Grid.Column="2" Text="{DynamicResource EffectPage_Slot}" 
							   Style="{StaticResource FieldLabelStyle}" />
					<TextBlock Grid.Column="3" Text="{DynamicResource EffectPage_Category}"
							   Style="{StaticResource FieldLabelStyle}" />
				</Grid>
			</Border>

			<!-- Data Rows -->
			<ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto"
						  HorizontalScrollBarVisibility="Disabled">
				<ItemsControl ItemsSource="{Binding Data}">
					<ItemsControl.ItemTemplate>
						<DataTemplate>
							<Border Background="{DynamicResource RegionBrush}"
									BorderBrush="{DynamicResource BorderBrush}"
									BorderThickness="1,0,1,1"
									Padding="15,8">
								<Border.ContextMenu>
									<ContextMenu>
										<MenuItem Header="{DynamicResource Text.Remove}" Command="{Binding DeleteCommand}"
												  Foreground="{DynamicResource DangerBrush}">
											<MenuItem.Icon>
												<TextBlock Text="&#xE74D;" Style="{StaticResource SegoeIcon}"
														   Foreground="{DynamicResource DangerBrush}" />
											</MenuItem.Icon>
										</MenuItem>
									</ContextMenu>
								</Border.ContextMenu>

								<Grid>
									<Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="250" />
										<ColumnDefinition Width="*" />
										<ColumnDefinition Width="*" />
										<ColumnDefinition Width="*" />
									</Grid.ColumnDefinitions>

									<!-- Alias Field -->
									<TextBox Grid.Column="0" Text="{Binding Alias, UpdateSourceTrigger=PropertyChanged}" FontSize="12" Margin="0,0,12,0" />

									<!-- Message Type Field -->
									<CheckBox Grid.Column="1" IsChecked="{Binding BattleMessage}" Style="{StaticResource ToggleButtonSwitch}"
											  Margin="0,0,8,0" />

									<!-- Slot Field -->
									<ComboBox Grid.Column="2" SelectedItem="{Binding UiSlot}"
											  ItemsSource="{Binding UiSlots}"
											  ItemTemplate="{StaticResource EnumTemplate}"
											  Margin="0,0,8,0" />

									<!-- Category Field -->
									<ComboBox Grid.Column="3" SelectedItem="{Binding UiCategory}"
											  ItemsSource="{Binding UiCategorys}"
											  ItemTemplate="{StaticResource EnumTemplate}"
											  Margin="0,0,8,0" />
								</Grid>
							</Border>
						</DataTemplate>
					</ItemsControl.ItemTemplate>
				</ItemsControl>
			</ScrollViewer>
		</Grid>
	</Grid>
</Page>