<?php
namespace app\middleware;

use Closure;
use think\Request;
use think\Response;

/**
 * 禁用ThinkPHP兼容模式中间件
 * 防止c和s参数被误解析为控制器和操作
 */
class DisableCompatMode
{
    /**
     * 处理请求
     *
     * @param Request $request
     * @param Closure $next
     * @return Response
     */
    public function handle(Request $request, Closure $next)
    {
        // 获取原始GET参数
        $get = $request->get();
        
        // 如果存在c或s参数，将它们从路由解析中排除
        if (isset($get['c']) || isset($get['s'])) {
            // 强制设置路由变量为空，防止被解析为控制器/操作
            $request->withGet([]);
            
            // 重新设置GET参数，但不影响路由解析
            foreach ($get as $key => $value) {
                $request->withGet($key, $value);
            }
        }
        
        return $next($request);
    }
}
