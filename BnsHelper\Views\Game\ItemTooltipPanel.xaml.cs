﻿using System.Windows;
using System.Windows.Controls;
using CUE4Parse.BNS.Assets.Exports;
using CUE4Parse.UE4.Objects.Core;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.BnsHelper.Resources;
using Xylia.Preview.Common;
using Xylia.Preview.Common.Extension;
using Xylia.Preview.Data.Common.DataStruct;
using Xylia.Preview.Data.Models;
using Xylia.Preview.Data.Models.Document;
using Xylia.Preview.Data.Models.Sequence;
using Xylia.Preview.UI.Controls;
using Xylia.Preview.UI.Controls.Helpers;
using Xylia.Preview.UI.Converters;
using Xylia.Preview.UI.Extensions;
using static Xylia.Preview.Data.Models.Item;
using static Xylia.Preview.Data.Models.Item.Costume;
using static Xylia.Preview.Data.Models.ItemRandomOptionGroup;

namespace Xylia.Preview.UI.GameUI.Scene.Game_Tooltip;
public partial class ItemTooltipPanel
{
	#region Constructors
	public ItemTooltipPanel()
	{
		InitializeComponent();
#if DEVELOP
		DataContext = Globals.GameData.Provider.GetTable<Item>()["Cash_Grocery_GuildMaterial_0007"];
#endif
	}
	#endregion

	#region Methods
	protected override void OnDataChanged(DependencyPropertyChangedEventArgs e)
	{
		#region Data		
		if (e.NewValue is not Item item) return;

		var jobs = item.EquipJobCheck.Where(x => x != JobSeq.JobNone);  // get job
		var jobfilter = !jobs.Any() && item.RandomOptionGroupId != 0;
		if (jobfilter) jobs = [SettingHelper.Default.Job];
		#endregion

		#region Common	
		TextArguments arguments = [null, item];
		ItemName.String.LabelText = item.ItemNameOnly;
		ItemIcon.SetExpansionImageProperties("BackgroundImage", item.BackgroundImage);
		ItemIcon.SetExpansionImageProperties("IconImage", item.FrontIcon);
		ItemIcon.SetExpansionImageProperties("UnusableImage", item.UnusableImage);
		ItemIcon.SetExpansionImageProperties("Grade_Image", null);
		ItemIcon.SetExpansionImageProperties("CanSaleItem", item.CanSaleItemImage);
		ItemIcon.SetExpansionVisibleFlag("AppearanceChanged", false);
		ItemIcon.SetExpansionVisibleFlag("SymbolImage", item.CustomDressDesignState == CustomDressDesignStateSeq.Disabled);
		ItemIcon.SetExpansionVisibleFlag("SymbolImage_Chacked", item.CustomDressDesignState == CustomDressDesignStateSeq.Activated);

		#region Substitute
		List<string> Substitute1 = [], Substitute2 = [];
		Substitute1.Add(item.Attributes.Get<Record>("main-info").GetText(arguments));
		Substitute2.Add(item.Attributes.Get<Record>("sub-info").GetText(arguments));

		#region Ability
		var data = new Dictionary<MainAbilitySeq, long>();

		var AttackPowerEquipMin = item.Attributes.Get<short>("attack-power-equip-min");
		var AttackPowerEquipMax = item.Attributes.Get<short>("attack-power-equip-max");
		data[MainAbilitySeq.AttackPowerEquipMinAndMax] = (AttackPowerEquipMin + AttackPowerEquipMax) / 2;

		var PveBossLevelNpcAttackPowerEquipMin = item.Attributes.Get<short>("pve-boss-level-npc-attack-power-equip-min");
		var PveBossLevelNpcAttackPowerEquipMax = item.Attributes.Get<short>("pve-boss-level-npc-attack-power-equip-max");
		data[MainAbilitySeq.PveBossLevelNpcAttackPowerEquipMinAndMax] = (PveBossLevelNpcAttackPowerEquipMin + PveBossLevelNpcAttackPowerEquipMax) / 2;

		var PvpAttackPowerEquipMin = item.Attributes.Get<short>("pvp-attack-power-equip-min");
		var PvpAttackPowerEquipMax = item.Attributes.Get<short>("pvp-attack-power-equip-max");
		data[MainAbilitySeq.PvpAttackPowerEquipMinAndMax] = (PvpAttackPowerEquipMin + PvpAttackPowerEquipMax) / 2;

		// HACK: Actually, the ability value is directly get
		foreach (var seq in Enum.GetValues<MainAbilitySeq>())
		{
			if (seq == MainAbilitySeq.None) continue;

			var name = seq.ToString().TitleLowerCase();
			var value = Convert.ToInt32(item.Attributes[name]);
			if (value != 0) data[seq] = value;
			else if (seq != MainAbilitySeq.AttackAttributeValue && seq != MainAbilitySeq.AttackCriticalDamageValue)
			{
				var value2 = Convert.ToInt32(item.Attributes[name + "-equip"]);
				if (value2 != 0) data[seq] = value2;
			}
		}

		// HACK: Actually, the MainAbilitySeq is not this sequence
		var MainAbility1 = item.Attributes.Get<MainAbilitySeq>("main-ability-1");
		var MainAbility2 = item.Attributes.Get<MainAbilitySeq>("main-ability-2");

		if (item is Gem)
		{
			var MainAbilitySeqFixed = item.Attributes.Get<ItemRandomAbilitySlot>("main-ability-fixed");
			var SubAbilityFixed = item.Attributes.Get<ItemRandomAbilitySlot>("sub-ability-fixed");
			var SubAbilityRandomCount = item.Attributes.Get<sbyte>("sub-ability-random-count");
			var SubAbilityRandom = item.Attributes.Get<ItemRandomAbilitySlot[]>("sub-ability-random");

			if (MainAbilitySeqFixed != null) Substitute1.Add(MainAbilitySeqFixed.Description);
			if (SubAbilityFixed != null) Substitute2.Add(SubAbilityFixed.Description);
			if (SubAbilityRandomCount > 0)
			{
				Substitute2.Add(StringHelper.Get("UI.ItemRandomOption.Undetermined", SubAbilityRandomCount));
				SubAbilityRandom.ForEach(x => Substitute2.Add(x.Description + " <Image imagesetpath='00015590.Tag_Random' enablescale='true' scalerate='1.2'/>"), true);
			}
		}
		else if (item is Grocery)
		{
			var SkillTrainByItem = item.Attributes.Get<SkillTrainByItem>("skill-train-by-item-for-transmit");
			SkillTrainByItem?.Ability.ForEach((seq, i) => data[seq] = SkillTrainByItem.AbilityValue[i]);
		}

		foreach (var ability in data)
		{
			if (ability.Key == MainAbilitySeq.None || ability.Value == 0) continue;

			var text = ability.Key.GetText(ability.Value);
			if (ability.Key == MainAbility1 || ability.Key == MainAbility2) Substitute1.Add(text);
			else Substitute2.Add(text);
		}
		#endregion

		#region Effect
		for (int i = 1; i <= 4; i++)
		{
			var EffectEquip = item.Attributes.Get<Record>("effect-equip-" + i);
			if (EffectEquip is null) continue;

			Substitute1.Add(EffectEquip.Attributes.Get<Record>("name3").GetText());
			Substitute2.Add(EffectEquip.Attributes.Get<Record>("description3").GetText());
		}
		#endregion

		CollectionSubstituteText.String.LabelText = LinqExtensions.Join(BR.Tag, Substitute1);
		CollectionSubstitute2Text.String.LabelText = LinqExtensions.Join(BR.Tag, Substitute2);
		ProbabilityText.String.LabelText = null;
		#endregion

		// SetItem
		SetItemEffect.Visibility = Visibility.Collapsed;
		if (item.SetItem != null)
		{
			SetItemEffect.Visibility = Visibility.Visible;
			SetItemEffect_Name.String.LabelText = item.SetItem.Name;
			SetItemEffect_Effect.String.LabelText = item.SetItem.Description;
		}

		#region Decompose
		var pages = DecomposePage.LoadFrom(item.DecomposeInfo);
		DecomposeDescription_Title.SetVisiable(pages.Count > 0);
		DecomposeDescription_Title.String.LabelText = (item is Grocery grocery2 && grocery2.GroceryType == Grocery.GroceryTypeSeq.RandomBox ?
			"UI.ItemTooltip.RandomboxPreview.Title" : "UI.ItemTooltip.Decompose.Title").GetText();
		DecomposeDescription.Children.Clear();
		if (pages.Count > 0) pages[0].Update(DecomposeDescription.Children);
		#endregion

		#region Description
		ItemDescription.String.LabelText = item.Attributes["description2"].GetText(arguments);
		ItemDescription_4_Title.String.LabelText = item.Attributes["description4-title"].GetText(arguments);
		ItemDescription_5_Title.String.LabelText = item.Attributes["description5-title"].GetText(arguments);
		ItemDescription_6_Title.String.LabelText = item.Attributes["description6-title"].GetText(arguments);
		ItemDescription_4.String.LabelText = item.Attributes["description4"].GetText(arguments);
		ItemDescription_5.String.LabelText = item.Attributes["description5"].GetText(arguments);
		ItemDescription_6.String.LabelText = item.Attributes["description6"].GetText(arguments);
		ItemDescription7.String.LabelText = LinqExtensions.Join(BR.Tag,
			item.Attributes["description7"].GetText(),
			string.Join(BR.Tag, item.ItemCombat.SelectNotNull(x => x.Value?.Description)),
			item.Attributes.Get<Record>("skill3")?.Attributes["description-weapon-soul-gem"]?.GetText());
		#endregion

		#region Required 
		var required = new List<string?>
		{
			"Name.Item.Required.Level".GetTextIf(item.Attributes.Get<sbyte>("equip-level") > 1, arguments),
			"Name.Item.Required.Result".GetTextIf(item.Attributes["equip-faction"] != null, ["Name.Item.Required.Faction".GetText(arguments)]),
			"Name.Item.Required.Result".GetTextIf(item.EquipRace != null || item.EquipSex != SexSeq2.All, ["Name.Item.Required.Race".GetText(arguments) + "Name.Item.Required.Sex".GetText(arguments)]),

			(item.Attributes.Get<sbyte>("skill-limit-level") > 0 ? item.Attributes.Get<sbyte>("skill-limit-level-max") > 0 ? "Name.Item.SkillLimitLevel" : "Name.Item.SkillLimitLevel.OnlyMin" : null) .GetText(arguments),
			"Name.Item.SkillLimitMasteryLevel".GetTextIf(item.Attributes.Get<sbyte>("skill-limit-mastery-level") > 0, arguments),
		};

		if (jobfilter) required.Add("UI.ItemRandomOption.EquipFilter.Warning".GetText());
		if (jobs.Any()) required.Add("Name.Item.Required.Result".GetText([string.Join("", jobs.Select(x => "Name.Item.Required.Job2".GetText([.. arguments, Job.GetJob(x)])))]));

		required.Add("UI.ItemTooltip.AccountUsed".GetTextIf(item.AccountUsed));
		required.Add(LinqExtensions.Join("Name.Item.Cannot.Comma".GetText(),
			item.CannotTrade && !item.Auctionable ? "Name.Item.Cannot.Trade.All.Global".GetText() :
			item.CannotTrade && item.Auctionable ? "Name.Item.Cannot.Trade.Player.Global".GetText() :
			item.CannotTrade ? "Name.Item.Cannot.Trade.Auction.Global".GetText() : null,
			item.CannotSell ? "Name.Item.Cannot.Sell.Global".GetText() : null,
			item.CannotDispose ? "Name.Item.Cannot.Dispose.Global".GetText() : null));

		ItemTooltipPanel_Required.String.LabelText = LinqExtensions.Join(BR.Tag, required);
		#endregion

		// Seal
		ItemTooltipPanel_SealEnable.SetVisiable(item.SealRenewalAuctionable);
		if (item.SealRenewalAuctionable)
		{
			var SealConsumeItem1 = item.Attributes.Get<Item>("seal-consume-item-1");
			var SealConsumeItem2 = item.Attributes.Get<Item>("seal-consume-item-2");
			var SealConsumeItemCount1 = item.Attributes.Get<short>("seal-consume-item-count-1");
			var SealConsumeItemCount2 = item.Attributes.Get<short>("seal-consume-item-count-2");
			// seal-acquire-item
			var SealKeepLevel = item.Attributes.Get<bool>("seal-keep-level");
			var SealEnableCount = item.Attributes.Get<sbyte>("seal-enable-count");

			ItemTooltipPanel_SealEnable.String.LabelText = (SealEnableCount == 0 ? "UI.Item.Tooltip.SealEnable" : "UI.Item.Tooltip.SealEnable.Count")
				.GetText([SealConsumeItem1, SealConsumeItemCount1, SealEnableCount]);
		}

		//UI.Tooltip.ItemStore.SellPrice.Plural
		//UI.Tooltip.ItemStore.SellPrice.Zero
		ItemTooltipPanel_Price.String.LabelText = "UI.Tooltip.ItemStore.SellPrice".GetText([item.Attributes["price"]]);
		#endregion

		#region Combat Holder
		Combat_Holder.Children.Clear();
		Combat_Holder.Visibility = Visibility.Collapsed;

		if (item.RandomOptionGroupId != 0)
		{
			Combat_Holder.Visibility = Visibility.Visible;
			var RandomOptionGroup = item.Provider.GetTable<ItemRandomOptionGroup>()[new ItemRandomOptionGroupKey(item.RandomOptionGroupId, jobs.FirstOrDefault())];
			if (RandomOptionGroup != null)
			{
				LinqExtensions.For(RandomOptionGroup.AbilityListTotalCount, (i) => ProbabilityText.String.LabelText += "UI.ItemRandomOption.SubAbility.Undetermined".GetText() + BR.Tag);
				LinqExtensions.For(RandomOptionGroup.SkillBuildUpGroupListTotalCount, (i) => ProbabilityText.String.LabelText += "UI.ItemRandomOption.SkillEnhancement.Undetermined".GetText() + BR.Tag);

				if (RandomOptionGroup.SkillTrainByItemListTotalCount > 0)
				{
					// title
					Combat_Holder.Children.Add(Combat_Holder_Title);
					Combat_Holder_Title.String.LabelText = RandomOptionGroup.SkillTrainByItemListTitle.GetText();

					foreach (var SkillTrainByItemList in RandomOptionGroup.SkillTrainByItemList.Values())
					{
						var ChangeSets = SkillTrainByItemList.ChangeSet.Values();
						if (ChangeSets.Count() > 1) Combat_Holder.Children.Add(new BnsCustomLabelWidget() { Text = StringHelper.Get("UI.ItemRandomOption.Undetermined", 1) });

						foreach (var SkillTrainByItem in ChangeSets)
						{
							var box = Combat_Holder.Children.Add(new HorizontalBox()
							{
								ToolTip = new BnsTooltipHolder(),
								DataContext = SkillTrainByItem.MainChangeSkill.Value,
								Margin = new Thickness(0, 0, 0, 3)
							}, FLayout.Anchor.Full);

							// icon
							box.Children.Add(new BnsCustomImageWidget
							{
								BaseImageProperty = SkillTrainByItem.Icon?.GetImage() ?? new ImageProperty("/Game/Art/UI/GameUI/Resource/GameUI_Icon/open_skill_traning.open_skill_traning"),
								Width = 32,
								Height = 32,
								Margin = new Thickness(0, 0, 5, 0),
								VerticalAlignment = System.Windows.VerticalAlignment.Top,
							});

							// description
							box.Children.Add(new BnsCustomLabelWidget()
							{
								Text = SkillTrainByItem.Description2,
								VerticalAlignment = System.Windows.VerticalAlignment.Center
							});
						}
					}
				}
			}
		}

		if (item is Grocery)
		{
			var SkillTrainByItem = item.Attributes.Get<SkillTrainByItem>("skill-train-by-item-for-transmit");
			if (SkillTrainByItem != null)
			{
				Combat_Holder.Visibility = Visibility.Visible;
				CollectionSubstituteText.String.LabelText += "UI.ItemTooltip.SkillTrainByItemExtract".GetText([SkillTrainByItem.ItemEquipType.GetText(), SkillTrainByItem.Job.GetText()]);

				var box = Combat_Holder.Children.Add(new HorizontalBox()
				{
					ToolTip = new BnsTooltipHolder(),
					DataContext = SkillTrainByItem.MainChangeSkill.Value,
					Margin = new Thickness(0, 0, 0, 3)
				}, FLayout.Anchor.Full);

				// icon
				box.Children.Add(new BnsCustomImageWidget
				{
					BaseImageProperty = SkillTrainByItem.Icon?.GetImage() ?? new ImageProperty("/Game/Art/UI/GameUI/Resource/GameUI_Icon/open_skill_traning.open_skill_traning"),
					Width = 32,
					Height = 32,
					Margin = new Thickness(0, 0, 5, 0),
					VerticalAlignment = System.Windows.VerticalAlignment.Top,
				});

				// description
				box.Children.Add(new BnsCustomLabelWidget()
				{
					Text = SkillTrainByItem.Description2,
					VerticalAlignment = System.Windows.VerticalAlignment.Center
				});
			}
		}
		else if (item is Weapon)
		{
			var SkillByEquipment = item.Attributes.Get<SkillByEquipment>("skill-by-equipment");
			if (SkillByEquipment != null)
			{
				Combat_Holder.Visibility = Visibility.Visible;
				Combat_Holder.Children.Add(Combat_Holder_Title);
				Combat_Holder_Title.String.LabelText = "UI.ItemTooltip.SkillChanged.Title".GetText();

				for (int i = 0; i < 4; i++)
				{
					var Skill3Id = SkillByEquipment.Skill3Id[i];
					if (Skill3Id == 0) continue;

					var Skill3 = Globals.GameData.Provider.GetTable<Skill3>()[new Ref(Skill3Id, 1)];

					var icon = new BnsCustomImageWidget
					{
						BaseImageProperty = Skill3?.FrontIcon,
						Width = 32,
						Height = 32,
						Margin = new Thickness(0, 0, 5, 0),
					};
					var description = new BnsCustomLabelWidget();
					description.String.LabelText = "UI.ItemGrowth.SkillByEquipment.Skill".GetText([null, Skill3, SkillByEquipment.GetTooltipText(i)]);

					var box = new HorizontalBox() { Margin = new Thickness(7, 0, 0, 3) };
					LayoutData.SetAnchors(box, FLayout.Anchor.Full);
					Combat_Holder.Children.Add(box);

					box.Children.Add(icon);
					box.Children.Add(description);
				}
			}
		}
		#endregion

		//item.Attributes.Get<ItemEvent>("event-info")?.IsExpiration;
	}
	#endregion

	#region Helpers
	internal sealed class DecomposePage
	{
		#region Fields
		public JobSeq Job;

		public Reward? DecomposeReward;
		public Item? DecomposeByItem2;
		public short DecomposeByItem2StackCount;
		#endregion

		#region Methods
		public static List<DecomposePage> LoadFrom(ItemDecomposeInfo info)
		{
			var pages = new List<DecomposePage>();

			// reward
			for (int index = 0; index < info.DecomposeReward.Length; index++)
			{
				var reward = info.DecomposeReward[index];
				if (reward is null) continue;

				pages.Add(new DecomposePage()
				{
					DecomposeReward = reward,
					DecomposeByItem2 = info.DecomposeByItem2[info.DecomposeRewardByConsumeIndex ? index : 0],
					DecomposeByItem2StackCount = info.DecomposeByItem2StackCount[info.DecomposeRewardByConsumeIndex ? index : 0],
				});
			}

			// job reward
			var job = SettingHelper.Default.Job;
			if (info.DecomposeJobRewards.TryGetValue(job, out var jobreward) && jobreward != null)
			{
				pages.Add(new DecomposePage()
				{
					Job = job,
					DecomposeReward = jobreward,
					DecomposeByItem2 = info.JobDecomposeByItem2[0],
					DecomposeByItem2StackCount = info.JobDecomposeByItem2StackCount[0],
				});
			}

			return pages;
		}

		public void Update(UIElementCollection collection)
		{
			ArgumentNullException.ThrowIfNull(DecomposeReward);

			var info = DecomposeReward.GetRewards().OrderByDescending(x => x.Data?.ItemGrade ?? 0);
			info.Where(x => x.Group is "fixed").DistinctBy(x => x.Data).ForEach(item =>
			{
				collection.Add(new BnsCustomLabelWidget()
				{
					Arguments = [null, item.Data, item.Min, item.Max],
					String = new StringProperty()
					{
						LabelText = (item.Min == item.Max ? item.Min == 1 ?
						"UI.ItemTooltip.RandomboxPreview.Fixed" :
						"UI.ItemTooltip.RandomboxPreview.Fixed.Min" :
						"UI.ItemTooltip.RandomboxPreview.Fixed.MinMax").GetText(),
					}
				});
			});
			info.Where(x => x.Group.StartsWith("smart-fixed-reward-") || x.Group is 
				"group-1" or "group-2" or "group-3" or "group-4" or "group-5" or "rare" or
				"smart-group-1-reward" or "smart-group-2-reward" or "smart-group-3-reward" or "smart-group-4-reward" or "smart-group-5-reward" or "smart-rare-reward")
			.DistinctBy(x => x.Data).ForEach(item =>
			{
				collection.Add(new BnsCustomLabelWidget()
				{
					Arguments = [null, item.Data, item.Min, item.Max],
					String = new StringProperty()
					{
						LabelText = (item.Min == item.Max ? item.Min == 0 ?
						"UI.ItemTooltip.RandomboxPreview.Random" :
						"UI.ItemTooltip.RandomboxPreview.Random.Min" :
						"UI.ItemTooltip.RandomboxPreview.Random.MinMax").GetText(),
					}
				});
			});
			info.Where(x => x.Group is "selected").ForEach(item =>
			{
				collection.Add(new BnsCustomLabelWidget()
				{
					Arguments = [null, item.Data, item.Min],
					String = new StringProperty() { LabelText = "UI.ItemTooltip.RandomboxPreview.Selected".GetText() }
				});
			});
		}
		#endregion
	}
	#endregion
}