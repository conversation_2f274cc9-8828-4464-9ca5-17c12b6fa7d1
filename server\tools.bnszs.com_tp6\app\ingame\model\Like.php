<?php
namespace app\ingame\model;

use think\Model;
use think\facade\Db;

class Like extends Model
{
    protected $pk = 'id';
    protected $table = 'bns_character_like';

    /**
     * 获取角色点赞数量
     */
    static public function getVoteCount($roleName, $serverId)
    {
        $count = 0;
        $sum = 0;
        
        // 获取基础点赞数量
        $res = Db::query("select count(`id`) as `count` from `bns_character_like` where `target_roleName`='$roleName' and `target_serverId`='$serverId' and `favor`>= 1 and `multi` is null;");
        $count += $res[0]['count'];
        
        // 获取多倍点赞数量
        $res2 = Db::query("select sum(`multi`) as `sum` from `bns_character_like` where `target_roleName` = '$roleName' and `target_serverId` = '$serverId' and `favor` >= 1;");
        $sum = $res2[0]['sum'] ?? 0;

        return $count + $sum;
    }

    /**
     * 获取我的投票数量,用于判断是否给某人点过赞
     */
    static public function getMyVoteCount($roleName, $serverId, $target_roleName, $target_serverId)
    {
        $res = Db::query("select `favor` from `bns_character_like` where `roleName` = '$roleName' and `serverId` = '$serverId' and `target_roleName` = '$target_roleName' and `target_serverId` = '$target_serverId';");
        if (empty($res)){
            return 0;
        }

        return $res[0]['favor'];
    }

    /**
     * 设置角色点赞
     */
    static public function setVote($roleName, $serverId, $target_roleName, $target_serverId, $state = 1)
    {
        Db::execute("insert into `bns_character_like`( `serverId`, `roleName`, `target_serverId`, `target_roleName`, `favor`, `updatetime`) VALUES ( '$serverId','$roleName','$target_serverId','$target_roleName','$state',now()) ON DUPLICATE KEY UPDATE `favor`='$state',`updatetime`=now();");
    }

    /**
     * 取消角色点赞
     */
    static public function cancelVote($roleName, $serverId, $target_roleName, $target_serverId)
    {
        Db::execute("update `bns_character_like` set `favor`='0',`updatetime`=now() where `roleName` = '$roleName' and `serverId` = '$serverId' and `target_roleName` = '$target_roleName' and `target_serverId` = '$target_serverId';");
    }

    /**
     * 获取角色点赞记录
     */
    static public function getVoteHistory($target_roleName, $target_serverId)
    {
        $res = Db::query("select * from `bns_character_like` where `target_roleName` = '$target_roleName' and `target_serverId` = '$target_serverId' and `favor` >= 1 order by id desc LIMIT 50");
        
        if (empty($res)) return [];

        return $res;
    }
    
    /**
     * 获取用户点赞的角色列表
     */
    static public function getUserLikedCharacters($roleName, $serverId, $limit = 20)
    {
        $res = Db::query("select `target_roleName`, `target_serverId`, `favor`, `updatetime` from `bns_character_like` where `roleName` = '$roleName' and `serverId` = '$serverId' and `favor` >= 1 order by updatetime desc LIMIT $limit");
        
        return $res ?: [];
    }
    
    /**
     * 获取角色的粉丝列表
     */
    static public function getCharacterFans($target_roleName, $target_serverId, $limit = 50)
    {
        $res = Db::query("select `roleName`, `serverId`, `favor`, `updatetime`, `multi` from `bns_character_like` where `target_roleName` = '$target_roleName' and `target_serverId` = '$target_serverId' and `favor` >= 1 order by updatetime desc LIMIT $limit");
        
        return $res ?: [];
    }
    
    /**
     * 检查是否已经点赞
     */
    static public function hasLiked($roleName, $serverId, $target_roleName, $target_serverId)
    {
        $count = static::getMyVoteCount($roleName, $serverId, $target_roleName, $target_serverId);
        return $count > 0;
    }
    
    /**
     * 切换点赞状态
     */
    static public function toggleLike($roleName, $serverId, $target_roleName, $target_serverId)
    {
        $currentState = static::getMyVoteCount($roleName, $serverId, $target_roleName, $target_serverId);
        
        if ($currentState > 0) {
            // 已点赞，取消点赞
            static::cancelVote($roleName, $serverId, $target_roleName, $target_serverId);
            return false;
        } else {
            // 未点赞，添加点赞
            static::setVote($roleName, $serverId, $target_roleName, $target_serverId, 1);
            return true;
        }
    }
    
    /**
     * 获取点赞统计信息
     */
    static public function getLikeStats($target_roleName, $target_serverId)
    {
        $totalLikes = static::getVoteCount($target_roleName, $target_serverId);
        $uniqueLikers = Db::query("select count(distinct `roleName`, `serverId`) as count from `bns_character_like` where `target_roleName` = '$target_roleName' and `target_serverId` = '$target_serverId' and `favor` >= 1");
        $recentLikes = Db::query("select count(*) as count from `bns_character_like` where `target_roleName` = '$target_roleName' and `target_serverId` = '$target_serverId' and `favor` >= 1 and `updatetime` >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
        
        return [
            'total_likes' => $totalLikes,
            'unique_likers' => $uniqueLikers[0]['count'] ?? 0,
            'recent_likes' => $recentLikes[0]['count'] ?? 0
        ];
    }
    
    /**
     * 获取热门角色排行榜
     */
    static public function getPopularCharacters($limit = 20, $serverId = 0)
    {
        $whereCondition = '';
        if ($serverId > 0) {
            $whereCondition = "WHERE `target_serverId` = '$serverId'";
        }
        
        $res = Db::query("
            SELECT `target_roleName`, `target_serverId`, 
                   COUNT(*) + COALESCE(SUM(`multi`), 0) as total_likes,
                   COUNT(DISTINCT `roleName`, `serverId`) as unique_likers
            FROM `bns_character_like` 
            $whereCondition AND `favor` >= 1
            GROUP BY `target_roleName`, `target_serverId`
            ORDER BY total_likes DESC
            LIMIT $limit
        ");
        
        return $res ?: [];
    }
    
    /**
     * 删除过期的点赞记录
     */
    static public function cleanExpiredLikes($days = 365)
    {
        return Db::execute("DELETE FROM `bns_character_like` WHERE `updatetime` < DATE_SUB(NOW(), INTERVAL $days DAY)");
    }

    /**
     * 获取用户今日点赞次数
     */
    static public function getTodayLikeCount($roleName, $serverId)
    {
        $today = date('Y-m-d');
        $res = Db::query("SELECT COUNT(*) as count FROM `bns_character_like` WHERE `roleName` = ? AND `serverId` = ? AND `favor` >= 1 AND DATE(`updatetime`) = ?", [$roleName, $serverId, $today]);

        return $res[0]['count'] ?? 0;
    }

    /**
     * 获取用户点赞历史（分页）
     */
    static public function getUserLikeHistory($roleName, $serverId, $page = 1, $limit = 20)
    {
        $offset = ($page - 1) * $limit;
        $res = Db::query("SELECT `target_roleName`, `target_serverId`, `favor`, `updatetime`, `multi` FROM `bns_character_like` WHERE `roleName` = ? AND `serverId` = ? AND `favor` >= 1 ORDER BY `updatetime` DESC LIMIT ?, ?", [$roleName, $serverId, $offset, $limit]);

        return $res ?: [];
    }

    /**
     * 批量获取角色点赞统计
     */
    static public function getBatchLikeStats($characters)
    {
        if (empty($characters)) {
            return [];
        }

        $conditions = [];
        foreach ($characters as $char) {
            $conditions[] = "(`target_roleName` = '{$char['roleName']}' AND `target_serverId` = '{$char['serverId']}')";
        }
        $whereClause = implode(' OR ', $conditions);

        $res = Db::query("
            SELECT `target_roleName`, `target_serverId`,
                   COUNT(*) + COALESCE(SUM(`multi`), 0) as total_likes
            FROM `bns_character_like`
            WHERE ($whereClause) AND `favor` >= 1
            GROUP BY `target_roleName`, `target_serverId`
        ");

        return $res ?: [];
    }
}
