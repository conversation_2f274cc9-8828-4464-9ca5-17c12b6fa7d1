var BnsWeb = (function (W, $) {
	return W;
}(BnsWeb || {}, jQuery));

BnsWeb.Stat = (function (S, $) {
	S.showPointsEffects = function(records) {
		for (var idx in records) {	
			$("#bonus-"+records[idx].type+" .split-bonus-list").append('<li class="disabled"><span class="bonus">'+records[idx].point+'</span> '+records[idx].description+'</li>');
		}
	},
	
	S.showAbilities = function (records, type) {
		for (var idx in records.base_ability) {
			$("#"+type+"-stat #base-"+idx).text(records.base_ability[idx]);
			if (idx.indexOf("_rate") >= 0) $("#"+type+"-stat #base-"+idx).append("%");
		}
		for (var idx in records.equipped_ability) {
			$("#"+type+"-stat #equip-"+idx).text(records.equipped_ability[idx]);
			if (idx.indexOf("_rate") >= 0) $("#"+type+"-stat #equip-"+idx).append("%");
		}
		for (var idx in records.total_ability) {		
			if (idx.indexOf("_level") >= 0) {
				$("#"+type+"-stat #total-"+idx).text(records.total_ability[idx]+ Messages.get("character.jsp.profile.text.stat.depth_text"));
			} else {
				$("#"+type+"-stat #total-"+idx).text(records.total_ability[idx]);
				if (idx.indexOf("_rate") >= 0) $("#"+type+"-stat #total-"+idx).append("%");
			}
		}

		if (type == 'me') {
			//if (pointeffects != "undefined") {
				//BnsWeb.Stat.showPointsEffects(pointeffects);
							
				var defensePoint = 0;
				var offensePoint = 0;
				var defenseEffectCnt = 0;
				var offenseEffectCnt = 0;
				var effectType;
				for (var idx in records.point_ability) {		
					if (idx == 'picks') {
						if (records.point_ability[idx] != null) {
							for (var pickIdx = 0 ; pickIdx < records.point_ability[idx].length ; pickIdx++) {
								if (records.point_ability[idx][pickIdx].point > 0) {
									$("#point-point_pick"+records.point_ability[idx][pickIdx].slot).text(records.point_ability[idx][pickIdx].point+"P").removeClass("disabed");
									
									effectType = $("#point-point_pick"+records.point_ability[idx][pickIdx].slot).parent("div").parent("div").parent("div").attr("class");
									$("#effect-"+effectType).find(".split-select-list").append('<li><strong class="name effect-'+records.point_ability[idx][pickIdx].slot+'">'+records.point_ability[idx][pickIdx].name+' '+Messages.get("character.jsp.profile.text.stat.point_text")+' '+records.point_ability[idx][pickIdx].tier+'</strong><p class="text">'+((records.point_ability[idx][pickIdx].description != null) ? records.point_ability[idx][pickIdx].description:'')+'</p></li>');
						
									if (effectType == 'attack') {
										offenseEffectCnt++;
									} else if (effectType == 'defense') {
										defenseEffectCnt++;
									}
								} 
							}
						}
					} else {
						$("#"+type+"-stat #point-"+idx).text(records.point_ability[idx]);
						if (idx.indexOf("_point") >= 0) $("#"+type+"-stat #point-"+idx).append("P");
						
						if (idx == 'offense_point') {
							offensePoint = records.point_ability[idx];
						}
						if (idx == 'defense_point') {
							defensePoint = records.point_ability[idx];
						}						
					}
				}

				if (offenseEffectCnt > 0) {
					$("#effect-attack").show();
				}
				if (defenseEffectCnt > 0) {
					$("#effect-defense").show();
				}
				
				$("#me-stat #bonus-offense .split-bonus-list").find("li").each(function() {
					if (offensePoint >= parseInt($(this).find("span").text())) {
						$(this).removeClass("disabled");
					}
				});
		
				$("#me-stat #bonus-defense .split-bonus-list").find("li").each(function() {
					if (defensePoint >= parseInt($(this).find("span").text())) {
						$(this).removeClass("disabled");
					}
				});
			//}
		}
		
		return true;
	},
	S.compareAbilities = function () {
		$("#me-stat .stat-title .stat-point").each(function() {
			if (typeof $(this).attr("id") != "undefined") {
				var id = $(this).attr("id");
	
				var mePoint = parseInt($("#me-stat .stat-title").find("#"+id).text().replace('Messages.get("character.jsp.profile.text.stat.depth_text")', '').replace('%', ''));
				var otherPoint = parseInt($("#other-stat .stat-title").find("#"+id).text().replace('Messages.get("character.jsp.profile.text.stat.depth_text")', '').replace('%', ''));
	
				id = id.replace('total-', '');
	
				if (mePoint > otherPoint) {
					$("#me-stat").find("#compare-"+id).addClass("morethan");
				} else if (mePoint < otherPoint) {
					$("#me-stat").find("#compare-"+id).addClass("lessthan");
				} else {
					$("#me-stat").find("#compare-"+id).addClass("equal");
				}
			}
		});
	}
	return S;
}(BnsWeb.Stat || {}, jQuery));