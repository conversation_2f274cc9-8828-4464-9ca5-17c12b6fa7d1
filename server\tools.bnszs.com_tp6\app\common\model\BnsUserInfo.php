<?php
namespace app\common\model;

use think\Model;
use think\facade\Db;

class BnsUserInfo extends Model
{
    protected $pk = 'id';
    protected $table = 'bnsuserinfo';

    /**
     * 查询角色详细信息
     */
    public static function detail($serverId, $roleName) {
        
        $roleName = urldecode($roleName);
        // 先搜索指定服务器数据，若不存在再查询同大区服务器数据
        $user = static::where(['worldid' => $serverId, 'name' => $roleName])->find();
        
        if(empty($user)) {
            $hasuser = static::where(['name' => $roleName])->find();
            if(empty($hasuser)) {
                return null;
            }
        }

        if(empty($user)) {
             try 
             {
                 // 从服务器数据库上查询当前服务器的大区 Id
                 $areaInfo = Db::query("SELECT `server_id` as `area` FROM `bns_server` WHERE `area_id`=$serverId LIMIT 1");
                 if (!empty($areaInfo)) {
                     $areaId = $areaInfo[0]["area"];
                     
                     $areaInfos = Db::query("SELECT `area_id` as `server` FROM `bns_server` WHERE `server_id`=$areaId AND `area_id`<>$serverId");
                 
                     foreach ($areaInfos as $value)
                     {
                           $user = static::where(['worldid' => $value["server"], 'name' => $roleName])->find();
                      
                           if(!empty($user)) return $user->toArray();
                     }
                 }
             } 
             catch(\Exception $e)
             {
                 // echo $e;
             }
        }
        
        if(!empty($user)) {
            return $user->toArray();
        } else {
            return null;
        }
    }
    
    /**
     * 根据QQ号获取角色列表
     */
    public static function GetRoles($uin, $serverId) {
        if (empty($uin)) {
            return [];
        }
        
        $roles = static::where('iuin', $uin)
            ->where('worldid', $serverId)
            ->field('name, worldid, job, level, mastery_level, allscore, newpcid')
            ->order('allscore DESC')
            ->select();
            
        return $roles ? $roles->toArray() : [];
    }
    
    /**
     * 搜索角色
     */
    public static function searchCharacter($keyword, $serverId = 0, $limit = 20) {
        $query = static::where('name', 'like', '%' . $keyword . '%');
        
        if ($serverId > 0) {
            $query->where('worldid', $serverId);
        }
        
        $result = $query->field('name, worldid, job, level, mastery_level, allscore, newpcid')
            ->order('allscore DESC')
            ->limit($limit)
            ->select();
            
        return $result ? $result->toArray() : [];
    }
    
    /**
     * 获取角色基本信息
     */
    public static function getBasicInfo($serverId, $roleName) {
        $user = static::where(['worldid' => $serverId, 'name' => $roleName])
            ->field('name, worldid, job, level, mastery_level, allscore, newpcid, iuin')
            ->find();
            
        return $user ? $user->toArray() : null;
    }
    
    /**
     * 更新角色信息
     */
    public static function updateCharacter($serverId, $roleName, $data) {
        return static::where(['worldid' => $serverId, 'name' => $roleName])
            ->update($data);
    }
    
    /**
     * 获取服务器角色数量
     */
    public static function getServerPlayerCount($serverId) {
        return static::where('worldid', $serverId)->count();
    }
    
    /**
     * 获取职业分布
     */
    public static function getJobDistribution($serverId = 0) {
        $query = static::field('job, COUNT(*) as count');
        
        if ($serverId > 0) {
            $query->where('worldid', $serverId);
        }
        
        $result = $query->group('job')
            ->order('count DESC')
            ->select();
            
        return $result ? $result->toArray() : [];
    }
    
    /**
     * 获取等级分布
     */
    public static function getLevelDistribution($serverId = 0) {
        $query = static::field('level, COUNT(*) as count');
        
        if ($serverId > 0) {
            $query->where('worldid', $serverId);
        }
        
        $result = $query->group('level')
            ->order('level DESC')
            ->select();
            
        return $result ? $result->toArray() : [];
    }
    
    /**
     * 获取战力分布
     */
    public static function getPowerDistribution($serverId = 0) {
        $query = static::field('
            CASE 
                WHEN allscore >= 100000 THEN "100000+"
                WHEN allscore >= 80000 THEN "80000-99999"
                WHEN allscore >= 60000 THEN "60000-79999"
                WHEN allscore >= 40000 THEN "40000-59999"
                WHEN allscore >= 20000 THEN "20000-39999"
                ELSE "0-19999"
            END as power_range,
            COUNT(*) as count
        ');
        
        if ($serverId > 0) {
            $query->where('worldid', $serverId);
        }
        
        $result = $query->group('power_range')
            ->order('MIN(allscore) DESC')
            ->select();
            
        return $result ? $result->toArray() : [];
    }
}
