
.audiojs  {
  font-size: 12px;
  overflow: hidden;
}
h1, h2, h3, h4, h5, h6 {
    font-size: 100%;
}
body{
    font-family: Arial, Helvetica, sans-serif;
}
em, i {
    font-style: normal;
    text-align: left;
    font-size: inherit;
}
body, html, h1, h2, h3, h4, h5, h6, ul, ol, li, dl, dt, dd, header, menu, section, p, input, td, th, ins {
    padding: 0;
    margin: 0;
}

.audiojs  .mid {
    float: left;
    margin: 0 9px;
}



/* 背景图 */
.bg{background: url(/res/images/audio_sprite.png) no-repeat 0 9999px;}
/* loading */
.loading{width:12px;height:12px;background: url(/res/images/loading.gif) no-repeat 0 0;}
/* 播放器 */
.player {border-radius:2px;box-shadow:0 0 10px #ccc;background:#fff;margin: 10px;}
/* 封面 */
.cover,.cover img{border-bottom-left-radius:1px;border-top-left-radius:1px;}
.cover{float:left;width:90px;height:90px;}
.cover img{width:100%;height:100%;border-top-left-radius: 2px;}

.cover-sm{width:66px;height:66px;}
/* 歌曲标题 */
h2{position:relative;left:0;top:0;height:36px;margin-bottom:10px;line-height:36px;padding-left:24px;font-size:14px;font-weight:normal;zoom:1;}
h2 .logo{position:absolute;left:0;top:10px;}
h2 .title{padding-top: 10px;line-height:18px;}
h2 .title .sub{font-size:12px;color:#666;}

.prev,.next{width:9px;height:10px;cursor:pointer;}
/* 上一首 */
.prev{background-position:-33px 0;}
.prev:hover{background-position:-33px -28px;}
/* 下一首 */
.next{background-position:-58px 0;}
.next:hover{background-position:-58px -28px;}
.play,.pause{width:14px;height:18px;cursor:pointer;}
/* 播放 */
.play{background-position:0 0;}
.play:hover{background-position:0 -31px;}
/* 暂停 */
.pause{background-position:-36px -63px;}
.pause:hover{background-position:-66px -63px;}

.play-sm,.pause-sm{width:10px;height:14px;cursor:pointer;}
/* 播放 */
.play-sm{background-position:0 -98px;}
.play-sm:hover{background-position:0 -188px;}
/* 暂停 */
.pause-sm{background-position:-30px -98px;}
.pause-sm:hover{background-position:-67px -98px;}
.play-bg{width:32px;height:32px;background-position:-68px -118px;cursor:pointer;}
.play-bg:hover{background-position:-68px -167px;}
.pause-bg{width:20px;height:20px;background-position: 0 -60px;cursor:pointer;}
.pause-bg:hover{background-position:0 -157px;}

.logo{display:inline-block;width:28px;height:28px;vertical-align:middle;cursor:pointer;background-image:url('/res/images/bnszs.png');background-size: 100%;}

/* 播放条 */
.scrubber,.scrubber .progress{position:relative;left:0;top:2px;height:2px;background:#cdcdcd;}
.scrubber .progress,.scrubber .thumb{position:absolute;left:0;top:0;}
.scrubber .progress{background:#e12828;}
.scrubber .thumb{right:-4px;left:auto;top:-3px;width:8px;height:8px;background-position:-92px -14px;cursor:pointer;/*pointer-events:none;*/}
.ctrlBox{float:right;width:200px;padding:10px 10px 0 0;position:relative;}

.oprBox{position:relative;height: 20px;margin-top:10px;line-height: 20px;}
.oprBox .time{position:absolute;left:0;font-size:11px;color:#999;}
.oprBox .btnGroup{position:relative;margin:0 auto;width:72px;}
.oprBox .btnGroup .prev,.oprBox .btnGroup .next{position:absolute;top:4px;}
.oprBox .btnGroup .prev{left:0;}
.oprBox .btnGroup .next{right:0;}
.oprBox .btnGroup .mid{margin:0 auto;}
.oprBox .open{position:absolute;right:0;top:4px;width:13px;height:10px;background-position:-58px -14px;cursor:pointer;opacity:0.8;filter:alpha(opacity=80);}
.oprBox .open:hover{opacity:1;filter:alpha(opacity=100);}
.oprBox .open.z-dis{opacity:0.3;filter:alpha(opacity=30);cursor: default;}

.list{height:340px;border-top:1px solid #e6e6e6;}
div.list.z-close{height:0 !important;overflow: hidden;display:none;}
.list ul{height:100%;overflow:hidden;position:relative;}
.list .box{height:300px;position:relative;}
.list .box .track{position:absolute;right:0;top:0;width:7px;height:100%;}
.list .box .scroll{position:absolute;top:0;right:0;width:7px;height:114px;background:#dadada;border-radius:3px;z-index:9999;}
.list li{height:30px;position:relative;line-height:30px;padding-right:18px;float:left;}
.list li.odd{background:#f7f7f7;}
.list li.z-sel{background:#e9e9e9;}
.list li .cur{width:3px;height:22px;position:absolute;left:0;top:4px;display:none;background:#df2d2d;}
.list li.z-sel .cur{display:block;}
.list .index,.list .name{float:left;}
.list .index{width:40px;text-align:center;color:#999;}
.list .by{float:right;color:#666;text-align:right;}
.list .foot{height:40px;line-height:40px;padding:0 10px;background:#f1f1f1;cursor:pointer;}
.list .logo{float:left;margin-top:11px;}
.list .yyy{float:left;margin-left:10px;font-size:14px;}
.list .slogan{float:right;color:#999;}
/* 小尺寸播放器 */
.player-sm{height:32px;}
.player-sm .head{float:left;width:30px;height:30px;padding:2px 0 0 2px;background:#f3f3f4;border-top-left-radius: 2px;border-bottom-left-radius: 2px;border-right: 1px solid #dbdbdb;}
.player-sm .mid{float:left;margin:0 9px;width: calc(100% - 95px)}
.player-sm h3{height:24px;line-height:24px;font-weight:normal;overflow:hidden;pointer-events:none;}
.player-sm h3 .marquee{height:24px;line-height:24px;font-weight:normal;overflow:hidden;width: calc(100% - 70px);}
.player-sm h3 .time{height:24px;line-height:24px;font-weight:normal; float:right;pointer-events:none;}
.player-sm .play-pause{float:right;height:16px;padding-left:10px;margin:8px;border-left:1px solid #d7d7d7;}
.player-sm .play-sm,.player-sm .pause-sm{margin-top:1px;}

/* 中尺寸播放器 */
.player-mid{height:66px;}
.player-mid .ctrlBox{padding-top:7px;}
.player-mid .ctrlBox .bar{width: 192px;}
.player-mid .time{position:absolute;color:#999;right:10px;top:46px;font-size: 11px;}
.player-mid .play-bg{position:absolute;left:17px;top:17px;}
.player-mid .pause-bg{position:absolute;left:42px;top:42px;}
.player-mid .mask{position: absolute;left:0;top:0;width:100%;height:100%;background:#000;opacity:0.4;filter:alpha(opacity=40);}

.audiojs p {
    display: none;
}
.audiojs .play {
    display: block;
}
.playing .play, .playing .loading, .playing .error {
    display: none;
}
.playing .pause {
    display: block;
}
.loading .loading {
    display: block;
}

.progress_volume{position: relative; width:6px;display: inline-block;margin-top:3px;}
.progress_volume_bg{height: 24px; /*border: 1px solid #cdcdcd;*/ overflow: hidden;background-color:#cdcdcd;border-radius: 5px;}
.progress_volume_bar{background: linear-gradient(to top, rgb(80, 218, 228), rgb(158, 54, 199));
box-shadow: 0 3px 3px -5px rgb(80, 218, 228), 0 2px 5px rgb(158, 54, 199);width: 6px; height: 0; border-radius: 5px;margin-top: 0px;}
.progress_volume_btn:hover{border-color:#F7B824;}
.progress_volume_btn{width: 10px; height: 10px; position: absolute;background:#fff; border-radius: 50%;
left: -2px; margin-top: 0px; top: 19px; cursor: pointer;border:1px #ddd solid;box-sizing:border-box;}
.progress_volume_btn:hover{border-color:#F7B824;}