﻿<Page x:Class="Xylia.BnsHelper.Views.Pages.AssetPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
	  xmlns:hc="https://handyorg.github.io/handycontrol">

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="170" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- Data -->
        <ListBox ItemsSource="{Binding Folders.Children}" SelectedItem="{Binding SelectedFolder}">
            <ListBox.ItemTemplate>
                <DataTemplate>
                    <Grid Margin="5">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Grid.InputBindings>
                            <MouseBinding MouseAction="LeftClick" Command="{Binding TestCommand}" />
                        </Grid.InputBindings>

                        <TextBlock Grid.Column="0" Text="{Binding Name}" />
                    </Grid>
                </DataTemplate>
            </ListBox.ItemTemplate>
        </ListBox>
        <ListBox ItemsSource="{Binding Folder.Children}" SelectedItem="{Binding SelectedMod}" Grid.Column="1">
            <ListBox.ItemContainerStyle>
                <Style TargetType="ListBoxItem" BasedOn="{StaticResource ListBoxItemBaseStyle}">
                    <Setter Property="HorizontalContentAlignment" Value="Stretch" />
                    <Setter Property="ToolTip">
                        <Setter.Value>
                            <TextBlock Padding="3 0">
								<Run Text="{Binding Setting.Author}" Foreground="{StaticResource PrimaryBrush}"/>
                                <Run Text="最后更新于" />
								<Run Text="{Binding LastUpdateTime,Converter={StaticResource TimeConverter},StringFormat=d}" />
										
								<LineBreak/><LineBreak/>
								<Run Text="{Binding Setting.Describe}" />
                            </TextBlock>
                        </Setter.Value>
                    </Setter>
                </Style>
            </ListBox.ItemContainerStyle>
            <ListBox.ItemTemplate>
                <DataTemplate>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Grid.InputBindings>
                            <MouseBinding MouseAction="LeftClick" Command="{Binding TestCommand}" />
                        </Grid.InputBindings>

                        <TextBlock Grid.Column="0" Text="{Binding Name}" />
                        <TextBlock Grid.Column="1" Margin="7 0 5 0" Text="{Binding Size,Converter={StaticResource SizeConverter}}" />
                        <Button Command="{Binding InstallCommand}" Grid.Column="3" HorizontalAlignment="Right">
                            <Button.Style>
                                <Style TargetType="Button" BasedOn="{StaticResource ButtonDefault.Small}">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsNormal}" Value="True">
                                            <Setter Property="Content" Value="卸载" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding IsUpdatable}" Value="True">
                                            <Setter Property="Content" Value="有更新" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding IsAvailable}" Value="True">
                                            <Setter Property="Content" Value="安装" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </Button.Style>
                        </Button>
                    </Grid>
                </DataTemplate>
            </ListBox.ItemTemplate>
        </ListBox>

        <!-- Tooltip -->
        <Grid Grid.Row="1" Grid.ColumnSpan="99" Margin="5 3" >
            <TextBlock Foreground="{StaticResource PrimaryTextBrush}" FontSize="13">
				<Run Text="使用说明，不看后悔" FontSize="14.5" /><LineBreak />
				<Run Text="① 模组由剑灵小助手团队制作，暂不制作服装类模组" /><LineBreak />
				<Run Text="② 使用模组必须先行"/><Run Foreground="{StaticResource DangerBrush}" Text="安装剑灵小助手插件"/><Run Text="，否则会出现1002错误"/><LineBreak />
				<Run Text="③ 游戏更新后插件会被恢复，请重新安装插件或删除所有模组文件" /><LineBreak />
                <Run Text="④ 使用模组出现的任何问题请自行承担" />
            </TextBlock>

            <StackPanel Orientation="Vertical" HorizontalAlignment="Right">
                <Button Content="{DynamicResource AssetPage_OpenModFolder}" Command="{Binding OpenModFolderCommand}" Style="{StaticResource ButtonPrimary}" Margin="0 2" />
                <Button Content="{DynamicResource AssetPage_Font}" Command="{Binding PackFontCommand}" />
            </StackPanel>
        </Grid>
    </Grid>
</Page>