<?php
namespace app\common\controller;

use think\App;
use think\Request;
use think\facade\Cache;

/**
 * API基础控制器
 */
class ApiBase extends BaseController
{
    public function __construct(App $app, Request $request) {
        parent::__construct($app, $request);
        
        // 接口鉴权
        if (!isset($_SERVER['HTTP_USER_AGENT']) || $_SERVER['HTTP_USER_AGENT'] !== 'bnszs') {
            abort(403, 'Forbidden');
        }

        // 检查请求频率
        $this->checkRequestFrequency();
    }

    /**
     * 检查请求频率
     */
    protected function checkRequestFrequency() {
        try {
            $redis = Cache::store('redis')->handler();
            $clientIP = $this->request->ip();
            $ipFreqKey = "ip_freq:{$clientIP}";
            $freqCount = $redis->get($ipFreqKey) ?: 0;
            
            if ($freqCount > 20) {
                abort(403, '请求频繁，请稍后再试');
            }
          
            $redis->incr($ipFreqKey);
            if ($freqCount == 0) {
                $redis->expire($ipFreqKey, 300); // 5分钟过期
            }
        } catch (\Exception $e) {
            // 如果Redis连接失败，记录日志但不阻止请求
            trace('Redis连接失败: ' . $e->getMessage(), 'error');
        }
    }

    /**
     * 记录日志
     * @param string $text
     */
    protected function log($text) {
        $path = sprintf("%s/log/%s/", $this->app->getRuntimePath(), date("Ym"));
        if (!is_dir($path)) {
            mkdir($path, 0777, true);
        }

        $time = date('Y-m-d H:i:s');
        $log = sprintf("[%s] %s|%s\n", $time, $text, json_encode($_SERVER));
        file_put_contents($path . date("d") . '.log', $log, FILE_APPEND);
    }

    /**
     * 验证token
     * @param string $app
     * @return bool
     */
    protected function check($app) {
        $time = $this->request->param('time');
        $token = $this->request->param('token');

        // 设置每个token的过期时间为60秒
        if (time() - intval($time) > 60) {
            abort(401, '请将当前设备时间同步为北京时间。');
        }

        // 验证token
        if (!isset($token) || empty($token)) {
            return false;
        }
        
        if (md5(md5($app) . md5($time)) !== $token) {
            return false;
        }

        return true;
    }

    /**
     * 请求日志
     */
    public function request_log() {
        ini_set('display_errors', 1);
        error_reporting(E_ALL);

        // data
        $requestData = ($_SERVER["CONTENT_TYPE"] ?? '') === 'text/plain; charset=utf-8' ?
            file_get_contents("php://input") : $_REQUEST;
        $requestTime = date('Y-m-d H:i:s');
        $clientIP = $this->request->ip();

        // write
        $path = $this->app->getRuntimePath() . 'log/request/';
        if(!is_dir($path)) {
            mkdir($path, 0777, true);
        }

        $text = sprintf("%s|%s|%s\n", $requestTime, $clientIP, json_encode($requestData));
        $text2 = sprintf("%s %s\n", $requestTime, $clientIP);
        file_put_contents($path . date("Y-m-d") . '.log', $text, FILE_APPEND);
        file_put_contents($path . 'request_ip.log', $text2, FILE_APPEND);
    }

    /**
     * HTTP摘要认证解析
     */
    function http_digest_parse($txt) {
        $needed_parts = array('nonce'=>1, 'nc'=>1, 'cnonce'=>1, 'qop'=>1, 'username'=>1, 'uri'=>1, 'response'=>1);
        $data = array();

        preg_match_all('@(\w+)=(?:([\'"])([^$2]+)$2|([^\s,]+))@', $txt, $matches, PREG_SET_ORDER);
        foreach ($matches as $m) {
            $data[$m[1]] = $m[3] ? trim($m[3],"\",'") : trim($m[4],"\",'");
            unset($needed_parts[$m[1]]);
        }
        return $needed_parts ? false : $data;
    }
}
