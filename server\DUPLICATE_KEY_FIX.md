# 主键冲突错误修复说明

## 问题描述

在GO服务端的签到功能中出现了数据库主键冲突错误：

```
Error 1062 (23000): Duplicate entry '9-4' for key 'PRIMARY'
```

**错误位置：** `server/internal/service/lucky_service.go:251`

**错误原因：**
1. `user_draw` 表使用复合主键：`uid` + `schedule`（用户ID + 活动ID）
2. 在高并发情况下，多个请求同时尝试为同一用户创建签到记录
3. 原代码使用 `FirstOrCreate` 方法，在并发环境下存在竞态条件

## 解决方案

### 方案一：使用 INSERT ... ON DUPLICATE KEY UPDATE（推荐）

使用MySQL的 `INSERT ... ON DUPLICATE KEY UPDATE` 语法，这是处理并发插入的最佳实践：

```go
// 使用 INSERT ... ON DUPLICATE KEY UPDATE 语法避免主键冲突
currentTime := time.Now()

sql := `
    INSERT INTO user_draw (uid, schedule, extra, day, point, number, today, time) 
    VALUES (?, ?, 0, 1, 1, 0, 0, ?)
    ON DUPLICATE KEY UPDATE 
        uid = uid  -- 保持原值，这样可以触发查询但不修改数据
`

result := s.db.Exec(sql, uid, activityID, currentTime)
```

**优点：**
- 原子操作，完全避免竞态条件
- 性能最优，只需要一次数据库操作
- MySQL原生支持，稳定可靠

### 方案二：事务 + 双重检查（备选）

```go
err := s.db.Transaction(func(tx *gorm.DB) error {
    // 再次检查记录是否存在
    var existingRecord model.UserDraw
    if err := tx.Where("uid = ? AND schedule = ?", uid, activityID).First(&existingRecord).Error; err == nil {
        userDraw = existingRecord
        return nil
    }
    
    // 创建新记录，如果冲突则查询现有记录
    if err := tx.Create(&userDraw).Error; err != nil {
        if strings.Contains(err.Error(), "Duplicate entry") {
            return tx.Where("uid = ? AND schedule = ?", uid, activityID).First(&userDraw).Error
        }
        return err
    }
    return nil
})
```

## 修复内容

### 修改文件
- `server/internal/service/lucky_service.go`

### 修改位置
- `getUserDraw` 方法（第246-278行）

### 主要变更
1. 移除了 `FirstOrCreate` 方法
2. 使用 `INSERT ... ON DUPLICATE KEY UPDATE` 语法
3. 添加了详细的日志记录
4. 保持了原有的缓存逻辑

## 测试验证

### 1. SQL测试脚本
运行 `server/scripts/test_concurrent_insert.sql` 验证数据库层面的修复。

### 2. 并发测试
可以使用 `server/scripts/test_duplicate_key_fix.go` 进行并发测试。

### 3. 生产环境验证
- 监控错误日志，确认不再出现 `Duplicate entry` 错误
- 检查签到功能是否正常工作
- 验证缓存机制是否正常

## 性能影响

### 正面影响
- 减少了数据库查询次数（从2次减少到1次）
- 消除了事务开销
- 避免了重试逻辑

### 注意事项
- `INSERT ... ON DUPLICATE KEY UPDATE` 在记录已存在时仍会增加自增ID计数器
- 对于本项目影响很小，因为 `user_draw` 表没有自增主键

## 相关文档

- [MySQL INSERT ... ON DUPLICATE KEY UPDATE 语法](https://dev.mysql.com/doc/refman/8.0/en/insert-on-duplicate.html)
- [GORM 原生SQL执行](https://gorm.io/docs/sql_builder.html#Raw-SQL)

## 回滚方案

如果需要回滚到原来的实现，可以恢复使用 `FirstOrCreate`：

```go
result := s.db.Where("uid = ? AND schedule = ?", uid, activityID).FirstOrCreate(&userDraw, model.UserDraw{
    UID:      uid,
    Schedule: activityID,
    Extra:    0,
    Day:      1,
    Point:    1,
    Number:   0,
    Today:    0,
    Time:     time.Now(),
})
```

但建议保持当前的修复方案，因为它更加稳定和高效。

---

## 权限过期时间计算重构

### 问题描述
在修复主键冲突问题的过程中，发现权限过期时间计算逻辑分散在多个文件中，导致：
1. 代码重复，维护困难
2. 逻辑不一致，容易出现bug
3. 修改时需要同时更新多个地方

### 解决方案
创建了统一的权限过期时间计算方法 `GetUserPermissionExpiration`：

**新增方法位置：** `server/internal/service/permission_service.go`

```go
// GetUserPermissionExpiration 统一的用户权限过期时间获取方法
// 这个方法整合了数据库权限检查和动态权限计算的完整逻辑
func (s *PermissionService) GetUserPermissionExpiration(uid uint64, permissionType string) (int64, error)
```

**逻辑流程：**
1. 检查用户数据库中的 `Permission` 字段
2. 如果 `Permission > 0`，直接返回永久权限 (-1)
3. 如果 `Permission = 0`，调用 `GetExpiration()` 计算CDKey等动态权限

### 修改的文件
1. `server/cmd/main.go` - 登录处理逻辑
2. `server/internal/handler/lucky_handler.go` - 签到和CDKey激活处理
3. `server/internal/service/lucky_service.go` - 权限服务调用
4. `server/internal/service/permission_service.go` - 新增统一方法

### 优势
- **代码复用**：权限计算逻辑集中在一个方法中
- **逻辑一致**：所有地方使用相同的计算规则
- **易于维护**：修改权限逻辑只需要更新一个地方
- **减少错误**：避免了多处重复代码可能导致的不一致

---

## 复合主键Save问题修复

### 问题描述
在实际运行中发现新的主键冲突错误：
```
Error 1062 (23000): Duplicate entry '0-0' for key 'PRIMARY'
INSERT INTO `user_draw` (`uid`,`schedule`,`extra`,`day`,`point`,`number`,`today`,`time`) VALUES (0,0,0,1,1,1,1,'2025-07-11 03:14:09.515')
```

**根本原因：**
GORM的 `Save` 方法在处理复合主键时存在问题，可能会尝试执行 `INSERT` 而不是 `UPDATE`。

### 解决方案
将 `Save` 方法替换为 `Updates` 方法：

**修改前：**
```go
if err := s.db.Save(userDraw).Error; err != nil {
    return nil, fmt.Errorf("保存用户签到数据失败: %v", err)
}
```

**修改后：**
```go
if err := s.db.Model(&model.UserDraw{}).Where("uid = ? AND schedule = ?", userDraw.UID, userDraw.Schedule).Updates(map[string]interface{}{
    "extra":  userDraw.Extra,
    "day":    userDraw.Day,
    "point":  userDraw.Point,
    "number": userDraw.Number,
    "today":  userDraw.Today,
    "time":   userDraw.Time,
}).Error; err != nil {
    return nil, fmt.Errorf("更新用户签到数据失败: %v", err)
}
```

### 优势
- **避免主键冲突**：`Updates` 方法明确指定更新条件，不会尝试插入新记录
- **更精确的控制**：明确指定要更新的字段
- **更好的性能**：避免了不必要的主键检查
