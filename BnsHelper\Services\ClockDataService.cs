using System.Diagnostics;
using System.Windows.Threading;

namespace Xylia.BnsHelper.Services;

/// <summary>
/// 时钟数据管理服务
/// </summary>
public class ClockDataService : IService
{
    private static readonly Lazy<ClockDataService> _instance = new(() => new ClockDataService());
    public static ClockDataService Instance => _instance.Value;

    private DispatcherTimer? _refreshTimer;

    private ClockDataService()
    {

    }

    /// <summary>
    /// 注册服务
    /// </summary>
    public void Register()
    {
        StartRefreshTimer();
        Debug.WriteLine("[INFO] ClockDataService 已注册");
    }

    /// <summary>
    /// 启动定时刷新计时器
    /// </summary>
    private void StartRefreshTimer()
    {
        // 停止现有计时器
        StopRefreshTimer();

        // 创建新的计时器，每小时检查一次
        //_refreshTimer = new DispatcherTimer
        //{
        //    Interval = TimeSpan.FromHours(1)
        //};
        //_refreshTimer.Tick += OnRefreshTimerTick;
        //_refreshTimer.Start();

        Debug.WriteLine("[INFO] 时钟数据定时刷新计时器已启动，间隔: 1小时");
    }

    /// <summary>
    /// 停止定时刷新计时器
    /// </summary>
    private void StopRefreshTimer()
    {
        //if (_refreshTimer != null)
        //{
        //    _refreshTimer.Stop();
        //    _refreshTimer.Tick -= OnRefreshTimerTick;
        //    _refreshTimer = null;
        //}
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        StopRefreshTimer();
        Debug.WriteLine("[INFO] ClockDataService 已释放");
    }
}
