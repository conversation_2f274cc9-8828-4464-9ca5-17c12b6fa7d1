﻿<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<RootNamespace>Xylia.BnsHelper</RootNamespace>
		<TargetFramework>net8.0-windows</TargetFramework>
		<PlatformTarget>x64</PlatformTarget>
		<RuntimeIdentifier>win-x64</RuntimeIdentifier>
		<OutputType>WinExe</OutputType>
		<Nullable>enable</Nullable>
		<SignAssembly>True</SignAssembly>
		<NeutralLanguage>zh-CN</NeutralLanguage>
		<RuntimeIdentifier>win-x64</RuntimeIdentifier>
		<GenerateAssemblyInfo>False</GenerateAssemblyInfo>
		<UseWPF>true</UseWPF>
		<AssemblyName>剑灵小助手</AssemblyName>
		<ApplicationIcon>Properties\logo.ico</ApplicationIcon>
		<ApplicationManifest>Properties\app.manifest</ApplicationManifest>
		<SupportedOSPlatformVersion>7.0</SupportedOSPlatformVersion>
		<ImplicitUsings>enable</ImplicitUsings>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
		<DebugType>none</DebugType>
		<IsPublishable>True</IsPublishable>
		<PublishDir>bin</PublishDir>
		<SelfContained>False</SelfContained>
		<PublishSingleFile>True</PublishSingleFile>
		<IncludeContentInSingleFile>True</IncludeContentInSingleFile>
		<IncludeNativeLibrariesForSelfExtract>True</IncludeNativeLibrariesForSelfExtract>
		<TransformOutOfDateOnly>False</TransformOutOfDateOnly>
		<IsPublishable>True</IsPublishable>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
		<PackageReference Include="HandyControl" Version="3.5.1" />
		<PackageReference Include="NAudio" Version="2.2.1" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="Quartz" Version="3.14.0" />
		<PackageReference Include="RestSharp" Version="112.1.0" />
		<PackageReference Include="Serilog.Sinks.Debug" Version="3.0.0" />
		<PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
		<PackageReference Include="SharpCompress" Version="0.40.0" />
		<PackageReference Include="System.Management" Version="9.0.6" />
		<PackageReference Include="WpfScreenHelper" Version="2.1.1" />
		<ProjectReference Include="..\..\..\..\..\DotNet\Xylia\bns-preview-tools\Preview.UI.Common\Preview.UI.Common.csproj" />
		<ProjectReference Include="..\Updater\Updater.csproj" />
		<Using Include="HandyControl.Controls.MessageBox" Alias="MessageBox" />
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="Properties\Data\*.xml" />
		<EmbeddedResource Include="Resources\Musics\*" />
		<Resource Include="Resources\Images\*" />
		<Resource Include="Resources\Fonts\*.ttf" />
	</ItemGroup>

	<ProjectExtensions><VisualStudio><UserProperties Reactor_Commands="-obfuscation 0" Reactor_Configuration="Release" Reactor_Deploy="0" Reactor_Enabled="1" Reactor_Output="&lt;AssemblyLocation&gt;\&lt;AssemblyFileName&gt;" Reactor_Project="D:\Build\DotNet\Xylia\BnsPlugin\BnsHelper\剑灵小助手.nrproj" /></VisualStudio></ProjectExtensions>
</Project>