using System.Diagnostics;
using System.IO;
using System.Text.Json;

namespace Xylia.BnsHelper.Services;

/// <summary>
/// 用户协议管理服务
/// </summary>
public class UserAgreementService
{
    private static readonly Lazy<UserAgreementService> _instance = new(() => new UserAgreementService());
    public static UserAgreementService Instance => _instance.Value;

    private readonly string _agreementFilePath;
    private readonly string _currentVersion = "1.0.1"; // 协议版本

    private UserAgreementService()
    {
        var appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "Xylia");
        Directory.CreateDirectory(appDataPath);
        _agreementFilePath = Path.Combine(appDataPath, "user_agreement.json");
    }

    /// <summary>
    /// 检查用户是否已同意当前版本的协议
    /// </summary>
    public bool HasUserAgreed()
    {
        try
        {
            if (!File.Exists(_agreementFilePath))
            {
                return false;
            }

            var json = File.ReadAllText(_agreementFilePath);
            var agreementData = JsonSerializer.Deserialize<UserAgreementData>(json);

            if (agreementData == null)
            {
                return false;
            }

            // 检查版本是否匹配
            bool hasAgreed = agreementData.Version == _currentVersion && agreementData.IsAgreed;
            
            Debug.WriteLine($"[INFO] 用户协议检查: 版本={agreementData.Version}, 当前版本={_currentVersion}, 已同意={agreementData.IsAgreed}, 结果={hasAgreed}");
            
            return hasAgreed;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 检查用户协议失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 记录用户同意协议
    /// </summary>
    public void RecordUserAgreement()
    {
        try
        {
            var agreementData = new UserAgreementData
            {
                Version = _currentVersion,
                IsAgreed = true,
                AgreedTime = DateTime.Now
            };

            var json = JsonSerializer.Serialize(agreementData, new JsonSerializerOptions
            {
                WriteIndented = true
            });

            File.WriteAllText(_agreementFilePath, json);
            
            Debug.WriteLine($"[INFO] 用户协议已记录: 版本={_currentVersion}, 时间={agreementData.AgreedTime}");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 记录用户协议失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 重置用户协议状态（用于测试或协议更新）
    /// </summary>
    public void ResetAgreementStatus()
    {
        try
        {
            if (File.Exists(_agreementFilePath))
            {
                File.Delete(_agreementFilePath);
                Debug.WriteLine("[INFO] 用户协议状态已重置");
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 重置用户协议状态失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取协议信息
    /// </summary>
    public UserAgreementInfo GetAgreementInfo()
    {
        try
        {
            if (!File.Exists(_agreementFilePath))
            {
                return new UserAgreementInfo
                {
                    CurrentVersion = _currentVersion,
                    HasAgreed = false,
                    AgreedVersion = null,
                    AgreedTime = null
                };
            }

            var json = File.ReadAllText(_agreementFilePath);
            var agreementData = JsonSerializer.Deserialize<UserAgreementData>(json);

            return new UserAgreementInfo
            {
                CurrentVersion = _currentVersion,
                HasAgreed = agreementData?.IsAgreed == true && agreementData.Version == _currentVersion,
                AgreedVersion = agreementData?.Version,
                AgreedTime = agreementData?.AgreedTime
            };
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 获取协议信息失败: {ex.Message}");
            return new UserAgreementInfo
            {
                CurrentVersion = _currentVersion,
                HasAgreed = false,
                AgreedVersion = null,
                AgreedTime = null
            };
        }
    }

    /// <summary>
    /// 用户协议数据结构（用于序列化）
    /// </summary>
    private class UserAgreementData
    {
        public string Version { get; set; } = string.Empty;
        public bool IsAgreed { get; set; }
        public DateTime AgreedTime { get; set; }
    }
}

/// <summary>
/// 用户协议信息
/// </summary>
public class UserAgreementInfo
{
    public string CurrentVersion { get; set; } = string.Empty;
    public bool HasAgreed { get; set; }
    public string? AgreedVersion { get; set; }
    public DateTime? AgreedTime { get; set; }
}
