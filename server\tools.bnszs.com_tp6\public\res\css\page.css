.stamp {
	position: relative;
}

#comment .send {
	position: absolute;
	top: 600px;
	left: 180px;
	display: block;
	z-index: 10;
	display: none;
}

#comment .comment_text {
	outline-style: none;
	border: 1px solid #ccc;
	border-radius: 3px;
	padding: 13px 14px;
	width: 620px;
	font-size: 14px;
	font-weight: 700;
	font-family: "微软雅黑","Microsoft Yahei";
}

#comment .comment_send {
	width: 120px;
	height: 45px;
	color: white;
/*字体颜色*/
	background-color: cornflowerblue;
/*按钮背景颜色*/
	border-radius: 3px;
/*让按钮变得圆滑一点*/
	border-width: 0;
/*消去按钮丑的边框*/
	margin: 0;
	outline: none;
/*取消轮廓*/
	font-family: "微软雅黑","Microsoft Yahei";
	font-size: 17px;
	text-align: center;
	cursor: pointer;
}

#comment .comment_text:focus {
	border-color: #66afe9;
	outline: 0;
	-webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(102,175,233,.6);
	box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(102,175,233,.6)
}

#comment .wrap_comments {
	width: 100%;
	padding: 10px 20px;
	overflow-x: auto;
	overflow-x: hidden;
	display: none;
}

#comment .comment_send:hover {
/*鼠标移动时的颜色变化*/
	background-color: antiquewhite;
}

#modallessLoading .loading {
	width: 660px;
	z-index: 100;
}

#modallessLoading .loading .loader {
	display: block;
	position: absolute;
	left: 50%;
	top: 300px;
	width: 140px;
	height: 140px;
	margin: -56px 0 0 -36px;
	background-color: #0b131f;
	border-radius: 100px;
}

#modallessLoading .loading .loader img {
	margin: 25px 23px 0;
}

#modallessLoading .loading .text {
	display: block;
	position: absolute;
	left: 48.5%;
	top: 360px;
	margin: 30px 0 0;
	font-size: 20px;
}

#comment .wrap_comments .comment_list {
	overflow-y: auto;
	overflow-x: hidden;
	height: 460px;
}

#comment .wrap_comments .comment_list .comment .user-info .username {
	position: absolute;
	left: 70px;
	color: #6699FF;
	margin-right: 30px;
	width: 120px;
	display: block;
	overflow: hidden;
	word-break: keep-all;
	white-space: nowrap;
	text-overflow: ellipsis;
	font-weight: bold;
}

#comment .wrap_comments .comment_list .comment .user-info .username_super {
	position: absolute;
	left: 70px;
	color: #FF9900;
	margin-right: 30px;
	width: 120px;
	display: block;
	overflow: hidden;
	word-break: keep-all;
	white-space: nowrap;
	text-overflow: ellipsis;
	font-weight: bold;
}

#comment .wrap_comments .comment_list .comment .user-info {
	position: relative;
}

#comment .wrap_comments .comment_list .comment .user-info .area-super {
	position: absolute;
	left: 178px;
	margin-left: 15px;
	color: #00db00;
	font-weight: bold;
}

#comment .wrap_comments .comment_list .comment .user-info .area-diff {
	position: absolute;
	left: 178px;
	margin-left: 15px;
	color: #ff0033;
	font-weight: bold;
}

#comment .wrap_comments .comment_list .comment .user-info .area-same {
	position: absolute;
	left: 178px;
	margin-left: 15px;
	color: #ffffff;
	font-weight: bold;
}

#comment .wrap_comments .comment_list .comment .user-info img {
	width: 65px;
	height: 65px;
	position: absolute;
}

#comment .wrap_comments .comment_list .comment .comment-time {
	position: absolute;
	right: 2px;
	top: 0px;
}

#comment .wrap_comments .comment_list .comment .comment-content {
	font-size: 20px;
	position: relative;
	left: 70px;
	top: 30px;
}

#comment .wrap_comments .comment_list .comment {
	position: relative;
	box-sizing: border-box;
	min-height: 80px;
	height: auto;
	clear: both;
}

.btnDelete {
	position: absolute;
	height: 10px;
}

.ban-reason {
	color: #FF0033;
	font-weight: bold;
}

.ban-time {
	color: #3399FF;
	font-weight: bold;
}

.comment-operator {
	position: relative;
	float: right;
	top: 30px;
}

.Separator {
	position: absolute;
	bottom: 1px;
	display: block;
	height: 2px;
	width: 1030px;
	border-top: 1px solid #ccc;
}

.loaded .stamp:after {
	border: solid 0.1em #d00;
	border-radius: .2em;
	color: #d00;
	content: '骗子';
	font-size: 50px;
	font-weight: bold;
	line-height: 1;
	position: absolute;
	padding: .1em .5em;
	margin: 0 auto;
	top: 5%;
	left: 10%;
	text-transform: uppercase;
	transform-origin: 50% 50%;
	transition: all 0.3s cubic-bezier(0.6, 0.04, 0.98, 0.335);
	opacity: .75;
	transform: rotate(-15deg) scale(1);
	z-index: 999;
}

.charaterViewBorder:after {
	content: '';
	position: absolute;
	width: 80px;
	height: 100%;
	top: 0;
	left: -200px;
	overflow: hidden;
	z-index: 9;
	background: -moz-linear-gradient(left,rgba(255,255,255,0) 0,rgba(255,255,255,.3) 50%,rgba(255,255,255,0) 100%);
	background: -webkit-gradient(linear,left top,right top,color-stop(0%,rgba(255,255,255,0)),color-stop(50%,rgba(255,255,255,.3)),color-stop(100%,rgba(255,255,255,0)));
	background: -webkit-linear-gradient(left,rgba(255,255,255,0) 0,rgba(255,255,255,.3) 50%,rgba(255,255,255,0) 100%);
	background: -o-linear-gradient(left,rgba(255,255,255,0) 0,rgba(255,255,255,.3)50%,rgba(255,255,255,0) 100%);
	background: linear-gradient(left,rgba(255,255,255,0) 0,rgba(255,255,255,.3) 50%,rgba(255,255,255,0) 100%);
	-webkit-transform: skewX(-25deg);
	-moz-transform: skewX(-25deg);
	transform: skewX(-25deg);
}

 /*鼠标滑过*/
.charaterViewBorder:hover:after {
	-webkit-transition: left 1s ease-in-out;
 /*过渡*/
	transition: left 1s ease-in-out;
	left: 500px;
 /*结束位置*/
}

:lang(cn) {
	font-family: "Microsoft Yahei UI","微软雅黑","Droid Sans",Sans-serif;
}

:lang(cn) .stat dt.stat-title {
	line-height: 32px;
}

.ic_bull {
	margin: 0 1px;
	font-size: 11px;
	vertical-align: bottom;
}

.wrapItem .wrapWeapon .quality {
	position: relative;
	display: block;
	width: 56px;
	height: 15px;
	margin: 1px 0 0;
	border: 1px solid #000;
	background-color: #000;
	overflow: hidden
}

.wrapItem .wrapWeapon .quality .bar {
	display: block;
	height: 13px;
	background-color: #008bc3
}

.wrapItem .wrapWeapon .quality .text {
	display: block;
	position: absolute;
	left: 0;
	top: 0;
	line-height: 15px;
	width: 100%;
	color: #fff;
	font-size: 11px;
	word-spacing: -1px;
	text-shadow: #000 1px 1px 0;
	text-align: center
}

.nova .wrapItem .icon .iconImg, .wrapItem .nova .icon .iconImg {
	background-position: 0 -610px;
}

.intro a {
	color: #fff;
}
		
		
		
		
		
   /* 页面滤镜 
    html {
    filter: grayscale(100%);
    -webkit-filter: grayscale(100%);
    -moz-filter: grayscale(100%);
    -ms-filter: grayscale(100%);
    -o-filter: grayscale(100%);
    filter: url(desaturate.svg#grayscale);
    filter:progid:DXImageTransform.Microsoft.BasicImage(grayscale=1);
    -webkit-filter: grayscale(1);
    }
    */
		
		
/*a{color:#2d374b;text-decoration:none} 
a:hover{color:#cd0200;text-decoration:underline} 
em{font-style:normal} 
li{list-style:none} 
img{border:0;vertical-align:middle} 
table{border-collapse:collapse;border-spacing:0} 
p{word-wrap:break-word}*/

#content {
	width: 100%;
	height: 100%;
	top: 0px;
	overflow: hidden;
	position: absolute;
}

#snowflake {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	overflow: hidden;
}

.snowRoll {
	position: absolute;
	opacity: 0;
	-webkit-animation-name: mysnow;
	-webkit-animation-duration: 20s;
	-moz-animation-name: mysnow;
	-moz-animation-duration: 20s;
	height: 80px;
}

@-webkit-keyframes mysnow {
  
	0% {
		bottom: 100%;
	}

	50% {
		-webkit-transform: rotate(1080deg);
	}

	100% {
		-webkit-transform: rotate(0deg) translate3d(50px, 50px, 50px);
	}
}

@-moz-keyframes mysnow {
  
	0% {
		bottom: 100%;
	}

	50% {
		-moz-transform: rotate(1080deg);
	}

	100% {
		-moz-transform: rotate(0deg) translate3d(50px, 50px, 50px);
	}
}

.searchEmpty a,.intro a {
	color: #fff;
}

.bnszs-skin .layui-layer-btn a {
	background-color: #84c101;
	border: 1px solid #84c101;
	color: #FFF;
}

.bnszs-skin .layui-layer-content {
	color: red;
}

.bnszs-msg-skin .layui-layer-content {
	color: #000;
}

#suggestForm #areaSelect1 {
	padding-right: 31px;
	font-size: 13px;
	padding-left: 5px;
	color: #fff;
	background: url(https://ossweb-img.qq.com/images/bangbang/home/<USER>/bns/bg_sprite.png) 
            no-repeat #222f40;
	width: 124px;
	height: 27px;
	background-position: 0 0;
	line-height: 27px;
	cursor: pointer;
	z-index: 10;
	float: left;
	border: #343c51 1px solid;
	border-radius: 4px;
}

#suggestBox .search {
	left: 140px;
}

#suggestBox .suggest_wrap li.focus {
	-webkit-box-shadow: rgba(100, 164, 255, .5) 0 0 20px inset, rgba(41, 68, 108, .6) 0 1px 0 inset, rgba(41, 68, 108, .6) 0 -1px 0 inset;
	box-shadow: rgba(100, 164, 255, .5) 0 0 20px inset, rgba(41, 68, 108, .6) 0 1px 0 inset, rgba(41, 68, 108, .6) 0 -1px 0 inset
}

#suggestBox input[type=text] {
	width: 102px;
	height: 27px;
	margin-left: 10px;
	padding: 0 0px 0 25px;
	color: #fff;
	font-size: 13px;
	text-shadow: #000 1px 1px 0
}

@media screen and (min-width:600px) {
	.container .img_bg {
		height: 700px;
		width: 100%;
		position: absolute;
		z-index: -1;
		background-position: center;
		background-image: url(https://api.bns.me/doc/sjbz/api?lx=dongman);
		background-size: cover;
	}

	.side-weixin-box {
		position: fixed;
		top: 580px;
		_position: absolute;
		margin-left: 930px;
		width: 100px;
	}

	.weixin-box-t,.weixin-box-c .close,.weixin-box-c a {
		background: url(/res/img/y_icons1.png) no-repeat;
	}

	.weixin-box-c {
		width: 102px;
		border: 1px solid #e8e8e8;
		background: #fff;
		box-shadow: 0 0 4px rgba(0,0,0,.1);
		position: relative;
	}

	.weixin-box-c .close {
		position: absolute;
		right: -1px;
		top: -1px;
		width: 22px;
		height: 22px;
		cursor: pointer;
		background-position: 1px -254px;
	}

	.weixin-box-c .content {
		float: none;
		margin: 0 auto;
		width: 100px;
	}

	.weixin-box-c p {
		margin: 0 auto 0;
		text-align: center;
		color: #000;
	}

	.weixin-box-c img {
		display: block;
		vertical-align: top;
	}

	.itemArea .wrapItem {
		background-color: #1a1c2cad;
	}
}

@media screen and (max-width:600px) {
	/*当屏幕尺寸小于600px时，应用下面的CSS样式*/
	.container {
		width: 100%;
		height: auto;
		top: 0px;
		bottom: 0px;
	}

	.container .img_bg {
		position: fixed;
		top: -35px;
		bottom: -35px;
		width: 100%;
		z-index: -1;
		background-position: center;
		background-image: url(https://api.bns.me/doc/sjbz/api?lx=m_dongman);
		background-size: cover;
		background-attachment: fixed;
	}

	.wrap_btLike_NP {
		display: none;
	}

	#suggestBox {
		width: auto;
	}

	#schForm {
		right: 0px;
	}

	#suggestBox input[type=text] {
		width: 140px;
		height: 27px;
		margin-left: 5px;
		padding: 0 10px 0 20px;
	}

	#suggestForm #areaSelect1 {
		padding-right: 5px;
		width: 85px;
		line-height: 24px;
	}

	#suggestBox .search {
		left: 92px;
	}

	header.summary {
		padding: 0 5px 5px;
	}

	header.summary .signature {
		padding: 0px 0 0 60px;
		width: auto;
	}

	header.summary dd.desc {
		margin: 5px 0 0;
	}

	header.summary .button {
		top: 0px;
		right: 5px;
	}

	.btnCompare,.btnTraining,.btnComparison,.btnSandboxChat,.wrapTraining .btnTraining {
		padding: 5px 5px 5px;
	}

	.characterInfo {
		padding: 0px;
	}

	.characterArea {
		display: none;
	}

	.statArea,.itemArea .wrapItem {
		margin: 0px;
		background-color: #222e4182;
		border-radius: 6px;
		background-position: top center;
	}

	.statArea .stat {
		width: auto;
	}

	.statArea .stat .attack,.statArea .stat .defense {
		width: 50%;
		margin-left: 0px;
		background-position: top center;
		border-radius: 5px;
	}

	.stat .stat-box {
		padding: 0 5px;
	}

	.stat .stat-box .split-point .point[class*=total]:nth-child(2)::after {
		right: -3px;
	}

	.stat .stat-box .split-point .point {
		margin: 0 -2px;
	}

	.stat dt.stat-title {
		padding: 0 5px 0 5px;
	}

	.stat dt.stat-title .title {
		width: auto;
		max-width: 65px;
	}

	.stat .ratio li {
		padding: 0 5px 2px 0;
	}

	.stat dd.stat-description .title {
		width: auto;
	}

	#lySplit-attack {
		right: auto;
	}

	.itemArea,.characterInfo .loading {
		padding: 10px 1px 0 1px;
		width: 100%;
	}

	.wrapItem .accessoryArea {
		height: auto;
		overflow-y: auto;
	}

	.itemArea .lyCharmEffect {
		width: max-content;
	}

	.itemArea .wrapGem {
		position: absolute;
		bottom: 0px;
		right: 1px;
		border-radius: 6px;
		background-color: #222e4182;
		padding: 10px 10px 40px 15px;
		border: rgba(41,68,108,.6) 1px solid;
	}

	#charmEffect {
		position: absolute;
		right: 0;
		bottom: 0px;
	}

	#floatlayer {
		width: 100%!important;
		left: 1px !important;
	}

	.itemLayer,.itemStat .base ul,.itemStatInfo {
		width: auto!important;
		min-width: 250px;
	}

	.side-weixin-box {
		display: none;
	}
}

