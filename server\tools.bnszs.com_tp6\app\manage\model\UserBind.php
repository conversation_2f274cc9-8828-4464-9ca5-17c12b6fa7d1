<?php
namespace app\manage\model;

use think\Model;

class UserBind extends Model
{
    protected $table = 'user_bind';

    /**
     * 获取用户绑定信息
     */
    public static function Get($uid) {
        // 如果不存在则创建
        $data = static::where(['uid' => $uid])->find();
        if(!$data) {
            $data = new static();
            $data->uid = $uid;
            $data->modify = 0;
            $data->save();
        }

        return $data;
    }
    
    /**
     * 绑定角色
     */
    public static function bindCharacter($uid, $serverId, $characterName) {
        $bind = static::Get($uid);
        $bind->server_id = $serverId;
        $bind->character_name = $characterName;
        $bind->bind_time = time();
        $bind->modify = 1;
        return $bind->save();
    }
    
    /**
     * 解除绑定
     */
    public static function unbind($uid) {
        $bind = static::where('uid', $uid)->find();
        if ($bind) {
            $bind->server_id = 0;
            $bind->character_name = '';
            $bind->bind_time = 0;
            $bind->modify = 0;
            return $bind->save();
        }
        return false;
    }
    
    /**
     * 检查是否已绑定
     */
    public static function isBound($uid) {
        $bind = static::where('uid', $uid)->find();
        return $bind && !empty($bind->character_name);
    }
    
    /**
     * 获取绑定的角色信息
     */
    public static function getBoundCharacter($uid) {
        $bind = static::where('uid', $uid)->find();
        if ($bind && !empty($bind->character_name)) {
            return [
                'server_id' => $bind->server_id,
                'character_name' => $bind->character_name,
                'bind_time' => $bind->bind_time
            ];
        }
        return null;
    }
    
    /**
     * 更新修改次数
     */
    public function incrementModify() {
        $this->modify += 1;
        return $this->save();
    }
    
    /**
     * 检查是否可以修改绑定
     */
    public function canModify($maxModifyCount = 3) {
        return $this->modify < $maxModifyCount;
    }
}
