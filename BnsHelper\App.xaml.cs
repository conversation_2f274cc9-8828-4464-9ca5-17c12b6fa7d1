﻿using Serilog;
using System.ComponentModel;
using System.Diagnostics;
using System.Reflection;
using System.Windows;
using System.Windows.Markup;
using System.Windows.Threading;
using Vanara.PInvoke;
using Xylia.BnsHelper.Common;
using Xylia.BnsHelper.Common.Extensions;
using Xylia.BnsHelper.Resources;
using Xylia.BnsHelper.Services;
using Xylia.BnsHelper.ViewModels;
using Xylia.BnsHelper.Views;

namespace Xylia.BnsHelper;
public partial class App : Application
{
    private bool started = false;

    protected override void OnStartup(StartupEventArgs e)
    {
        #region Check Process
        var current = Process.GetCurrentProcess();
        var process = ProcessExtensions.Find(current.MainModule?.FileName).Where(x => x.Id != current.Id).FirstOrDefault();
        if (process != null)
        {
            Log.Information("The application is already running.");
            User32.SetForegroundWindow(process!.MainWindowHandle);
            Environment.Exit(0);
        }
        #endregion

        #region Initialize
        var args = InitializeArgs(e.Args);
        AppDomain.CurrentDomain.UnhandledException += OnDomainUnhandledException;

        // 先初始化基础服务，但不包括UpdateService
        _ = new ServiceManager(new LogService(), new InitializeService());
        #endregion

        // 先检查更新，确认没有更新后再启动MainWindow
        CheckUpdateAndStartMainWindow();
    }

    private void CheckUpdateAndStartMainWindow()
    {
        try
        {
            var updateService = new UpdateService();

            // 设置更新检查完成的回调
            UpdateService.SetUpdateCheckCompleteCallback(() =>
            {
                // 更新检查完成且没有更新时，启动MainWindow
                Dispatcher.Invoke(() =>
                {
                    MainWindow = new MainWindow();
                    MainWindow.Show();
                    started = true;
                });
            });

            // 开始检查更新
            updateService.Register();
        }
        catch 
        {
            // 如果更新检查失败，直接启动MainWindow
            MainWindow = new MainWindow();
            MainWindow.Show();
            started = true;
        }
    }

    protected override void OnExit(ExitEventArgs e)
    {
        // 在程序退出时注销用户会话
        try
        {
            var viewModel = MainWindowViewModel.Instance;
            if (viewModel.IsLogin)
            {
                // 使用后台任务处理注销，完全不阻塞UI线程
                // 程序退出时服务端会自动清理过期会话，所以这里主要是为了优雅关闭
                _ = Task.Run(async () =>
                {
                    try
                    {
                        using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(1));
                        await viewModel.LogoutUserAsync().WaitAsync(cts.Token);
                        Debug.WriteLine("[INFO] 程序退出时注销完成");
                    }
                    catch (OperationCanceledException)
                    {
                        Debug.WriteLine("[WARNING] 程序退出时注销超时，继续退出");
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"[ERROR] 程序退出时注销失败: {ex.Message}");
                    }
                });

                Debug.WriteLine("[INFO] 程序退出，后台处理注销请求");
            }
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Failed to logout user session during application exit");
        }

        base.OnExit(e);
    }

    internal static Dictionary<string, string> InitializeArgs(string[] args)
    {
        return args.Where(x => x[0] == '/')
            .ToLookup(
                 x => x.Contains(':') ? x[1..x.IndexOf(':')].ToLower() : x,
                 x => x.Contains(':') ? x[(x.IndexOf(':') + 1)..] : string.Empty)
            .ToDictionary(x => x.Key, x => x.First());
    }

    private void OnUnhandledException(object sender, DispatcherUnhandledExceptionEventArgs e)
    {
        e.Handled = true;

        // if advanced exception
        var exception = e.Exception;
        if (exception is TargetInvocationException or TypeInitializationException or XamlParseException) exception = exception.InnerException;

        // check exception type
        if (exception is null or OperationCanceledException) return;
        if (exception is not WarningException or AppException) Log.Error(exception, "UnhandledException");

#if DEBUG
        HandyControl.Controls.Growl.Error(exception?.Message);
#else
		MessageBox.Show(exception.Message, StringHelper.Get("Application_ErrorMessage"), icon: MessageBoxImage.Error);
#endif

        // exit if occurs in startup
        if (!started) Environment.Exit(0);
    }

    private void OnDomainUnhandledException(object sender, UnhandledExceptionEventArgs e)
    {
        var exception = e.ExceptionObject as Exception;
        Log.Error(exception, "FatalException");
        MessageBox.Show(StringHelper.Get("Application_CrashMessage", exception!), "Fatal Error", icon: MessageBoxImage.Stop);
    }
}
