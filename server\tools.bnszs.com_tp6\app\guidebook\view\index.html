{include file="Guidebook/Template/header" /}

<div id="container" class="container">
	<div class="section-contents">
		<div>
			<div id="ncGuidebookSearch" class="nc-guidebook-search ncgbi-search-top" style="display: none;">
				<div class="ncgb-container">
					<div class="ncgbi-container">
						<section class="ncgb-searchform">
							<h2 class="ncgb-searchform-title">가이드북 검색</h2>
							<form id="ncgbAutoSuggest" class="ncgb-autoSuggest" method="get" action="" onsubmit="return false">
								<input id="ncgbSuggestInput" class="ncgb-suggest-input" type="text" value="" placeholder="가이드북 검색" autocomplete="off" data-name="suggest_input">
								<input type="button" value="X" class="ncgb-suggest-delete" title="삭제" data-name="suggest_delete" style="display: none;">
								<input id="ncgbSuggestSubmit" class="ncgb-suggest-submit" type="button" value="GO" title="검색" data-name="suggest_submit">
								<div id="ncgbSuggestWrap" class="ncgb-suggest-list-wrap" data-name="suggest_wrap" style="display: none;">
									<div class="ncgb-suggest-list" data-name="suggest_list" style="display: none;">
										<div data-name="suggest_scroll">
											<ul></ul>
										</div>
									</div>
									<div class="ncgb-suggest-recent" data-name="suggest_recent" style="display: none;">
										<div data-name="suggest_scroll">
											<ul></ul>
											<div class="ncgb-suggest-recent-none">최근검색어 내역이 없습니다.</div>
										</div>
										<div class="ncgb-suggest-recent-btn-wrap">
											<button type="button" class="ncgb-suggest-recent-btn">최근 검색어 전체 삭제</button>
										</div>
									</div>
								</div>
							</form>
						</section>
					</div>
				</div>
			</div>
			<div id="ncGuidebookGuide" class="nc-guidebook-guide">
				<div class="ncgbg-guide">
					<ul class="ncgbg-guide-depth-2-list">
						<li class="ncgbg-guide-depth-2-item">
							<p class="ncgbg-guide-depth-2-name">怀旧服指南</p>
							<ul class="ncgbg-guide-depth-2-guide-list">
								{volist name='data' id='item'}
								<li class="ncgbg-guide-depth-2-guide-item">
									<div class="ncgbg-guide-depth-2-guide-item-wrap">
										<a class="ncgbg-guide-depth-2-guide-item-link" href="/guidebook/view?title={$item->title}">
											<div class="ncgbg-guide-depth-2-guide-thumbnail" style="background-image: url({$item->thumbnail})"></div>
										</a>
										<div class="ncgbg-guide-depth-2-guide-item-info">
											<a class="ncgbg-guide-depth-2-guide-item-link" href="/guidebook/view?title={$item->title}">
												<p class="ncgbg-guide-depth-2-guide-name">
													<span class="ncgbg-guide-depth-2-guide-text">{$item->name}</span>
												</p>
												<p class="ncgbg-guide-depth-2-guide-summary">{$item->summary}</p>
											</a>
										</div>
									</div>
								</li>
								{/volist}
							</ul>
						</li>
					</ul>
					<p class="ncgbg-guide-depth-2-list-none">没有指南。</p>
				</div>
			</div>

			<script src="https://assets.playnccdn.com/uikit/guidebook/2.1.2/js/index.js?_=************"></script>
			<script src="https://bns.qq.com/webplat/info/news_version3/1298/61580/m22755/index.js"></script>
		</div>
	</div>
</div>

<script>
$.each(newsIndexData, function(i, data){
   	console.log(decodeURI(data.sTitle));
	console.log(data);
});
</script>