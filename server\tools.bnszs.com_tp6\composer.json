{"name": "topthink/think", "description": "the new thinkphp framework", "type": "project", "keywords": ["framework", "thinkphp", "ORM"], "homepage": "http://thinkphp.cn/", "license": "Apache-2.0", "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "require": {"php": ">=7.2.0", "topthink/framework": "^6.0.0", "topthink/think-orm": "^2.0", "topthink/think-view": "^1.0", "topthink/think-multi-app": "^1.1", "endroid/qrcode": "^3.0", "topthink/think-worker": "^3.0", "phpmailer/phpmailer": "^6.9", "voku/simple_html_dom": "^4.8"}, "require-dev": {"symfony/var-dumper": "^4.2", "topthink/think-trace": "^1.0"}, "autoload": {"psr-4": {"app\\": "app"}, "psr-0": {"": "extend/"}}, "config": {"preferred-install": "dist", "allow-plugins": {"topthink/think-installer": true}}, "scripts": {"post-autoload-dump": ["@php think service:discover", "@php think vendor:publish"]}}