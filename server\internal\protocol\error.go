package protocol

// ErrorCode 错误码类型
type ErrorCode int

const (
	ErrorCodeInvalidQQ     ErrorCode = 1001 // 无效的QQ号
	ErrorCodeServerError   ErrorCode = 1002 // 服务器错误
	ErrorCodeUnauthorized  ErrorCode = 1003 // 未授权
	ErrorCodeInvalidDevice ErrorCode = 1004 // 无效的设备
	ErrorCodeExpired       ErrorCode = 1005 // 过期
	ErrorCodeAccountBanned ErrorCode = 1006 // 账号被封禁
)

// Error 协议错误
type Error struct {
	Code    ErrorCode
	Message string
}

// NewError 创建新的协议错误
func NewError(code ErrorCode, message string) error {
	return &Error{
		Code:    code,
		Message: message,
	}
}

// Error 实现 error 接口
func (e *Error) Error() string {
	return e.Message
}
