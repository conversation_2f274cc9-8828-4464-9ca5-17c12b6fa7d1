﻿<Page x:Class="Xylia.BnsHelper.Views.Pages.HUDPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
	  xmlns:hc="https://handyorg.github.io/handycontrol">

	<Grid>
		<Grid.RowDefinitions>
			<RowDefinition Height="Auto" />
			<RowDefinition Height="*" />
		</Grid.RowDefinitions>

		<hc:ButtonGroup HorizontalAlignment="Right" Margin="3 0">
			<Button Content="{DynamicResource Text.Add}" Command="{Binding AddCommand}" />
			<Button Content="{DynamicResource Text.Import}" Command="{Binding ImportCommand}" />
			<Button Content="{DynamicResource Text.Export}" Command="{Binding ExportCommand}" />
		</hc:ButtonGroup>

		<ListBox ItemsSource="{Binding Data}" Grid.Row="1" ScrollViewer.HorizontalScrollBarVisibility="Disabled">
			<ListBox.Resources>
				<ContextMenu x:Key="ItemMenu">
					<MenuItem Header="{DynamicResource Text.Remove}" Command="{Binding DeleteCommand}">
						<MenuItem.Icon>
							<TextBlock Text="" Style="{StaticResource SegoeIcon}" />
						</MenuItem.Icon>
					</MenuItem>
				</ContextMenu>
			</ListBox.Resources>
			<ListBox.ItemContainerStyle>
				<Style TargetType="{x:Type ListBoxItem}" BasedOn="{StaticResource ListBoxItemBaseStyle}">
					<Setter Property="Focusable" Value="False" />
					<Setter Property="HorizontalContentAlignment" Value="Stretch" />
					<Setter Property="ContextMenu" Value="{StaticResource ItemMenu}" />
				</Style>
			</ListBox.ItemContainerStyle>
			<ListBox.ItemTemplate>
				<DataTemplate>
					<Grid>
						<Grid.ColumnDefinitions>
							<ColumnDefinition Width="*" />
							<ColumnDefinition Width="Auto" />
						</Grid.ColumnDefinitions>
						
						<TextBox Text="{Binding Name}" />
						<Path Data="{StaticResource CloseGeometry}" Fill="Red" Grid.Column="1" VerticalAlignment="Center" Margin="10 0 0 0" />
					</Grid>
				</DataTemplate>
			</ListBox.ItemTemplate>
		</ListBox>
	</Grid>
</Page>