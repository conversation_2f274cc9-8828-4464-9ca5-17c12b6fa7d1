﻿using IniParser.Model;
using System.IO;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.Preview.Common.Extension;
using Xylia.Preview.Properties;

namespace Xylia.BnsHelper.Models;
/// <summary>
/// Represent of the setting of mod.
/// </summary>
internal class ModSetting
{
	#region Properties
	public string? Name { get; set; }
	public int Version { get; set; }
	public string? Author { get; set; }
	public string? Describe { get; set; }
	public string? Url { get; set; }
	public string? Password { get; set; }

	public List<string> Files = [];
	#endregion

	#region Constructors
	internal ModSetting()
	{
		Url = "BNSR\\Content\\Paks\\~mods";
	}

	public ModSetting(IEnumerable<string> strings)
	{
		ReadLine(strings);
	}

	public ModSetting(SectionData section)
	{
		Name = section.Keys["name"];
		Version = section.Keys["version"].To<int>();

		foreach (var key in section.Keys)
		{
			if (!int.TryParse(key.KeyName, out _)) continue;

			Files.Add(key.Value);
		}
	}
	#endregion

	#region Methods
	private void ReadLine(IEnumerable<string> data)
	{
		var strings = data
			.Where(x => x.Contains('='))
			.ToDictionary(x => x[..x.IndexOf('=')], x => x[(x.IndexOf('=') + 1)..], StringComparer.OrdinalIgnoreCase);

		Author = strings.GetValueOrDefault("by");
		Name = strings.GetValueOrDefault("show");
		Url = strings.GetValueOrDefault("url", Url);
        Password = strings.GetValueOrDefault("password");
        Describe = string.Join('\n', data.Where(x => !x.Contains('=')));
    }
	#endregion
}

internal class ModRegister(string path) : IniSettings(path, true)
{
	public static string Root => Path.GetDirectoryName(SettingHelper.Default.Game.FullPath)!;

	static ModRegister? _instance;
	public static ModRegister Instance
	{
		internal set => _instance = value;
		get => _instance ??= new(Path.Combine(Root, "mod.ini"));
	}

	#region Methods
	public int GetVersion(string? name)
	{
		if (!Directory.Exists(Root)) return 0;

		var section = this[name];
		var version = section["version"].To<int>();

		foreach (var key in section)
		{
			if (!int.TryParse(key.KeyName, out _)) continue;

			var path = Path.Combine(Root, key.Value);
			if (!File.Exists(path)) return 0;
		}

		return version;
	}

	public void Uninstall(string? name)
	{
		foreach (var key in this[name])
		{
			if (!int.TryParse(key.KeyName, out _)) continue;

			var path = Path.Combine(Root, key.Value);
			if (File.Exists(path)) File.Delete(path);
		}

		this.RemoveSection(name);
	}
	#endregion
}
