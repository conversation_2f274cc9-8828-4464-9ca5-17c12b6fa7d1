﻿using Microsoft.Win32;
using Xylia.Preview.Common.Extension;

namespace Xylia.BnsHelper.Common.Helpers;
internal class RegistryHelper
{
	#region Constructor
	public static RegistryHelper Default { get; } = new();


	readonly RegistryKey Root = Registry.LocalMachine.CreateSubKey($@"Software\Xylia\bns-plugins", true);
	#endregion

	#region Properties
	public bool UseTestServer => Root.GetValue("UseTestServer").To<int>() == 1;

	public bool UseDPSMode => Root.GetValue("UseDPSMode").To<int>() == 1;
    #endregion
}
