{layout name="manage/template" /}
<div class="admin-content">
	<div class="admin-content-body">
	  <div class="am-cf am-padding am-padding-bottom-0">
		<div class="am-fl am-cf"><strong class="am-text-primary am-text-lg">自定义资料</strong></div>
	  </div>
	  <hr />
	  <div class="am-u-sm-12 am-u-md-8 am-u-md-pull">
		<form class="am-form am-form-horizontal" method="post" id="userinfo">
		  {if $isAdmin}
				// 管理模板
		  {else}
		  <div class="am-form-group">
			<label class="am-u-sm-3 am-form-label"></label>
			<div class="am-select am-u-sm-9">
				<input type="hidden" id="uin" name="uin" value="{$user->uin}">
				<span>游戏账号 {$user->uin}</span><br>
				<span>剩余修改次数 <span style="color:#FF0066"><b>{$user->modify}次</b></span>
				<br/><br/>
			    <b>
				  <div id="status-info">当前角色状态：{$status}</div>
				</b>
				<br />
			</div>
		  </div>
		  {/if}

		  <!-- 右侧悬浮栏 -->
		  <div class="tally">
			<a class="tally-a1 sign-but" href="javascript:openProfile(false);"><i class="tally-i1"></i>查看当前</a>
			<a class="tally-a3 sign-but" href="javascript:openProfile();"><i class="tally-i1"></i>预览定制</a>
			<a class="tally-a6 sign-but" href="javascript:Submit();" style="display: inline;"><i class="tally-i4"></i>提交</a>
			<a class="tally-a6 sign-but" href="javascript:gotoTop('.admin-content');" style="display: inline;"><i class="tally-i6"></i>回顶部</a>
		  </div>
		  <!-- 服务器选择-->
		  <div class="am-form-group">
			<label for="server" class="am-u-sm-3 am-form-label">大区</label>
			<div class="am-select am-u-sm-9">
			  <select id="areaSelect" name="area">
			  </select>
			</div>
		  </div>
		  <div class="am-form-group">
			<label for="area" class="am-u-sm-3 am-form-label">服务器</label>
			<div class="am-select am-u-sm-9">
			  <select id="serverSelect" name="server">
			  </select>
			  <small id="serverSelect_msg"></small>
			</div>
		  </div>
		  <!-- 角色选择 -->
		  <div class="am-form-group">
			<label for="username" class="am-u-sm-3 am-form-label">角色昵称</label>
			<div class="am-u-sm-9"> 
				{if $isAdmin}
				<input type="text" id="rolenameSelect" name="name" value="{$name}" placeholder="角色名称" required>
				{else}
				<select id="rolenameSelect" name="rolename">
					<option value="0">请选择角色</option>
				</select>
				{/if}
				<label id="rolename_msg" style="color:red;font-size:14.2px;"></label>
			</div>
		  </div>
		  <!-- 装备查询部分功能启用设置 -->
		  <div class="am-form-group">
			<label for="mouseStyle" class="am-u-sm-3 am-form-label">功能启用设置</label>
			<div class="am-u-sm-9">
			  <div class="enabled-group" style="margin-bottom:7px;">
				<label class="enabled-group" style='margin-right:10px;'><input style='margin-right:5px;' id='usecharaterview' name="usecharaterview" type='checkbox' {$info->use_charaterview?"checked":""}/>显示角色图 </label>
			  </div>
			</div>
		  </div>
		  <!-- 简介功能 -->
		  <div class="am-form-group">
			<label for="describe" class="am-u-sm-3 am-form-label">简介</label>
			<div class="am-u-sm-9">
			  <input type="text" id="describe" name="describe" value="{$info['describe'] ?? ''}" placeholder="昵称右边描述">
			  <small id="describe_msg"></small>
			</div>
		  </div>
		  {if $isAdmin}
		  <div class="am-form-group">
			<label for="describe" class="am-u-sm-3 am-form-label">扩展功能 *</label>
			<div class="am-u-sm-9">
			  <textarea type="text" id="extContent" name="extContent">{$info["extContent"]}</textarea>
			  <small id="ext_msg" style="color:#FF3300">此内容将放置在文档流最底部，故无需设置页面加载完毕再执行</small>
			</div>
		  </div>
		  {/if}

		  <div class="am-form-group">
			<label for="bg_img" class="am-u-sm-3 am-form-label">背景图</label>
			<div class="am-u-sm-9">
			  <input imagecheck="true" type="text" id="bg_img" name="bg_img" class="js-pattern-url" value="{$info->bg_img ?? ''}" placeholder="推荐1065x660尺寸的图片，直接填写图片URL链接即可">
			  <small>(下同) 如需上传，可以<a href="//www.tongleer.com/iiilab/qq.html" target="_blank">点开页面</a>。选择图片后上传，复制URL链接信息</small>
			  <small id="bg_img_msg"></small>
			</div>
		  </div>
		  <div class="am-form-group">
			<label for="charaterView" class="am-u-sm-3 am-form-label">角色图</label>
			<div class="am-u-sm-9">
			  <input imagecheck="true" type="text" id="charaterView" name="charaterView" class="js-pattern-url" value="{$info->charaterView ?? ''}" placeholder="推荐760x1230尺寸的图片，直接填写图片URL链接即可">
			  <small id="charaterView_msg"></small>
			</div>
		  </div>
		  <div class="am-form-group">
			<label for="charaterViewBorder" class="am-u-sm-3 am-form-label">角色边框</label>
			<div class="am-u-sm-9">
			  <input imagecheck="true" type="text" id="charaterViewBorder" name="charaterViewBorder" class="js-pattern-url" value="{$info->charaterViewBorder ?? ''}" placeholder="推荐760*1230尺寸的图片，直接填写图片URL链接即可">
			  <small> (下同) 如需上传，可以<a href="//www.tongleer.com/iiilab/qq.html" target="_blank">点开页面</a>。选择图片后上传，复制URL链接信息<br />位置调节方法:图片后追加 ?宽度,高度,左边,顶边,链接中已含有?时使用&cfg=</small>
			  <small id="charaterViewBorder_msg"></small>
			</div>
		  </div>
		  <!-- 由于此设置较难，前端不再显示。如果你看到了这行注释可以去除
		  <div class="am-form-group">
			<label for="titleImg" class="am-u-sm-3 am-form-label">称号图</label>
			<div class="am-u-sm-9">
			  <input imagecheck="true" type="text" id="titleImg" name="titleImg" class="js-pattern-url" value="{$info->titleImg ?? ''}" placeholder="尺寸自己看着办!     位置调节方法:图片后追加  ?宽度,高度,左边,底边  ">
			  <small></small>
			  <small id="titleImg_msg"></small>
			</div>
		  </div>
		  -->
		  <div class="am-form-group">
			<label for="jobImg" class="am-u-sm-3 am-form-label">头像图</label>
			<div class="am-u-sm-9">
			  <input imagecheck="true" type="text" id="jobImg" name="jobImg" class="js-pattern-url" value="{$info->jobImg ?? ''}" placeholder="任意尺寸均可，直接填写图片URL链接即可">
			  <small id="jobImg_msg"></small>
			</div>
		  </div>
		  <div class="am-form-group">
			<label for="jobImgBorder" class="am-u-sm-3 am-form-label">头像边框</label>
			<div class="am-u-sm-9">
			  <input imagecheck="true" type="text" id="jobImgBorder" name="jobImgBorder" class="js-pattern-url" value="{$info->jobImgBorder ?? ''}" placeholder="任意尺寸均可，直接填写图片URL链接即可">
			  <small id="jobImgBorder_msg"></small>
			</div>
		  </div>
		  <div class="am-form-group">
			<label for="otherImg" class="am-u-sm-3 am-form-label">其他图片</label>
			<div class="am-u-sm-9">
			  <input type="text" id="otherImg" name="otherImg" class="js-pattern-url" value="{$info->otherImg ?? ''}" placeholder="任意尺寸均可,多张图片使用双空格进行间隔">
			  <small id="otherImg_msg"></small>
			</div>
		  </div>
		  <div class="am-form-group">
			<label for="labelImg" class="am-u-sm-3 am-form-label">勋章图片</label>
			<div class="am-u-sm-9">
			  <input imagecheck="true" type="text" id="labelImg" name="labelImg" class="js-pattern-url" value="{$info->labelImg ?? ''}" placeholder="任意尺寸均可,多张图片使用双空格进行间隔">
			  <small id="labelImg_msg"></small>
			</div>
		  </div>
		  <div class="am-form-group">
			<label for="opacity" class="am-u-sm-3 am-form-label">界面不透明度</label>
			<div class="am-u-sm-9">
			  <input type="hidden" class="single-slider" value="0" />
			  <input type="text" id="opacity" name="opacity" placeholder="界面不透明度，区间是 0.00 ~ 1.00（等于1时将不显示背景图）" value="{$info->opacity ?? 0}">
			  <small id="opacity_msg" style="color:red;font-size:14.2px;"></small>
			</div>
		  </div>
		  <div class="am-form-group">
			<label for="bgm" class="am-u-sm-3 am-form-label">背景音乐</label>
			<div class="am-u-sm-9">
			  <input type="text" id="bgm" name="bgm" value="{$info->bgm ?? ''}" placeholder="背景音乐，请填写搜索结果的右边第一个链接">
			  <small><a href="http://www.xmsj.org/" target="_blank">查找音乐</a><br /><br />如果需要随机音乐请填 https://api.uomg.com/api/rand.music?sort=热歌榜&format=mp3 <br />分类可选 [热歌榜|新歌榜|飙升榜|抖音榜|电音榜]</small>
			</div>
		  </div>
		   <!--
				<div class="am-form-group">
				  <label for="volume" class="am-u-sm-3 am-form-label">背景音乐默认音量（暂无实际效果）</label>
				  <div class="am-u-sm-9">
					<input type="text" id="volume" name="volume" onkeyup="value=value.replace(/[^\d]/g,'')" value="{$info->volume ?? 100}"  placeholder="0-100 范围内整数">
						<small id="volume_msg"></small>
				  </div>
				</div>   
			-->
		  <div class="am-form-group">
			<label for="bgmTitle" class="am-u-sm-3 am-form-label">背景音乐框内标题</label>
			<div class="am-u-sm-9">
			  <input type="text" id="bgmTitle" name="bgmTitle" placeholder="不填将显示默认文字" value="{$info->bgmTitle ?? ''}">
			  <small id="bgmTitle_msg"></small>
			</div>
		  </div>
		  <div class="am-form-group">
			<label for="fontStyle" class="am-u-sm-3 am-form-label">文字效果</label>
			<div class="am-u-sm-9">
			  <div style="margin-bottom:5px;">
				<label class="fontStyle" onchange="changeColor();"><input style='margin-right:10px;' id='selectFontFamily' name="selectFontFamily" type='checkbox' <?= empty($info['fontFamily'])?"":"checked='checked'"?> value='' />文字字体</label>
				<input type="text" style="display: inline-block;padding: 0px;height:30px;width:50%;margin:0 10px;" id="fontFamily" name="fontFamily" value="{$info->fontFamily ?? ''}" placeholder="填写字体名称，设备上已安装此字体才会生效" onchange="changeColor();">
			  </div>
			  <div style="display: inline-block;margin:0 10px 5px 0;">
				<label class="fontStyle" onchange="changeColor();"><input style='margin-right:10px;' id='selectTextColor' name="selectTextColor" type='checkbox' <?= empty($info['textColor'])?"":"checked='checked'"?> value='' />文字颜色</label>
				<input type="color" style="display: inline-block;padding: 0px;height:30px;width:80px;margin:0 10px;" id="textColor" name="textColor" value="{$info->textColor ?? ''}" placeholder="文字颜色" onchange="changeColor();">
			  </div>
			  <div style="display: inline-block;margin:0 10px 5px 0;">
				<label class="fontStyle" onchange="changeColor();"><input style='margin-right:10px;' id='selectTextShadowColor' name="selectTextShadowColor" type='checkbox' <?= empty      ($info['textShadowColor'])?"":"checked='checked'"?> value='' />发光颜色</label>
				<input type="color" style="display: inline-block;padding: 0px;height:30px;width:80px;margin:0 10px;" id="textShadowColor" name="textShadowColor" value="{$info->textShadowColor ?? ''}" placeholder="文字发光颜色" onchange="changeColor();">
			  </div>
			  <small id="fontStyle_msg"></small>
			</div>
		  </div>
		  <div class="am-form-group">
			<label for="mouseStyle" class="am-u-sm-3 am-form-label">鼠标效果</label>
			<div class="am-u-sm-9">
			  <div style="margin-bottom:5px;">
				<label class="mouseStyle" onchange="changeMouse();"><input style='margin-right:10px;' id='selectMouseIco' name="selectMouseIco" type='checkbox' <?= empty($info['mouseIco'])?"":"checked='checked'"?> />鼠标图标</label>
				<input type="text" style="display: inline-block;padding: 0px;height:30px;width:50%;margin:0 10px;" id="mouseIco" name="mouseIco" value="{$info->mouseIco ?? ''}" placeholder="填写图片链接，上传方式和背景图相同" onchange="changeMouse();">
			  </div>
			  <div style="display: inline-block;margin:0 10px 5px 0;">
				<label class="mouseStyle" onchange="changeMouse();"><input style='margin-right:10px;' id='selectMouseColor' name="selectMouseColor" type='checkbox' <?= empty($info['mouseColor'])?"":"checked='checked'"?> />鼠标拖影颜色</label>
				<input type="color" style="display: inline-block;padding: 0px;height:30px;width:80px;margin:0 10px;" id="mouseColor" name="mouseColor" value="{$info->mouseColor ?? ''}" placeholder="鼠标拖影颜色" onchange="changeMouse();">
				<label class="mouseStyle" onchange="changeMouse();"><input style='margin-right:10px;' id='randomMouseColor' name="randomMouseColor" type='checkbox' <?= (($info->mouseColor ?? '') == "random")?"checked='checked'":""?> />随机颜色</label>
			  </div>
			  <small id="mouseStyle_msg"></small>
			</div>
		  </div>
		  <!-- 按钮操作层 -->
		  <div class="am-u-sm-9 am-u-sm-push-3" style="margin: 0px 0px 220px 0px;">
			<button type="button" id="Btn_Submit" onclick="Submit();" disabled class="am-btn am-btn-primary" style="float:right;margin-left:20px;">提交</button>
			<button type="button" id="Btn_Preview" class="am-btn am-btn-primary" style="margin-left:20px;float:right" onclick="openProfile();">预览</button>
		  </div>
	  </div>
	</div>
</div>
  
<div class="modal fade" id="preview">
	<div class="modal-dialog modal-dialog-centered" style="width:1225px; max-width:9999px">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="btn btn-danger" data-bs-dismiss="modal" style="margin-left: auto;">&times;</button>
			</div>
			<div class="modal-body">
				<iframe id="userView" name="userView" frameborder="0" src="about:blank" style="width: 1280px; height: 765px;"></iframe>
			</div>
		</div>
	</div>
</div>



<script src="//tools.bnszs.com/js/bns.server.js?v=20250316102"></script>    
<script>
	initServer("areaSelect","serverSelect", getQueryVariable("area"), getQueryVariable("server"));
	
	$(document).ready(function(){
	  changeColor();
	  changeMouse();
	});
	
	function changeMouse(){
	  var color="", ico = "";
	  if($('#selectMouseIco').prop("checked")){
		ico = $("#mouseIco").val();
		$('#selectMouseIco').val(ico);
	  }
	  if($('#selectMouseColor').prop("checked")){
		color = $("#mouseColor").val();
		$('#selectMouseColor').val(color);
	  }
	  if($('#randomMouseColor').prop("checked")){
		color = "random";
		$('#selectMouseColor').val(color);
	  }
	}
	
	function changeColor(){
	  var color="", shadow= "",fontFamily = "";
	  if($('#selectFontFamily').prop("checked")){
		fontFamily ="font-family:"+ $("#fontFamily").val()+" !important;";
		$('#selectFontFamily').val($("#fontFamily").val());
	  }
	  if($('#selectTextColor').prop("checked")){
		color ="color:"+ $("#textColor").val()+" !important;";
		$('#selectTextColor').val($("#textColor").val());
	  }
	  if($('#selectTextShadowColor').prop("checked")){
		var shadowColor= $("#textShadowColor").val();
		shadow ="text-shadow:"+shadowColor+" 0px 0px 4px,"+shadowColor+" 0px 0px 8px !important;";
		$('#selectTextShadowColor').val(shadowColor);
	  }
	  $(".fontStyle").css("cssText",fontFamily+color + shadow); 
	}
	 
	//服务器选择事件
	$('#serverSelect').change(function() {
		// 检查控件类型
		if($("#rolenameSelect").attr('type') != null) return;

		$("#rolenameSelect option").remove();
		$("#rolenameSelect").append('<option value="0">请选择角色</option>');
			
		//判断服务器信息
		var server = $(this).val();
		if (server == null) return;
		var name = getQueryVariable("name");

		$.ajax({
			type: "POST",
			dataType: "json",
			url: window.location.href,
			data: { "mode":"query","server":server },
			success: function (result) {
				if(result.data.length == 0) {
					alert("当前服务器没有角色");
					$("#rolename_msg").css("color","red");		
				} else {
					$("#rolename_msg").html("");
					for(var i=0; i<result.data.length; i++){
						var target = result.data[i].name == name;
						$("#rolenameSelect").append('<option '+ (target?'selected':null)+' data-pcid="' + result.data[i].newpcid + '">' + result.data[i].name + '</option>');
					}
				}
			},
			error : function() {
				alert("获取角色信息失败，请稍候再试");
			}
		});
	})
				
	//指示是修改角色名的情况	
	var IsChangingName = false;	
	var oServer = null;
	var oName = null;	
		
	//对角色框双击	
	$('#rolenameSelect').dblclick(function() {
		IsChangingName = !IsChangingName;
			
		if(IsChangingName) {
			if($("#rolenameSelect").attr("status") == "empty"){
	
				alert("此角色没有自定义信息，无法作为应用源数据");
				IsChangingName = false;
					
			} else {
				oServer = $("#serverSelect").val();
				oName   = $("#rolenameSelect").val();
				
				//关闭不可提交的前端显示
				$('#Btn_Submit').attr("disabled",false);
				
				$("#status-info").html("已开启快速修改应用角色，点击修改按钮将会立即生效。<br/>双击角色昵称选择框可关闭快速修改。");
				$("#status-info").css("color","#FF00CC");    
			}
		} 
		else {
			$("#status-info").html("已关闭快速修改应用角色，如您修改了角色名将会进入系统审核。<br/>双击角色昵称选择框可开启快速修改。");
			$("#status-info").css("color","#FF00CC"); 
		}
	});
		
	//角色选择事件
	$('#rolenameSelect').change(function() {
		//检测是否是快速修改应用角色状态
		if(IsChangingName && $("#rolenameSelect").val() != 0){
			return;
		}

		var area = $("#areaSelect").find("option:selected").val();
		var server = $("#serverSelect").find("option:selected").val();
		var role = $(this).find("option:selected").text();
		if (role == "请选择角色" || role == "0") return;

		window.location.href = window.location.href.split('?')[0] + "?area=" + area + "&server=" + server + "&name=" + role;
	})


	// 属性检查
	$('#opacity').bind('input propertychange', function() {
		$("#opacity_msg").html("");

		var value = parseFloat($(this).val());
		if (value < 0 || value > 1){
			$("#opacity_msg").html("界面不透明度超出范围，请填写 0.00 ~ 1.00 范围内的数字。&nbsp;快捷填写：0为完全透明，0.5为半透明，1为完全不透明。<br/>注意：界面不透明度越高，背景图会越虚化，等于1时将不显示背景图");
		}
	});

	$("input").blur(function(){
		if(IsChangingName) {
			alert("处于快速修改应用角色状态时，只能修改服务器与角色名。\n\n如需修改其他信息，请在提交后刷新 或 退出快速修改应用角色 即可。");
		}
			
		var area_id = $("#serverSelect").val();
		var rolename = $("#rolename").val();
		var contact = $("#contact").val();
		var cdkey = $("#cdkey").val();
		if (area_id == null || area_id == undefined || area_id == '') {
			$("#serverSelect_msg").html("服务器还没有选择");
			$("#serverSelect_msg").css("color","red");
			$('#Btn_Submit').attr("disabled",true);
			return false;
		}else{
			$("#serverSelect_msg").html("");
			$('#Btn_Submit').attr("disabled",false);
		}
			
		if (cdkey == ""){
			$("#ccdkey_msg").html("请填写激活Cdkey");
			$("#cdkey_msg").css("color","red");
			$('#Btn_Submit').attr("disabled",true);
			return false;
		}else{
			$("#contact_msg").html("");
			$('#Btn_Submit').attr("disabled",false);
		}
	});
		

	function Submit() {
    	if ($("#rolenameSelect").val() == "0") {
        	alert("您还没选择角色");
        	return;
    	}

    	var extra = null;
    	if (IsChangingName) {
        	if (!confirm("您正在快速修改应用角色，资料将会直接迁移到新角色名下\n\n审核状态和队列信息将不发生任何变化，是否确认？")) return;

        	//相同校验
        	if (oName == $("#rolenameSelect").val() && oServer == $("#serverSelect").val()) {
            	alert("快速修改应用角色时，前后角色不能相同");
            	return;
        	}

        	extra = "&ischangingname&oname=" + oName + "&oserver=" + oServer;
    	} 
		else {
        	var Status = true;

        	//判断图片是否为直链
       		$("#userinfo  input[imagecheck='true']").each(function() {
                var value = $(this).val();

                //设置需要判断的图床主域名
                var JudgeDomain = [
                    'imgchr',
                    'imgbed',
                    'i.loli',
                    'hualigs',
                    'sinaimg.cn',
                ];

                //设置以上图床可被允许使用的文件扩展名
                var AllowExts = [
                    '.png',
                    '.gif',
                    '.jpg',
                    '.jpeg',
                    '.bmp',
                ];

                //设置禁用的图床主域名及提示原因
                var BanDomain = {
                    'imgbed': '因访问速度过慢禁止继续使用',
                    'm.qpic.cn': '该图源禁止外链，无法正常显示',
                };

                if (value != null && value != "") {

                    for (key in BanDomain) {
                        //如果检测到相关图床主域名，判断是否以特定后缀结束
                        if (value.indexOf(key) >= 0) {
                            Status = false;
                            alert(key + " " + BanDomain[key] + "\r\n请使用其他图床上传涉及图片，给您带来不便敬请谅解！");

                            return;
                        }
                    }

                    for (x in JudgeDomain) {
                        //如果检测到相关图床主域名，判断是否以特定后缀结束
                        if (value.indexOf(JudgeDomain[x]) >= 0) {
                            var LegalExt = false;

                            for (Ext in AllowExts) {
                                //通过正则判断结尾符
                                var reg = new RegExp(AllowExts[Ext] + "$");
                                if (reg.test(value)) {
                                    LegalExt = true;
                                    break;
                                }
                            }

                            //获取当前文本值归属名称，用于返回提示
                            var name = $(this).parent().parent().find("label").html();
                            if (!LegalExt) {
                                Status = false;
                                alert(name + " 图片链接设置错误，请填写直链");
                                return;
                            }
                            break;
                        }
                    }
                }
            });

        	if (!Status) return;
        	if (!confirm("修改信息后会重新进入审核状态\n审核期间将恢复默认查询，是否确认提交?")) return;
		}

    	$.ajax({
        	type: "POST",
        	dataType: "json",
        	url: window.location.href,
        	data: $('#userinfo').serialize() + extra + "&mode=1",
        	success: function(result) {
            	alert(result.msg);
            	if (result.code == "success") location.reload();
        	},
        	error: function() {
            	alert("发生未知错误，提交失败");
        	}
    	});
	}

	//跳转装备查询页面
	function openProfile(isPreview = true){
		var server = $("#serverSelect").val();
		var name = $("#rolenameSelect").val();
	
		if (server == null || server == undefined || server == '') {
			alert("请先选择角色"); return false;
		} else if (name == null || name == undefined || name == '') {
			alert("请先选择角色"); return false;
		}
		
		var formdeal= document.getElementById("userinfo");
		formdeal.action= "https://tools.bnszs.com/ingame/bs/character/profile?s="+ server +"&c="+ encodeURI(name) + (isPreview?"&preview=true":"");
	  	formdeal.target="userView";
		formdeal.submit();  //提交表单
		$('#preview').modal("show");
	}
</script>