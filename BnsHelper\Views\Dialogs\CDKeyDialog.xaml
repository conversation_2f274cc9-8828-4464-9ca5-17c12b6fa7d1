<Border x:Class="Xylia.Preview.UI.Views.Dialogs.CDKeyDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:hc="https://handyorg.github.io/handycontrol"
        Background="{DynamicResource RegionBrush}" CornerRadius="8"
        Width="400" Margin="50 0 0 30">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="{DynamicResource CDKeyDialog_Title}" FontSize="16" FontWeight="Bold" Margin="20,20,20,10" HorizontalAlignment="Center"/>

        <!-- 输入区域 -->
        <StackPanel Grid.Row="1" Margin="20,10">
            <TextBox x:Name="CDKeyTextBox" Text="{Binding CdKey, UpdateSourceTrigger=PropertyChanged}"
                     FontSize="14" Padding="10,8" MaxLength="50"
                     hc:InfoElement.Placeholder="{DynamicResource CDKeyDialog_Placeholder}"
                     hc:InfoElement.ShowClearButton="True"/>
            
            <!-- 错误提示 -->
            <TextBlock Text="{Binding ErrorMessage}"
                       Foreground="Red"
                       Margin="0,5,0,0"
                       Visibility="{Binding HasError, Converter={StaticResource Boolean2VisibilityConverter}}"/>
        </StackPanel>

        <!-- 按钮区域 -->
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Right" 
                    Margin="20,10,20,20">
            <Button Content="{DynamicResource Button_Cancel}" Command="{Binding CloseCommand}" IsCancel="True" Width="80" Height="32" Margin="0,0,10,0" />
            <Button Content="{DynamicResource Button_Activate}" Command="{Binding ActivateCommand}" IsDefault="True" Width="80" Height="32" Style="{StaticResource ButtonPrimary}"/>
        </StackPanel>
    </Grid>
</Border>
