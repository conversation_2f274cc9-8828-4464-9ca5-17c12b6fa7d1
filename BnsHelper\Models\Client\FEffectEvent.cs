﻿
using CommunityToolkit.Mvvm.ComponentModel;
using Newtonsoft.Json;
using System.ComponentModel;
using Xylia.Preview.Data.Models.Sequence;

namespace Xylia.BnsHelper.Models;
internal struct FEffectEvent
{
	public DateTime Time;
	[JsonIgnore] public string Caster;
	[JsonIgnore] public string Target;
	public string Skill;
	public long Damage;
	public SkillResultSeq Type;
}

/// <summary>
/// 目标统计数据
/// </summary>
internal class FTargetResult : INotifyPropertyChanged
{
	public FPlayer Owner { get; set; }
	public string Name { get; set; } = string.Empty;

	// 时间信息
	public DateTime? StartTime { get; set; }
	public DateTime EndTime { get; set; }

	/// <summary>
	/// 对该目标的战斗时间（秒）
	/// </summary>
	public double Seconds => StartTime.HasValue ? Math.Ceiling((EndTime - StartTime.Value).TotalSeconds) : 0;

	/// <summary>
	/// 目标是否已死亡
	/// </summary>
	public bool IsDead { get; set; } = false;

	private long _damage;
	public long Damage
	{
		get => _damage;
		set
		{
			_damage = value;
			OnPropertyChanged(nameof(Damage));
			OnPropertyChanged(nameof(DamageRate));
		}
	}

	private int _hitCount;
	public int HitCount
	{
		get => _hitCount;
		set
		{
			_hitCount = value;
			OnPropertyChanged(nameof(HitCount));
		}
	}

	private int _criticalHitCount;
	public int CriticalHitCount
	{
		get => _criticalHitCount;
		set
		{
			_criticalHitCount = value;
			OnPropertyChanged(nameof(CriticalHitCount));
			OnPropertyChanged(nameof(CriticalRate));
		}
	}

	private int _dodgeCount;
	public int DodgeCount
	{
		get => _dodgeCount;
		set
		{
            _dodgeCount = value;
			OnPropertyChanged(nameof(DodgeCount));
			OnPropertyChanged(nameof(HitRate));
		}
	}

	private int _parryCount;
	public int ParryCount
	{
		get => _parryCount;
		set
		{
			_parryCount = value;
			OnPropertyChanged(nameof(ParryCount));
		}
	}

	public double DamageRate => Owner?.TotalDamage > 0 ? (double)Math.Max(0, Damage) / Owner.TotalDamage : 0;
	public double CriticalRate => HitCount > 0 ? (double)CriticalHitCount / HitCount : 0;
	public double HitRate => (HitCount + DodgeCount) > 0 ? (double)HitCount / (HitCount + DodgeCount) : 0;
	public long DamagePerHit => HitCount > 0 ? Math.Max(0, Damage) / HitCount : 0;

	public void Refresh()
	{
		OnPropertyChanged(nameof(Damage));
		OnPropertyChanged(nameof(DamageRate));
		OnPropertyChanged(nameof(CriticalRate));
		OnPropertyChanged(nameof(HitRate));
		OnPropertyChanged(nameof(DamagePerHit));
	}

	public event PropertyChangedEventHandler? PropertyChanged;

	protected void OnPropertyChanged(string propertyName)
	{
		PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
	}
}

internal class FEffectResult : ObservableObject
{
	#region Fields
	public FPlayer? Owner { get; set; }
	public string? Name { get; set; }
	public long Damage { get; set; }
	public long HitCount { get; set; }
	public long CriticalHitCount { get; set; }
	public long BlockedCount { get; set; }
	public long DodgeCount { get; set; }
	public double CriticalHitRate => 1d * CriticalHitCount / HitCount;
	public double TotalRate => Owner!.TotalDamage > 0 ? 1d * Math.Max(0, Damage) / Owner.TotalDamage : 0;
	#endregion

	#region Methods
	public void Refresh()
	{
		OnPropertyChanged(nameof(Damage));
		OnPropertyChanged(nameof(CriticalHitRate));
		OnPropertyChanged(nameof(TotalRate));
	}
	#endregion
}
