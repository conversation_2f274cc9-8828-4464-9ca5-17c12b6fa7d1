﻿using System.Globalization;
using System.Windows.Data;
using System.Windows.Markup;
using Xylia.BnsHelper.Properties;
using Xylia.BnsHelper.Resources;
using Xylia.Preview.Common.Attributes;
using Xylia.Preview.Common.Extension;
using Xylia.Preview.Data.Models;

namespace Xylia.BnsHelper.Common.Converters;
public class NameConverter : MarkupExtension, IValueConverter
{
	public override object ProvideValue(IServiceProvider serviceProvider) => this;

	public object? Convert(object value, Type targetType, object? parameter, CultureInfo? culture) => Convert(value, parameter);

	public object ConvertBack(object value, Type targetType, object? parameter, CultureInfo? culture)
	{
		throw new NotImplementedException();
	}

	internal static string? Convert(object value, object? parameter = null) => (parameter switch
	{
		"zone" when value is int zone => ResourceProvider.Instance.Provider.GetTable<Zone>()[zone]?.Attributes["name"]?.ToString(),
		_ when value is Enum e => StringHelper.Get(e.GetAttribute<TextAttribute>()?.Alias ?? e.ToString()),
		_ => null,
	}) ?? value?.ToString();
}