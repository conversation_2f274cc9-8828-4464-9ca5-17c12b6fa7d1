<?php

namespace app\manage\controller\manage;

use app\common\controller\BaseController;
use think\App;
use think\Request;
use think\facade\Db;
use think\facade\Session;
use think\facade\View;
use think\facade\Cache;
use app\manage\model\BnsUserInfo as BnsUserInfo;
use app\manage\model\Profile as ProfileModel;
use app\manage\model\User as User;

class Profile extends BaseController 
{
    private $isAdmin = false;
    private $uid = 0;

    public function __construct(App $app, Request $request) {
        parent::__construct($app, $request);
        
        $this->isAdmin = $this->request->get('admin') && ($_SESSION['admin'] ?? null);
        $this->uid = $_SESSION['user'] ?? null;

        if(!$this->isAdmin && ($this->uid == 0 || $this->uid == null)) {
            return redirect("/bns/manage/login");
        }
    }

    /**
     * 个人资料主页
     */
    public function index() {
        $server = $this->request->param('server');
        $name = $this->request->param('name');

        // 获取过期信息
        if(!$this->isAdmin) {
            $user = User::Get($this->uid);
            $expiration = User::GetExpiration($this->uid, 'customize');
            $time = $expiration == -1 ? '和天地一样久' : date("Y年m月d号 H时i分", $expiration);
            
            if($expiration == 0) {
                return $this->error("您还未获得使用权限");
            }
            
            // 检查权限是否过期（暂时注释掉）
            // if($expiration != -1 && $expiration < time()) {
            //     return $this->error("您的使用权限已在". $time. "过期，重新激活后才能再次使用。");
            // }
        }

        if ($this->request->isPost()) {
            $mode = $this->request->post('mode', '');
            
            switch ($mode) {
                // 查询角色
                case 'query': 
                    $roles = BnsUserInfo::GetRoles($user->uin ?? 0, $server);
                    return json(['code' => 1, 'data' => $roles]);
                    
                // 提交个人资料
                default: 
                    $result = ProfileModel::Submit($server, $name, $this->isAdmin);
                    return json(['code' => 1, 'msg' => $result]);
            }
        } 

        // 获取状态信息
        $status = ProfileModel::GetStatus($server, $name);
        $userInfo = $this->isAdmin ? null : User::Get($this->uid);
        
        // 获取管理员菜单项
        $adminMenuItems = [];
        if (session('admin')) {
            $adminMenuItems = \app\bns\model\Manage\UserAdmin::GetItems();
        }

        return View::fetch('manage/profile', [
            'server' => $server,
            'name' => $name,
            'status' => $status,
            'user_info' => $userInfo,
            'is_admin' => $this->isAdmin,
            'expiration_time' => $this->isAdmin ? null : User::GetExpiration($this->uid, 'customize'),
            'adminMenuItems' => $adminMenuItems
        ]);
    }

    /**
     * 主要功能处理（兼容旧接口）
     */
    public function main() {
        return $this->index();
    }

    /**
     * 创建个人资料
     */
    public function create() {
        if ($this->request->isPost()) {
            $data = $this->request->post();
            
            // 验证必要字段
            if (empty($data['server']) || empty($data['name'])) {
                return json(['code' => 0, 'msg' => '服务器和角色名不能为空']);
            }

            try {
                $result = ProfileModel::Create($data, $this->uid, $this->isAdmin);
                return json(['code' => 1, 'msg' => '个人资料创建成功', 'data' => $result]);
            } catch (\Exception $e) {
                return json(['code' => 0, 'msg' => $e->getMessage()]);
            }
        }

        return View::fetch('Manage/profile_create');
    }

    /**
     * 编辑个人资料
     */
    public function edit() {
        $profileId = $this->request->get('id', 0);
        
        if (empty($profileId)) {
            abort(404);
        }

        $profile = ProfileModel::find($profileId);
        
        if (!$profile) {
            abort(404);
        }

        // 权限检查
        if (!$this->isAdmin && $profile['user_id'] != $this->uid) {
            abort(403);
        }

        if ($this->request->isPost()) {
            $data = $this->request->post();
            
            try {
                $result = ProfileModel::Update($profileId, $data, $this->isAdmin);
                return json(['code' => 1, 'msg' => '个人资料更新成功']);
            } catch (\Exception $e) {
                return json(['code' => 0, 'msg' => $e->getMessage()]);
            }
        }

        return View::fetch('Manage/profile_edit', [
            'profile' => $profile
        ]);
    }

    /**
     * 删除个人资料
     */
    public function delete() {
        $profileId = $this->request->post('id', 0);
        
        if (empty($profileId)) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        $profile = ProfileModel::find($profileId);
        
        if (!$profile) {
            return json(['code' => 0, 'msg' => '个人资料不存在']);
        }

        // 权限检查
        if (!$this->isAdmin && $profile['user_id'] != $this->uid) {
            return json(['code' => 0, 'msg' => '没有权限']);
        }

        try {
            ProfileModel::Delete($profileId);
            return json(['code' => 1, 'msg' => '删除成功']);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 获取我的个人资料列表
     */
    public function myProfiles() {
        if ($this->isAdmin) {
            // 管理员可以查看所有资料
            $profiles = ProfileModel::getAllProfiles();
        } else {
            // 普通用户只能查看自己的资料
            $profiles = ProfileModel::getUserProfiles($this->uid);
        }

        return View::fetch('Manage/my_profiles', [
            'profiles' => $profiles,
            'is_admin' => $this->isAdmin
        ]);
    }

    /**
     * 查询角色信息
     */
    public function queryRoles() {
        $server = $this->request->post('server', 0);
        $uin = $this->request->post('uin', 0);
        
        if (empty($server)) {
            return json(['code' => 0, 'msg' => '服务器不能为空']);
        }

        try {
            if (!$this->isAdmin) {
                $user = User::Get($this->uid);
                $uin = $user->uin ?? 0;
            }
            
            $roles = BnsUserInfo::GetRoles($uin, $server);
            return json(['code' => 1, 'data' => $roles]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 获取个人资料状态
     */
    public function getStatus() {
        $server = $this->request->get('server', 0);
        $name = $this->request->get('name', '');
        
        if (empty($server) || empty($name)) {
            return json(['code' => 0, 'msg' => '参数不完整']);
        }

        try {
            $status = ProfileModel::GetStatus($server, $name);
            return json(['code' => 1, 'data' => $status]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 上传头像
     */
    public function uploadAvatar() {
        if ($this->request->isPost()) {
            $file = $this->request->file('avatar');
            
            if (!$file) {
                return json(['code' => 0, 'msg' => '请选择头像文件']);
            }

            // 验证文件
            $validate = [
                'size' => 2 * 1024 * 1024, // 2MB
                'ext' => 'jpg,jpeg,png,gif'
            ];

            if (!$file->check($validate)) {
                return json(['code' => 0, 'msg' => $file->getError()]);
            }

            try {
                $saveName = \think\facade\Filesystem::disk('public')->putFile('avatars', $file);
                
                if (!$saveName) {
                    return json(['code' => 0, 'msg' => '头像上传失败']);
                }

                // 更新用户头像
                User::updateAvatar($this->uid, $saveName);
                
                return json(['code' => 1, 'msg' => '头像上传成功', 'data' => $saveName]);
            } catch (\Exception $e) {
                return json(['code' => 0, 'msg' => $e->getMessage()]);
            }
        }

        return View::fetch('Manage/upload_avatar');
    }

    /**
     * 个人资料预览
     */
    public function preview() {
        $server = $this->request->get('server', 0);
        $name = $this->request->get('name', '');
        
        if (empty($server) || empty($name)) {
            abort(404);
        }

        $profile = ProfileModel::getByServerAndName($server, $name);
        
        if (!$profile) {
            abort(404);
        }

        return View::fetch('Manage/profile_preview', [
            'profile' => $profile
        ]);
    }

    /**
     * 导出个人资料
     */
    public function export() {
        $profileId = $this->request->get('id', 0);
        
        if (empty($profileId)) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        $profile = ProfileModel::find($profileId);
        
        if (!$profile) {
            return json(['code' => 0, 'msg' => '个人资料不存在']);
        }

        // 权限检查
        if (!$this->isAdmin && $profile['user_id'] != $this->uid) {
            return json(['code' => 0, 'msg' => '没有权限']);
        }

        try {
            $data = ProfileModel::exportProfile($profileId);
            
            header('Content-Type: application/json');
            header('Content-Disposition: attachment; filename="profile_' . $profile['name'] . '_' . date('Y-m-d') . '.json"');
            
            echo json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            exit;
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => $e->getMessage()]);
        }
    }
}
