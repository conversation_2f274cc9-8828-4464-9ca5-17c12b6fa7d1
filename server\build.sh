#!/bin/bash

# 编译脚本：生成适用于各种Linux发行版的二进制文件
# 此脚本会生成静态链接的二进制文件，通过禁用CGO来确保最大的兼容性

# 创建输出目录
mkdir -p build

# 设置版本号和构建日期
VERSION="1.0.0"
BUILD_DATE=$(date "+%Y%m%d")

# 确保嵌入式配置文件存在
if [ ! -f "pkg/config/default_config.yaml" ]; then
    echo "错误：嵌入式配置文件不存在 pkg/config/default_config.yaml"
    exit 1
fi

# 编译函数
build() {
    local ARCH=$1
    local OUTPUT="build/bnszs-server-linux-$ARCH"
    
    echo "开始编译 $ARCH 架构的二进制文件..."
    
    # 使用环境变量进行跨平台编译
    # CGO_ENABLED=0 确保静态链接，增强兼容性
    # -trimpath 移除构建路径信息，增强可重现性
    # -ldflags 设置链接标志，包括禁用符号表和DWARF生成以减小文件大小
    
    env CGO_ENABLED=0 GOOS=linux GOARCH=$ARCH \
    go build -trimpath \
    -ldflags="-w -s -X main.Version=$VERSION -X main.BuildDate=$BUILD_DATE" \
    -o $OUTPUT ./cmd
    
    if [ $? -eq 0 ]; then
        echo "✅ 编译成功: $OUTPUT"
        # 给二进制文件添加执行权限
        chmod +x $OUTPUT
    else
        echo "❌ 编译失败: $ARCH"
        exit 1
    fi
}

echo "开始构建适用于Linux的二进制文件..."

# AMD64/x86_64架构 (通常用于大多数服务器)
build "amd64"

# ARM64架构 (如树莓派4、AWS Graviton等)
# 取消注释下一行以构建ARM64版本
# build "arm64"

# 创建一个简单的发布包
echo "创建发布包..."
mkdir -p build/release
cp build/bnszs-server-linux-amd64 build/release/bnszs-server
cp -r config build/release/ 2>/dev/null || :  # 如果有配置文件夹则复制，没有则忽略错误

# 复制安装脚本
cp install.sh build/release/

# 添加执行权限
chmod +x build/release/install.sh

# 创建服务文件
cat > build/release/bnszs-server.service << EOF
[Unit]
Description=BNSZS UDP Server
After=network.target mysql.service

[Service]
Type=simple
User=root
WorkingDirectory=/opt/bnszs
ExecStart=/opt/bnszs/bnszs-server
Restart=on-failure
RestartSec=5s

[Install]
WantedBy=multi-user.target
EOF

# 创建README文件
cat > build/release/README.txt << EOF
BNSZS UDP 服务器 v$VERSION (构建于 $BUILD_DATE)

【安装说明】
1. 使用一键安装脚本 (推荐):
   chmod +x install.sh
   sudo ./install.sh

2. 手动安装:
   a. 将整个目录复制到服务器上，如 /opt/bnszs/
      例如: mkdir -p /opt/bnszs && cp -r * /opt/bnszs/

   b. 配置文件将在首次运行时自动创建在 /opt/bnszs/config/config.yaml
      或者您也可以手动创建配置文件

   c. 设置权限:
      chmod +x /opt/bnszs/bnszs-server

   d. 安装为系统服务:
      cp /opt/bnszs/bnszs-server.service /etc/systemd/system/
      systemctl daemon-reload
      systemctl enable bnszs-server
      systemctl start bnszs-server

【配置说明】
- 配置文件默认位于 ./config/config.yaml
- 您也可以使用命令行参数指定配置文件: ./bnszs-server -config /path/to/config.yaml
- 使用 -init-config 参数初始化配置文件: ./bnszs-server -init-config
- 使用 -version 参数查看版本信息: ./bnszs-server -version

【日志查看】
- 如果作为服务运行: journalctl -u bnszs-server -f
EOF

# 创建最终的tar.gz包
cd build/release
tar -czf ../bnszs-server-linux-$VERSION.tar.gz *
cd ../..

echo "构建完成! 发布包位于 build/bnszs-server-linux-$VERSION.tar.gz"
echo "提示: 这些二进制文件应该兼容CentOS 9和Ubuntu 22.04等大多数现代Linux发行版" 