<?php
namespace app\guidebook\controller;

use think\App;
use think\Request;
use app\common\controller\BaseController;
use app\ingame\model\BnsTop;

class Api extends BaseController
{
    public function __construct(App $app, Request $request) {
        parent::__construct($app, $request);
    }

    /**
     * 获取排行榜JSON数据
     */
    public function topJson() {
        try {
            $count = $this->request->param('count', 1000);
            $data = BnsTop::GetPowerTop($count);

            return json(['code' => 0, 'data' => $data]);

        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '获取排行榜失败: ' . $e->getMessage()]);
        }
    }


}
