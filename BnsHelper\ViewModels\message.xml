<table>
	<record alias="Msg.Battle.Attack.Hit.HP.Damage"><arg p="3:skill.name2"/>命中<arg p="2:creature.name"/>，造成了<arg p="5:integer"/>点伤害。</record>
	<record alias="Msg.Battle.Attack.Hit.Attach.Damage"><arg p="3:skill.name2"/>命中<arg p="2:creature.name"/>，造成了<arg p="5:integer"/>点伤害及<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Attack.Hit.Interval.Damage"><arg p="3:skill.name2"/>的<arg p="4:effect.name2"/>效果给<arg p="2:creature.name"/>造成了<arg p="5:integer"/>点伤害。</record>
	<record alias="Msg.Battle.Attack.Hit.Attach.Fail.Damage"><arg p="3:skill.name2"/>命中<arg p="2:creature.name"/>，造成了<arg p="5:integer"/>伤害，但抵抗了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Attack.Hit.Detech.Damage"><arg p="3:skill.name2"/>命中<arg p="2:creature.name"/>，造成了<arg p="5:integer"/>点伤害，并且解除了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Attack.Hit.HP.Heal">由于<arg p="3:skill.name2"/>的效果影响，<arg p="2:creature.name"/>恢复了<arg p="5:integer"/>点生命。</record>
	<record alias="Msg.Battle.Attack.Hit.HP.Heal.Interval">由于<arg p="3:skill.name2"/>的效果影响，<arg p="2:creature.name"/>恢复了<arg p="5:integer"/>点生命。</record>
	<record alias="Msg.Battle.Attack.Hit.SP.Heal">由于<arg p="3:skill.name2"/>的效果影响，<arg p="2:creature.name"/>恢复了<arg p="5:integer"/>点内力。</record>
	<record alias="Msg.Battle.Attack.Hit.SP.Heal.Interval">由于<arg p="3:skill.name2"/>的效果影响，<arg p="2:creature.name"/>恢复了<arg p="5:integer"/>点内力。</record>
	<record alias="Msg.Battle.Attack.Hit.SP.Damage"><arg p="3:skill.name2"/>命中<arg p="2:creature.name"/>，造成了<arg p="5:integer"/>点内力伤害。</record>
	<record alias="Msg.Battle.Attack.Hit.SP.Interval"><arg p="3:skill.name2"/>的<arg p="4:effect.name2"/>对<arg p="2:creature.name"/>造成了<arg p="5:integer"/>点内力伤害。</record>
	<record alias="Msg.Battle.Attack.HitHP.Drain"><arg p="2:creature.name"/>被<arg p="3:skill.name2"/>命中，造成了<arg p="5:integer"/>点伤害及吸收了<arg p="6:integer"/>点生命。</record>
	<record alias="Msg.Battle.Attack.Hit.SP.Drain"><arg p="2:creature.name"/>被<arg p="3:skill.name2"/>命中，造成了<arg p="5:integer"/>点伤害及吸收了<arg p="7:integer"/>点内力。</record>
	<record alias="Msg.Battle.Attack.HitHP.SP.Drain"><arg p="3:skill.name2"/>命中<arg p="2:creature.name"/>，造成了<arg p="5:integer"/>点伤害，并且吸收了<arg p="6:integer"/>点生命并吸收了<arg p="7:integer"/>点内力。</record>
	<record alias="Msg.Battle.Attack.Hit.Attach"><arg p="3:skill.name2"/>命中<arg p="2:creature.name"/>，造成了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Attack.Hit.Attach.Fail"><arg p="3:skill.name2"/>命中了<arg p="2:creature.name"/>，但抵抗了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Attack.Hit.Detech"><arg p="3:skill.name2"/>命中<arg p="2:creature.name"/>解除了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Attack.Hit.Exhaustion"><arg p="2:creature.name"/>受到<arg p="3:skill.name2"/>的效果而陷入了濒死状态。</record>
	<record alias="Msg.Battle.Attack.Hit.Dead"><arg p="2:creature.name"/>受到<arg p="3:skill.name2"/>的效果而死亡了。</record>
	<record alias="Msg.Battle.Attack.Cri.HP.Damage"><arg p="3:skill.name2"/>命中<arg p="2:creature.name"/>，造成了<arg p="5:integer"/>点暴击伤害。</record>
	<record alias="Msg.Battle.Attack.Cri.Attach.Damage"><arg p="3:skill.name2"/>命中<arg p="2:creature.name"/>，造成了<arg p="5:integer"/>点暴击伤害及<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Attack.Cri.Attach.Fail.Damage"><arg p="3:skill.name2"/>命中<arg p="2:creature.name"/>，造成了<arg p="5:integer"/>点暴击伤害，但抵抗了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Attack.Cri.Detech.Damage"><arg p="3:skill.name2"/>命中<arg p="2:creature.name"/>，造成了<arg p="5:integer"/>点暴击伤害，并且解除了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Attack.Cri.SP.Damage"><arg p="3:skill.name2"/>命中<arg p="2:creature.name"/>，造成了<arg p="5:integer"/>点内力伤害。</record>
	<record alias="Msg.Battle.Attack.Cri.SP.Drain"><arg p="3:skill.name2"/>命中了<arg p="2:creature.name"/>，造成<arg p="5:integer"/>点暴击伤害，并吸收了<arg p="7:integer"/>点内力。</record>
	<record alias="Msg.Battle.Attack.CriHP.Drain"><arg p="3:skill.name2"/>命中<arg p="2:creature.name"/>，造成了<arg p="5:integer"/>点暴击伤害，并且吸收了<arg p="6:integer"/>点生命。</record>
	<record alias="Msg.Battle.Attack.CriHP.SP.Drain"><arg p="3:skill.name2"/>命中<arg p="2:creature.name"/>，造成了<arg p="5:integer"/>点暴击伤害，并且吸收了<arg p="6:integer"/>点生命并吸收了<arg p="7:integer"/>点内力。</record>
	<record alias="Msg.Battle.Attack.Cri.Attach"><arg p="3:skill.name2"/>命中<arg p="2:creature.name"/>，造成了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Attack.Cri.Attach.Fail"><arg p="3:skill.name2"/>命中了<arg p="2:creature.name"/>，但抵抗了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Attack.Cri.Detech"><arg p="3:skill.name2"/>命中<arg p="2:creature.name"/>解除了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Attack.Cri.Exhaustion"><arg p="2:creature.name"/>受到<arg p="3:skill.name2"/>的效果而陷入了濒死状态。</record>
	<record alias="Msg.Battle.Attack.Cri.Dead"><arg p="2:creature.name"/>受到<arg p="3:skill.name2"/>的效果而死亡了。</record>
	<record alias="Msg.Battle.Attack.Parry.Effect.None"><arg p="3:skill.name2"/>被<arg p="2:creature.name"/>格挡了。</record>
	<record alias="Msg.Battle.Attack.Parry.HP.Damage">虽然<arg p="3:skill.name2"/>被<arg p="2:creature.name"/>格挡了，但还是造成了<arg p="5:integer"/>点伤害。</record>
	<record alias="Msg.Battle.Attack.Parry.Attach.Damage">虽然<arg p="3:skill.name2"/>被<arg p="2:creature.name"/>格挡了，但还是造成了<arg p="5:integer"/>伤害及<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Attack.Parry.Attach.Fail.Damage">虽然<arg p="3:skill.name2"/>被<arg p="2:creature.name"/>格挡了，但还是造成了<arg p="5:integer"/>点伤害，但抵抗了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Attack.Parry.Detech.Damage">虽然<arg p="3:skill.name2"/>被<arg p="2:creature.name"/>格挡了，但还是造成了<arg p="5:integer"/>伤害，并且解除了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Attack.Parry.SP.Damage">虽然<arg p="3:skill.name2"/>被<arg p="2:creature.name"/>格挡了，但还是造成了<arg p="5:integer"/>点内力伤害。</record>
	<record alias="Msg.Battle.Attack.ParryHP.Drain">虽然<arg p="3:skill.name2"/>被<arg p="2:creature.name"/>格挡了，但还是造成了<arg p="5:integer"/>点伤害，并且吸收了<arg p="6:integer"/>点生命。</record>
	<record alias="Msg.Battle.Attack.Parry.SP.Drain">虽然<arg p="3:skill.name2"/>被<arg p="2:creature.name"/>格挡了，但还是造成了<arg p="5:integer"/>点伤害，并且吸收了<arg p="7:integer"/>点内力。</record>
	<record alias="Msg.Battle.Attack.ParryHP.SP.Drain">虽然<arg p="3:skill.name2"/>被<arg p="2:creature.name"/>格挡了，但还是造成了<arg p="5:integer"/>点伤害，并且吸收了<arg p="6:integer"/>点生命并吸收了<arg p="7:integer"/>点内力。</record>
	<record alias="Msg.Battle.Attack.Parry.Attach">虽然<arg p="3:skill.name2"/>被<arg p="2:creature.name"/>格挡了，但还是造成了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Attack.Parry.Attach.Fail"><arg p="3:skill.name2"/>被<arg p="2:creature.name"/>挡住，抵抗了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Attack.Parry.Detech"><arg p="3:skill.name2"/>被<arg p="2:creature.name"/>格挡了，解除了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Attack.Parry.Exhaustion"><arg p="2:creature.name"/>受到<arg p="3:skill.name2"/>的效果而陷入了濒死状态。</record>
	<record alias="Msg.Battle.Attack.Parry.Dead"><arg p="2:creature.name"/>受到<arg p="3:skill.name2"/>的效果而死亡了。</record>
	<record alias="Msg.Battle.Attack.PParry.Effect.None"><arg p="3:skill.name2"/>被<arg p="2:creature.name"/>完全格挡了。</record>
	<record alias="Msg.Battle.Attack.PParry.HP.Damage">虽然<arg p="3:skill.name2"/>被<arg p="2:creature.name"/>完全挡住，但还是造成了<arg p="5:integer"/>点伤害。</record>
	<record alias="Msg.Battle.Attack.PParry.Attach.Damage">虽然<arg p="3:skill.name2"/>被<arg p="2:creature.name"/>完全挡住，但还是造成了<arg p="5:integer"/>点伤害及<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Attack.PParry.Attach.Fail.Damage">虽然<arg p="3:skill.name2"/>被<arg p="2:creature.name"/>完全挡住，但还是造成了<arg p="5:integer"/>点伤害，但抵抗了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Attack.PParry.Detech.Damage">虽然<arg p="3:skill.name2"/>被<arg p="2:creature.name"/>完全挡住，但还是造成了<arg p="5:integer"/>点伤害，并且解除了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Attack.PParry.SP.Damage">虽然<arg p="3:skill.name2"/>被<arg p="2:creature.name"/>完全挡住，但还是造成了<arg p="5:integer"/>点内力伤害。</record>
	<record alias="Msg.Battle.Attack.PParryHP.Drain">虽然<arg p="3:skill.name2"/>被<arg p="2:creature.name"/>完全挡住，但还是造成了<arg p="5:integer"/>点伤害，并且吸收了<arg p="6:integer"/>点生命。</record>
	<record alias="Msg.Battle.Attack.PParry.SP.Drain">虽然<arg p="3:skill.name2"/>被<arg p="2:creature.name"/>完全挡住，但还是造成了<arg p="5:integer"/>点伤害，并且吸收了<arg p="7:integer"/>点内力。</record>
	<record alias="Msg.Battle.Attack.PParryHP.SP.Drain">虽然<arg p="3:skill.name2"/>被<arg p="2:creature.name"/>完全挡住，但还是造成了<arg p="5:integer"/>点伤害，并且吸收了<arg p="6:integer"/>点生命并吸收了<arg p="7:integer"/>点内力。</record>
	<record alias="Msg.Battle.Attack.PParry.Attach">虽然<arg p="3:skill.name2"/>被<arg p="2:creature.name"/>完全挡住，但还是造成了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Attack.PParry.Attach.Fail"><arg p="3:skill.name2"/>被<arg p="2:creature.name"/>完全挡住，抵抗了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Attack.PParry.Detech"><arg p="3:skill.name2"/>被<arg p="2:creature.name"/>完全挡住，解除了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Attack.PParry.Exhaustion"><arg p="2:creature.name"/>受到<arg p="3:skill.name2"/>的效果而陷入了濒死状态。</record>
	<record alias="Msg.Battle.Attack.PParry.Dead"><arg p="2:creature.name"/>受到<arg p="3:skill.name2"/>的效果而死亡了。</record>
	<record alias="Msg.Battle.Attack.Counter.Effect.None"><arg p="3:skill.name2"/>受到了<arg p="2:creature.name"/>的反击。</record>
	<record alias="Msg.Battle.Attack.Counter.HP.Damage">虽然<arg p="3:skill.name2"/>受到了<arg p="2:creature.name"/>的反击，但还是造成了<arg p="5:integer"/>点伤害。</record>
	<record alias="Msg.Battle.Attack.Counter.Attach.Damage">虽然<arg p="3:skill.name2"/>受到了<arg p="2:creature.name"/>的反击，但还是造成了<arg p="5:integer"/>点伤害及<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Attack.Counter.Attach.Fail.Damage">虽然<arg p="3:skill.name2"/>受到了<arg p="2:creature.name"/>的反击，但还是造成了<arg p="5:integer"/>点伤害，并且抵抗了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Attack.Counter.Detech.Damage">虽然<arg p="3:skill.name2"/>受到了<arg p="2:creature.name"/>的反击，但还是造成了<arg p="5:integer"/>点伤害，并且解除了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Attack.Counter.SP.Damage">虽然<arg p="3:skill.name2"/>受到了<arg p="2:creature.name"/>的反击，但还是造成了<arg p="5:integer"/>点内力伤害。</record>
	<record alias="Msg.Battle.Attack.CounterHP.Drain">虽然<arg p="3:skill.name2"/>受到了<arg p="2:creature.name"/>的反击，但还是造成了<arg p="5:integer"/>点伤害，并且吸收了<arg p="6:integer"/>点生命。</record>
	<record alias="Msg.Battle.Attack.Counter.SP.Drain">虽然<arg p="3:skill.name2"/>受到了<arg p="2:creature.name"/>的反击，但还是造成了<arg p="5:integer"/>点伤害，并且吸收了<arg p="7:integer"/>点内力。</record>
	<record alias="Msg.Battle.Attack.CounterHP.SP.Drain">虽然<arg p="3:skill.name2"/>受到了<arg p="2:creature.name"/>的反击，但还是造成了<arg p="5:integer"/>点伤害，并且吸收了<arg p="6:integer"/>点生命并吸收了<arg p="7:integer"/>点内力。</record>
	<record alias="Msg.Battle.Attack.Counter.Attach">虽然<arg p="3:skill.name2"/>受到了<arg p="2:creature.name"/>的反击，但还是造成了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Attack.Counter.Attach.Fail"><arg p="3:skill.name2"/>受到了<arg p="2:creature.name"/>的反击，抵抗了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Attack.Counter.Detech"><arg p="3:skill.name2"/>受到了<arg p="2:creature.name"/>的反击，<arg p="4:effect.name2"/>被解除了。</record>
	<record alias="Msg.Battle.Attack.Counter.Exhaustion"><arg p="2:creature.name"/>受到<arg p="3:skill.name2"/>的效果而陷入了濒死状态。</record>
	<record alias="Msg.Battle.Attack.Counter.Dead"><arg p="2:creature.name"/>受到<arg p="3:skill.name2"/>的效果而死亡了。</record>
	<record alias="Msg.Battle.Attack.Miss.Effect.None"><arg p="2:creature.name"/>抵抗了<arg p="3:skill.name2"/>。</record>
	<record alias="Msg.Battle.Attack.Dodge.Effect.None"><arg p="2:creature.name"/>闪避了<arg p="3:skill.name2"/>。</record>
	<record alias="Msg.Battle.Attack.Bounce.Effect.None"><arg p="2:creature.name"/>反制了<arg p="3:skill.name2"/>。</record>
	<record alias="Msg.Battle.Attack.Effect.HP.Damage"><arg p="4:effect.name2"/>给<arg p="2:creature.name"/>造成了<arg p="5:integer"/>点伤害。</record>
	<record alias="Msg.Battle.Attack.Effect.Interval.Damage"><arg p="4:effect.name2"/>给<arg p="2:creature.name"/>造成了<arg p="5:integer"/>点伤害。</record>
	<record alias="Msg.Battle.Attack.Effect.HP.Heal"><arg p="4:effect.name2"/>给<arg p="2:creature.name"/>恢复了<arg p="5:integer"/>点生命。</record>
	<record alias="Msg.Battle.Attack.Effect.HP.Heal.Interval"><arg p="4:effect.name2"/>给<arg p="2:creature.name"/>恢复了<arg p="5:integer"/>点生命。</record>
	<record alias="Msg.Battle.Attack.Effect.SP.Heal"><arg p="4:effect.name2"/>给<arg p="2:creature.name"/>恢复了<arg p="5:integer"/>点内力。</record>
	<record alias="Msg.Battle.Attack.Effect.SP.Heal.Interval"><arg p="4:effect.name2"/>给<arg p="2:creature.name"/>恢复了<arg p="5:integer"/>点内力。</record>
	<record alias="Msg.Battle.Attack.Effect.SP.Damage"><arg p="4:effect.name2"/>给<arg p="2:creature.name"/>造成了<arg p="5:integer"/>点内力伤害。</record>
	<record alias="Msg.Battle.Attack.Effect.SP.Interval"><arg p="4:effect.name2"/>给<arg p="2:creature.name"/>造成了<arg p="5:integer"/>点内力伤害。</record>
	<record alias="Msg.Battle.Attack.Effect.Attach"><arg p="2:creature.name"/>受到了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Attack.Effect.Attach.Fail"><arg p="2:creature.name"/>抵抗了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Attack.Effect.Detech"><arg p="2:creature.name"/>的<arg p="4:effect.name2"/>被解除了。</record>
	<record alias="Msg.Battle.Attack.Effect.Exhaustion">由于<arg p="4:effect.name2"/>效果，<arg p="2:creature.name"/>陷入了濒死状态。</record>
	<record alias="Msg.Battle.Attack.Effect.Dead">由于<arg p="4:effect.name2"/>效果，<arg p="2:creature.name"/>死亡了。</record>
	<record alias="Msg.Battle.Hit.HP.Damage">被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，受到了<arg p="5:integer"/>点伤害。</record>
	<record alias="Msg.Battle.Hit.Attach.Damage">被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，受到了<arg p="5:integer"/>点伤害及<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Hit.Interval.Damage">由于<arg p="3:skill.name2"/>的<arg p="4:effect.name2"/>受到了<arg p="5:integer"/>点伤害。</record>
	<record alias="Msg.Battle.Hit.Attach.Fail.Damage">被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，受到了<arg p="5:integer"/>点伤害，但是抵抗了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Hit.Detech.Damage"><arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，造成了<arg p="5:integer"/>点伤害，并且解除了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Hit.HP.Heal">由于<arg p="3:skill.name2"/>恢复了<arg p="5:integer"/>点生命。</record>
	<record alias="Msg.Battle.Hit.HP.Heal.Interval">由于<arg p="3:skill.name2"/>恢复了<arg p="5:integer"/>点生命。</record>
	<record alias="Msg.Battle.Hit.SP.Heal">由于<arg p="3:skill.name2"/>恢复了<arg p="5:integer"/>点内力。</record>
	<record alias="Msg.Battle.Hit.SP.Heal.Interval">由于<arg p="3:skill.name2"/>恢复了<arg p="5:integer"/>点内力。</record>
	<record alias="Msg.Battle.Hit.SP.Damage">被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，受到了<arg p="5:integer"/>点内力伤害。</record>
	<record alias="Msg.Battle.Hit.SP.Interval">由于<arg p="3:skill.name2"/>的<arg p="4:effect.name2"/>，受到了<arg p="5:integer"/>点内力伤害。</record>
	<record alias="Msg.Battle.HitHP.Drain">被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，受到了<arg p="5:integer"/>点伤害，并且被吸收了<arg p="6:integer"/>的生命。</record>
	<record alias="Msg.Battle.Hit.SP.Drain"><arg p="1:creature.name"/>的<arg p="3:skill.name2"/><ga/>命中，受到了<arg p="5:integer"/>伤害。</record>
	<record alias="Msg.Battle.HitHP.SP.Drain"><arg p="1:creature.name"/>的<arg p="3:skill.name2"/><ga/>命中，受到<arg p="5:integer"/>伤害，被吸收<arg p="6:integer"/>生命。</record>
	<record alias="Msg.Battle.Hit.Attach">被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，遭到了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Hit.Attach.Fail">虽然被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，但抵抗了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Hit.Detech">被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，解除了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Hit.Exhaustion">受到<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>影响，陷入了濒死状态。</record>
	<record alias="Msg.Battle.Hit.Dead">受到<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>的影响，死亡了。</record>
	<record alias="Msg.Battle.Cri.HP.Damage">被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，受到了<arg p="5:integer"/>点暴击伤害。</record>
	<record alias="Msg.Battle.Cri.Attach.Damage">被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，受到了<arg p="5:integer"/>点暴击伤害及<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Cri.Attach.Fail.Damage">被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，受到了<arg p="5:integer"/>点暴击伤害，但抵抗了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Cri.Detech.Damage">被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，受到了<arg p="5:integer"/>点暴击伤害，并且解除了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Cri.SP.Damage">被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，受到了<arg p="5:integer"/>点内力伤害。</record>
	<record alias="Msg.Battle.CriHP.Drain">被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，受到了<arg p="5:integer"/>点暴击伤害，并且被吸收了<arg p="6:integer"/>点生命。</record>
	<record alias="Msg.Battle.CriHP.SP.Drain"><arg p="1:creature.name"/>的<arg p="3:skill.name2"/><ga/>命中，受到<arg p="5:integer"/>暴击伤害，被吸收<arg p="6:integer"/>生命。</record>
	<record alias="Msg.Battle.Cri.Attach">被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，遭到了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Cri.Attach.Fail">虽然被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，但抵抗了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Cri.Detech">被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，解除了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Cri.Exhaustion">受到<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>影响，陷入了濒死状态。</record>
	<record alias="Msg.Battle.Cri.Dead">受到<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>的影响，死亡了。</record>
	<record alias="Msg.Battle.Parry.Effect.None">格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>。</record>
	<record alias="Msg.Battle.Parry.HP.Damage">虽然格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，但还是受到了<arg p="5:integer"/>点伤害。</record>
	<record alias="Msg.Battle.Parry.Attach.Damage">虽然格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，但还是受到了<arg p="5:integer"/>点伤害及<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Parry.Attach.Fail.Damage">虽然格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，受到了<arg p="5:integer"/>点伤害，但抵抗了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Parry.Detech.Damage">虽然格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，但还是受到了<arg p="5:integer"/>点伤害，并且解除了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Parry.SP.Damage">虽然格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，但还是受到了<arg p="5:integer"/>点内力伤害。</record>
	<record alias="Msg.Battle.ParryHP.Drain">虽然格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，但还是受到了<arg p="5:integer"/>点伤害，并且被吸收了<arg p="6:integer"/>的生命。</record>
	<record alias="Msg.Battle.Parry.SP.Drain">挡下了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/><eul/>，却受到了<arg p="5:integer"/>伤害。</record>
	<record alias="Msg.Battle.ParryHP.SP.Drain">挡下了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/><eul/>，却受到<arg p="5:integer"/>伤害，被吸收<arg p="6:integer"/>生命。</record>
	<record alias="Msg.Battle.Parry.Attach">虽然格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，但还是受到了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Parry.Attach.Fail">格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，抵抗了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Parry.Detech">格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，解除了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Parry.Exhaustion">受到<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>影响，陷入了濒死状态。</record>
	<record alias="Msg.Battle.Parry.Dead">受到<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>的影响，死亡了。</record>
	<record alias="Msg.Battle.PParry.Effect.None">完全格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>。</record>
	<record alias="Msg.Battle.PParry.HP.Damage">虽然完全格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，但还是受到了<arg p="5:integer"/>点伤害。</record>
	<record alias="Msg.Battle.PParry.Attach.Damage">虽然完全格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，但还是受到了<arg p="5:integer"/>点伤害及<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.PParry.Attach.Fail.Damage">完全格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，受到了<arg p="5:integer"/>点伤害，但抵抗了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.PParry.Detech.Damage">虽然完全格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，但还是受到了<arg p="5:integer"/>点伤害，并且解除了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.PParry.SP.Damage">虽然完全格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，但还是受到了<arg p="5:integer"/>点内力伤害。</record>
	<record alias="Msg.Battle.PParryHP.Drain">虽然完全格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，但还是受到了<arg p="5:integer"/>点伤害，并且被吸收了<arg p="6:integer"/>的生命。</record>
	<record alias="Msg.Battle.PParry.SP.Drain">完美挡下了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/><eul/>，却受到<arg p="5:integer"/>伤害。</record>
	<record alias="Msg.Battle.PParryHP.SP.Drain">完美挡下了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/><eul/>，却受到<arg p="5:integer"/>伤害，被吸收<arg p="6:integer"/>生命。</record>
	<record alias="Msg.Battle.PParry.Attach">虽然完全格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，但还是受到了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.PParry.Attach.Fail">完全格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，抵抗了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.PParry.Detech">完全格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，解除了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.PParry.Exhaustion">受到<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>影响，陷入了濒死状态。</record>
	<record alias="Msg.Battle.PParry.Dead">受到<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>的影响，死亡了。</record>
	<record alias="Msg.Battle.Counter.Effect.None">对<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>进行了反击。</record>
	<record alias="Msg.Battle.Counter.HP.Damage">虽然对<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>进行了反击，但还是受到了<arg p="5:integer"/>点伤害。</record>
	<record alias="Msg.Battle.Counter.Attach.Damage">虽然对<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>进行了反击，但还是受到了<arg p="5:integer"/>点伤害及<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Counter.Attach.Fail.Damage">虽然对<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>进行了反击，受到了<arg p="5:integer"/>点伤害，但抵抗了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Counter.Detech.Damage">虽然对<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>进行了反击，但还是受到了<arg p="5:integer"/>点伤害，并且解除了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Counter.SP.Damage">虽然对<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>进行了反击，但还是受到了受到了<arg p="5:integer"/>点内力伤害。</record>
	<record alias="Msg.Battle.CounterHP.Drain">虽然对<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>进行了反击，但还是受到了受到了<arg p="5:integer"/>点伤害，并且被吸收了<arg p="6:integer"/>的生命。</record>
	<record alias="Msg.Battle.Counter.SP.Drain">对<arg p="1:creature.name"/>的<arg p="3:skill.name2"/><eul/>做出了反击，却受到<arg p="5:integer"/>伤害。</record>
	<record alias="Msg.Battle.CounterHP.SP.Drain">对<arg p="1:creature.name"/>的<arg p="3:skill.name2"/><eul/>做出了反击，却受到<arg p="5:integer"/>伤害，被吸收<arg p="6:integer"/>生命。</record>
	<record alias="Msg.Battle.Counter.Attach">虽然对<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>进行了反击，但还是受到了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Counter.Attach.Fail">对<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>进行了反击，抵抗了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Counter.Detech">对<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>进行了反击，解除了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Counter.Exhaustion">受到<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>影响，陷入了濒死状态。</record>
	<record alias="Msg.Battle.Counter.Dead">受到<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>的影响，死亡了。</record>
	<record alias="Msg.Battle.Miss.Effect.None">抵抗了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>。</record>
	<record alias="Msg.Battle.Dodge.Effect.None">闪避了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>。</record>
	<record alias="Msg.Battle.Bounce.Effect.None">反制了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>。</record>
	<record alias="Msg.Battle.Effect.HP.Damage">由于<arg p="4:effect.name2"/>效果，受到了<arg p="5:integer"/>点伤害。</record>
	<record alias="Msg.Battle.Effect.Interval.Damage">由于<arg p="4:effect.name2"/>效果，受到了<arg p="5:integer"/>点伤害。</record>
	<record alias="Msg.Battle.Effect.HP.Heal">由于<arg p="4:effect.name2"/>效果，恢复了<arg p="5:integer"/>点生命。</record>
	<record alias="Msg.Battle.Effect.HP.Heal.Interval">由于<arg p="4:effect.name2"/>效果，恢复了<arg p="5:integer"/>点生命。</record>
	<record alias="Msg.Battle.Effect.SP.Heal">由于<arg p="4:effect.name2"/>效果，恢复了<arg p="5:integer"/>点内力。</record>
	<record alias="Msg.Battle.Effect.SP.Heal.Interval">由于<arg p="4:effect.name2"/>效果，恢复了<arg p="5:integer"/>点内力。</record>
	<record alias="Msg.Battle.Effect.SP.Damage">由于<arg p="4:effect.name2"/>效果，受到了<arg p="5:integer"/>点内力伤害。</record>
	<record alias="Msg.Battle.Effect.SP.Interval">由于<arg p="4:effect.name2"/>效果，受到了<arg p="5:integer"/>点内力伤害。</record>
	<record alias="Msg.Battle.Effect.Attach">造成了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Effect.Attach.Fail">抵抗了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Effect.Detech">解除了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Effect.Exhaustion">由于<arg p="4:effect.name2"/>效果，陷入了濒死状态。</record>
	<record alias="Msg.Battle.Effect.Dead">由于<arg p="4:effect.name2"/>效果，死亡了。</record>
	<record alias="Msg.Battle.Other.Hit.HP.Damage"><arg p="2:creature.name"/>被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，受到了<arg p="5:integer"/>点伤害。</record>
	<record alias="Msg.Battle.Other.Hit.Attach.Damage"><arg p="2:creature.name"/>被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，受到了<arg p="5:integer"/>点伤害及<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Other.Hit.Interval.Damage">由于<arg p="3:skill.name2"/>的<arg p="4:effect.name2"/>的效果影响，<arg p="2:creature.name"/>受到了<arg p="5:integer"/>点伤害。</record>
	<record alias="Msg.Battle.Other.Hit.Attach.Fail.Damage"><arg p="2:creature.name"/>被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，受到了<arg p="5:integer"/>点伤害，但抵抗了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Other.Hit.Detech.Damage"><arg p="2:creature.name"/>被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，受到了<arg p="5:integer"/>点伤害，并且解除了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Other.Hit.HP.Heal">由于<arg p="3:skill.name2"/>的效果影响，<arg p="2:creature.name"/>恢复了<arg p="5:integer"/>点生命。</record>
	<record alias="Msg.Battle.Other.Hit.HP.Heal.Interval">由于<arg p="3:skill.name2"/>的效果影响，<arg p="2:creature.name"/>恢复了<arg p="5:integer"/>点生命。</record>
	<record alias="Msg.Battle.Other.Hit.SP.Heal">由于<arg p="3:skill.name2"/>的效果影响，<arg p="2:creature.name"/>恢复了<arg p="5:integer"/>点内力。</record>
	<record alias="Msg.Battle.Other.Hit.SP.Heal.Interval">由于<arg p="3:skill.name2"/>的效果影响，<arg p="2:creature.name"/>恢复了<arg p="5:integer"/>点内力。</record>
	<record alias="Msg.Battle.Other.Hit.SP.Damage"><arg p="2:creature.name"/>被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，受到了<arg p="5:integer"/>点内力伤害。</record>
	<record alias="Msg.Battle.Other.Hit.SP.Interval">由于<arg p="3:skill.name2"/>的<arg p="4:effect.name2"/><arg p="2:creature.name"/>受到了<arg p="5:integer"/>点内力伤害。</record>
	<record alias="Msg.Battle.Other.HitHP.Drain"><arg p="2:creature.name"/>被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，受到了<arg p="5:integer"/>点伤害，并且吸收了<arg p="6:integer"/>点生命。</record>
	<record alias="Msg.Battle.Other.Hit.SP.Drain"><arg p="2:creature.name"/>被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，受到了<arg p="5:integer"/>点伤害，并且吸收了<arg p="7:integer"/>点内力。</record>
	<record alias="Msg.Battle.Other.HitHP.SP.Drain"><arg p="2:creature.name"/>被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，受到了<arg p="5:integer"/>点伤害，并且吸收了<arg p="6:integer"/>点生命并吸收了<arg p="7:integer"/>点内力。</record>
	<record alias="Msg.Battle.Other.Hit.Attach"><arg p="2:creature.name"/>被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，受到了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Other.Hit.Attach.Fail">虽然<arg p="2:creature.name"/>被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，但抵抗了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Other.Hit.Detech"><arg p="2:creature.name"/>被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，解除了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Other.Hit.Exhaustion">受到<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，<arg p="2:creature.name"/>陷入了濒死状态。</record>
	<record alias="Msg.Battle.Other.Hit.Dead">受到<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，<arg p="2:creature.name"/>死亡了。</record>
	<record alias="Msg.Battle.Other.Cri.HP.Damage"><arg p="2:creature.name"/>被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，受到了<arg p="5:integer"/>点暴击伤害。</record>
	<record alias="Msg.Battle.Other.Cri.Attach.Damage"><arg p="2:creature.name"/>被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，受到了<arg p="5:integer"/>点暴击伤害及<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Other.Cri.Attach.Fail.Damage"><arg p="2:creature.name"/>被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，受到了<arg p="5:integer"/>点暴击伤害，但抵抗了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Other.Cri.Detech.Damage"><arg p="2:creature.name"/>被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，受到了<arg p="5:integer"/>点暴击伤害，并且解除了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Other.Cri.SP.Damage"><arg p="2:creature.name"/>被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，受到了<arg p="5:integer"/>点内力伤害。</record>
	<record alias="Msg.Battle.Other.CriHP.Drain"><arg p="2:creature.name"/>被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，受到了<arg p="5:integer"/>点暴击伤害，并且吸收了<arg p="6:integer"/>点生命。</record>
	<record alias="Msg.Battle.Other.CriHP.SP.Drain"><arg p="2:creature.name"/>被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，受到了<arg p="5:integer"/>点暴击伤害，并且被吸收了<arg p="6:integer"/>点生命并吸收了<arg p="7:integer"/>点内力。</record>
	<record alias="Msg.Battle.Other.Cri.Attach"><arg p="2:creature.name"/>被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，受到了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Other.Cri.Attach.Fail">虽然<arg p="2:creature.name"/>被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，但抵抗了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Other.Cri.Detech"><arg p="2:creature.name"/>被<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>命中，解除了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Other.Cri.Exhaustion">受到<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，<arg p="2:creature.name"/>陷入了濒死状态。</record>
	<record alias="Msg.Battle.Other.Cri.Dead">受到<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，<arg p="2:creature.name"/>死亡了。</record>
	<record alias="Msg.Battle.Other.Parry.Effect.None"><arg p="2:creature.name"/>格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>。</record>
	<record alias="Msg.Battle.Other.Parry.HP.Damage"><arg p="2:creature.name"/>格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，但受到了<arg p="5:integer"/>点伤害。</record>
	<record alias="Msg.Battle.Other.Parry.Attach.Damage"><arg p="2:creature.name"/>格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，但受到了<arg p="5:integer"/>点伤害及<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Other.Parry.Attach.Fail.Damage"><arg p="2:creature.name"/>格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，受到了<arg p="5:integer"/>点伤害，但抵抗了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Other.Parry.Detech.Damage"><arg p="2:creature.name"/>格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，但受到了<arg p="5:integer"/>点伤害，并且解除了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Other.Parry.SP.Damage"><arg p="2:creature.name"/>格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，但受到了<arg p="5:integer"/>点内力伤害。</record>
	<record alias="Msg.Battle.Other.ParryHP.Drain"><arg p="2:creature.name"/>格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，但受到了<arg p="5:integer"/>点伤害，并且吸收了<arg p="6:integer"/>点生命。</record>
	<record alias="Msg.Battle.Other.Parry.SP.Drain"><arg p="2:creature.name"/>格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，但受到了<arg p="5:integer"/>点伤害，并且吸收了<arg p="7:integer"/>点内力。</record>
	<record alias="Msg.Battle.Other.ParryHP.SP.Drain"><arg p="2:creature.name"/>格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，但受到了<arg p="5:integer"/>点伤害，并且吸收了<arg p="6:integer"/>点生命并吸收了<arg p="7:integer"/>点内力。</record>
	<record alias="Msg.Battle.Other.Parry.Attach"><arg p="2:creature.name"/>格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，但受到了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Other.Parry.Attach.Fail"><arg p="2:creature.name"/>格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，抵抗了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Other.Parry.Detech"><arg p="2:creature.name"/>格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，解除了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Other.Parry.Exhaustion">受到<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，<arg p="2:creature.name"/>陷入了濒死状态。</record>
	<record alias="Msg.Battle.Other.Parry.Dead">受到<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，<arg p="2:creature.name"/>死亡了。</record>
	<record alias="Msg.Battle.Other.PParry.Effect.None"><arg p="2:creature.name"/>完全格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>。</record>
	<record alias="Msg.Battle.Other.PParry.HP.Damage"><arg p="2:creature.name"/>完全格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，但受到了<arg p="5:integer"/>点伤害。</record>
	<record alias="Msg.Battle.Other.PParry.Attach.Damage"><arg p="2:creature.name"/>完全格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，但受到了<arg p="5:integer"/>点伤害及<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Other.PParry.Attach.Fail.Damage"><arg p="2:creature.name"/>完全格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，受到了<arg p="5:integer"/>点伤害，但抵抗了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Other.PParry.Detech.Damage"><arg p="2:creature.name"/>完全格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，但受到了<arg p="5:integer"/>点伤害，并且解除了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Other.PParry.SP.Damage"><arg p="2:creature.name"/>完全格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，但受到了<arg p="5:integer"/>点内力伤害。</record>
	<record alias="Msg.Battle.Other.PParryHP.Drain"><arg p="2:creature.name"/>完全格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，但受到了<arg p="5:integer"/>点伤害，并且吸收了<arg p="6:integer"/>点生命。</record>
	<record alias="Msg.Battle.Other.PParry.SP.Drain"><arg p="2:creature.name"/>完全格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，但受到了<arg p="5:integer"/>点伤害，并且吸收了<arg p="7:integer"/>点内力。</record>
	<record alias="Msg.Battle.Other.PParryHP.SP.Drain"><arg p="2:creature.name"/>完全格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，但受到了<arg p="5:integer"/>点伤害，并且吸收了<arg p="6:integer"/>点生命并吸收了<arg p="7:integer"/>点内力。</record>
	<record alias="Msg.Battle.Other.PParry.Attach">虽然<arg p="2:creature.name"/>完全格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，但还是造成了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Other.PParry.Attach.Fail"><arg p="2:creature.name"/>完全格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，抵抗了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Other.PParry.Detech"><arg p="2:creature.name"/>完全格挡了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，解除了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Other.PParry.Exhaustion">受到<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，<arg p="2:creature.name"/>陷入了濒死状态。</record>
	<record alias="Msg.Battle.Other.PParry.Dead">受到<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，<arg p="2:creature.name"/>死亡了。</record>
	<record alias="Msg.Battle.Other.Counter.Effect.None"><arg p="2:creature.name"/>对<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>进行了反击。</record>
	<record alias="Msg.Battle.Other.Counter.HP.Damage"><arg p="2:creature.name"/>对<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>进行了反击，但受到了<arg p="5:integer"/>点伤害。</record>
	<record alias="Msg.Battle.Other.Counter.Attach.Damage"><arg p="2:creature.name"/>对<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>进行了反击，但受到了<arg p="5:integer"/>点伤害及<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Other.Counter.Attach.Fail.Damage"><arg p="2:creature.name"/>对<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>进行了反击，受到了<arg p="5:integer"/>点伤害，但抵抗了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Other.Counter.Detech.Damage"><arg p="2:creature.name"/>对<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>进行了反击，但受到了<arg p="5:integer"/>点伤害，并且解除了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Other.Counter.SP.Damage"><arg p="2:creature.name"/>对<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>进行了反击，但受到了<arg p="5:integer"/>点内力伤害。</record>
	<record alias="Msg.Battle.Other.CounterHP.Drain"><arg p="2:creature.name"/>对<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>进行了反击，但受到了<arg p="5:integer"/>点伤害，并且吸收了<arg p="6:integer"/>点生命。</record>
	<record alias="Msg.Battle.Other.Counter.SP.Drain"><arg p="2:creature.name"/>对<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>进行了反击，但受到了<arg p="5:integer"/>点伤害，并且吸收了<arg p="7:integer"/>点内力。</record>
	<record alias="Msg.Battle.Other.CounterHP.SP.Drain"><arg p="2:creature.name"/>对<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>进行了反击，但受到了<arg p="5:integer"/>点伤害，并且吸收了<arg p="6:integer"/>点生命并吸收了<arg p="7:integer"/>点内力。</record>
	<record alias="Msg.Battle.Other.Counter.Attach"><arg p="2:creature.name"/>对<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>进行了反击，但受到了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Other.Counter.Attach.Fail"><arg p="2:creature.name"/>对<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>进行了反击，抵抗了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Other.Counter.Detech"><arg p="2:creature.name"/>对<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>进行了反击，解除了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Other.Counter.Exhaustion">受到<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，<arg p="2:creature.name"/>陷入了濒死状态。</record>
	<record alias="Msg.Battle.Other.Counter.Dead">受到<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>，<arg p="2:creature.name"/>死亡了。</record>
	<record alias="Msg.Battle.Other.Miss.Effect.None"><arg p="2:creature.name"/>抵抗了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>。</record>
	<record alias="Msg.Battle.Other.Dodge.Effect.None"><arg p="2:creature.name"/>闪避了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>。</record>
	<record alias="Msg.Battle.Other.Bounce.Effect.None"><arg p="2:creature.name"/>反制了<arg p="1:creature.name"/>的<arg p="3:skill.name2"/>。</record>
	<record alias="Msg.Battle.Other.Effect.HP.Damage">由于<arg p="4:effect.name2"/>效果，<arg p="2:creature.name"/>受到了<arg p="5:integer"/>点伤害。</record>
	<record alias="Msg.Battle.Other.Effect.Interval.Damage">由于<arg p="4:effect.name2"/>效果，<arg p="2:creature.name"/>受到了<arg p="5:integer"/>点伤害。</record>
	<record alias="Msg.Battle.Other.Effect.HP.Heal">由于<arg p="4:effect.name2"/>效果，<arg p="2:creature.name"/>恢复了<arg p="5:integer"/>点生命。</record>
	<record alias="Msg.Battle.Other.Effect.HP.Heal.Interval">由于<arg p="4:effect.name2"/>效果，<arg p="2:creature.name"/>恢复了<arg p="5:integer"/>点生命。</record>
	<record alias="Msg.Battle.Other.Effect.SP.Heal">由于<arg p="4:effect.name2"/>效果，<arg p="2:creature.name"/>恢复了<arg p="5:integer"/>点内力。</record>
	<record alias="Msg.Battle.Other.Effect.SP.Heal.Interval">由于<arg p="4:effect.name2"/>效果，<arg p="2:creature.name"/>恢复了<arg p="5:integer"/>点内力。</record>
	<record alias="Msg.Battle.Other.Effect.SP.Damage">由于<arg p="4:effect.name2"/>效果，<arg p="2:creature.name"/>受到了<arg p="5:integer"/>点内力伤害。</record>
	<record alias="Msg.Battle.Other.Effect.SP.Interval">由于<arg p="4:effect.name2"/>效果，<arg p="2:creature.name"/>受到了<arg p="5:integer"/>点内力伤害。</record>
	<record alias="Msg.Battle.Other.Effect.Attach"><arg p="2:creature.name"/>受到了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Other.Effect.Attach.Fail"><arg p="2:creature.name"/>抵抗了<arg p="4:effect.name2"/>效果。</record>
	<record alias="Msg.Battle.Other.Effect.Detech"><arg p="2:creature.name"/>的<arg p="4:effect.name2"/>被解除了。</record>
	<record alias="Msg.Battle.Other.Effect.Exhaustion">由于<arg p="4:effect.name2"/>效果，<arg p="2:creature.name"/>陷入了濒死状态。</record>
	<record alias="Msg.Battle.Other.Effect.Dead">由于<arg p="4:effect.name2"/>效果，<arg p="2:creature.name"/>死亡了。</record>
</table>