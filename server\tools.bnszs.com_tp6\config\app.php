<?php
// ThinkPHP 6.0 应用配置文件
return [
    // 应用地址
    'app_host'         => '',
    // 应用的命名空间
    'app_namespace'    => '',
    // 是否启用路由
    'with_route'       => true,
    // 默认应用
    'default_app'      => 'ingame',
    // 自动多应用模式
    'auto_multi_app'   => true,
    // 默认时区
    'default_timezone' => 'Asia/Shanghai',
    // 应用映射（自动多应用模式有效）
    'app_map' => [

        'service' => 'service',
        'common' => 'common',
        'manage' => 'manage',
        'guidebook' => 'guidebook',
        'ingame' => 'ingame',
    ],
    // 域名绑定（自动多应用模式有效）
    'domain_bind' => [
        // 'localhost:8080' => 'bns',  // 注释掉域名绑定，允许多应用访问
    ],
    // 禁止URL访问的应用列表（自动多应用模式有效）
    'deny_app_list' => [],
    // 异常页面的模板文件
    'exception_tmpl'   => '',
    // 错误显示信息,非调试模式有效
    'error_message'    => '页面错误！请稍后再试～',
    // 显示错误信息
    'show_error_msg'   => false,

    // HTTP异常页面配置
    'http_exception_template' => [
        403 => __DIR__ . '/../public/error/403.html',
        404 => __DIR__ . '/../public/error/404.html',
        500 => __DIR__ . '/../public/error/500.html',
    ],
];