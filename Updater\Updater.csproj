﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<RootNamespace>Xylia.Updater</RootNamespace>
		<OutputType>Library</OutputType>
		<TargetFramework>net45</TargetFramework>
		<UseWPF>true</UseWPF>
		<AssemblyName>ZipExtractor</AssemblyName>
		<Product>剑灵小助手下载器</Product>
		<Copyright>Copyright © 2025 Xylia</Copyright>
		<Version>1.0.0</Version>
		<SignAssembly>true</SignAssembly>
		<LangVersion>default</LangVersion>
		<ApplicationIcon>Properties\ZipExtractor.ico</ApplicationIcon>
		<ApplicationManifest>Properties\app.manifest</ApplicationManifest>
		<IsPackable>false</IsPackable>
		<Configurations>Debug;Release;Exe</Configurations>
	</PropertyGroup>
	<PropertyGroup Condition=" '$(TargetFramework)' != 'net45' ">
		<RuntimeIdentifiers>win-x86;win-x64</RuntimeIdentifiers>
		<PublishSingleFile>true</PublishSingleFile>
		<SelfContained>false</SelfContained>
		<PublishTrimmed>false</PublishTrimmed>
		<IncludeNativeLibrariesForSelfExtract>true</IncludeNativeLibrariesForSelfExtract>
	</PropertyGroup>
	<PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
		<DebugType>full</DebugType>
		<DebugSymbols>true</DebugSymbols>
	</PropertyGroup>
	<PropertyGroup Condition=" '$(Configuration)' == 'Exe' ">
		<OutputType>WinExe</OutputType>
		<AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
		<AppendRuntimeIdentifierToOutputPath>false</AppendRuntimeIdentifierToOutputPath>
		<DebugType>none</DebugType>
		<DebugSymbols>false</DebugSymbols>
	</PropertyGroup>
	<ItemGroup Condition=" '$(TargetFramework)' == 'net45' ">
		<Reference Include="System.IO.Compression" />
		<Reference Include="System.IO.Compression.FileSystem" />
	</ItemGroup>
	<ItemGroup Condition=" '$(Configuration)' != 'Exe' ">
		<ApplicationDefinition Remove="App.xaml" />
		<None Include="App.xaml" />
	</ItemGroup>
	<ItemGroup>
		<Compile Update="Resources\Resource.Designer.cs">
			<DependentUpon>Resource.resx</DependentUpon>
			<DesignTime>True</DesignTime>
			<AutoGen>True</AutoGen>
		</Compile>
		<EmbeddedResource Update="Resources\Resource.resx">
			<LastGenOutput>Resource.Designer.cs</LastGenOutput>
			<Generator>ResXFileCodeGenerator</Generator>
		</EmbeddedResource>
	</ItemGroup>

</Project>