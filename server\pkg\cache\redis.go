package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
)

// Redis缓存实现
type redisCache struct {
	client *redis.Client
	ctx    context.Context
}

// NewRedisCache 创建Redis缓存
func NewRedisCache(host string, port int, password string, db int) (Cache, error) {
	client := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", host, port),
		Password: password,
		DB:       db,
	})

	// 测试连接
	ctx := context.Background()
	_, err := client.Ping(ctx).Result()
	if err != nil {
		return nil, err
	}

	return &redisCache{
		client: client,
		ctx:    ctx,
	}, nil
}

// Get 从Redis获取缓存值
func (r *redisCache) Get(key string, value interface{}) error {
	data, err := r.client.Get(r.ctx, key).Bytes()
	if err != nil {
		if err == redis.Nil {
			return ErrCacheMiss
		}
		return err
	}

	return json.Unmarshal(data, value)
}

// Set 设置Redis缓存值
func (r *redisCache) Set(key string, value interface{}, expiration time.Duration) error {
	data, err := json.Marshal(value)
	if err != nil {
		return err
	}

	return r.client.Set(r.ctx, key, data, expiration).Err()
}

// Delete 删除Redis缓存值
func (r *redisCache) Delete(key string) error {
	return r.client.Del(r.ctx, key).Err()
}

// Close 关闭Redis连接
func (r *redisCache) Close() error {
	return r.client.Close()
}
