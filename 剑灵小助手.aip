<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<DOCUMENT Type="Advanced Installer" CreateVersion="22.5" version="22.5" PreviousModules="professional" Modules="enterprise" RootPath="." Language="zh" Id="{5865EC25-7932-426F-BB29-10F08B7B157D}">
  <COMPONENT cid="caphyon.advinst.msicomp.ProjectOptionsComponent">
    <ROW Name="HiddenItems" Value="ActSyncAppComponent;CPLAppletComponent;AutorunComponent;GameUxComponent;SilverlightSlnComponent;SharePointSlnComponent"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiPropsComponent">
    <ROW Property="AI_APP_FILE" Value="[#exe]"/>
    <ROW Property="AI_BITMAP_DISPLAY_MODE" Value="0"/>
    <ROW Property="AI_README_FILE" Value="https://www.bnszs.com/changelog.html"/>
    <ROW Property="AI_RUN_AS_ADMIN" Value="1"/>
    <ROW Property="AI_UPGRADE" Value="No"/>
    <ROW Property="ALLUSERS" Value="1"/>
    <ROW Property="ARPCOMMENTS" Value="[|ProductName]安装程序" ValueLocId="*"/>
    <ROW Property="ARPPRODUCTICON" Value="logo.exe" Type="8"/>
    <ROW Property="ARPURLINFOABOUT" Value="https://www.bnszs.com"/>
    <ROW Property="ARPURLUPDATEINFO" Value="https://www.bnszs.com/changelog.html"/>
    <ROW Property="AiLicenseAgreementLink" Value="//www.bnszs.com"/>
    <ROW Property="AppLogo" Value="applogo.png" MultiBuildValue="DefaultBuild:logo.ico" Type="1" MsiKey="AppLogo"/>
    <ROW Property="CTRLS" Value="3"/>
    <ROW Property="Manufacturer" Value="Xylia"/>
    <ROW Property="ProductCode" Value="2052:{48031AB6-3D7C-4763-9165-9CB64AF54DA8} " Type="16"/>
    <ROW Property="ProductLanguage" Value="2052"/>
    <ROW Property="ProductName" Value="剑灵小助手"/>
    <ROW Property="ProductVersion" Value="3.1.2" Options="32"/>
    <ROW Property="RUNAPPLICATION" Value="1" Type="4"/>
    <ROW Property="SecureCustomProperties" Value="OLDPRODUCTS;AI_NEWERPRODUCTFOUND"/>
    <ROW Property="UpgradeCode" Value="{64947BAE-C533-4C1E-A396-EDF420606D85}"/>
    <ROW Property="WindowsType9X" MultiBuildValue="DefaultBuild:Windows 9x/ME" ValueLocId="-"/>
    <ROW Property="WindowsType9XDisplay" MultiBuildValue="DefaultBuild:Windows 9x/ME" ValueLocId="-"/>
    <ROW Property="WindowsTypeNT40" MultiBuildValue="DefaultBuild:Windows NT 4.0" ValueLocId="-"/>
    <ROW Property="WindowsTypeNT40Display" MultiBuildValue="DefaultBuild:Windows NT 4.0" ValueLocId="-"/>
    <ROW Property="WindowsTypeNT50" MultiBuildValue="DefaultBuild:Windows 2000" ValueLocId="-"/>
    <ROW Property="WindowsTypeNT50Display" MultiBuildValue="DefaultBuild:Windows 2000" ValueLocId="-"/>
    <ROW Property="WindowsTypeNT5X" MultiBuildValue="DefaultBuild:Windows XP/2003" ValueLocId="-"/>
    <ROW Property="WindowsTypeNT5XDisplay" MultiBuildValue="DefaultBuild:Windows XP/2003" ValueLocId="-"/>
    <ROW Property="WindowsTypeNT60" MultiBuildValue="DefaultBuild:Windows Vista/Server 2008" ValueLocId="-"/>
    <ROW Property="WindowsTypeNT60Display" MultiBuildValue="DefaultBuild:Windows Vista/Server 2008" ValueLocId="-"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiDirsComponent">
    <ROW Directory="APPDIR" Directory_Parent="TARGETDIR" DefaultDir="APPDIR:." IsPseudoRoot="1" DirectoryOptions="3"/>
    <ROW Directory="TARGETDIR" DefaultDir="SourceDir"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.SideBySideGuidComponent">
    <ROW Component="A0BFC224E0E9125ADE0B2DBF343" Value="{A52CD9BF-5CF1-4957-9BB9-482FF9845862}"/>
    <ROW Component="APPDIR" Value="{ECB25883-B5A2-4788-8EEB-D7CEA34E50E3}"/>
    <ROW Component="ProductInformation" Value="{8C5E402F-35BE-4656-AB4D-ED38284C08F6}"/>
    <ROW Component="exe" Value="{3170CCFF-BB0A-4738-8244-9CC028256AAF}"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiCompsComponent">
    <ROW Component="A0BFC224E0E9125ADE0B2DBF343" ComponentId="{B6295AC3-D043-4CB9-8FC6-3BF144FC3590}" Directory_="APPDIR" Attributes="4" KeyPath="A0BFC224E0E9125ADE0B2DBF343" Options="2"/>
    <ROW Component="APPDIR" ComponentId="{6A12D566-DB5B-4B6D-9EA4-89CB3C74491C}" Directory_="APPDIR" Attributes="0"/>
    <ROW Component="ProductInformation" ComponentId="{7CD72A0A-5585-4414-AE8E-C0B2048D90E9}" Directory_="APPDIR" Attributes="260" KeyPath="Version"/>
    <ROW Component="exe" ComponentId="{26ECE8FC-1661-4873-A2B4-19511E284D0E}" Directory_="APPDIR" Attributes="256" KeyPath="exe"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiFeatsComponent">
    <ROW Feature="A0BFC224E0E9125ADE0B2DBF343" Title=".NET Desktop Runtime 8.0.13 x64" Description=".NET Desktop Runtime 8.0.13 x64" Display="3" Level="1" Attributes="0"/>
    <ROW Feature="MainFeature" Title="MainFeature" Description="Description" Display="1" Level="1" Directory_="APPDIR" Attributes="0"/>
    <ATTRIBUTE name="CurrentFeature" value="MainFeature"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiFilesComponent">
    <ROW File="exe" Component_="exe" FileName="剑灵小助手.exe" Version="65535.65535.65535.65535" Attributes="0" SourcePath="BnsHelper\bin\剑灵小助手_Secure\剑灵小助手.exe" SelfReg="false"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.BootstrOptComponent">
    <ROW BootstrOptKey="GlobalOptions" GeneralOptions="b" DownloadFolder="[AppDataFolder][|Manufacturer]\[|ProductName]\prerequisites" Options="18"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.BootstrapperUISequenceComponent">
    <ROW Action="AI_BACKUP_AI_SETUPEXEPATH" Sequence="249"/>
    <ROW Action="AI_RESTORE_AI_SETUPEXEPATH" Condition="AI_SETUPEXEPATH_ORIGINAL" Sequence="252"/>
    <ROW Action="AI_DownloadPrereq" Condition="AI_BOOTSTRAPPER AND (NOT AI_PrereqsFulfilled)" Sequence="695"/>
    <ROW Action="AI_ExtractPrereq" Condition="AI_BOOTSTRAPPER AND (NOT AI_PrereqsFulfilled)" Sequence="696"/>
    <ROW Action="AI_InstallPrePrerequisite" Condition="AI_BOOTSTRAPPER AND (NOT AI_PrereqsFulfilled)" Sequence="697"/>
    <ROW Action="AI_VerifyPrePrereq" Condition="AI_BOOTSTRAPPER AND (NOT AI_PrereqsFulfilled)" Sequence="698"/>
    <ROW Action="AI_CleanPrePrereq" Condition="AI_BOOTSTRAPPER AND (NOT AI_PrereqsFulfilled)" Sequence="699"/>
    <ROW Action="AI_AppSearchEx" Condition="AI_BOOTSTRAPPER" Sequence="251"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.BuildComponent">
    <ROW BuildKey="DefaultBuild" BuildName="DefaultBuild" BuildOrder="1" BuildType="0" Languages="zh" InstallationType="4" SummInfoMetadata="Page Count:450" ExtUI="true" UseLargeSchema="true" UACExecutionLevel="2"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.DictionaryComponent">
    <ROW Path="&lt;AI_DICTS&gt;ui.ail"/>
    <ROW Path="&lt;AI_DICTS&gt;ui_zh.ail"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.DigCertStoreComponent">
    <ROW TimeStampUrl="http://timestamp.digicert.com" SignerDescription="[|ProductName]" SignOptions="7" SignTool="0" UseSha256="1" Thumbprint="84BA43FADA0FB42ED718D346F4B7FE474916BF94 Subject: 剑灵小助手&#10;Issuer: 剑灵小助手&#10;Valid from 01/01/2024 to 01/01/2100" Subject="CN=剑灵小助手" Store="Machine\MY"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.FragmentComponent">
    <ROW Fragment="CommonUI.aip" Path="&lt;AI_FRAGS&gt;CommonUI.aip"/>
    <ROW Fragment="FolderDlg.aip" Path="&lt;AI_THEMES&gt;serene\fragments\FolderDlg.aip"/>
    <ROW Fragment="InstallDlg.aip" Path="&lt;AI_THEMES&gt;serene\fragments\InstallDlg.aip"/>
    <ROW Fragment="MaintenanceTypeDlg.aip" Path="&lt;AI_THEMES&gt;serene\fragments\MaintenanceTypeDlg.aip"/>
    <ROW Fragment="MaintenanceWelcomeDlg.aip" Path="&lt;AI_THEMES&gt;serene\fragments\MaintenanceWelcomeDlg.aip"/>
    <ROW Fragment="SequenceDialogs.aip" Path="&lt;AI_THEMES&gt;serene\fragments\SequenceDialogs.aip"/>
    <ROW Fragment="Sequences.aip" Path="&lt;AI_FRAGS&gt;Sequences.aip"/>
    <ROW Fragment="StaticUIStrings.aip" Path="&lt;AI_FRAGS&gt;StaticUIStrings.aip"/>
    <ROW Fragment="Themes.aip" Path="&lt;AI_FRAGS&gt;Themes.aip"/>
    <ROW Fragment="UI.aip" Path="&lt;AI_THEMES&gt;serene\fragments\UI.aip"/>
    <ROW Fragment="Validation.aip" Path="&lt;AI_FRAGS&gt;Validation.aip"/>
    <ROW Fragment="VerifyRemoveDlg.aip" Path="&lt;AI_THEMES&gt;serene\fragments\VerifyRemoveDlg.aip"/>
    <ROW Fragment="VerifyRepairDlg.aip" Path="&lt;AI_THEMES&gt;serene\fragments\VerifyRepairDlg.aip"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiActionTextComponent">
    <ROW Action="AI_ConfigureChainer" Description="配置先决条件启动器" DescriptionLocId="ActionText.Description.AI_ConfigureChainer" Template="配置启动器" TemplateLocId="ActionText.Template.AI_ConfigureChainer"/>
    <ROW Action="AI_DownloadPrereq" Description="正在下载运行环境软件" DescriptionLocId="ActionText.Description.AI_DownloadPrereq" Template="[1]" TemplateLocId="ActionText.Template.AI_DownloadPrereq"/>
    <ROW Action="AI_ExtractPrereq" Description="正在提取运行环境软件" DescriptionLocId="ActionText.Description.AI_ExtractPrereq" Template="[1]" TemplateLocId="ActionText.Template.AI_ExtractPrereq"/>
    <ROW Action="AI_InstallPostPrerequisite" Description="正在安装运行环境软件" DescriptionLocId="ActionText.Description.AI_InstallPrerequisite" Template="[1]" TemplateLocId="ActionText.Template.AI_InstallPrerequisite"/>
    <ROW Action="AI_InstallPrePrerequisite" Description="正在安装运行环境软件" DescriptionLocId="ActionText.Description.AI_InstallPrerequisite" Template="[1]" TemplateLocId="ActionText.Template.AI_InstallPrerequisite"/>
    <ROW Action="AI_InstallPrerequisite" Description="正在安装运行环境软件" DescriptionLocId="ActionText.Description.AI_InstallPrerequisite" Template="[1]" TemplateLocId="ActionText.Template.AI_InstallPrerequisite"/>
    <ROW Action="AI_VerifyPrePrereq" Description="正在验证运行环境" DescriptionLocId="ActionText.Description.AI_VerifyPrereq" Template="[1] 没有被正确安装。" TemplateLocId="ActionText.Template.AI_VerifyPrereq"/>
    <ROW Action="AI_VerifyPrereq" Description="正在验证运行环境" DescriptionLocId="ActionText.Description.AI_VerifyPrereq" Template="[1] 没有被正确安装。" TemplateLocId="ActionText.Template.AI_VerifyPrereq"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiBinaryComponent">
    <ROW Name="Prereq.dll" SourcePath="&lt;AI_CUSTACTS&gt;Prereq.dll"/>
    <ROW Name="aicustact.dll" SourcePath="&lt;AI_CUSTACTS&gt;aicustact.dll"/>
    <ROW Name="aipackagechainer.exe" SourcePath="&lt;AI_CUSTACTS&gt;aipackagechainer.exe" DigSign="true"/>
    <ROW Name="file_deleter.ps1" SourcePath="&lt;AI_SCRIPTS&gt;file_deleter.ps1"/>
    <ROW Name="logo.ico" SourcePath="BnsHelper\Properties\logo.ico"/>
    <ROW Name="viewer.exe" SourcePath="&lt;AI_CUSTACTS&gt;viewer.exe" DigSign="true"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiControlComponent">
    <ROW Dialog_="AdminInstallPointDlg" Control="Back" Type="PushButton" X="15" Y="290" Width="56" Height="20" Attributes="3" Text="[ButtonText_Back]" TextStyle="CfTitleFont" Order="600" TextLocId="-" MsiKey="AdminInstallPointDlg#Back" Options="1"/>
    <ROW Dialog_="AdminInstallPointDlg" Control="Next" Type="PushButton" X="449" Y="290" Width="56" Height="20" Attributes="3" Text="[ButtonText_Next]" TextStyle="CfTitleFont" Order="700" TextLocId="-" MsiKey="AdminInstallPointDlg#Next" Options="1"/>
    <ROW Dialog_="AdminWelcomeDlg" Control="Next" Type="PushButton" X="449" Y="290" Width="56" Height="20" Attributes="3" Text="[ButtonText_Next]" TextStyle="CfTitleFont" Order="300" TextLocId="-" MsiKey="AdminWelcomeDlg#Next" Options="1"/>
    <ROW Dialog_="AdminWelcomeDlg" Control="Back" Type="PushButton" X="15" Y="290" Width="56" Height="20" Attributes="1" Text="[ButtonText_Back]" TextStyle="CfTitleFont" Order="400" TextLocId="-" MsiKey="AdminWelcomeDlg#Back" Options="1"/>
    <ROW Dialog_="CustomizeDlg" Control="Browse" Type="PushButton" X="383" Y="214" Width="66" Height="20" Attributes="3" Text="[ButtonText_Browse]" TextStyle="CfTitleFont" Order="300" TextLocId="-" MsiKey="CustomizeDlg#Browse"/>
    <ROW Dialog_="CustomizeDlg" Control="Reset" Type="PushButton" X="100" Y="239" Width="66" Height="20" Attributes="3" Text="[ButtonText_Reset]" TextStyle="CfTitleFont" Order="400" TextLocId="-" MsiKey="CustomizeDlg#Reset"/>
    <ROW Dialog_="CustomizeDlg" Control="DiskCost" Type="PushButton" X="174" Y="239" Width="66" Height="20" Attributes="3" Text="磁盘使用(&amp;U)" TextStyle="CfTitleFont" Order="500" TextLocId="Control.Text.CustomizeDlg#DiskCost" MsiKey="CustomizeDlg#DiskCost"/>
    <ROW Dialog_="CustomizeDlg" Control="Back" Type="PushButton" X="15" Y="290" Width="56" Height="20" Attributes="3" Text="[ButtonText_Back]" TextStyle="CfTitleFont" Order="600" TextLocId="-" MsiKey="CustomizeDlg#Back" Options="1"/>
    <ROW Dialog_="CustomizeDlg" Control="Next" Type="PushButton" X="449" Y="290" Width="56" Height="20" Attributes="3" Text="[ButtonText_Next]" TextStyle="CfTitleFont" Order="700" TextLocId="-" MsiKey="CustomizeDlg#Next" Options="1"/>
    <ROW Dialog_="ExitDialog" Control="Finish" Type="PushButton" X="449" Y="290" Width="56" Height="20" Attributes="3" Text="[ButtonText_Finish]" TextStyle="CfTitleFont" Order="800" TextLocId="-" MsiKey="ExitDialog#Finish" Options="1"/>
    <ROW Dialog_="ExitDialog" Control="Hyperlink" Type="Hyperlink" X="101" Y="225" Width="220" Height="20" Attributes="65539" Property="AiReadmeLink" Text="&lt;a href=&quot;http://[AiReadmeLink]&quot;&gt;查看更新日志&lt;/a&gt;" TextStyle="DlgFont10" Order="1300" MsiKey="ExitDialog#Hyperlink"/>
    <ROW Dialog_="FolderDlg" Control="FolderDlgDialogInitializer" Type="DialogInitializer" X="0" Y="0" Width="0" Height="0" Attributes="0" Order="-1" TextLocId="-" HelpLocId="-" ExtDataLocId="-"/>
    <ROW Dialog_="FolderDlg" Control="Next" Type="PushButton" X="449" Y="290" Width="56" Height="20" Attributes="3" Text="[ButtonText_Next]" TextStyle="CfTitleFont" Order="200" TextLocId="-" MsiKey="FolderDlg#Next" Options="1"/>
    <ROW Dialog_="FolderDlg" Control="Browse" Type="PushButton" X="393" Y="234" Width="56" Height="20" Attributes="3" Text="[ButtonText_Browse]" TextStyle="CfTitleFont" Help="|" Order="500" TextLocId="-" HelpLocId="Control.Help.FolderDlg#Browse" MsiKey="FolderDlg#Browse"/>
    <ROW Dialog_="FolderDlg" Control="Back" Type="PushButton" X="15" Y="290" Width="56" Height="20" Attributes="3" Text="[ButtonText_Back]" TextStyle="CfTitleFont" Order="700" TextLocId="-" MsiKey="FolderDlg#Back" Options="1"/>
    <ROW Dialog_="InstallDlg" Control="Next" Type="PushButton" X="449" Y="290" Width="56" Height="20" Attributes="3" Text="[ButtonText_Next]" TextStyle="CfTitleFont" Order="300" TextLocId="-" MsiKey="InstallDlg#Next" Options="1"/>
    <ROW Dialog_="InstallDlg" Control="AgreementLink" Type="Hyperlink" X="281" Y="239" Width="138" Height="15" Attributes="65539" Text="&lt;a href=&quot;http://[AiLicenseAgreementLink]&quot;&gt;使用条款和隐私政策&lt;/a&gt;" TextStyle="HyperlinkFont" Order="500" MsiKey="InstallDlg#AgreementLink"/>
    <ROW Dialog_="InstallDlg" Control="AgreeText" Type="Text" X="104" Y="239" Width="177" Height="15" Attributes="65539" Text="安装此应用程序，您需要同意我们的" Order="600" MsiKey="InstallDlg#AgreeText"/>
    <ROW Dialog_="MaintenanceTypeDlg" Control="Next" Type="PushButton" X="449" Y="290" Width="56" Height="20" Attributes="1" Text="[ButtonText_Next]" TextStyle="CfTitleFont" Order="100" TextLocId="-" MsiKey="MaintenanceTypeDlg#Next" Options="1"/>
    <ROW Dialog_="MaintenanceTypeDlg" Control="Back" Type="PushButton" X="15" Y="290" Width="56" Height="20" Attributes="3" Text="[ButtonText_Back]" TextStyle="CfTitleFont" Order="1000" TextLocId="-" MsiKey="MaintenanceTypeDlg#Back" Options="1"/>
    <ROW Dialog_="MaintenanceWelcomeDlg" Control="Next" Type="PushButton" X="449" Y="290" Width="56" Height="20" Attributes="3" Text="[ButtonText_Next]" TextStyle="CfTitleFont" Order="400" TextLocId="-" MsiKey="MaintenanceWelcomeDlg#Next" Options="1"/>
    <ROW Dialog_="NewDialog" Control="Bitmap" Type="Bitmap" X="0" Y="0" Width="520" Height="320" Attributes="1" Text="[BannerBitmap]" Order="100"/>
    <ROW Dialog_="NewDialog" Control="Next" Type="PushButton" X="449" Y="290" Width="56" Height="20" Attributes="3" Text="[ButtonText_Next]" TextStyle="CfTitleFont" Order="200" TextLocId="-" Options="1"/>
    <ROW Dialog_="NewDialog" Control="Cancel" Type="PushButton" X="1" Y="1" Width="1" Height="1" Attributes="3" Text="[ButtonText_Cancel]" Order="300" TextLocId="-" Options="1"/>
    <ROW Dialog_="NewDialog" Control="Back" Type="PushButton" X="15" Y="290" Width="56" Height="20" Attributes="3" Text="[ButtonText_Back]" TextStyle="CfTitleFont" Order="400" TextLocId="-" Options="1"/>
    <ROW Dialog_="NewDialog" Control="ProductNameTitle" Type="Text" X="193" Y="95" Width="312" Height="94" Attributes="196611" Text="[ProductName]" TextStyle="AppNameSegoeUI28" Order="500" TextLocId="-"/>
    <ROW Dialog_="NewDialog" Control="CompanyText" Type="Text" X="193" Y="75" Width="312" Height="27" Attributes="196611" Text="[Manufacturer]" TextStyle="TitleFontSegoe" Order="600" TextLocId="-"/>
    <ROW Dialog_="NewDialog" Control="ProductLogo" Type="Bitmap" X="120" Y="75" Width="65" Height="69" Attributes="1" Text="[AppLogo]" Order="700"/>
    <ROW Dialog_="NewSeqDialog" Control="Bitmap" Type="Bitmap" X="0" Y="0" Width="520" Height="320" Attributes="1" Text="[BannerBitmap]" Order="100"/>
    <ROW Dialog_="NewSeqDialog" Control="Next" Type="PushButton" X="449" Y="290" Width="56" Height="20" Attributes="3" Text="[ButtonText_Next]" TextStyle="CfTitleFont" Order="200" TextLocId="-" Options="1"/>
    <ROW Dialog_="NewSeqDialog" Control="Cancel" Type="PushButton" X="1" Y="1" Width="1" Height="1" Attributes="3" Text="[ButtonText_Cancel]" Order="300" TextLocId="-" Options="1"/>
    <ROW Dialog_="NewSeqDialog" Control="Back" Type="PushButton" X="15" Y="290" Width="56" Height="20" Attributes="1" Text="[ButtonText_Back]" TextStyle="CfTitleFont" Order="400" TextLocId="-" Options="1"/>
    <ROW Dialog_="NewSeqDialog" Control="Title" Type="Text" X="160" Y="9" Width="200" Height="15" Attributes="196611" Text="新建对话框" TextStyle="TitleFontSegoe" Order="500"/>
    <ROW Dialog_="PatchWelcomeDlg" Control="Next" Type="PushButton" X="449" Y="290" Width="56" Height="20" Attributes="3" Text="[ButtonText_Next]" TextStyle="CfTitleFont" Order="300" TextLocId="-" MsiKey="PatchWelcomeDlg#Next" Options="1"/>
    <ROW Dialog_="PatchWelcomeDlg" Control="Back" Type="PushButton" X="15" Y="290" Width="56" Height="20" Attributes="1" Text="[ButtonText_Back]" TextStyle="CfTitleFont" Order="400" TextLocId="-" MsiKey="PatchWelcomeDlg#Back" Options="1"/>
    <ROW Dialog_="PrepareDlg" Control="Next" Type="PushButton" X="449" Y="290" Width="56" Height="20" Attributes="1" Text="[ButtonText_Next]" TextStyle="CfTitleFont" Order="300" TextLocId="-" MsiKey="PrepareDlg#Next" Options="1"/>
    <ROW Dialog_="PrepareDlg" Control="Back" Type="PushButton" X="15" Y="290" Width="56" Height="20" Attributes="0" Text="[ButtonText_Back]" TextStyle="CfTitleFont" Order="400" TextLocId="-" MsiKey="PrepareDlg#Back" Options="1"/>
    <ROW Dialog_="ProgressDlg" Control="Back" Type="PushButton" X="26" Y="277" Width="56" Height="20" Attributes="0" Text="[ButtonText_Back]" TextStyle="CfTitleFont" Order="200" TextLocId="-" MsiKey="ProgressDlg#Back" Options="1"/>
    <ROW Dialog_="ResumeDlg" Control="Cancel" Type="PushButton" X="387" Y="290" Width="56" Height="20" Attributes="3" Text="[ButtonText_Cancel]" TextStyle="CfTitleFont" Order="200" TextLocId="-" MsiKey="ResumeDlg#Cancel" Options="1"/>
    <ROW Dialog_="ResumeDlg" Control="Install" Type="PushButton" X="449" Y="290" Width="56" Height="20" Attributes="3" Text="[ButtonText_Install]" TextStyle="CfTitleFont" Order="300" TextLocId="-" MsiKey="ResumeDlg#Install" Options="1"/>
    <ROW Dialog_="ResumeDlg" Control="Back" Type="PushButton" X="15" Y="290" Width="56" Height="20" Attributes="1" Text="[ButtonText_Back]" TextStyle="CfTitleFont" Order="400" TextLocId="-" MsiKey="ResumeDlg#Back" Options="1"/>
    <ROW Dialog_="VerifyReadyDlg" Control="Install" Type="PushButton" X="449" Y="290" Width="56" Height="20" Attributes="3" Text="[ButtonText_Install]" TextStyle="CfTitleFont" Order="200" TextLocId="-" MsiKey="VerifyReadyDlg#Install" Options="1"/>
    <ROW Dialog_="VerifyReadyDlg" Control="Back" Type="PushButton" X="15" Y="290" Width="56" Height="20" Attributes="3" Text="[ButtonText_Back]" TextStyle="CfTitleFont" Order="400" TextLocId="-" MsiKey="VerifyReadyDlg#Back" Options="1"/>
    <ROW Dialog_="VerifyRemoveDlg" Control="Back" Type="PushButton" X="15" Y="290" Width="56" Height="20" Attributes="3" Text="[ButtonText_Back]" TextStyle="CfTitleFont" Order="200" TextLocId="-" MsiKey="VerifyRemoveDlg#Back" Options="1"/>
    <ROW Dialog_="VerifyRepairDlg" Control="Back" Type="PushButton" X="15" Y="290" Width="56" Height="20" Attributes="3" Text="[ButtonText_Back]" TextStyle="CfTitleFont" Order="400" TextLocId="-" MsiKey="VerifyRepairDlg#Back" Options="1"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiControlEventComponent">
    <ROW Dialog_="InstallDlg" Control_="Next" Event="NewDialog" Argument="FolderDlg" Condition="AI_INSTALL" Ordering="1"/>
    <ROW Dialog_="MaintenanceWelcomeDlg" Control_="Next" Event="NewDialog" Argument="MaintenanceTypeDlg" Condition="AI_MAINT" Ordering="99"/>
    <ROW Dialog_="CustomizeDlg" Control_="Next" Event="NewDialog" Argument="VerifyReadyDlg" Condition="AI_MAINT" Ordering="101"/>
    <ROW Dialog_="CustomizeDlg" Control_="Back" Event="NewDialog" Argument="MaintenanceTypeDlg" Condition="AI_MAINT" Ordering="1"/>
    <ROW Dialog_="VerifyReadyDlg" Control_="Install" Event="EndDialog" Argument="Return" Condition="AI_MAINT" Ordering="198"/>
    <ROW Dialog_="VerifyReadyDlg" Control_="Back" Event="NewDialog" Argument="CustomizeDlg" Condition="AI_MAINT" Ordering="201"/>
    <ROW Dialog_="MaintenanceTypeDlg" Control_="ChangeButton" Event="NewDialog" Argument="CustomizeDlg" Condition="AI_MAINT" Ordering="501"/>
    <ROW Dialog_="MaintenanceTypeDlg" Control_="Back" Event="NewDialog" Argument="MaintenanceWelcomeDlg" Condition="AI_MAINT" Ordering="1"/>
    <ROW Dialog_="MaintenanceTypeDlg" Control_="RemoveButton" Event="NewDialog" Argument="VerifyRemoveDlg" Condition="AI_MAINT AND InstallMode=&quot;Remove&quot;" Ordering="601"/>
    <ROW Dialog_="VerifyRemoveDlg" Control_="Back" Event="NewDialog" Argument="MaintenanceTypeDlg" Condition="AI_MAINT AND InstallMode=&quot;Remove&quot;" Ordering="1"/>
    <ROW Dialog_="MaintenanceTypeDlg" Control_="RepairButton" Event="NewDialog" Argument="VerifyRepairDlg" Condition="AI_MAINT AND InstallMode=&quot;Repair&quot;" Ordering="601"/>
    <ROW Dialog_="VerifyRepairDlg" Control_="Back" Event="NewDialog" Argument="MaintenanceTypeDlg" Condition="AI_MAINT AND InstallMode=&quot;Repair&quot;" Ordering="1"/>
    <ROW Dialog_="VerifyRepairDlg" Control_="Repair" Event="EndDialog" Argument="Return" Condition="AI_MAINT AND InstallMode=&quot;Repair&quot;" Ordering="399" Options="1"/>
    <ROW Dialog_="VerifyRemoveDlg" Control_="Remove" Event="EndDialog" Argument="Return" Condition="AI_MAINT AND InstallMode=&quot;Remove&quot;" Ordering="299" Options="1"/>
    <ROW Dialog_="PatchWelcomeDlg" Control_="Next" Event="NewDialog" Argument="VerifyReadyDlg" Condition="AI_PATCH" Ordering="201"/>
    <ROW Dialog_="VerifyReadyDlg" Control_="Install" Event="EndDialog" Argument="Return" Condition="AI_PATCH" Ordering="199"/>
    <ROW Dialog_="VerifyReadyDlg" Control_="Back" Event="NewDialog" Argument="PatchWelcomeDlg" Condition="AI_PATCH" Ordering="202"/>
    <ROW Dialog_="ResumeDlg" Control_="Install" Event="EndDialog" Argument="Return" Condition="AI_RESUME" Ordering="299"/>
    <ROW Dialog_="FatalError" Control_="Finish" Event="DoAction" Argument="AI_RollbackPrereq" Condition="1" Ordering="102"/>
    <ROW Dialog_="ExitDialog" Control_="Finish" Event="DoAction" Argument="AI_CleanPrereq" Condition="1" Ordering="301"/>
    <ROW Dialog_="FatalError" Control_="Finish" Event="DoAction" Argument="AI_CleanPrereq" Condition="1" Ordering="103"/>
    <ROW Dialog_="UserExit" Control_="Finish" Event="DoAction" Argument="AI_CleanPrereq" Condition="1" Ordering="101"/>
    <ROW Dialog_="FatalError" Control_="Finish" Event="DoAction" Argument="AI_RollbackPrePrereq" Condition="1" Ordering="104"/>
    <ROW Dialog_="ExitDialog" Control_="Finish" Event="DoAction" Argument="AI_CleanPrePrereq" Condition="1" Ordering="302"/>
    <ROW Dialog_="FatalError" Control_="Finish" Event="DoAction" Argument="AI_CleanPrePrereq" Condition="1" Ordering="105"/>
    <ROW Dialog_="UserExit" Control_="Finish" Event="DoAction" Argument="AI_CleanPrePrereq" Condition="1" Ordering="102"/>
    <ROW Dialog_="NewDialog" Control_="Cancel" Event="SpawnDialog" Argument="CancelDlg" Condition="1" Ordering="100"/>
    <ROW Dialog_="NewSeqDialog" Control_="Cancel" Event="SpawnDialog" Argument="CancelDlg" Condition="1" Ordering="100"/>
    <ROW Dialog_="FolderDlg" Control_="Next" Event="EndDialog" Argument="Return" Condition="AI_INSTALL" Ordering="201"/>
    <ROW Dialog_="FolderDlg" Control_="Back" Event="NewDialog" Argument="InstallDlg" Condition="AI_INSTALL" Ordering="1"/>
    <ROW Dialog_="FolderDlg" Control_="Next" Event="SpawnDialog" Argument="OutOfRbDiskDlg" Condition="AI_INSTALL AND OutOfDiskSpace = 1 AND OutOfNoRbDiskSpace = 0 AND (PROMPTROLLBACKCOST=&quot;P&quot; OR NOT PROMPTROLLBACKCOST)" Ordering="202" Options="2"/>
    <ROW Dialog_="FolderDlg" Control_="Next" Event="EnableRollback" Argument="False" Condition="AI_INSTALL AND OutOfDiskSpace = 1 AND OutOfNoRbDiskSpace = 0 AND PROMPTROLLBACKCOST=&quot;D&quot;" Ordering="203" Options="2"/>
    <ROW Dialog_="FolderDlg" Control_="Next" Event="SpawnDialog" Argument="OutOfDiskDlg" Condition="AI_INSTALL AND ( (OutOfDiskSpace = 1 AND OutOfNoRbDiskSpace = 1) OR (OutOfDiskSpace = 1 AND PROMPTROLLBACKCOST=&quot;F&quot;) )" Ordering="204" Options="2"/>
    <ROW Dialog_="FolderDlg" Control_="FolderDlgDialogInitializer" Event="[AI_ButtonText_Next_Orig]" Argument="[ButtonText_Next]" Condition="AI_INSTALL" Ordering="0" Options="2"/>
    <ROW Dialog_="FolderDlg" Control_="FolderDlgDialogInitializer" Event="[ButtonText_Next]" Argument="[[AI_CommitButton]]" Condition="AI_INSTALL" Ordering="1" Options="2"/>
    <ROW Dialog_="FolderDlg" Control_="FolderDlgDialogInitializer" Event="[AI_Text_Next_Orig]" Argument="[Text_Next]" Condition="AI_INSTALL" Ordering="2" Options="2"/>
    <ROW Dialog_="FolderDlg" Control_="FolderDlgDialogInitializer" Event="[Text_Next]" Argument="[Text_Install]" Condition="AI_INSTALL" Ordering="3" Options="2"/>
    <ROW Dialog_="FolderDlg" Control_="Back" Event="[ButtonText_Next]" Argument="[AI_ButtonText_Next_Orig]" Condition="AI_INSTALL" Ordering="2" Options="2"/>
    <ROW Dialog_="FolderDlg" Control_="Back" Event="[Text_Next]" Argument="[AI_Text_Next_Orig]" Condition="AI_INSTALL" Ordering="3" Options="2"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiCreateFolderComponent">
    <ROW Directory_="APPDIR" Component_="APPDIR" ManualDelete="true"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiCustActComponent">
    <ROW Action="AI_AppSearchEx" Type="1" Source="Prereq.dll" Target="DoAppSearchEx"/>
    <ROW Action="AI_BACKUP_AI_SETUPEXEPATH" Type="51" Source="AI_SETUPEXEPATH_ORIGINAL" Target="[AI_SETUPEXEPATH]"/>
    <ROW Action="AI_CleanPrePrereq" Type="65" Source="Prereq.dll" Target="CleanPrereq" WithoutSeq="true"/>
    <ROW Action="AI_CleanPrereq" Type="65" Source="Prereq.dll" Target="CleanPrereq" WithoutSeq="true"/>
    <ROW Action="AI_ConfigureChainer" Type="1" Source="Prereq.dll" Target="ConfigurePrereqLauncher"/>
    <ROW Action="AI_DownloadPrereq" Type="1" Source="Prereq.dll" Target="DownloadPrereq"/>
    <ROW Action="AI_DpiContentScale" Type="1" Source="aicustact.dll" Target="DpiContentScale"/>
    <ROW Action="AI_EnableDebugLog" Type="321" Source="aicustact.dll" Target="EnableDebugLog"/>
    <ROW Action="AI_ExtractPrereq" Type="65" Source="Prereq.dll" Target="ExtractPrereq"/>
    <ROW Action="AI_InstallModeCheck" Type="1" Source="aicustact.dll" Target="UpdateInstallMode" WithoutSeq="true"/>
    <ROW Action="AI_InstallPostPrerequisite" Type="1" Source="Prereq.dll" Target="InstallPostPrereq"/>
    <ROW Action="AI_InstallPrePrerequisite" Type="65" Source="Prereq.dll" Target="InstallPrePrereq"/>
    <ROW Action="AI_InstallPrerequisite" Type="1" Source="Prereq.dll" Target="InstallPrereq"/>
    <ROW Action="AI_LaunchApp" Type="1" Source="aicustact.dll" Target="LaunchApp"/>
    <ROW Action="AI_LaunchChainer" Type="3314" Source="AI_PREREQ_CHAINER"/>
    <ROW Action="AI_MigrateInstallerProps" Type="1" Source="aicustact.dll" Target="MigrateInstallerProps"/>
    <ROW Action="AI_PREPARE_UPGRADE" Type="65" Source="aicustact.dll" Target="PrepareUpgrade"/>
    <ROW Action="AI_PRESERVE_INSTALL_TYPE" Type="65" Source="aicustact.dll" Target="PreserveInstallType"/>
    <ROW Action="AI_RESTORE_AI_SETUPEXEPATH" Type="51" Source="AI_SETUPEXEPATH" Target="[AI_SETUPEXEPATH_ORIGINAL]"/>
    <ROW Action="AI_ResolveKnownFolders" Type="1" Source="aicustact.dll" Target="AI_ResolveKnownFolders"/>
    <ROW Action="AI_RestartElevated" Type="1" Source="aicustact.dll" Target="RestartElevated"/>
    <ROW Action="AI_RestoreInstallerProps" Type="1" Source="aicustact.dll" Target="RestoreInstallerProps" WithoutSeq="true"/>
    <ROW Action="AI_RollbackPrePrereq" Type="65" Source="Prereq.dll" Target="RollbackPrePrereq" WithoutSeq="true"/>
    <ROW Action="AI_RollbackPrereq" Type="65" Source="Prereq.dll" Target="RollbackPrereq" WithoutSeq="true"/>
    <ROW Action="AI_SHOW_LOG" Type="65" Source="aicustact.dll" Target="LaunchLogFile" WithoutSeq="true"/>
    <ROW Action="AI_STORE_LOCATION" Type="51" Source="ARPINSTALLLOCATION" Target="[APPDIR]"/>
    <ROW Action="AI_VerifyPrePrereq" Type="1" Source="Prereq.dll" Target="VerifyPrePrereq"/>
    <ROW Action="AI_VerifyPrereq" Type="1" Source="Prereq.dll" Target="VerifyPrereq"/>
    <ROW Action="AI_ViewReadme" Type="1" Source="aicustact.dll" Target="ViewReadMe"/>
    <ROW Action="SET_APPDIR" Type="307" Source="APPDIR" Target="[ProgramFilesFolder][Manufacturer]\[ProductName]"/>
    <ROW Action="SET_SHORTCUTDIR" Type="307" Source="SHORTCUTDIR" Target="[ProgramMenuFolder][ProductName]"/>
    <ROW Action="SET_TARGETDIR_TO_APPDIR" Type="51" Source="TARGETDIR" Target="[APPDIR]"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiDialogComponent">
    <ROW Dialog="NewDialog" HCentering="50" VCentering="50" Width="520" Height="320" Attributes="3" Title="[ProductName] [Setup]" Control_Default="Next" Control_Cancel="Cancel"/>
    <ROW Dialog="NewSeqDialog" HCentering="50" VCentering="50" Width="520" Height="320" Attributes="3" Title="[ProductName] [Setup]" Control_Default="Next" Control_Cancel="Cancel"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiFeatCompsComponent">
    <ROW Feature_="MainFeature" Component_="APPDIR"/>
    <ROW Feature_="MainFeature" Component_="ProductInformation"/>
    <ROW Feature_="A0BFC224E0E9125ADE0B2DBF343" Component_="A0BFC224E0E9125ADE0B2DBF343"/>
    <ROW Feature_="MainFeature" Component_="exe"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiIconsComponent">
    <ROW Name="logo.exe" SourcePath="BnsHelper\Properties\logo.ico" Index="0"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiInstExSeqComponent">
    <ROW Action="AI_STORE_LOCATION" Condition="(Not Installed) OR REINSTALL" Sequence="1501"/>
    <ROW Action="AI_PREPARE_UPGRADE" Condition="AI_UPGRADE=&quot;No&quot; AND (Not Installed)" Sequence="1397"/>
    <ROW Action="AI_ResolveKnownFolders" Sequence="52"/>
    <ROW Action="AI_EnableDebugLog" Sequence="51"/>
    <ROW Action="AI_MigrateInstallerProps" Condition="SETUPEXEDIR AND ((UILevel = 2) OR (UILevel = 3))" Sequence="6596"/>
    <ROW Action="AI_VerifyPrereq" Sequence="1101"/>
    <ROW Action="AI_ConfigureChainer" Condition="(NOT SETUPEXEDIR) AND ((UILevel = 2) OR (UILevel = 3)) AND (NOT UPGRADINGPRODUCTCODE)" Sequence="6597"/>
    <ROW Action="AI_LaunchChainer" Condition="AI_PREREQ_CHAINER AND (NOT UPGRADINGPRODUCTCODE)" Sequence="6598"/>
    <ROW Action="AI_AppSearchEx" Sequence="101"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiInstallUISequenceComponent">
    <ROW Action="AI_PRESERVE_INSTALL_TYPE" Sequence="199"/>
    <ROW Action="AI_ResolveKnownFolders" Sequence="54"/>
    <ROW Action="AI_DpiContentScale" Sequence="53"/>
    <ROW Action="AI_EnableDebugLog" Sequence="52"/>
    <ROW Action="AI_BACKUP_AI_SETUPEXEPATH" Sequence="99"/>
    <ROW Action="AI_RESTORE_AI_SETUPEXEPATH" Condition="AI_SETUPEXEPATH_ORIGINAL" Sequence="102"/>
    <ROW Action="InstallDlg" Condition="AI_INSTALL" Sequence="1230" SeqType="3" MsiKey="WelcomeDlg"/>
    <ROW Action="AI_DownloadPrereq" Sequence="1295"/>
    <ROW Action="AI_ExtractPrereq" Sequence="1296"/>
    <ROW Action="AI_InstallPrerequisite" Sequence="1297"/>
    <ROW Action="AI_InstallPostPrerequisite" Sequence="1301"/>
    <ROW Action="AI_CleanPrereq" Sequence="1302"/>
    <ROW Action="AI_InstallPrePrerequisite" Condition="Installed" Sequence="1298"/>
    <ROW Action="AI_AppSearchEx" Sequence="101"/>
    <ROW Action="AI_RestartElevated" Sequence="51" Builds="DefaultBuild"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiLaunchConditionsComponent">
    <ROW Condition="((VersionNT &lt;&gt; 501) AND (VersionNT &lt;&gt; 502))" Description="[ProductName] 无法安装在 [WindowsTypeNT5XDisplay]." DescriptionLocId="AI.LaunchCondition.NoNT5X" IsPredefined="true" Builds="DefaultBuild"/>
    <ROW Condition="(VersionNT &lt;&gt; 400)" Description="[ProductName] 无法安装在 [WindowsTypeNT40Display]." DescriptionLocId="AI.LaunchCondition.NoNT40" IsPredefined="true" Builds="DefaultBuild"/>
    <ROW Condition="(VersionNT &lt;&gt; 500)" Description="[ProductName] 无法安装在 [WindowsTypeNT50Display]." DescriptionLocId="AI.LaunchCondition.NoNT50" IsPredefined="true" Builds="DefaultBuild"/>
    <ROW Condition="(VersionNT &lt;&gt; 600)" Description="[ProductName] 无法安装在 [WindowsTypeNT60Display]." DescriptionLocId="AI.LaunchCondition.NoNT60" IsPredefined="true" Builds="DefaultBuild"/>
    <ROW Condition="VersionNT" Description="[ProductName] 无法安装在 [WindowsType9XDisplay]." DescriptionLocId="AI.LaunchCondition.No9X" IsPredefined="true" Builds="DefaultBuild"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiRegsComponent">
    <ROW Registry="A0BFC224E0E9125ADE0B2DBF343" Root="-1" Key="Software\Caphyon\Advanced Installer\Prereqs\[ProductCode]\[ProductVersion]" Name="A0BFC224E0E9125ADE0B2DBF343" Value="1" Component_="A0BFC224E0E9125ADE0B2DBF343"/>
    <ROW Registry="AdvancedInstaller" Root="-1" Key="Software\Caphyon\Advanced Installer" Name="\"/>
    <ROW Registry="Caphyon" Root="-1" Key="Software\Caphyon" Name="\"/>
    <ROW Registry="Manufacturer_1" Root="2" Key="Software\[Manufacturer]" Name="\"/>
    <ROW Registry="Path" Root="2" Key="Software\[Manufacturer]\[ProductName]" Name="Path" Value="[APPDIR]" Component_="ProductInformation"/>
    <ROW Registry="Prereqs" Root="-1" Key="Software\Caphyon\Advanced Installer\Prereqs" Name="\"/>
    <ROW Registry="ProductCode" Root="-1" Key="Software\Caphyon\Advanced Installer\Prereqs\[ProductCode]" Name="\"/>
    <ROW Registry="ProductName_1" Root="2" Key="Software\[Manufacturer]\[ProductName]" Name="\"/>
    <ROW Registry="ProductVersion" Root="-1" Key="Software\Caphyon\Advanced Installer\Prereqs\[ProductCode]\[ProductVersion]" Name="\"/>
    <ROW Registry="Software" Root="-1" Key="Software" Name="\"/>
    <ROW Registry="Software_1" Root="2" Key="Software" Name="\"/>
    <ROW Registry="Version" Root="2" Key="Software\[Manufacturer]\[ProductName]" Name="Version" Value="[ProductVersion]" Component_="ProductInformation"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiThemeComponent">
    <ATTRIBUTE name="UsedTheme" value="serene"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiUpgradeComponent">
    <ROW UpgradeCode="[|UpgradeCode]" VersionMin="0.0.1" VersionMax="[|ProductVersion]" Attributes="257" ActionProperty="OLDPRODUCTS"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.PreReqComponent">
    <ROW PrereqKey="A0BFC224E0E9125ADE0B2DBF343" DisplayName=".NET Desktop Runtime 8.0.13 x64" VersionMin="8.0" SetupFileUrl="https://builds.dotnet.microsoft.com/dotnet/WindowsDesktop/8.0.16/windowsdesktop-runtime-8.0.16-win-x64.exe" Location="1" ExactSize="0" WinNTVersions="Windows 9x/ME/NT/2000/XP/Vista/Windows 7/Windows 8 x86/Windows 8.1 x86/Windows 10 x86" WinNT64Versions="Windows 7 RTM x64, Windows 8 x64, Windows Server 2012 x64, Windows 10 version 1507 x64, Windows 10 version 1511 x64" Operator="1" NoUiComLine="/q /norestart" Options="fx" TargetName=".NET 8.0" Feature="A0BFC224E0E9125ADE0B2DBF343"/>
    <ATTRIBUTE name="PrereqsOrder" value="A0BFC224E0E9125ADE0B2DBF343"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.PreReqSearchComponent">
    <ROW SearchKey="A0BFC224E0E9125ADE0B2DBF343SystemFo" Prereq="A0BFC224E0E9125ADE0B2DBF343" SearchType="14" SearchString="[ProgramFiles64Folder]dotnet\shared\Microsoft.WindowsDesktop.App" VerMin="8.0.13" Order="1" Property="PreReqSearch_A0BFC224E0E9125ADE0B2D" Platform="1"/>
  </COMPONENT>
</DOCUMENT>
