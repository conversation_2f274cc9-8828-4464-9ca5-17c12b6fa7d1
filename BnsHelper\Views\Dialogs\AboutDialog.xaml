﻿<Border x:Class="Xylia.Preview.UI.Views.Dialogs.AboutDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
		xmlns:hc="https://handyorg.github.io/handycontrol"
        xmlns:helper="clr-namespace:Xylia.BnsHelper.Common.Helpers"
        Background="{DynamicResource RegionBrush}" CornerRadius="8"
		Width="500" BorderBrush="{DynamicResource BorderBrush}" BorderThickness="1" Margin="25 15 0 15">

    <Border.Resources>
        <Style x:Key="ListBoxItemStyle" TargetType="ListBoxItem" BasedOn="{StaticResource ListBoxItemBaseStyle}">
            <Style.Setters>
                <Setter Property="Focusable" Value="False" />
                <Setter Property="Background" Value="Transparent" />
            </Style.Setters>
        </Style>

        <Style TargetType="ListBox" BasedOn="{StaticResource ListBoxBaseStyle}">
            <Style.Setters>
                <Setter Property="BorderThickness" Value="0" />
                <Setter Property="Background" Value="Transparent" />
                <Setter Property="ItemsPanel">
                    <Setter.Value>
                        <ItemsPanelTemplate>
                            <WrapPanel Orientation="Horizontal" HorizontalAlignment="Center" />
                        </ItemsPanelTemplate>
                    </Setter.Value>
                </Setter>
                <Setter Property="ItemContainerStyle" Value="{StaticResource ListBoxItemStyle}" />
                <Setter Property="ItemTemplate">
                    <Setter.Value>
                        <DataTemplate>
                            <Grid Width="45">
                                <Grid.ToolTip>
                                    <MultiBinding Converter="{StaticResource StringConverter}" ConverterParameter="{}{0}&#13;{1}&#13;" >
                                        <Binding Path="Strengths" />
                                        <Binding Path="Status" />
                                    </MultiBinding>
                                </Grid.ToolTip>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="*" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>
                                <Grid.InputBindings>
                                    <MouseBinding Command="{Binding OpenLinkCommand}" MouseAction="LeftClick" />
                                </Grid.InputBindings>

                                <Image Source="{Binding HeadImg}" />
                                <TextBlock Text="{Binding Name}" Grid.Row="1" HorizontalAlignment="Center" TextWrapping="WrapWithOverflow" />
                            </Grid>
                        </DataTemplate>
                    </Setter.Value>
                </Setter>
            </Style.Setters>
        </Style>
    </Border.Resources>

    <Grid>
        <hc:ScrollViewer Margin="0 3">
            <StackPanel Margin="20 15">
                <StackPanel HorizontalAlignment="Center" Margin="0 0 0 15">
                    <TextBlock Text="Xylia 开发与 &#x2665; 技术支持" FontSize="30" FontWeight="Bold" Foreground="#85bcff" HorizontalAlignment="Center" />
                </StackPanel>

                <!-- Contributors -->
                <StackPanel>
                    <TextBlock Text="{DynamicResource About_Contributors_Title}" FontSize="15" FontWeight="Bold" Foreground="#9DA3DD" FontStretch="Expanded" />
                    <TextBlock Text="&#09;&#09;&#09;&#09;" FontSize="25" FontWeight="Bold" Height="2" Foreground="Transparent" HorizontalAlignment="Center" />
                </StackPanel>
                <TextBlock Foreground="#727272" TextWrapping="Wrap" Margin="0 0 0 10" Text="{DynamicResource About_Contributors}" />
                <ListBox ItemsSource="{Binding Team.Author}" HorizontalAlignment="Left" />
                <ListBox ItemsSource="{Binding Team.Friends}" HorizontalAlignment="Left" />

                <!-- References -->
                <StackPanel>
                    <TextBlock Text="{DynamicResource About_Acknowledgement_Title}" FontSize="15" FontWeight="Bold" Foreground="#9DA3DD" FontStretch="Expanded" />
                    <TextBlock Text="&#09;&#09;&#09;&#09;" FontSize="25" FontWeight="Bold" Height="2" Foreground="Transparent" HorizontalAlignment="Center" />
                </StackPanel>
                <TextBlock Foreground="#727272" TextWrapping="Wrap" Text="{Binding ReferencesLabel}" />
            </StackPanel>
        </hc:ScrollViewer>

        <Button Command="hc:ControlCommands.Close" Width="20" Height="20" Margin="5" Padding="3" hc:IconElement.Geometry="{StaticResource DeleteGeometry}" 
                Foreground="{DynamicResource SecondaryTextBrush}" Style="{StaticResource ButtonIcon}" HorizontalAlignment="Right" VerticalAlignment="Top" />
    </Grid>
</Border>
