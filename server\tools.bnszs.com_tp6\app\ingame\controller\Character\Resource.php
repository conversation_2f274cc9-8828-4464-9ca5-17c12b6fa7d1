<?php

namespace app\ingame\controller\Character;

use app\common\controller\BaseController;
use think\App;
use think\Request;
use think\facade\Db;
use think\facade\Cache;
use think\facade\View;

class Resource extends BaseController
{
    public function __construct(App $app, Request $request)
    {
        parent::__construct($app, $request);
    }

    /**
     * BMG页面
     */
    public function bmg()
    {
        try {
            // 获取URL参数
            $music = urldecode($this->request->get('music', ''));
            $msg = urldecode($this->request->get('msg', ''));
            $autoplay = $this->request->get('autoplay', '1') === '0' ? '' : 'autoplay';
            $loop = $this->request->get('loop', '1') === '0' ? '' : 'loop';

            // 设置默认值
            if (empty($music)) {
                $music = "//music.163.com/song/media/outer/url?id=468176711.mp3";
            }
            if (empty($msg)) {
                $msg = "欢迎使用剑灵小助手装备查询优化服务，定制功能仅对受邀用户开放";
            }

            // 分配变量到视图
            View::assign([
                'music' => $music,
                'msg' => $msg,
                'autoplay' => $autoplay,
                'loop' => $loop
            ]);

            return View::fetch(app()->getAppPath() . 'common/view/bmg.html');
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取BMG页面失败', 'error' => $e->getMessage()]);
        }
    }

    /**
     * 资源中心首页
     */
    public function index()
    {
        $categories = $this->getResourceCategories();
        $hotResources = $this->getHotResources();
        
        return View::fetch('Character/resource_center', [
            'categories' => $categories,
            'hot_resources' => $hotResources
        ]);
    }

    /**
     * 资源列表
     */
    public function list()
    {
        $category = $this->request->get('category', '');
        $keyword = $this->request->get('keyword', '');
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 20);

        $query = Db::name('character_resources')
            ->where('status', 1); // 只显示已审核的资源

        if (!empty($category)) {
            $query->where('category', $category);
        }

        if (!empty($keyword)) {
            $query->where('title|description', 'like', '%' . $keyword . '%');
        }

        $resources = $query->order('create_time DESC')
            ->page($page, $limit)
            ->select();

        $total = $query->count();

        return View::fetch('Character/resource_list', [
            'resources' => $resources,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'category' => $category,
            'keyword' => $keyword
        ]);
    }

    /**
     * 资源详情
     */
    public function detail()
    {
        $id = $this->request->get('id', 0);
        
        if (empty($id)) {
            abort(404);
        }

        $resource = Db::name('character_resources')
            ->where('id', $id)
            ->where('status', 1)
            ->find();

        if (!$resource) {
            abort(404);
        }

        // 增加浏览次数
        Db::name('character_resources')
            ->where('id', $id)
            ->inc('view_count')
            ->update();

        // 获取相关资源
        $relatedResources = Db::name('character_resources')
            ->where('category', $resource['category'])
            ->where('id', '<>', $id)
            ->where('status', 1)
            ->order('view_count DESC')
            ->limit(5)
            ->select();

        return View::fetch('Character/resource_detail', [
            'resource' => $resource,
            'related_resources' => $relatedResources
        ]);
    }

    /**
     * 下载资源
     */
    public function download()
    {
        $id = $this->request->get('id', 0);
        
        if (empty($id)) {
            return json(['code' => 0, 'msg' => '资源ID不能为空']);
        }

        $resource = Db::name('character_resources')
            ->where('id', $id)
            ->where('status', 1)
            ->find();

        if (!$resource) {
            return json(['code' => 0, 'msg' => '资源不存在']);
        }

        // 增加下载次数
        Db::name('character_resources')
            ->where('id', $id)
            ->inc('download_count')
            ->update();

        return json([
            'code' => 1,
            'data' => [
                'download_url' => $resource['file_url'],
                'filename' => $resource['filename']
            ]
        ]);
    }

    /**
     * 上传资源
     */
    public function upload()
    {
        if ($this->request->isPost()) {
            $data = $this->request->post();
            
            // 验证必要字段
            if (empty($data['title']) || empty($data['category'])) {
                return json(['code' => 0, 'msg' => '标题和分类不能为空']);
            }

            try {
                $resourceData = [
                    'title' => $data['title'],
                    'description' => $data['description'] ?? '',
                    'category' => $data['category'],
                    'file_url' => $data['file_url'] ?? '',
                    'filename' => $data['filename'] ?? '',
                    'file_size' => $data['file_size'] ?? 0,
                    'uploader_name' => $data['uploader_name'] ?? '',
                    'uploader_contact' => $data['uploader_contact'] ?? '',
                    'status' => 0, // 待审核
                    'create_time' => date('Y-m-d H:i:s'),
                    'update_time' => date('Y-m-d H:i:s')
                ];

                $result = Db::name('character_resources')->insert($resourceData);

                if ($result) {
                    return json(['code' => 1, 'msg' => '资源上传成功，等待审核']);
                } else {
                    return json(['code' => 0, 'msg' => '资源上传失败']);
                }
            } catch (\Exception $e) {
                return json(['code' => 0, 'msg' => '上传失败: ' . $e->getMessage()]);
            }
        }

        $categories = $this->getResourceCategories();
        
        return View::fetch('Character/resource_upload', [
            'categories' => $categories
        ]);
    }

    /**
     * 获取资源分类
     */
    private function getResourceCategories()
    {
        return [
            'skin' => '皮肤',
            'mod' => '模组',
            'tool' => '工具',
            'guide' => '攻略',
            'other' => '其他'
        ];
    }

    /**
     * 获取热门资源
     */
    private function getHotResources($limit = 10)
    {
        return Db::name('character_resources')
            ->where('status', 1)
            ->order('download_count DESC, view_count DESC')
            ->limit($limit)
            ->select();
    }

    /**
     * 搜索资源
     */
    public function search()
    {
        $keyword = $this->request->get('keyword', '');
        $category = $this->request->get('category', '');
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 20);

        if (empty($keyword)) {
            return json(['code' => 0, 'msg' => '搜索关键词不能为空']);
        }

        $query = Db::name('character_resources')
            ->where('status', 1)
            ->where('title|description|tags', 'like', '%' . $keyword . '%');

        if (!empty($category)) {
            $query->where('category', $category);
        }

        $resources = $query->order('download_count DESC, create_time DESC')
            ->page($page, $limit)
            ->select();

        $total = $query->count();

        return json([
            'code' => 1,
            'data' => [
                'resources' => $resources,
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ]
        ]);
    }

    /**
     * 获取资源统计信息
     */
    public function stats()
    {
        try {
            $stats = [
                'total_resources' => Db::name('character_resources')->where('status', 1)->count(),
                'total_downloads' => Db::name('character_resources')->where('status', 1)->sum('download_count'),
                'categories' => []
            ];

            $categories = $this->getResourceCategories();
            foreach ($categories as $key => $name) {
                $stats['categories'][$key] = [
                    'name' => $name,
                    'count' => Db::name('character_resources')
                        ->where('status', 1)
                        ->where('category', $key)
                        ->count()
                ];
            }

            return json(['code' => 1, 'data' => $stats]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取统计信息失败', 'error' => $e->getMessage()]);
        }
    }
}
