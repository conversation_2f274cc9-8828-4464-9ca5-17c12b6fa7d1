﻿using CommunityToolkit.Mvvm.ComponentModel;
using Xylia.BnsHelper.Models;
using Xylia.BnsHelper.Services;

namespace Xylia.BnsHelper.ViewModels;
internal partial class ClockAlarmViewModel : ObservableObject
{
	private IEnumerable<Schedule>? _cachedSchedules;
	private DateTime _lastCacheTime = DateTime.MinValue;
	private readonly TimeSpan _cacheTimeout = TimeSpan.FromMinutes(5); // 缓存5分钟

	public IEnumerable<Schedule> Schedules
	{
		get
		{
			// 使用缓存避免频繁访问API
			var now = DateTime.Now;
			if (_cachedSchedules == null || (now - _lastCacheTime) > _cacheTimeout)
			{
				try
				{
					var schedules = ApiEndpointService.BnszsApi.Schedules.OrderBy(x => x.DateTime);

					// 确保每个Schedule都加载了用户设置
					foreach (var schedule in schedules)
					{
						schedule.LoadNotificationSettings();
					}

					_cachedSchedules = schedules.ToList(); // 转换为List避免重复枚举
					_lastCacheTime = now;
				}
				catch (Exception ex)
				{
					System.Diagnostics.Debug.WriteLine($"[ERROR] 获取Schedule数据失败: {ex.Message}");
					_cachedSchedules ??= Array.Empty<Schedule>();
				}
			}

			return _cachedSchedules;
		}
	}

	/// <summary>
	/// 手动刷新数据
	/// </summary>
	public void RefreshSchedules()
	{
		_cachedSchedules = null;
		_lastCacheTime = DateTime.MinValue;
		OnPropertyChanged(nameof(Schedules));
	}
}
