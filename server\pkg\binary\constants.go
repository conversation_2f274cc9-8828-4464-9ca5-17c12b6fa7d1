package binary

// 协议常量定义
const (
	// 协议魔数和版本
	ProtocolMagic   = 0xDA
	ProtocolVersion = 0x01

	// 消息头大小
	HeaderSize = 16 // 固定16字节

	// 标志位定义
	FlagNeedResponse = 0x01 // 需要响应
	FlagCompressed   = 0x02 // 已压缩
	FlagEncrypted    = 0x04 // 已加密

	// 限制常量
	MaxMessageSize = 65536 // 最大消息大小 64KB
)

// 消息类型定义
const (
	MsgTypeLogin                    = 0x01 // 登录请求
	MsgTypeLoginResponse            = 0x81 // 登录响应
	MsgTypeHeartbeat                = 0x02 // 心跳请求
	MsgTypeHeartbeatResponse        = 0x82 // 心跳响应
	MsgTypeLogout                   = 0x03 // 登出请求
	MsgTypeLogoutResponse           = 0x83 // 登出响应
	MsgTypeGetDeviceHistory         = 0x04 // 获取设备历史
	MsgTypeGetDeviceHistoryResponse = 0x84 // 设备历史响应
	MsgTypeGetActiveDevices         = 0x05 // 获取活跃设备
	MsgTypeGetActiveDevicesResponse = 0x85 // 活跃设备响应
	MsgTypeLuckyDraw                = 0x06 // 签到抽奖请求
	MsgTypeLuckyDrawResponse        = 0x86 // 签到抽奖响应
	MsgTypeLuckyStatus              = 0x07 // 获取签到状态
	MsgTypeLuckyStatusResponse      = 0x87 // 签到状态响应
	MsgTypeCDKeyActivate            = 0x08 // CDKEY激活请求
	MsgTypeCDKeyActivateResponse    = 0x88 // CDKEY激活响应
	MsgTypeGetActivityInfo          = 0x09 // 获取活动信息请求
	MsgTypeGetActivityInfoResponse  = 0x89 // 获取活动信息响应
	MsgTypeError                    = 0xFF // 错误响应
)

// 字段类型定义
const (
	// 基本字段类型
	FieldTypeQQNumber          = 0x01 // QQ号码 (string)
	FieldTypeDeviceFingerprint = 0x02 // 设备指纹 (string)
	FieldTypeToken             = 0x03 // 认证令牌 (string)
	FieldTypeCPU               = 0x04 // CPU信息 (string)
	FieldTypeMemory            = 0x05 // 内存信息 (string)
	FieldTypeMotherboard       = 0x06 // 主板信息 (string)
	FieldTypeDisk              = 0x07 // 硬盘信息 (string)
	FieldTypeMACAddress        = 0x08 // MAC地址 (string)

	// 响应字段类型
	FieldTypeSuccess      = 0x10 // 成功标志 (bool)
	FieldTypeErrorCode    = 0x11 // 错误码 (uint32)
	FieldTypeErrorMessage = 0x12 // 错误信息 (string)

	// 用户信息字段类型
	FieldTypeUID       = 0x20 // 用户ID (uint64)
	FieldTypeUIN       = 0x21 // 用户UIN (string)
	FieldTypeName      = 0x22 // 用户名 (string)
	FieldTypeEmail     = 0x23 // 邮箱 (string)
	FieldTypeStatus    = 0x24 // 状态 (uint32)
	FieldTypeBeta      = 0x25 // Beta标志 (bool)
	FieldTypeLoginTime = 0x26 // 登录时间 (uint64)
	FieldTypeTokenTime = 0x27 // Token时间 (uint64)

	// 签到抽奖字段类型
	FieldTypeLuckyMessage = 0x28 // 抽奖结果消息 (string)
	FieldTypeLuckyCount   = 0x29 // 额外次数 (uint32)
	FieldTypeLuckyDay     = 0x2A // 连续签到天数 (uint32)
	FieldTypeLuckyPoint   = 0x2B // 奖励点数 (uint32)
	FieldTypeLuckyNumber  = 0x2C // 总签到次数 (uint32)
	FieldTypeLuckyToday   = 0x2D // 今日签到次数 (uint32)
	FieldTypeLuckyExtra   = 0x2E // 额外次数 (uint32)
	FieldTypeCanSign      = 0x2F // 是否可以签到 (bool)

	// 复合字段类型
	FieldTypeDeviceList = 0x30 // 设备列表 (array)
	FieldTypeDeviceInfo = 0x31 // 设备信息 (object)
)

// 错误码定义
const (
	ErrorCodeSuccess       = 0
	ErrorCodeInvalidFormat = 1001 // 无效格式
	ErrorCodeInvalidQQ     = 1002 // 无效QQ号
	ErrorCodeInvalidDevice = 1003 // 无效设备
	ErrorCodeUnauthorized  = 1004 // 未授权
	ErrorCodeTokenExpired  = 1005 // Token过期
	ErrorCodeServerError   = 2001 // 服务器错误
	ErrorCodeDatabaseError = 2002 // 数据库错误
	ErrorCodeCacheError    = 2003 // 缓存错误
)

// 消息类型到字符串的映射
var MsgTypeNames = map[uint8]string{
	MsgTypeLogin:                    "Login",
	MsgTypeLoginResponse:            "LoginResponse",
	MsgTypeHeartbeat:                "Heartbeat",
	MsgTypeHeartbeatResponse:        "HeartbeatResponse",
	MsgTypeLogout:                   "Logout",
	MsgTypeLogoutResponse:           "LogoutResponse",
	MsgTypeGetDeviceHistory:         "GetDeviceHistory",
	MsgTypeGetDeviceHistoryResponse: "GetDeviceHistoryResponse",
	MsgTypeGetActiveDevices:         "GetActiveDevices",
	MsgTypeGetActiveDevicesResponse: "GetActiveDevicesResponse",
	MsgTypeLuckyDraw:                "LuckyDraw",
	MsgTypeLuckyDrawResponse:        "LuckyDrawResponse",
	MsgTypeLuckyStatus:              "LuckyStatus",
	MsgTypeLuckyStatusResponse:      "LuckyStatusResponse",
	MsgTypeCDKeyActivate:            "CDKeyActivate",
	MsgTypeCDKeyActivateResponse:    "CDKeyActivateResponse",
	MsgTypeError:                    "Error",
}

// 字段类型到字符串的映射
var FieldTypeNames = map[uint8]string{
	FieldTypeQQNumber:          "QQNumber",
	FieldTypeDeviceFingerprint: "DeviceFingerprint",
	FieldTypeToken:             "Token",
	FieldTypeCPU:               "CPU",
	FieldTypeMemory:            "Memory",
	FieldTypeMotherboard:       "Motherboard",
	FieldTypeDisk:              "Disk",
	FieldTypeMACAddress:        "MACAddress",
	FieldTypeSuccess:           "Success",
	FieldTypeErrorCode:         "ErrorCode",
	FieldTypeErrorMessage:      "ErrorMessage",
	FieldTypeUID:               "UID",
	FieldTypeUIN:               "UIN",
	FieldTypeName:              "Name",
	FieldTypeEmail:             "Email",
	FieldTypeStatus:            "Status",
	FieldTypeBeta:              "Beta",
	FieldTypeLoginTime:         "LoginTime",
	FieldTypeTokenTime:         "TokenTime",
	FieldTypeLuckyMessage:      "LuckyMessage",
	FieldTypeLuckyCount:        "LuckyCount",
	FieldTypeLuckyDay:          "LuckyDay",
	FieldTypeLuckyPoint:        "LuckyPoint",
	FieldTypeLuckyNumber:       "LuckyNumber",
	FieldTypeLuckyToday:        "LuckyToday",
	FieldTypeLuckyExtra:        "LuckyExtra",
	FieldTypeCanSign:           "CanSign",
	FieldTypeDeviceList:        "DeviceList",
	FieldTypeDeviceInfo:        "DeviceInfo",
}

// 错误码到消息的映射
var ErrorMessages = map[uint32]string{
	ErrorCodeSuccess:       "Success",
	ErrorCodeInvalidFormat: "Invalid message format",
	ErrorCodeInvalidQQ:     "Invalid QQ number",
	ErrorCodeInvalidDevice: "Invalid device information",
	ErrorCodeUnauthorized:  "Unauthorized access",
	ErrorCodeTokenExpired:  "Token expired",
	ErrorCodeServerError:   "Internal server error",
	ErrorCodeDatabaseError: "Database error",
	ErrorCodeCacheError:    "Cache error",
}

// IsResponseType 检查是否为响应类型
func IsResponseType(msgType uint8) bool {
	return (msgType & 0x80) != 0
}

// GetResponseType 获取对应的响应类型
func GetResponseType(requestType uint8) uint8 {
	return requestType | 0x80
}

// GetRequestType 获取对应的请求类型
func GetRequestType(responseType uint8) uint8 {
	return responseType & 0x7F
}
