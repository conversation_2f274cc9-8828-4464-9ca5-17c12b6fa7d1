<?php
// +----------------------------------------------------------------------
// | Ingame应用路由配置
// +----------------------------------------------------------------------

use think\facade\Route;

// 角色查询主路由
Route::group('bs', function() {
    Route::group('character', function() {
        Route::rule('/profile', 'Character.CharacterInfo/profile');
        Route::rule('/profile_simple', 'Character.CharacterInfo/profile_simple');
        Route::rule('/profile_pcid', 'Character.CharacterInfo/profile_pcid');

        Route::get('/board', 'Character.Board/index');
        Route::get('/compare', 'Character.CharacterInfo/compare');
        Route::get('/search/info', 'Character.CharacterInfo/search');
        Route::get('/data/abilities.json', 'Character.CharacterInfo/abilities');
        Route::get('/data/abilities/pointseffects.json', 'Character.CharacterInfo/pointseffects');
    });

    Route::get('error/error', 'Character.Message/ErrorPage');
});

// API路由
Route::group('api', function() {
    Route::get('suggest/v1.0/user', 'Character.CharacterInfo/SuggestUser');
    Route::get('characters.json', 'Character.CharacterInfo/characters');

    Route::group('character', function() {
        Route::get('/jobs.json','Character.CharacterInfo/jobs'); 
        Route::get('/allowed.json','Character.CharacterInfo/allowed');
        Route::get('/info.json','Character.CharacterInfo/info');  
        Route::get('/info_ue4.json','Character.CharacterInfo/info_ue4'); 
        Route::get('/search.json','Character.CharacterInfo/search'); 
        Route::get('/abilities.json','Character.CharacterInfo/abilities'); 
        Route::get('/allowed.json','Character.CharacterInfo/allowed');  
    
        //由于新版不会传递服务器编号，不能直接提交给接口处理
        Route::get('/equipments.json','Character.CharacterInfo/equipments'); 
        Route::get('/abilities/pointseffects.json','Character.CharacterInfo/pointseffects');
            
        Route::group('like', function() {
            Route::rule('/info','Character.Like/info');      
            Route::rule('/vote','Character.Like/vote');    
            Route::rule('/cancel','Character.Like/cancel');    
            Route::rule('/accounts','Character.Like/accounts');  
            Route::rule('/delete','Character.Like/delete'); 
        });
    });
});

return [];