<?php

namespace app\common\service;

use think\facade\Cache;
use think\exception\Exception;

/**
 * Redis统一服务类
 * 统一管理Redis连接，避免重复代码
 */
class RedisService
{
    /**
     * 获取Redis原生连接实例
     * 用于需要使用Redis原生方法的场景
     * 
     * @return \Redis
     * @throws Exception
     */
    public static function getRedisHandler()
    {
        try {
            // 获取Redis缓存驱动实例
            $driver = Cache::store('redis');
            
            // 通过反射获取Redis原生连接
            $reflection = new \ReflectionClass($driver);
            $handlerProperty = $reflection->getProperty('handler');
            $handlerProperty->setAccessible(true);
            $handler = $handlerProperty->getValue($driver);
            
            if (!$handler instanceof \Redis) {
                throw new Exception('Redis连接获取失败');
            }
            
            return $handler;
            
        } catch (\Exception $e) {
            throw new Exception('Redis连接错误: ' . $e->getMessage());
        }
    }
    
    /**
     * 使用ThinkPHP缓存门面设置缓存
     * 
     * @param string $key 缓存键
     * @param mixed $value 缓存值
     * @param int $expire 过期时间（秒）
     * @return bool
     */
    public static function set(string $key, $value, int $expire = null): bool
    {
        return Cache::store('redis')->set($key, $value, $expire);
    }
    
    /**
     * 使用ThinkPHP缓存门面获取缓存
     * 
     * @param string $key 缓存键
     * @param mixed $default 默认值
     * @return mixed
     */
    public static function get(string $key, $default = null)
    {
        return Cache::store('redis')->get($key, $default);
    }
    
    /**
     * 使用ThinkPHP缓存门面删除缓存
     * 
     * @param string $key 缓存键
     * @return bool
     */
    public static function delete(string $key): bool
    {
        return Cache::store('redis')->delete($key);
    }
    
    /**
     * 使用ThinkPHP缓存门面检查缓存是否存在
     * 
     * @param string $key 缓存键
     * @return bool
     */
    public static function has(string $key): bool
    {
        return Cache::store('redis')->has($key);
    }
    
    /**
     * 缓存自增
     * 
     * @param string $key 缓存键
     * @param int $step 步长
     * @return int|false
     */
    public static function inc(string $key, int $step = 1)
    {
        try {
            $redis = self::getRedisHandler();
            return $redis->incrBy($key, $step);
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 缓存自减
     * 
     * @param string $key 缓存键
     * @param int $step 步长
     * @return int|false
     */
    public static function dec(string $key, int $step = 1)
    {
        try {
            $redis = self::getRedisHandler();
            return $redis->decrBy($key, $step);
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 设置键的过期时间
     * 
     * @param string $key 缓存键
     * @param int $seconds 过期时间（秒）
     * @return bool
     */
    public static function expire(string $key, int $seconds): bool
    {
        try {
            $redis = self::getRedisHandler();
            return $redis->expire($key, $seconds);
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 获取哈希表长度
     * 
     * @param string $key 哈希表键
     * @return int|false
     */
    public static function hLen(string $key)
    {
        try {
            $redis = self::getRedisHandler();
            return $redis->hLen($key);
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 获取哈希表字段值
     * 
     * @param string $key 哈希表键
     * @param string $field 字段名
     * @return string|false
     */
    public static function hGet(string $key, string $field)
    {
        try {
            $redis = self::getRedisHandler();
            return $redis->hGet($key, $field);
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 设置哈希表字段值
     * 
     * @param string $key 哈希表键
     * @param string $field 字段名
     * @param mixed $value 字段值
     * @return bool
     */
    public static function hSet(string $key, string $field, $value): bool
    {
        try {
            $redis = self::getRedisHandler();
            return $redis->hSet($key, $field, $value) !== false;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 删除哈希表字段
     * 
     * @param string $key 哈希表键
     * @param string $field 字段名
     * @return bool
     */
    public static function hDel(string $key, string $field): bool
    {
        try {
            $redis = self::getRedisHandler();
            return $redis->hDel($key, $field) > 0;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 获取所有哈希表字段和值
     * 
     * @param string $key 哈希表键
     * @return array|false
     */
    public static function hGetAll(string $key)
    {
        try {
            $redis = self::getRedisHandler();
            return $redis->hGetAll($key);
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 清空所有缓存
     * 
     * @return bool
     */
    public static function clear(): bool
    {
        return Cache::store('redis')->clear();
    }
}
