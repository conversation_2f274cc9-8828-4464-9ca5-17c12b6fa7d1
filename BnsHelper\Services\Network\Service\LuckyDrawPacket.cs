using Xylia.Preview.Data.Engine;

namespace Xylia.BnsHelper.Services.Network.Service;

/// <summary>
/// 签到抽奖包，包含抽奖请求和响应的处理逻辑
/// </summary>
internal class LuckyDrawPacket : BasePacket
{
    #region Response Fields
    /// <summary>
    /// 奖励消息
    /// </summary>
    public string RewardMessage { get; set; }

    /// <summary>
    /// 奖励数量
    /// </summary>
    public int RewardCount { get; set; }

    /// <summary>
    /// 更新后的权限级别（如果权限有变化）
    /// </summary>
    public byte? Permission { get; set; } = null;

    /// <summary>
    /// 更新后的权限过期时间（如果权限有变化）：-1=永久，0=无权限，>0=具体时间戳
    /// </summary>
    public long? PermissionExpiration { get; set; } = null;
    #endregion

    #region Methods
    protected override void ReadResponse(DataArchive reader)
    {
        RewardMessage = reader.ReadString() ?? "签到成功";
        RewardCount = reader.Read<int>();

        // 检查是否还有权限信息数据
        if (reader.Position < reader.Length)
        {
            Permission = reader.Read<byte>();
            PermissionExpiration = reader.Read<long>(); 
        }
    }
    #endregion
}
