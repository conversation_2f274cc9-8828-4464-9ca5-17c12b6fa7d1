﻿using System.Globalization;
using System.Windows.Data;
using System.Windows.Markup;
using Xylia.BnsHelper.Resources;

namespace Xylia.BnsHelper.Common.Converters;
public class StringConverter : MarkupExtension, IMultiValueConverter
{
	public override object ProvideValue(IServiceProvider serviceProvider) => this;

	public object? Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
	{
		var format = parameter.ToString();
		if (string.IsNullOrEmpty(format)) return null;

		return StringHelper.Get(format, values) ?? string.Format(format, values);
	}

	public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture) => throw new NotImplementedException();
}