﻿using CommunityToolkit.Mvvm.ComponentModel;
using Xylia.BnsHelper.Services;

namespace Xylia.BnsHelper.Models;
public partial class Schedule : ObservableObject
{
	public int Type { get; set; }
	public int Time { get; set; }
	public string Zone { get; set; }

	public DateTime DateTime => DateTimeOffset.FromUnixTimeSeconds(Time).DateTime.ToLocalTime();

	public override string ToString() => Type switch
	{
		0 => "世界首领",
		1 => "野外首领事件：浊魔灵",
		2 => "其他事件",
		_ => Type.ToString()
	};

	// 生成唯一标识符，用于保存用户设置
	public string UniqueId => $"{Type}_{Time}_{Zone}";

	// 标志位，用于避免在加载设置时触发保存
	private bool _isLoading = false;

	[ObservableProperty]
	private bool _notify15;

	[ObservableProperty]
	private bool _notify5 = true;

	[ObservableProperty]
	private bool _notify3 = true;

	partial void OnNotify15Changed(bool value)
	{
		if (!_isLoading)
		{
			SaveNotificationSettings();
			ScheduleNotificationService.Instance.UpdateScheduleNotifications();
		}
	}

	partial void OnNotify5Changed(bool value)
	{
		if (!_isLoading)
		{
			SaveNotificationSettings();
			ScheduleNotificationService.Instance.UpdateScheduleNotifications();
		}
	}

	partial void OnNotify3Changed(bool value)
	{
		if (!_isLoading)
		{
			SaveNotificationSettings();
			ScheduleNotificationService.Instance.UpdateScheduleNotifications();
		}
	}

	private void SaveNotificationSettings()
	{
		ScheduleNotificationService.Instance.SaveScheduleSettings(this);
	}

	public void LoadNotificationSettings()
	{
		var settings = ScheduleNotificationService.Instance.LoadScheduleSettings(UniqueId);
		if (settings != null)
		{
			_isLoading = true;
			try
			{
				// 使用生成的属性而不是直接访问字段
				Notify15 = settings.Notify15;
				Notify5 = settings.Notify5;
				Notify3 = settings.Notify3;
			}
			finally
			{
				_isLoading = false;
			}
		}
	}
}
