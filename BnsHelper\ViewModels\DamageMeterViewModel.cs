﻿using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections;
using System.Collections.ObjectModel;
using System.Data;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Threading;
using Vanara.PInvoke;
using Xylia.BnsHelper.Common.Extensions;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.BnsHelper.Models;
using Xylia.BnsHelper.Resources;
using Xylia.BnsHelper.Services;
using Xylia.BnsHelper.Services.Network;
using Xylia.BnsHelper.Services.Network.Plugin;
using Xylia.Preview.Data.Models.Sequence;
using static Xylia.BnsHelper.Services.Network.Plugin.InstantEffectNotification;
using static Xylia.Preview.Data.Models.ChatChannelOption;

namespace Xylia.BnsHelper.ViewModels;
internal partial class DamageMeterViewModel : ObservableObject, IDisposable
{
    #region Fields
    internal static nint gHwnd;
    internal static EventHandler? OnRefresh;

    private readonly DispatcherTimer Timer;
    private readonly PluginSession Service;
    private readonly DispatcherTimer _delayedRefreshTimer;
    private bool _refreshPending = false;

    [ObservableProperty] FPlayerCollection _players = [];

    // 保存真实的当前用户信息，用于重置时恢复
    private string? _realPlayerName;
    private JobSeq _realPlayerJob = JobSeq.PcMax;

    // Target selection
    private object? _selectedTarget;
    public object? SelectedTarget
    {
        get => _selectedTarget;
        set
        {
            if (SetProperty(ref _selectedTarget, value))
            {
                OnPropertyChanged(nameof(CurrentTargetName));
            }
        }
    }

    // 标记PauseAuto的原因：true=目标死亡，false=超时
    private bool _pauseAutoByTargetDeath = false;

    public string CurrentTargetName
    {
        get
        {
            if (SelectedTarget == null)
            {
                // 显示所有目标名称的集合
                if (Players.Default?.Targets != null && Players.Default.Targets.Count > 0)
                {
                    var targetNames = Players.Default.Targets
                        .OrderByDescending(t => t.Damage)
                        .Select(t => t.Name)
                        .ToArray();
                    return string.Join(", ", targetNames);
                }

                // 如果正在战斗中但还没有目标，显示更友好的提示
                if (Status == StatusType.Work && Players.Default != null && Players.Default.Count > 0)
                {
                    return "战斗中...";
                }

                return "无目标";
            }

            var nameProperty = SelectedTarget.GetType().GetProperty("Name");
            var targetName = nameProperty?.GetValue(SelectedTarget)?.ToString();

            // 如果是"全部目标"选项，也显示目标集合
            if (targetName == "全部目标")
            {
                if (Players.Default?.Targets != null && Players.Default.Targets.Count > 0)
                {
                    var targetNames = Players.Default.Targets
                        .OrderByDescending(t => t.Damage)
                        .Select(t => t.Name)
                        .ToArray();
                    return string.Join(", ", targetNames);
                }

                // 如果正在战斗中但还没有目标，显示更友好的提示
                if (Status == StatusType.Work && Players.Default != null && Players.Default.Count > 0)
                {
                    return "战斗中...";
                }

                return "无目标";
            }

            return targetName ?? "无目标";
        }
    }

    public IEnumerable<object> TargetOptions
    {
        get
        {
            var options = new List<object>();

            // Check if there are any targets
            if (Players.Default?.Targets != null && Players.Default.Targets.Count > 0)
            {
                // Add "All Targets" option with target names and total time
                string allTargetsName = "全部目标";
                double totalSeconds = Players.Default?.Seconds ?? 0;
                var targetNames = Players.Default.Targets
                    .OrderByDescending(t => t.Damage)
                    .Select(t => t.Name)
                    .ToArray();
                allTargetsName = $"{string.Join(", ", targetNames)} ({totalSeconds:F0}秒)";

                options.Add(new { Name = allTargetsName, Damage = Players.TotalDamage, DamageRate = 1.0 });

                // Add individual targets with their combat time
                foreach (var target in Players.Default.Targets.OrderByDescending(t => t.Damage))
                {
                    // Create a wrapper object that includes time information in the display name
                    var targetWithTime = new
                    {
                        Name = $"{target.Name} ({target.Seconds:F0}秒)",
                        OriginalName = target.Name,
                        Damage = target.Damage,
                        DamageRate = target.DamageRate,
                        Seconds = target.Seconds,
                        HitCount = target.HitCount,
                        CriticalHitCount = target.CriticalHitCount,
                        CriticalRate = target.CriticalRate,
                        MissCount = target.DodgeCount,
                        ParryCount = target.ParryCount
                    };
                    options.Add(targetWithTime);
                }
            }
            else
            {
                // No targets available, add a "无目标" option
                options.Add(new { Name = "无目标", Damage = 0L, DamageRate = 0.0 });
            }

            return options;
        }
    }


    #endregion

    #region Properties
    // Status
    public enum StatusType
    {
        Wait,
        Work,
        Pause,
        PauseAuto
    }

    StatusType _status;
    public StatusType Status
    {
        get => _status;
        set
        {
            if (_status == value) return;
            switch (value)
            {
                case StatusType.Wait:
                    Timer.Stop(); // 停止Timer，避免自动暂停逻辑触发
                    Reset();
                    break;
                case StatusType.Work:
                    {
                        // 根据暂停原因决定是否重置
                        if (_status == StatusType.PauseAuto)
                        {
                            if (!_pauseAutoByTargetDeath)
                            {
                                // 超时导致的暂停，重置数据开始新战斗
                                Reset();
                            }
                            // 目标死亡导致的暂停，保持数据继续统计
                        }
                        else if (_status == StatusType.Pause)
                        {
                            // 手动暂停后重新开始，重置数据开始新战斗
                            Reset();
                        }

                        // 读图过程中会出现多线程异常，因此此时不要发送消息
                        if (!Timer.IsEnabled) Timer.Start();
                        break;
                    }
                case StatusType.Pause:
                    {
                        Timer.Stop();
                        break;
                    }
                case StatusType.PauseAuto:
                    {
                        Timer.Stop();
                        break;
                    }
            }

            SetProperty(ref _status, value);
            OnPropertyChanged(nameof(IsWork));
            OnPropertyChanged(nameof(IsPause));
        }
    }

    public bool IsWork => Status == StatusType.Work || Status == StatusType.Wait;
    public bool IsPause => Status == StatusType.Pause || Status == StatusType.PauseAuto;

    int _page;
    public int Page
    {
        get => _page;
        set => SetProperty(ref _page, value);
    }

    // History
    int _zone = 0;
    public int Zone
    {
        get => _zone;
        set
        {
            var oldZone = _zone;
            Status = StatusType.Wait;
            SetProperty(ref _zone, value);

            // 如果离开了BOSS倒计时器支持的区域，清除所有倒计时器
            if (IsBossTimerZone(oldZone) && !IsBossTimerZone(value))
            {
                BossTimerService.Instance.ClearAllTimers();
                Debug.WriteLine($"[BossTimer] 离开支持区域 {oldZone} -> {value}，清除所有倒计时器");
            }
        }
    }

    /// <summary>
    /// 获取当前服务器ID（从EnterWorld包中的Player.world字段获取）
    /// </summary>
    private int GetCurrentServerId()
    {
        if (_currentServerId > 0)
        {
            return _currentServerId;
        }

        // 如果还没有从EnterWorld包中获取到服务器ID，返回默认值
        Debug.WriteLine("[BossTimer] 服务器ID尚未从EnterWorld包中获取，使用默认值");
        return 2000; // 默认使用原服务器组
    }

    // Current Channel for Boss Timer
    int _currentChannel = 0;
    int _pendingChannel = 0; // 待确认的频道号，用于处理频道切换失败的情况
    private DispatcherTimer? _channelConfirmTimer; // 频道切换确认超时定时器

    // Current Server ID for Boss Timer
    int _currentServerId = 0;

    public int CurrentChannel
    {
        get => _currentChannel;
        set
        {
            var oldChannel = _currentChannel;
            if (oldChannel != 0 && value != 0 && oldChannel != value)
            {
                // 切换频道时清除统计数据（排除初始化时的设置）
                Status = StatusType.Wait;
                Debug.WriteLine($"[DamageMeter] 切换频道 {oldChannel} -> {value}，清除统计数据");
            }
            SetProperty(ref _currentChannel, value);
        }
    }

    /// <summary>
    /// 设置待确认的频道号（用于处理频道切换失败的情况）
    /// </summary>
    /// <param name="channel">待确认的频道号</param>
    public void SetPendingChannel(int channel)
    {
        _pendingChannel = channel;
        Debug.WriteLine($"[BossTimer] 设置待确认频道: {channel}");

        // 启动确认超时定时器（1.5秒后自动确认，减少等待时间）
        StopChannelConfirmTimer();
        _channelConfirmTimer = new DispatcherTimer
        {
            Interval = TimeSpan.FromSeconds(1.5)
        };
        _channelConfirmTimer.Tick += (s, e) =>
        {
            Debug.WriteLine($"[BossTimer] 频道切换确认超时，自动确认频道: {_pendingChannel}");
            ConfirmChannelSwitch();
        };
        _channelConfirmTimer.Start();
    }

    /// <summary>
    /// 确认频道切换成功
    /// </summary>
    public void ConfirmChannelSwitch()
    {
        if (_pendingChannel > 0)
        {
            CurrentChannel = _pendingChannel;
            _pendingChannel = 0;
            StopChannelConfirmTimer();
            Debug.WriteLine($"[BossTimer] 确认频道切换成功: {CurrentChannel}");
        }
    }

    /// <summary>
    /// 取消频道切换（切换失败时调用）
    /// </summary>
    public void CancelChannelSwitch()
    {
        if (_pendingChannel > 0)
        {
            Debug.WriteLine($"[BossTimer] 频道切换失败，维持原频道: {CurrentChannel}，取消待确认频道: {_pendingChannel}");
            _pendingChannel = 0;
            StopChannelConfirmTimer();
        }
    }

    /// <summary>
    /// 停止频道确认定时器
    /// </summary>
    private void StopChannelConfirmTimer()
    {
        if (_channelConfirmTimer != null)
        {
            _channelConfirmTimer.Stop();
            _channelConfirmTimer = null;
        }
    }

    // Boss Timer Display
    public ObservableCollection<BossTimer> BossTimers => BossTimerService.Instance.ActiveTimers;

    public bool GroupMode
    {
        get => SettingHelper.Default.GroupMode;
        set
        {
            SettingHelper.Default.GroupMode = value;
            OnPropertyChanged(nameof(History));
        }
    }

    public IEnumerable History
    {
        get
        {
            var data = new List<HistoryData>();

            // 如果禁用了战斗记录日志，返回空列表
            if (SettingHelper.Default.DisableBattleLog)
            {
                return HistoryGroup.GroupBy<object>(data, o => HistoryType.Other);
            }

            var logs = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");

            // 检查logs目录是否存在
            if (!Directory.Exists(logs))
            {
                return HistoryGroup.GroupBy<object>(data, o => HistoryType.Other);
            }

            foreach (var file in new DirectoryInfo(logs).GetFiles("act*.json"))
            {
                try
                {
                    data.Add(HistoryData.Load(file));
                }
                catch
                {
                    Debug.Fail(file.FullName);
                }
            }

            return HistoryGroup.GroupBy<object>(data, o =>
            {
                if (GroupMode) return o.Zone;

                var date = (DateTime.Now - o.Time).TotalDays;
                if (date <= 1) return HistoryType.Today;
                else if (date <= 7) return HistoryType.Week;
                else return HistoryType.Other;
            });
        }
    }


    bool _isHitVisible = true;
    public bool IsHitTestVisible
    {
        get => _isHitVisible;
        set => SetProperty(ref _isHitVisible, value);
    }
    #endregion

    #region Methods
    public DamageMeterViewModel()
    {
        Players.Clear();
        Service = new PluginSession(OnReceived);
        Timer = new(new TimeSpan(TimeSpan.TicksPerMillisecond * 500), DispatcherPriority.Render, Refresh, Application.Current.Dispatcher);

        // 初始化延迟刷新定时器
        _delayedRefreshTimer = new DispatcherTimer(DispatcherPriority.Background, Application.Current.Dispatcher)
        {
            Interval = TimeSpan.FromMilliseconds(100) // 100ms延迟，减少UI刷新频率
        };
        _delayedRefreshTimer.Tick += OnDelayedRefresh;

        // Subscribe to target changes
        Players.PropertyChanged += (s, e) =>
        {
            if (e.PropertyName == nameof(Players.Default))
            {
                // 使用延迟刷新而不是立即刷新
                ScheduleDelayedRefresh();
            }
        };

        // Subscribe to boss timer updates
        BossTimerService.Instance.TimersUpdated += (s, e) =>
        {
            // 使用延迟刷新而不是立即刷新
            ScheduleDelayedRefresh();
        };
    }

    public void Initialize()
    {
        gHwnd = WindowHelper.GetGameWindow();
        if (gHwnd == IntPtr.Zero) throw new Exception(StringHelper.Get("Exception_NoRunningGame"));

        // send hello message
        User32.SendMessage(gHwnd, User32.WindowMessage.WM_APP, AppMessage.Register, Service.Port);
    }

    public void Reset()
    {
        Players.Save(_zone);
        Players.Clear();

        // 如果有真实的用户信息，恢复它
        if (!string.IsNullOrEmpty(_realPlayerName) && Players.Default != null)
        {
            Players.Default.Name = _realPlayerName;
            Players.Default.Job = _realPlayerJob;
        }

        // Reset target selection
        SelectedTarget = null;

        // 注意：不清除BOSS倒计时器，因为它们独立于战斗数据
        // BOSS倒计时器应该持续到自然过期或手动清除

        OnPropertyChanged(nameof(History));
        OnPropertyChanged(nameof(TargetOptions));
        OnPropertyChanged(nameof(CurrentTargetName));
    }

    private void Refresh(object? sender, EventArgs e)
    {
        // auto encounter
        if (Players.LastCombatTime.Ticks != 0 &&
            (DateTime.Now - Players.LastCombatTime).TotalSeconds > SettingHelper.Default.AutoResetEncounter)
        {
            Timer.Stop();
            _pauseAutoByTargetDeath = false; // 标记为超时导致的暂停
            Status = StatusType.PauseAuto;
        }

        // 使用延迟刷新机制，减少UI更新频率
        ScheduleDelayedRefresh();

        OnRefresh?.Invoke(sender, e);
    }

    /// <summary>
    /// 安排延迟刷新，避免频繁的UI更新
    /// </summary>
    private void ScheduleDelayedRefresh()
    {
        if (!_refreshPending)
        {
            _refreshPending = true;
            _delayedRefreshTimer.Stop();
            _delayedRefreshTimer.Start();
        }
    }

    /// <summary>
    /// 延迟刷新事件处理
    /// </summary>
    private void OnDelayedRefresh(object? sender, EventArgs e)
    {
        _delayedRefreshTimer.Stop();
        _refreshPending = false;

        // 批量更新UI，减少重绘次数
        OnPropertyChanged(nameof(TargetOptions));
        OnPropertyChanged(nameof(CurrentTargetName));
        OnPropertyChanged(nameof(BossTimers));

        // 通知Players刷新
        Players.OnPropertyChanged(nameof(Players.Default));
    }

    public void Dispose()
    {
        User32.SendMessage(gHwnd, User32.WindowMessage.WM_APP, AppMessage.UnRegister, nint.Zero);
        OnRefresh = null;

        // 停止并清理主定时器
        Timer?.Stop();

        Service.Dispose();
        BossTimerService.Instance.Dispose();

        // 清理延迟刷新定时器
        if (_delayedRefreshTimer != null)
        {
            _delayedRefreshTimer.Stop();
            _delayedRefreshTimer.Tick -= OnDelayedRefresh;
        }

        // 清理频道确认定时器
        StopChannelConfirmTimer();
    }

    [RelayCommand]
    void SwitchStauts()
    {
        Status = Status switch
        {
            StatusType.Work => StatusType.Pause,
            StatusType.PauseAuto => StatusType.Pause, // 自动暂停状态切换到手动暂停，保持数据不清除
            _ => StatusType.Wait,
        };
    }

    [RelayCommand]
    void SelectTarget(object target)
    {
        SelectedTarget = target;

        // Update the target filter in Players collection
        if (target != null)
        {
            string? targetName = null;

            // Try to get OriginalName first (for individual targets with time info)
            var originalNameProperty = target.GetType().GetProperty("OriginalName");
            if (originalNameProperty != null)
            {
                targetName = originalNameProperty.GetValue(target) as string;
            }
            else
            {
                // Fallback to Name property
                var nameProperty = target.GetType().GetProperty("Name");
                if (nameProperty != null)
                {
                    targetName = nameProperty.GetValue(target) as string;
                }
            }

            if (!string.IsNullOrEmpty(targetName))
            {
                // Check if it's a specific target (not the "all targets" or "no target" option)
                bool isAllTargets = targetName.Contains(",") || targetName == "全部目标" || targetName.Contains("全部目标");
                bool isNoTarget = targetName == "无目标";

                if (isAllTargets || isNoTarget)
                {
                    Players.TargetFilter = null;
                }
                else
                {
                    Players.TargetFilter = targetName;
                }
            }
            else
            {
                Players.TargetFilter = null;
            }
        }
        else
        {
            Players.TargetFilter = null;
        }

        // Refresh the view to apply the filter
        Players.OnPropertyChanged(nameof(Players.View));
    }

    private void OnReceived(object? sender, IPacket packet)
    {
        switch (packet)
        {
            case EnterWorld i:
                {
                    Zone = i.ZoneId;

                    if (i.Player != null && Players.Default != null)
                    {
                        // 保存真实的用户信息
                        _realPlayerName = i.Player.Name;
                        _realPlayerJob = i.Player.Job;

                        // 总是更新为最新的真实用户名和职业信息
                        Players.Default.Name = i.Player.Name;
                        Players.Default.Job = i.Player.Job;

                        // 获取服务器ID用于BOSS倒计时器
                        _currentServerId = i.Player.world;
                        Debug.WriteLine($"[BossTimer] 更新服务器ID: {_currentServerId} ({i.Player.World})");
                    }
                    break;
                }
            case EnterChannel i:
                {
                    // 设置待确认频道，等待后续消息确认是否切换成功
                    SetPendingChannel(i.Channel);
                    Debug.WriteLine($"[BossTimer] 尝试进入频道: {i.Channel}");
                    break;
                }
            case KeyInput i when i.Key == User32.VK.VK_F1: SwitchStauts(); break;
            case InstantNotification i: Parse(i.Text, i.Category); break;
            case InstantEffectNotification i: Players.Add(i); break;    // ItemSkill_SU_580_Damage	skip
        }
    }


    public void Parse(string? line, CategorySeq category = CategorySeq.CombatNormal) =>
    Application.Current.Dispatcher.Invoke(() =>
    {
        if (string.IsNullOrEmpty(line)) return;

        // BOSS倒计时器检测 - 在指定区域内才处理，忽略当前状态
        if (IsBossTimerZone(Zone)) CheckBossTimerMessages(line);

        // 战斗统计
        if (Status == StatusType.Pause) return;

        Status = StatusType.Work;
        Players.EndTime = DateTime.Now;

        Match m;

        // 1. 自己造成伤害 - 基础伤害
        m = regex_damage1.Match(line);
        if (m.Success)
        {
            var target = m.Groups["target"].Value;
            var skill = m.Groups["skill"].Value;
            var damage = long.Parse(m.Groups["damage"].Value, NumberStyles.AllowThousands);
            bool critical = m.Groups["critical"].Value == "暴击伤害";

            Players.Add(new FEffectEvent()
            {
                Time = DateTime.Now,
                Target = target,
                Skill = skill,
                Damage = damage,
                Type = critical ? SkillResultSeq.CriticalHit : SkillResultSeq.Hit,
            });
            return;
        }

        // 2. 受到伤害 - 被别人攻击 (需要区分队友伤害和自己受伤)
        m = regex_incomingdamage1.Match(line);
        if (m.Success)
        {
            var target = m.Groups["target"].Value;
            var caster = m.Groups["caster"].Value;
            var skill = m.Groups["skill"].Value;
            var damage = long.Parse(m.Groups["damage"].Value, NumberStyles.AllowThousands);
            bool critical = m.Groups["critical"].Value == "暴击伤害";

            // 队友造成的伤害，统计到输出
            Players.Add(new FEffectEvent()
            {
                Time = DateTime.Now,
                Caster = caster,
                Target = target,
                Skill = skill,
                Damage = damage,
                Type = critical ? SkillResultSeq.CriticalHit : SkillResultSeq.Hit,
            });
            return;
        }

        // 3. 受到伤害 - 简化版本 (需要区分队友伤害和自己受伤)
        m = regex_incomingdamage2.Match(line);
        if (m.Success)
        {
            var caster = m.Groups["caster"].Value;
            var skill = m.Groups["skill"].Value;
            var damage = long.Parse(m.Groups["damage"].Value, NumberStyles.AllowThousands);
            bool critical = m.Groups["critical"].Value == "暴击伤害";

            // 这个模式没有明确的target，假设是自己受到伤害
            // 但如果caster是队友，则可能是队友对其他目标造成的伤害
            // 队友造成的伤害，但目标不明确，暂时跳过以避免错误统计
            // TODO: 需要更精确的方式来确定目标
            return;
        }

        // 4. 效果伤害 - DOT等
        m = regex_debuff.Match(line);
        if (m.Success)
        {
            var target = m.GetValue("target");
            var effect = m.GetValue("effect") ?? StringHelper.Get("DamageMeterPanel_Effect_Unknown");
            var damage = long.Parse(m.Groups["damage"].Value, NumberStyles.AllowThousands);

            Players.Add(new FEffectEvent()
            {
                Time = DateTime.Now,
                Target = target,
                Skill = effect,
                Damage = damage,
            });
            return;
        }

        // 5. 效果伤害 - 由于效果
        // 这个效果其实是他人的，由于文本缺少施展者暂时不进行处理
        m = regex_debuff2.Match(line);
        if (false && m.Success)
        {
            var target = m.GetValue("target");
            var effect = m.GetValue("effect") ?? StringHelper.Get("DamageMeterPanel_Effect_Unknown");
            var damage = long.Parse(m.Groups["damage"].Value, NumberStyles.AllowThousands);

            Players.Add(new FEffectEvent()
            {
                Time = DateTime.Now,
                Target = target,
                Skill = effect,
                Damage = damage,
            });
            return;
        }

        // 6. 技能效果伤害
        m = regex_effect_damage.Match(line);
        if (m.Success)
        {
            var skill = m.Groups["skill"].Value;
            var effect = m.Groups["effect"].Value;
            var target = m.Groups["target"].Value;
            var damage = long.Parse(m.Groups["damage"].Value, NumberStyles.AllowThousands);

            Players.Add(new FEffectEvent()
            {
                Time = DateTime.Now,
                Target = target,
                Skill = $"{skill}({effect})",
                Damage = damage,
            });
            return;
        }

        // 7. 闪避 - 只统计防御数据，不添加目标
        m = regex_dodge1.Match(line);
        if (m.Success)
        {
            var target = m.Groups["target"].Value;
            var skill = m.Groups["skill"].Value;

            Players.Add(new FEffectEvent()
            {
                Time = DateTime.Now,
                Skill = skill,
                Type = SkillResultSeq.Dodge,
            });
            return;
        }

        // 8. 闪避 - 带施法者，只统计防御数据，不添加目标
        m = regex_dodge2.Match(line);
        if (m.Success)
        {
            var target = m.Groups["target"].Value;
            var caster = m.Groups["caster"].Value;
            var skill = m.Groups["skill"].Value;

            Players.Add(new FEffectEvent()
            {
                Time = DateTime.Now,
                Caster = caster,
                Skill = skill,
                Type = SkillResultSeq.Dodge
            });
            return;
        }

        // 9. 格挡 - 只统计防御数据，不添加目标
        m = regex_parry.Match(line);
        if (m.Success)
        {
            var target = m.Groups["target"].Value;
            var caster = m.Groups["caster"].Value;
            var skill = m.Groups["skill"].Value;

            Players.Add(new FEffectEvent()
            {
                Time = DateTime.Now,
                Caster = caster,
                Skill = skill,
                Type = SkillResultSeq.Parry
            });
            return;
        }

        // 10. 完全格挡 - 只统计防御数据，不添加目标
        m = regex_perfect_parry.Match(line);
        if (m.Success)
        {
            var target = m.Groups["target"].Value;
            var caster = m.Groups["caster"].Value;
            var skill = m.Groups["skill"].Value;

            Players.Add(new FEffectEvent()
            {
                Time = DateTime.Now,
                Caster = caster,
                Skill = skill,
                Type = SkillResultSeq.ParryPerfectParry
            });
            return;
        }

        // 11. 反击 - 只统计防御数据，不添加目标
        m = regex_counter.Match(line);
        if (m.Success)
        {
            var target = m.Groups["target"].Value;
            var caster = m.Groups["caster"].Value;
            var skill = m.Groups["skill"].Value;

            Players.Add(new FEffectEvent()
            {
                Time = DateTime.Now,
                Caster = caster,
                Skill = skill,
                Type = SkillResultSeq.Counter
            });
            return;
        }

        // 12. 抵抗 - 只统计防御数据，不添加目标
        m = regex_resist.Match(line);
        if (m.Success)
        {
            var target = m.Groups["target"].Value;
            var caster = m.Groups["caster"].Value;
            var skill = m.Groups["skill"].Value;

            Players.Add(new FEffectEvent()
            {
                Time = DateTime.Now,
                Caster = caster,
                Skill = skill,
                Type = SkillResultSeq.NotHit
            });
            return;
        }

        // 13. 反制 - 只统计防御数据，不添加目标
        m = regex_bounce.Match(line);
        if (m.Success)
        {
            var target = m.Groups["target"].Value;
            var caster = m.Groups["caster"].Value;
            var skill = m.Groups["skill"].Value;

            Players.Add(new FEffectEvent()
            {
                Time = DateTime.Now,
                Caster = caster,
                Skill = skill,
                Type = SkillResultSeq.Bounce
            });
            return;
        }

        // 14. 格挡但受伤 - 只有当受伤者是自己时才统计防御数据，不统计伤害
        m = regex_parry_damage.Match(line);
        if (m.Success)
        {
            var target = m.Groups["target"].Value;
            var caster = m.Groups["caster"].Value;
            var skill = m.Groups["skill"].Value;
            var damage = long.Parse(m.Groups["damage"].Value, NumberStyles.AllowThousands);
            bool critical = m.Groups["critical"].Value == "暴击伤害";

            Players.Add(new FEffectEvent()
            {
                Time = DateTime.Now,
                Caster = caster,
                Skill = skill,
                Damage = damage,
                Type = SkillResultSeq.Parry,
            });
            return;
        }

        // 15. 完全格挡但受伤 - 只有当受伤者是自己时才统计防御数据，不统计伤害
        m = regex_perfect_parry_damage.Match(line);
        if (m.Success)
        {
            var target = m.Groups["target"].Value;
            var caster = m.Groups["caster"].Value;
            var skill = m.Groups["skill"].Value;
            var damage = long.Parse(m.Groups["damage"].Value, NumberStyles.AllowThousands);
            bool critical = m.Groups["critical"].Value == "暴击伤害";

            Players.Add(new FEffectEvent()
            {
                Time = DateTime.Now,
                Caster = caster,
                Skill = skill,
                Damage = damage,
                Type = SkillResultSeq.Parry,
            });
            return;
        }

        // 16. 反击但受伤 - 只有当受伤者是自己时才统计防御数据，不统计伤害
        m = regex_counter_damage.Match(line);
        if (m.Success)
        {
            var target = m.Groups["target"].Value;
            var caster = m.Groups["caster"].Value;
            var skill = m.Groups["skill"].Value;
            var damage = long.Parse(m.Groups["damage"].Value, NumberStyles.AllowThousands);
            bool critical = m.Groups["critical"].Value == "暴击伤害";

            Players.Add(new FEffectEvent()
            {
                Time = DateTime.Now,
                Caster = caster,
                Skill = skill,
                Damage = damage,
                Type = SkillResultSeq.HitCriticalHitParry,
            });
            return;
        }

        // 20. 内力伤害
        m = regex_sp_damage.Match(line);
        if (m.Success)
        {
            var skill = m.Groups["skill"].Value;
            var target = m.Groups["target"].Value;
            var damage = long.Parse(m.Groups["damage"].Value, NumberStyles.AllowThousands);

            Players.Add(new FEffectEvent()
            {
                Time = DateTime.Now,
                Skill = skill,
                Damage = damage,
                Type = SkillResultSeq.Hit, // 内力伤害也算作命中
            });
            return;
        }

        // 21. 受到内力伤害 (不统计到输出伤害)
        m = regex_incoming_sp_damage.Match(line);
        if (m.Success) return;

        // 22. 效果内力伤害
        m = regex_effect_sp_damage.Match(line);
        if (m.Success)
        {
            var effect = m.Groups["effect"].Value;
            var target = m.Groups["target"].Value;
            var damage = long.Parse(m.Groups["damage"].Value, NumberStyles.AllowThousands);

            Players.Add(new FEffectEvent()
            {
                Time = DateTime.Now,
                Skill = effect,
                Damage = damage,
                Type = SkillResultSeq.Hit, // 内力伤害也算作命中
            });
            return;
        }

        // 23. 第三人称 - 其他人受到伤害
        m = regex_other_hit_damage.Match(line);
        if (m.Success)
        {
            var target = m.Groups["target"].Value;
            var caster = m.Groups["caster"].Value;
            var skill = m.Groups["skill"].Value;
            var damage = long.Parse(m.Groups["damage"].Value, NumberStyles.AllowThousands);
            bool critical = m.Groups["critical"].Value == "暴击伤害";

            Players.Add(new FEffectEvent()
            {
                Time = DateTime.Now,
                Caster = caster,
                Target = target,
                Skill = skill,
                Damage = -damage,
                Type = critical ? SkillResultSeq.CriticalHit : SkillResultSeq.Hit,
            });
            return;
        }

        // 24. 第三人称 - 格挡
        m = regex_other_parry.Match(line);
        if (m.Success)
        {
            var target = m.Groups["target"].Value;
            var caster = m.Groups["caster"].Value;
            var skill = m.Groups["skill"].Value;

            Players.Add(new FEffectEvent()
            {
                Time = DateTime.Now,
                Caster = caster,
                Target = target,
                Skill = skill,
                Type = SkillResultSeq.Parry
            });
            return;
        }

        // 25. 第三人称 - 完全格挡
        m = regex_other_perfect_parry.Match(line);
        if (m.Success)
        {
            var target = m.Groups["target"].Value;
            var caster = m.Groups["caster"].Value;
            var skill = m.Groups["skill"].Value;

            Players.Add(new FEffectEvent()
            {
                Time = DateTime.Now,
                Caster = caster,
                Target = target,
                Skill = skill,
                Type = SkillResultSeq.PerfectParry
            });
            return;
        }

        // 26. 第三人称 - 反击
        m = regex_other_counter.Match(line);
        if (m.Success)
        {
            var target = m.Groups["target"].Value;
            var caster = m.Groups["caster"].Value;
            var skill = m.Groups["skill"].Value;

            Players.Add(new FEffectEvent()
            {
                Time = DateTime.Now,
                Caster = caster,
                Target = target,
                Skill = skill,
                Type = SkillResultSeq.Counter
            });
            return;
        }

        // 27. 第三人称 - 闪避
        m = regex_other_dodge.Match(line);
        if (m.Success)
        {
            var target = m.Groups["target"].Value;
            var caster = m.Groups["caster"].Value;
            var skill = m.Groups["skill"].Value;

            Players.Add(new FEffectEvent()
            {
                Time = DateTime.Now,
                Caster = caster,
                Target = target,
                Skill = skill,
                Type = SkillResultSeq.Dodge
            });
            return;
        }

        // 28. 第三人称 - 抵抗（只统计自己的攻击被抵抗）
        m = regex_other_resist.Match(line);
        if (m.Success)
        {
            var target = m.Groups["target"].Value;
            var caster = m.Groups["caster"].Value;
            var skill = m.Groups["skill"].Value;


            Players.Add(new FEffectEvent()
            {
                Time = DateTime.Now,
                Caster = caster,
                Target = target,
                Skill = skill,
                Type = SkillResultSeq.NotHit
            });
            return;
        }

        // 29. 第三人称 - 反制
        m = regex_other_bounce.Match(line);
        if (m.Success)
        {
            var target = m.Groups["target"].Value;
            var caster = m.Groups["caster"].Value;
            var skill = m.Groups["skill"].Value;

            Players.Add(new FEffectEvent()
            {
                Time = DateTime.Now,
                Caster = caster,
                Target = target,
                Skill = skill,
                Type = SkillResultSeq.Bounce
            });
            return;
        }

        // 30. 第三人称 - 效果伤害
        m = regex_other_effect_damage.Match(line);
        if (m.Success)
        {
            var effect = m.Groups["effect"].Value;
            var target = m.Groups["target"].Value;
            var damage = long.Parse(m.Groups["damage"].Value, NumberStyles.AllowThousands);

            // 这个模式匹配的是"由于XXX效果，目标受到了伤害"
            // 由于无法从消息中确定效果的施法者，我们需要检查这个效果是否是自己造成的
            // 暂时跳过这种模式，避免统计到其他人的效果伤害
            // TODO: 需要更精确的方式来判断效果的归属
            return;
        }

        // 31. 死亡检测 - 目标死亡
        m = regex_death.Match(line);
        if (m.Success)
        {
            var target = m.Groups["target"].Value;
            CheckTargetDeath(target);
            return;
        }

        // 32. 濒死检测 - 目标濒死
        m = regex_exhaustion.Match(line);
        if (m.Success)
        {
            var target = m.Groups["target"].Value;
            CheckTargetDeath(target);
            return;
        }

        // 33. 效果死亡检测
        m = regex_effect_death.Match(line);
        if (m.Success)
        {
            var target = m.Groups["target"].Value;
            CheckTargetDeath(target);
            return;
        }

        // 34. 效果濒死检测
        m = regex_effect_exhaustion.Match(line);
        if (m.Success)
        {
            var target = m.Groups["target"].Value;
            CheckTargetDeath(target);
            return;
        }

        // 35. 第三人称死亡检测
        m = regex_other_death.Match(line);
        if (m.Success)
        {
            var target = m.Groups["target"].Value;
            CheckTargetDeath(target);
            return;
        }

        // 36. 第三人称濒死检测
        m = regex_other_exhaustion.Match(line);
        if (m.Success)
        {
            var target = m.Groups["target"].Value;
            CheckTargetDeath(target);
            return;
        }

        // 37. 第三人称效果死亡检测
        m = regex_other_effect_death.Match(line);
        if (m.Success)
        {
            var target = m.Groups["target"].Value;
            CheckTargetDeath(target);
            return;
        }

        // 38. 第三人称效果濒死检测
        m = regex_other_effect_exhaustion.Match(line);
        if (m.Success)
        {
            var target = m.Groups["target"].Value;
            CheckTargetDeath(target);
            return;
        }

        // not handled
        Debug.WriteLine(category + " " + line);
    });

    /// <summary>
    /// 检查目标死亡，如果所有正在统计的目标都死亡，则停止记录
    /// </summary>
    /// <param name="deadTarget">死亡的目标名称</param>
    private void CheckTargetDeath(string deadTarget)
    {
        if (Players.Default?.Targets == null || Players.Default.Targets.Count == 0)
            return;

        // 检查死亡的目标是否在我们的目标列表中
        var targetInList = Players.Default.Targets.FirstOrDefault(t => t.Name == deadTarget);
        if (targetInList == null)
            return; // 不是我们正在统计的目标

        // 标记目标为死亡
        targetInList.IsDead = true;
        Debug.WriteLine($"目标 {deadTarget} 已死亡");

        // 获取所有有伤害且未死亡的目标（正在统计的活跃目标）
        var aliveTargets = Players.Default.Targets.Where(t => t.Damage > 0 && !t.IsDead).ToList();

        // 如果所有有伤害的目标都死亡了，则停止记录
        if (aliveTargets.Count == 0)
        {
            // 所有正在统计的目标都死亡了，停止记录
            Timer.Stop();
            _pauseAutoByTargetDeath = true; // 标记为目标死亡导致的暂停
            Status = StatusType.PauseAuto;

            Debug.WriteLine($"所有目标都已死亡，自动停止伤害统计");

            // 更新目标选项显示
            OnPropertyChanged(nameof(TargetOptions));
            OnPropertyChanged(nameof(CurrentTargetName));
        }
        else
        {
            // 还有其他目标存活，继续统计
            Debug.WriteLine($"目标 {deadTarget} 死亡，还有 {aliveTargets.Count} 个目标存活，继续统计");

            // 更新目标选项显示
            OnPropertyChanged(nameof(TargetOptions));
            OnPropertyChanged(nameof(CurrentTargetName));
        }
    }

    #region Boss Timer Methods
    /// <summary>
    /// 检查是否为BOSS倒计时器支持的区域
    /// </summary>
    /// <param name="zoneId">区域ID</param>
    /// <returns>是否支持BOSS倒计时器</returns>
    private bool IsBossTimerZone(int zoneId)
    {
        // 原支持区域
        var originalZones = new[] { 2000, 2300, 2440, 3010, 4000, 4250, 4302, 4400, 3086 };

        // 新服务器组特殊区域
        var newServerSpecialZones = new[] { 5200, 5295, 5500 };

        return originalZones.Contains(zoneId) || newServerSpecialZones.Contains(zoneId);
    }

    /// <summary>
    /// 判断是否为实际的战斗消息
    /// </summary>
    /// <param name="message">消息内容</param>
    /// <returns>是否为战斗消息</returns>
    private bool IsCombatMessage(string message)
    {
        // 排除非战斗消息
        if (string.IsNullOrEmpty(message)) return false;

        // 排除频道切换相关消息
        if (message.Contains("无法切换频道") || message.Contains("无法移动到该频道") ||
            message.Contains("频道切换失败") || message.Contains("不能切换到该频道") ||
            message.Contains("进入") && message.Contains("频道") ||
            message.Contains("频道切换成功") || message.Contains("成功进入频道"))
        {
            return false;
        }

        // 排除系统消息
        if (message.Contains("系统") || message.Contains("公告") ||
            message.Contains("维护") || message.Contains("更新"))
        {
            return false;
        }

        // 检查是否包含战斗相关关键词
        return message.Contains("命中") || message.Contains("造成") ||
               message.Contains("受到") || message.Contains("伤害") ||
               message.Contains("暴击") || message.Contains("格挡") ||
               message.Contains("闪避") || message.Contains("反击") ||
               message.Contains("消灭了") || message.Contains("击败") ||
               message.Contains("治疗") || message.Contains("恢复");
    }

    /// <summary>
    /// 检查BOSS倒计时器相关消息
    /// </summary>
    /// <param name="message">消息内容</param>
    private void CheckBossTimerMessages(string message)
    {
        try
        {
            // 检查频道切换失败消息
            if (message.Contains("无法切换频道"))
            {
                CancelChannelSwitch();
                Debug.WriteLine($"[BossTimer] 检测到频道切换失败消息: {message}");
                return;
            }

            // 检查频道切换成功的相关消息（如果有待确认的频道）
            if (_pendingChannel > 0)
            {
                // 检查是否有表示成功进入频道的消息
                // 根据实际游戏消息格式检测成功标识
                if (message.Contains($"进入{_pendingChannel}频道") ||
                    message.Contains($"频道{_pendingChannel}") ||
                    message.Contains("频道切换成功") ||
                    message.Contains("成功进入频道"))
                {
                    ConfirmChannelSwitch();
                    return;
                }

                // 如果检测到实际的战斗消息，说明已经在新频道中，确认切换成功
                if (IsCombatMessage(message))
                {
                    ConfirmChannelSwitch();
                    return;
                }
            }

            // 正则表达式：消灭了<arg p="2:npc.name2"/><eul/>，消耗了<arg p="3:integer"/> 点洪门庇护
            var bossKillRegex = new Regex(@"消灭了(.+?)，消耗了(\d+)\s*点洪门庇护", RegexOptions.Compiled);
            var bossKillMatch = bossKillRegex.Match(message);

            if (bossKillMatch.Success)
            {
                var bossName = bossKillMatch.Groups[1].Value.Trim();
                var isMutant = bossName.Contains("变异体");
                var serverId = GetCurrentServerId();

                if (CurrentChannel > 0)
                {
                    // 使用新的重载方法，根据服务器ID和区域ID自动确定类型
                    BossTimerService.Instance.AddOrUpdateTimer(CurrentChannel, serverId, Zone, isMutant);
                    Debug.WriteLine($"[BossTimer] 检测到BOSS击杀: {bossName}, 频道: {CurrentChannel}, 服务器: {serverId}, 区域: {Zone}, 变异体: {isMutant}");
                }
                else
                {
                    Debug.WriteLine($"[BossTimer] BOSS击杀检测到但频道信息无效: {CurrentChannel}");
                }
                return;
            }

            // 检查不祥力量消息：<image imagesetpath="00027918.Portrait_Alert"/>不祥的力量开始笼罩。
            if (message.Contains("不祥的力量开始笼罩"))
            {
                if (CurrentChannel > 0)
                {
                    BossTimerService.Instance.AddOrUpdateTimer(CurrentChannel, BossTimerType.ZNCS_MutantAlarm);
                    Debug.WriteLine($"[BossTimer] 检测到不祥力量: 频道: {CurrentChannel}");
                }
                else
                {
                    Debug.WriteLine($"[BossTimer] 不祥力量检测到但频道信息无效: {CurrentChannel}");
                }
                return;
            }

            // 调试：记录所有在BOSS区域的消息，帮助调试（仅在Debug模式下）
            Debug.WriteLine($"[BossTimer] 区域{Zone}频道{CurrentChannel}消息: {message}");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[BossTimer] 消息处理异常: {ex.Message}");
        }
    }

#if DEBUG
    /// <summary>
    /// 测试方法：手动添加BOSS倒计时器（用于调试）
    /// </summary>
    public void TestAddBossTimer(BossTimerType type)
    {
        if (CurrentChannel == 0)
        {
            CurrentChannel = 1; // 默认频道1用于测试
        }
        BossTimerService.Instance.AddOrUpdateTimer(CurrentChannel, type);
        Debug.WriteLine($"[BossTimer] 测试添加倒计时器: 频道{CurrentChannel}, 类型{type}");
    }

    /// <summary>
    /// 测试方法：模拟BOSS击杀消息
    /// </summary>
    public void TestBossKillMessage(string bossName = "血魔君主", int channel = 1)
    {
        CurrentChannel = channel;
        Zone = 2000; // 设置为支持的区域
        var testMessage = $"消灭了{bossName}，消耗了1点洪门庇护";
        Parse(testMessage);
        Debug.WriteLine($"[BossTimer] 测试BOSS击杀消息: {testMessage}");
    }
#endif

#if DEBUG
    /// <summary>
    /// 测试方法：模拟不祥力量消息
    /// </summary>
    public void TestOminousPowerMessage(int channel = 1)
    {
        CurrentChannel = channel;
        Zone = 2000; // 设置为支持的区域
        var testMessage = "不祥的力量开始笼罩。";
        Parse(testMessage);
        Debug.WriteLine($"[BossTimer] 测试不祥力量消息: {testMessage}");
    }
#endif

    /// <summary>
    /// 手动清除单个BOSS倒计时器
    /// </summary>
    /// <param name="channel">要清除的频道号</param>
    public void ClearSingleBossTimer(int channel)
    {
        BossTimerService.Instance.RemoveTimer(channel);
        Debug.WriteLine($"[BossTimer] 手动清除频道{channel}的倒计时器");
    }

    /// <summary>
    /// 手动清除所有BOSS倒计时器
    /// </summary>
    public void ClearBossTimers()
    {
        BossTimerService.Instance.ClearAllTimers();
        Debug.WriteLine("[BossTimer] 手动清除所有倒计时器");
    }


    #endregion
    #endregion
}
