package service

import (
	"fmt"
	"log"
	"time"
	"udp-server/server/internal/model"
	"udp-server/server/pkg/cache"

	"gorm.io/gorm"
)

// PermissionService 权限服务
type PermissionService struct {
	db    *gorm.DB
	cache cache.Cache
}

// NewPermissionService 创建新的权限服务
func NewPermissionService(db *gorm.DB, cache cache.Cache) *PermissionService {
	return &PermissionService{
		db:    db,
		cache: cache,
	}
}

// CDKey信息结构
type CDKeyInfo struct {
	CDKey    string     `gorm:"column:cdkey"`
	Type     string     `gorm:"column:type"`
	TimeType string     `gorm:"column:timeType"`
	Fixed    *time.Time `gorm:"column:fixed"`
	Duration int        `gorm:"column:duration"`
}

// 获取用户权限过期时间
// 参数：uid - 用户ID，permissionType - 权限类型（如 'client'）
// 返回值：过期时间戳（0=无权限，-1=永久权限，>0=具体过期时间）
func (s *PermissionService) GetExpiration(uid uint64, permissionType string) (int64, error) {
	// 首先检查用户数据库中的权限字段
	var user model.User
	if err := s.db.Where("uid = ?", uid).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			log.Printf("[DEBUG] 用户不存在: UID=%d", uid)
			return 0, nil
		}
		log.Printf("[ERROR] 查询用户信息失败: UID=%d, Error=%v", uid, err)
		return 0, err
	}

	// 如果用户权限字段大于0，直接返回永久权限
	if user.Permission > 0 {
		log.Printf("[DEBUG] 用户有数据库权限，返回永久权限: UID=%d, Permission=%d", uid, user.Permission)
		return -1, nil
	}

	// 如果用户权限字段等于0，优先计算CDKey权限，然后考虑活动权限
	log.Printf("[DEBUG] 用户无数据库权限，开始动态计算: UID=%d", uid)

	// 使用哈希表存储用户权限过期时间
	hashKey := fmt.Sprintf("user_expir_%s", permissionType)
	fieldKey := fmt.Sprintf("%d", uid)

	// 先检查缓存
	var cachedTime int64
	if err := s.getFromHash(hashKey, fieldKey, &cachedTime); err == nil {
		log.Printf("[DEBUG] 从哈希表缓存获取权限过期时间: UID=%d, Type=%s, Time=%d", uid, permissionType, cachedTime)
		return cachedTime, nil
	}

	log.Printf("[DEBUG] 哈希表缓存未命中，开始计算权限过期时间: UID=%d, Type=%s", uid, permissionType)

	// 缓存未命中，从数据库计算
	var calculatedTime int64 = 0
	var logs []model.UserLog
	if err := s.db.Where("uid = ? AND type = ?", uid, "cdkey").Order("id").Find(&logs).Error; err != nil {
		log.Printf("[ERROR] 查询用户CDKey日志失败: UID=%d, Error=%v", uid, err)
		return 0, err
	}

	log.Printf("[DEBUG] 找到用户CDKey记录数量: UID=%d, Count=%d", uid, len(logs))

	// 遍历每条CDKey记录
	for _, logRecord := range logs {
		// 计算起始时间戳，支持多种时间格式
		startTime, err := s.parseTimeString(logRecord.Time)
		if err != nil {
			log.Printf("[WARN] 解析时间失败: %s, Error=%v", logRecord.Time, err)
			continue
		}
		startTimestamp := startTime.Unix()

		// 如果当前计算的时间大于起始时间，则使用当前时间作为起始时间
		if calculatedTime > startTimestamp {
			startTimestamp = calculatedTime
		}

		// 查询CDKey配置信息
		var cdkeyInfo CDKeyInfo
		query := `
			SELECT c.cdkey, c.type, t.timeType, t.fixed, t.duration 
			FROM bns_cdkey c 
			JOIN bns_cdkey_customize t ON t.cdkey = c.cdkey 
			WHERE c.type = ? AND c.cdkey = ?
		`

		if err := s.db.Raw(query, permissionType, logRecord.Extra).Scan(&cdkeyInfo).Error; err != nil {
			log.Printf("[WARN] 查询CDKey配置失败: CDKey=%s, Error=%v", logRecord.Extra, err)
			continue
		}

		log.Printf("[DEBUG] CDKey配置: CDKey=%s, TimeType=%s, Duration=%d",
			cdkeyInfo.CDKey, cdkeyInfo.TimeType, cdkeyInfo.Duration)

		// 根据时间类型计算过期时间
		switch cdkeyInfo.TimeType {
		case "duration":
			// 持续时间类型：起始时间 + 天数
			newTime := startTimestamp + int64(cdkeyInfo.Duration*24*3600)
			if newTime > calculatedTime {
				calculatedTime = newTime
			}

		case "fixed":
			// 固定时间类型
			if cdkeyInfo.Fixed == nil {
				// 固定时间为空，表示永久权限
				calculatedTime = -1
				log.Printf("[DEBUG] Fixed时间为空，设置为永久权限: CDKey=%s", cdkeyInfo.CDKey)
				break // 跳出循环，永久权限优先级最高
			} else {
				// 取最大的固定时间
				fixedTime := cdkeyInfo.Fixed.Unix()
				if fixedTime > calculatedTime {
					calculatedTime = fixedTime
				}
			}
		}
	}

	log.Printf("[DEBUG] CDKey权限计算结果: UID=%d, Type=%s, Time=%d", uid, permissionType, calculatedTime)

	// 如果没有CDKey权限且在活动期间，使用活动权限
	if calculatedTime == 0 && s.isInFreeTrialPeriod() {
		// 活动期间，返回活动结束时间
		endTime := time.Date(2025, 7, 10, 23, 59, 59, 0, time.Local)
		calculatedTime = endTime.Unix()
		log.Printf("[DEBUG] 无CDKey权限但在活动期间，返回活动结束时间: UID=%d, EndTime=%d", uid, calculatedTime)
	}

	log.Printf("[DEBUG] 最终权限过期时间: UID=%d, Type=%s, Time=%d", uid, permissionType, calculatedTime)

	// 根据权限状态设置不同的缓存时间
	var cacheDuration time.Duration
	if calculatedTime == -1 {
		// 永久权限，缓存24小时
		cacheDuration = 24 * time.Hour
	} else if calculatedTime == 0 {
		// 无权限，缓存15天（避免频繁查询，CDKey插入时会清理缓存）
		cacheDuration = 15 * 24 * time.Hour
	} else {
		// 有期限权限，缓存15天（CDKey插入时会清理缓存）
		cacheDuration = 15 * 24 * time.Hour
	}

	// 将结果存入哈希表缓存
	if err := s.setToHash(hashKey, fieldKey, calculatedTime, cacheDuration); err != nil {
		log.Printf("[WARN] 缓存权限过期时间到哈希表失败: %v", err)
	} else {
		log.Printf("[DEBUG] 权限过期时间已缓存到哈希表: UID=%d, Type=%s, Duration=%v", uid, permissionType, cacheDuration)
	}

	return calculatedTime, nil
}

// GetUserPermissionExpiration 获取用户权限过期时间
// 参数：uid - 用户ID，permissionType - 权限类型（如 'client'）
// 返回值：过期时间戳（0=无权限，-1=永久权限，>0=具体过期时间）
func (s *PermissionService) GetUserPermissionExpiration(uid uint64, permissionType string) (int64, error) {
	// 首先检查用户数据库中的权限字段
	var user model.User
	if err := s.db.Where("uid = ?", uid).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			log.Printf("[DEBUG] 用户不存在: UID=%d", uid)
			return 0, nil
		}
		log.Printf("[ERROR] 查询用户信息失败: UID=%d, Error=%v", uid, err)
		return 0, err
	}

	// 如果用户权限字段大于0，直接返回永久权限
	if user.Permission > 0 {
		log.Printf("[DEBUG] 用户有数据库权限，返回永久权限: UID=%d, Permission=%d", uid, user.Permission)
		return -1, nil
	}

	// 如果用户权限字段等于0，通过GetExpiration方法计算动态权限
	log.Printf("[DEBUG] 用户无数据库权限，通过动态计算获取权限: UID=%d", uid)
	return s.GetExpiration(uid, permissionType)
}

// 获取用户权限级别
func (s *PermissionService) GetPermissionLevel(uid uint64, permissionType string) (int, error) {
	// 检查是否在活动期间（6月25日-7月10日）
	if s.isInFreeTrialPeriod() {
		log.Printf("[DEBUG] 活动期间，为用户提供免费权限: UID=%d", uid)
		return 1, nil
	}

	expirationTime, err := s.GetExpiration(uid, permissionType)
	if err != nil {
		return 0, err
	}

	// 如果过期时间比当前小，则没有权限
	now := time.Now().Unix()
	if (expirationTime <= now) return 0, nil
	else return 1, nil;
}

// isInFreeTrialPeriod 检查是否在免费体验期间
// 活动时间：2025年6月25日 - 2025年7月10日（15天免费体验）
func (s *PermissionService) isInFreeTrialPeriod() bool {
	now := time.Now()

	// 活动开始时间：2025年6月25日 00:00:00
	startTime := time.Date(2025, 6, 25, 0, 0, 0, 0, time.Local)

	// 活动结束时间：2025年7月10日 23:59:59（15天免费体验）
	endTime := time.Date(2025, 7, 10, 23, 59, 59, 0, time.Local)

	return now.After(startTime) && now.Before(endTime)
}

// GetActivityInfoForUser 获取特定用户的活动信息
func (s *PermissionService) GetActivityInfoForUser(userPermission uint8) map[string]interface{} {
	now := time.Now()

	// 活动开始时间：2025年6月25日 00:00:00
	startTime := time.Date(2025, 6, 25, 0, 0, 0, 0, time.Local)

	// 活动结束时间：2025年7月10日 23:59:59（15天免费体验）
	endTime := time.Date(2025, 7, 10, 23, 59, 59, 0, time.Local)

	// 签到权限恢复时间：2025年7月10日 00:00:00
	signInResumeTime := time.Date(2025, 7, 10, 0, 0, 0, 0, time.Local)

	isActive := now.After(startTime) && now.Before(endTime)

	result := map[string]interface{}{
		"is_active":           isActive,
		"start_time":          startTime.Unix(),
		"end_time":            endTime.Unix(),
		"sign_in_resume_time": signInResumeTime.Unix(),
		"title":               "战斗统计功能限时免费体验",
		"description":         "自6月25日起所有账号限时免费体验15日，7月10日起恢复连续签到3天获得3天的方式",
	}

	// 高级用户和会员用户（userPermission > 0）不显示活动剩余时间
	if userPermission > 0 {
		result["message"] = "您拥有永久权限"
		return result
	}

	if isActive {
		// 计算剩余时间（仅对普通用户）
		remaining := endTime.Sub(now)
		result["remaining_days"] = int(remaining.Hours() / 24)
		result["remaining_hours"] = int(remaining.Hours()) % 24
		result["message"] = fmt.Sprintf("免费体验还剩 %d 天 %d 小时",
			int(remaining.Hours()/24), int(remaining.Hours())%24)
	} else if now.Before(startTime) {
		result["message"] = "活动尚未开始"
	} else {
		result["message"] = "活动已结束，已恢复签到获得权限方式"
	}

	return result
}

// CheckDeviceSignInStatus 检查设备是否已签到
// 参数：deviceFingerprint - 设备指纹，activityID - 活动ID
// 返回值：是否已签到
func (s *PermissionService) CheckDeviceSignInStatus(deviceFingerprint string, activityID uint) (bool, error) {
	cacheKey := fmt.Sprintf("device_signin_%d_%s", activityID, deviceFingerprint)

	var lastSignTime int64
	err := s.cache.Get(cacheKey, &lastSignTime)
	if err != nil {
		// 缓存未命中，表示未签到
		log.Printf("[DEBUG] 设备签到状态缓存未命中: DeviceFingerprint=%s, ActivityID=%d", deviceFingerprint, activityID)
		return false, nil
	}

	lastTime := time.Unix(lastSignTime, 0)
	isToday := s.isToday(lastTime)

	log.Printf("[DEBUG] 设备签到状态检查: DeviceFingerprint=%s, ActivityID=%d, LastSignTime=%d, IsToday=%v",
		deviceFingerprint, activityID, lastSignTime, isToday)

	return isToday, nil
}

// SetDeviceSignInStatus 设置设备签到状态
// 参数：deviceFingerprint - 设备指纹，activityID - 活动ID
func (s *PermissionService) SetDeviceSignInStatus(deviceFingerprint string, activityID uint) error {
	cacheKey := fmt.Sprintf("device_signin_%d_%s", activityID, deviceFingerprint)
	now := time.Now().Unix()

	// 缓存到明天凌晨过期
	tomorrow := time.Now().AddDate(0, 0, 1)
	tomorrowMidnight := time.Date(tomorrow.Year(), tomorrow.Month(), tomorrow.Day(), 0, 0, 0, 0, tomorrow.Location())
	cacheDuration := time.Until(tomorrowMidnight)

	if err := s.cache.Set(cacheKey, now, cacheDuration); err != nil {
		log.Printf("[ERROR] 设置设备签到状态失败: DeviceFingerprint=%s, ActivityID=%d, Error=%v", deviceFingerprint, activityID, err)
		return err
	}

	log.Printf("[DEBUG] 设备签到状态已设置: DeviceFingerprint=%s, ActivityID=%d, CacheDuration=%v",
		deviceFingerprint, activityID, cacheDuration)

	return nil
}

// ClearDeviceSignInStatus 清除设备签到状态（用于测试或管理）
func (s *PermissionService) ClearDeviceSignInStatus(deviceFingerprint string, activityID uint) error {
	cacheKey := fmt.Sprintf("device_signin_%d_%s", activityID, deviceFingerprint)

	// 删除缓存键
	if err := s.cache.Delete(cacheKey); err != nil {
		log.Printf("[ERROR] 清除设备签到状态失败: DeviceFingerprint=%s, ActivityID=%d, Error=%v", deviceFingerprint, activityID, err)
		return err
	}

	log.Printf("[DEBUG] 设备签到状态已清除: DeviceFingerprint=%s, ActivityID=%d", deviceFingerprint, activityID)
	return nil
}

// isToday 检查时间是否为今天
func (s *PermissionService) isToday(t time.Time) bool {
	now := time.Now()
	return t.Year() == now.Year() && t.YearDay() == now.YearDay()
}

// ClearUserPermissionCache 清除用户权限缓存（CDKey激活时调用）
// 参数：uid - 用户ID，permissionType - 权限类型（如 'client'）
func (s *PermissionService) ClearUserPermissionCache(uid uint64, permissionType string) error {
	hashKey := fmt.Sprintf("user_expir_%s", permissionType)
	fieldKey := fmt.Sprintf("%d", uid)

	if err := s.deleteFromHash(hashKey, fieldKey); err != nil {
		log.Printf("[ERROR] 清除用户权限哈希表缓存失败: UID=%d, Type=%s, Error=%v", uid, permissionType, err)
		return err
	}

	log.Printf("[DEBUG] 用户权限哈希表缓存已清除: UID=%d, Type=%s", uid, permissionType)
	return nil
}

// getFromHash 从哈希表获取值
func (s *PermissionService) getFromHash(hashKey, fieldKey string, value interface{}) error {
	// 由于当前的cache接口不支持哈希操作，我们使用组合键的方式模拟
	compositeKey := fmt.Sprintf("%s:%s", hashKey, fieldKey)
	return s.cache.Get(compositeKey, value)
}

// setToHash 设置值到哈希表
func (s *PermissionService) setToHash(hashKey, fieldKey string, value interface{}, expiration time.Duration) error {
	// 由于当前的cache接口不支持哈希操作，我们使用组合键的方式模拟
	compositeKey := fmt.Sprintf("%s:%s", hashKey, fieldKey)
	return s.cache.Set(compositeKey, value, expiration)
}

// deleteFromHash 从哈希表删除字段
func (s *PermissionService) deleteFromHash(hashKey, fieldKey string) error {
	// 由于当前的cache接口不支持哈希操作，我们使用组合键的方式模拟
	compositeKey := fmt.Sprintf("%s:%s", hashKey, fieldKey)
	return s.cache.Delete(compositeKey)
}

// parseTimeString 解析时间字符串，支持多种格式
func (s *PermissionService) parseTimeString(timeStr string) (time.Time, error) {
	// 支持的时间格式列表
	timeFormats := []string{
		"2006-01-02T15:04:05Z07:00", // ISO 8601 with timezone: 2025-02-16T22:49:22+08:00
		"2006-01-02T15:04:05Z",      // ISO 8601 UTC: 2025-02-16T22:49:22Z
		"2006-01-02T15:04:05",       // ISO 8601 without timezone: 2025-02-16T22:49:22
		"2006-01-02 15:04:05",       // MySQL format: 2025-02-16 22:49:22
		"2006-01-02",                // Date only: 2025-02-16
		time.RFC3339,                // RFC3339: 2025-02-16T22:49:22Z
		time.RFC3339Nano,            // RFC3339 with nanoseconds
	}

	// 尝试每种格式
	for _, format := range timeFormats {
		if t, err := time.Parse(format, timeStr); err == nil {
			return t, nil
		}
	}

	// 如果所有格式都失败，返回错误
	return time.Time{}, fmt.Errorf("无法解析时间格式: %s", timeStr)
}
