﻿using Xylia.BnsHelper.Models;
using Xylia.Preview.Data.Engine;

namespace Xylia.BnsHelper.Services.Network.Plugin;
internal class EnterWorld : IPacket
{
	public int ZoneId = 0;
	public Creature? Player;

	#region Methods
	public DataArchiveWriter Create() => new DataArchiveWriter(); // 响应包返回空Writer

	public void Read(DataArchive reader)
	{
		ZoneId = reader.Read<int>();
		Player = reader.ReadByte() == 1 ? new Creature(reader) : null;
	}
	#endregion
}