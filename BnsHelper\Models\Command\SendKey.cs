﻿using System.Threading;
using Vanara.PInvoke;

namespace Xylia.BnsHelper.Models;
internal class SendKey(User32.VK key, int delay = 50) : BaseCommand
{
	public User32.VK Key { get; set; } = key;

	public int Delay { get; set; } = delay;

	public override int Type => 1;

	public override void Execute()
	{
		Execute(hwnd, Key, Delay);
	}

	public static void Execute(nint hwnd, User32.VK key, int delay)
	{
		User32.SendMessage(hwnd, User32.WindowMessage.WM_SYSKEYDOWN, key);
		Thread.Sleep(delay);
		User32.SendMessage(hwnd, User32.WindowMessage.WM_SYSKEYUP, key);
		Thread.Sleep(10);
	}
}