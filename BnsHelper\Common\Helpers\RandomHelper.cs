﻿namespace Xylia.BnsHelper.Common.Helpers;
internal static class RandomHelper
{
	static readonly Random rnd = new Random();

	/// <summary>
	/// 获取抽奖结果
	/// </summary>
	/// <param name="prob">各物品的抽中概率</param>
	/// <returns>返回抽中的物品所在数组的位置</returns>
	public static int Get(float[] prob)
	{
		int result = 0;
		int n = (int)(prob.Sum() * 1000);         
		float x = (float)rnd.Next(0, n) / 1000;    

		for (int i = 0; i < prob.Length; i++)
		{
			float pre = prob.Take(i).Sum();      
			float next = prob.Take(i + 1).Sum();   
			if (x >= pre && x < next)            
			{
				result = i;
				break;
			}
		}
		return result;
	}
}
