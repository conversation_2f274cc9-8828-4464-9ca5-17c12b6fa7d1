{layout name="manage/template" /}

<div class="admin-content">
    <div class="admin-content-body">
        <div class="am-cf am-padding am-padding-bottom-0">
            <div class="am-fl am-cf">
                <strong class="am-text-primary am-text-lg">在线用户统计</strong> / <small>Online Statistics</small>
            </div>
        </div>
        <hr>

        <!-- 统计卡片 -->
        <div class="am-g">
            <div class="am-u-sm-12 am-u-md-3">
                <div class="stats-card">
                    <div class="stats-number">{$online_stats.online_count}</div>
                    <div class="stats-label">当前在线</div>
                </div>
            </div>
            <div class="am-u-sm-12 am-u-md-3">
                <div class="stats-card">
                    <div class="stats-number">{$online_stats.auth_active_users}</div>
                    <div class="stats-label">认证用户</div>
                </div>
            </div>
            <div class="am-u-sm-12 am-u-md-3">
                <div class="stats-card">
                    <div class="stats-number">{$online_stats.heartbeat_active}</div>
                    <div class="stats-label">活跃设备</div>
                </div>
            </div>
            <div class="am-u-sm-12 am-u-md-3">
                <div class="stats-card">
                    <div class="stats-number">{$online_stats.auth_total_tokens}</div>
                    <div class="stats-label">总令牌数</div>
                </div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="am-panel am-panel-default">
            <div class="am-panel-hd">在线趋势图</div>
            <div class="am-panel-bd">
                <canvas id="onlineChart" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- 详细统计 -->
        <div class="am-panel am-panel-default">
            <div class="am-panel-hd">详细统计</div>
            <div class="am-panel-bd">
                <table class="am-table am-table-striped am-table-hover">
                    <thead>
                        <tr>
                            <th>统计项</th>
                            <th>数值</th>
                            <th>更新时间</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>在线用户总数</td>
                            <td>{$online_stats.online_count}</td>
                            <td>{$online_stats.last_update|date='Y-m-d H:i:s'}</td>
                        </tr>
                        <tr>
                            <td>认证活跃用户</td>
                            <td>{$online_stats.auth_active_users}</td>
                            <td>{$online_stats.last_update|date='Y-m-d H:i:s'}</td>
                        </tr>
                        <tr>
                            <td>心跳活跃设备</td>
                            <td>{$online_stats.heartbeat_active}</td>
                            <td>{$online_stats.last_update|date='Y-m-d H:i:s'}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// 图表初始化
const ctx = document.getElementById('onlineChart').getContext('2d');
const chartData = {$chart_data|raw};

const chart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: chartData.labels || [],
        datasets: [{
            label: '在线用户数',
            data: chartData.data || [],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// 自动刷新（每5分钟）
setInterval(function() {
    window.location.reload();
}, 5 * 60 * 1000);
</script>
