


 
var Type= "ease";
//ease：（逐渐变慢）默认值，
//linear：（匀速），
//ease-in：(加速)
//ease-out：（减速）
//ease-in-out：（加速然后减速）
//cubic-bezier 自定义


//需要预加载的图片路径存放在数组里
var snowflakeUR_Spring = [
    'http://note.youdao.com/yws/api/personal/file/C7A413BEDFE040E09FCE444943EF43D3?method=download&inline=true&shareKey=ca9d4560d9d53fcd1e9c8195b873b681',
    'http://note.youdao.com/yws/api/personal/file/7FF6CA0B34A3451B832C0949F46D7EDE?method=download&inline=true&shareKey=4d14d22c240c4b26cc90999521e4d2bc',
    'http://note.youdao.com/yws/api/personal/file/4F6D069FAF184682B989553EC1F1D890?method=download&inline=true&shareKey=fc6c06e3d4e0c6a37b735df502a35443',
    'http://note.youdao.com/yws/api/personal/file/06DA4B123B8F4FC8ADC29A7908DE7A5F?method=download&inline=true&shareKey=156385840c541ebde04d07fe364c7d56',
    'http://note.youdao.com/yws/api/personal/file/********************************?method=download&inline=true&shareKey=323640e222e404d4dfd4b8ba48f5b38c',
    'http://note.youdao.com/yws/api/personal/file/********************************?method=download&inline=true&shareKey=378465036f7344465b6e45dce19ea4ef',
    'http://note.youdao.com/yws/api/personal/file/9854BF817DD8405BA52DDDC57FBFE009?method=download&inline=true&shareKey=e40144c4d4fa9b0ce77d71d7cd7191bb',
    'http://note.youdao.com/yws/api/personal/file/1F814032FC084A45912CE28D7D29D5DD?method=download&inline=true&shareKey=3613543856904f30939345372fab40ec',
]  

var snowflakeURl_Fall = [
    'http://note.youdao.com/yws/api/personal/file/5CE5E363C1D3419BB1F55123432C23CF?method=download&inline=true&shareKey=f4a7043cd70cc174dded49d36081de36',
    'http://note.youdao.com/yws/api/personal/file/522E0D6FF56D4D39AA1BD275F2DF7517?method=download&inline=true&shareKey=724fd1e111009d43e730668ed47368d3',
    'http://note.youdao.com/yws/api/personal/file/8865BCADF73B489A90A347A9FB2A2108?method=download&inline=true&shareKey=9c916dfb51003b1820d5ad2e011da592',
    'http://note.youdao.com/yws/api/personal/file/A005286DB9F64E029E0FB6FEA83808CE?method=download&inline=true&shareKey=c9589545a7ea9da2abc7bc401b3434f5',
    'http://note.youdao.com/yws/api/personal/file/69AE91841F9E47FB9B11DF46F3757334?method=download&inline=true&shareKey=5da84291903761d70048269722a5c057',
]  


var snowflakeURl = [
    'http://note.youdao.com/yws/api/personal/file/5CE5E363C1D3419BB1F55123432C23CF?method=download&inline=true&shareKey=f4a7043cd70cc174dded49d36081de36',
    'http://note.youdao.com/yws/api/personal/file/522E0D6FF56D4D39AA1BD275F2DF7517?method=download&inline=true&shareKey=724fd1e111009d43e730668ed47368d3',
    'http://note.youdao.com/yws/api/personal/file/8865BCADF73B489A90A347A9FB2A2108?method=download&inline=true&shareKey=9c916dfb51003b1820d5ad2e011da592',
    'http://note.youdao.com/yws/api/personal/file/A005286DB9F64E029E0FB6FEA83808CE?method=download&inline=true&shareKey=c9589545a7ea9da2abc7bc401b3434f5',
    'http://note.youdao.com/yws/api/personal/file/69AE91841F9E47FB9B11DF46F3757334?method=download&inline=true&shareKey=5da84291903761d70048269722a5c057',
]  






visualWidth  = 900;
visualHeight = 750;


	
//获取content的宽高
function snowflake() {
    // 雪花容器
	var flakeContainer;
    flakeContainer = document.createElement('div');
	flakeContainer.id='flowerflake';
	flakeContainer.style.position = 'absolute';
	flakeContainer.style.top = '0';
	flakeContainer.style.left = '0';
	flakeContainer.style.pointerEvents = 'none';
	flakeContainer.style.width = '100%';
	flakeContainer.style.height = '100%';
	flakeContainer.style.overflow = 'hidden';
	document.body.appendChild(flakeContainer);
	
	flakeContainer= $('#flowerflake');
   

    // 开始飘花
    var FlowerInter = setInterval(function() {
        // 运动的轨迹
        var startPositionLeft = Math.random() * visualWidth - 100,
        startOpacity    = 1,
        endPositionTop  = visualHeight - 40,
        endPositionLeft = startPositionLeft - 100 + Math.random() * 500,
        duration        = visualHeight * 10 + Math.random() * 5000;
					
        // 随机透明度，不小于0.5
        var randomStart = Math.random();
        randomStart = randomStart < 0.5 ? startOpacity : randomStart;
				
        // 创建一个雪花
        var $flake = createSnowBox();
				
        // 设计起点位置
        $flake.css({
            left: startPositionLeft,
            opacity : randomStart
        });
				
        // 加入到容器
        flakeContainer.append($flake);
				
				
        // 开始执行动画
        $flake.transition({
            top: endPositionTop,
            left: endPositionLeft,
            opacity: 0.8,
            
        }, duration, Type, function() {
            $(this).remove() //结束后删除
        });
        
    }, 400);
 }


// 创建一个雪花元素
function createSnowBox() {
    var result = $('<div class="snowbox" />').css({
            'width': 25,
            'height': 26,
            'position': 'absolute',
            'backgroundRepeat':'no-repeat',
            'zIndex': 100000,
            'top': '-41px',
            //'backgroundImage': 'url(' + snowflakeURl[Math.floor(Math.random() * snowflakeURl.length)] + ')'
    }).addClass('snowRoll');
    
    
    
    var TargetImg = $(Imgs[Math.floor(Math.random() * Imgs.length)]);
    
    
    TargetImg.css({
        'width' : "95%",
        'height': "95%",
    });
    
    
    result.append(TargetImg);  //.cloneNode()

    return result;
}
    
    
    
    
// ***************************** 缓存图片 ***************************** //
var RepeatCount = 5;
var Imgs = new Array(snowflakeURl.length * RepeatCount);

for(var i=0; i < snowflakeURl.length ; i++){
    for(var h=0; h<RepeatCount; h++){
        Imgs[i*RepeatCount + h] = new Image();
        Imgs[i*RepeatCount + h].src = snowflakeURl[i]; 
    }
}


// ******************************************************************* //


//开始执行
snowflake();


