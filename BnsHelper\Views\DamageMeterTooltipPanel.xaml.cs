﻿using System.ComponentModel;
using System.Windows;
using System.Windows.Data;
using Xylia.BnsHelper.Models;
using Xylia.BnsHelper.ViewModels;

namespace Xylia.BnsHelper.Views;
public partial class DamageMeterTooltipPanel
{
	#region Constructor
	private System.Windows.Threading.DispatcherTimer? _refreshTimer;

	public DamageMeterTooltipPanel()
	{
		InitializeComponent();
		DataContextChanged += OnDataContextChanged;
		DamageMeterViewModel.OnRefresh += OnRefresh;
		IsVisibleChanged += OnVisibilityChanged;
		Loaded += OnLoaded;
		SizeChanged += OnSizeChanged;

		// 创建定时器定期温和刷新技能列表（降低频率以避免闪烁）
		_refreshTimer = new System.Windows.Threading.DispatcherTimer(System.Windows.Threading.DispatcherPriority.Background)
		{
			Interval = TimeSpan.FromMilliseconds(5000) // 每5秒刷新一次，平衡实时性和性能
		};
		_refreshTimer.Tick += (s, e) =>
		{
			// 只在控件可见、有数据且技能列表不为空时才刷新
			if (IsVisible && Current != null && Current.Skills?.Count > 0)
			{
				// 检查是否真的需要刷新（数据是否有变化）
				if (ShouldRefreshSkillList())
				{
					// 使用温和的刷新方式，只刷新Items而不重新绑定ItemsSource
					GentleRefreshSkillList();
				}
			}
		};
		_refreshTimer.Start();
	}

	~DamageMeterTooltipPanel()
	{
		_refreshTimer?.Stop();
		_refreshTimer = null;
		_skillsViewSource = null;
	}
	#endregion

	#region Methods
	private FPlayer? Current;
	private int _lastSkillCount = 0;
	private long _lastTotalDamage = 0;
	private bool _isRefreshing = false;
	private CollectionViewSource? _skillsViewSource;

	public void OnDataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
	{
		Current = e.NewValue as FPlayer;

		// 重置缓存的值
		_lastSkillCount = 0;
		_lastTotalDamage = 0;

		// 当DataContext改变时，延迟重置布局以确保数据绑定完成
		Dispatcher.BeginInvoke(new Action(() =>
		{
			ResetLayout();

			// 温和地更新技能数据绑定
			if (SkillHolder != null && Current?.Skills != null)
			{
				// 使用CollectionViewSource来管理数据绑定和排序
				if (_skillsViewSource == null || _skillsViewSource.Source != Current.Skills)
				{
					_skillsViewSource = new CollectionViewSource
					{
						Source = Current.Skills
					};

					// 添加排序
					_skillsViewSource.SortDescriptions.Add(new SortDescription("Damage", ListSortDirection.Descending));

					// 绑定到ListView
					SkillHolder.ItemsSource = _skillsViewSource.View;

					// 更新缓存值
					_lastSkillCount = Current.Skills.Count;
					_lastTotalDamage = Current.TotalDamage;
				}
			}

			UpdateLayout();
		}), System.Windows.Threading.DispatcherPriority.DataBind);
	}

	public void OnRefresh(object? sender, EventArgs e)
	{
		Current?.Refresh();

		// 使用温和的刷新方式，避免闪烁
		// 延迟执行以确保数据更新完成
		Dispatcher.BeginInvoke(new Action(() =>
		{
			GentleRefreshSkillList();
			// 只在必要时重置布局
			if (IsVisible)
			{
				InvalidateVisual();
			}
		}), System.Windows.Threading.DispatcherPriority.DataBind);
	}

	private void OnVisibilityChanged(object sender, DependencyPropertyChangedEventArgs e)
	{
		// 当控件变为可见时，重置布局缓存
		if (IsVisible)
		{
			// 延迟执行重置布局，确保控件完全加载
			Dispatcher.BeginInvoke(new Action(() =>
			{
				ResetLayout();
				// 使用温和的刷新方式，避免闪烁
				GentleRefreshSkillList();
				// 强制更新布局
				UpdateLayout();
			}), System.Windows.Threading.DispatcherPriority.Loaded);
		}
	}

	/// <summary>
	/// 强制刷新技能列表，确保数据更新
	/// </summary>
	private void ForceRefreshSkillList()
	{
		if (SkillHolder?.ItemsSource != null)
		{
			// 保存当前数据源
			var currentSource = SkillHolder.ItemsSource;

			// 清除绑定
			SkillHolder.ItemsSource = null;
			SkillHolder.UpdateLayout();

			// 重新绑定
			SkillHolder.ItemsSource = currentSource;

			// 刷新Items集合
			if (SkillHolder.Items != null)
			{
				SkillHolder.Items.Refresh();

				// 重新应用排序
				SkillHolder.Items.SortDescriptions.Clear();
				SkillHolder.Items.SortDescriptions.Add(new SortDescription("Damage", ListSortDirection.Descending));
			}
		}
	}

	/// <summary>
	/// 检查是否需要刷新技能列表
	/// </summary>
	private bool ShouldRefreshSkillList()
	{
		if (Current?.Skills == null)
			return false;

		// 检查技能数量是否变化
		int currentSkillCount = Current.Skills.Count;
		if (currentSkillCount != _lastSkillCount)
		{
			_lastSkillCount = currentSkillCount;
			return true;
		}

		// 检查总伤害是否变化
		long currentTotalDamage = Current.TotalDamage;
		if (currentTotalDamage != _lastTotalDamage)
		{
			_lastTotalDamage = currentTotalDamage;
			return true;
		}

		return false;
	}

	/// <summary>
	/// 温和地刷新技能列表，避免闪烁
	/// </summary>
	private void GentleRefreshSkillList()
	{
		// 防止重复刷新
		if (_isRefreshing || Current?.Skills == null)
			return;

		try
		{
			_isRefreshing = true;

			// 如果使用CollectionViewSource，只需要刷新视图
			if (_skillsViewSource?.View != null)
			{
				// 使用CollectionViewSource的刷新方法，这是最温和的方式
				_skillsViewSource.View.Refresh();
			}
			else if (SkillHolder?.Items != null)
			{
				// 回退到原来的方法
				// 检查是否需要重新排序
				bool needsResorting = SkillHolder.Items.SortDescriptions.Count == 0;

				// 如果需要排序，先添加排序描述
				if (needsResorting)
				{
					SkillHolder.Items.SortDescriptions.Add(new SortDescription("Damage", ListSortDirection.Descending));
				}

				// 只在有数据时才刷新
				if (Current.Skills.Count > 0)
				{
					// 使用最温和的刷新方式
					SkillHolder.Items.Refresh();
				}
			}
		}
		catch (Exception ex)
		{
			// 记录异常但不抛出，避免影响UI
			System.Diagnostics.Debug.WriteLine($"GentleRefreshSkillList error: {ex.Message}");
		}
		finally
		{
			_isRefreshing = false;
		}
	}

	private void OnLoaded(object sender, RoutedEventArgs e)
	{
		// 当控件加载时，重置布局
		ResetLayout();
	}

	private void OnSizeChanged(object sender, SizeChangedEventArgs e)
	{
		// 当尺寸发生变化时，确保布局正确更新
		if (IsVisible)
		{
			InvalidateVisual();
		}
	}

	private void ResetLayout()
	{
		try
		{
			// 清除所有尺寸相关的缓存，但保持固定宽度370
			ClearValue(HeightProperty);
			ClearValue(MinHeightProperty);
			ClearValue(MaxHeightProperty);
			// 不清除宽度相关属性，保持固定宽度370
			// ClearValue(WidthProperty);
			// ClearValue(MinWidthProperty);
			// ClearValue(MaxWidthProperty);

			// 强制重新测量和排列
			InvalidateMeasure();
			InvalidateArrange();
			InvalidateVisual();

			// 对ListView也进行重置
			if (SkillHolder != null)
			{
				SkillHolder.ClearValue(HeightProperty);
				SkillHolder.ClearValue(MinHeightProperty);
				SkillHolder.ClearValue(MaxHeightProperty);
				SkillHolder.InvalidateMeasure();
				SkillHolder.InvalidateArrange();
				SkillHolder.InvalidateVisual();

				// 温和地刷新ListView，避免闪烁
				if (SkillHolder.Items != null)
				{
					SkillHolder.Items.Refresh();

					// 确保排序正确
					if (SkillHolder.Items.SortDescriptions.Count == 0)
					{
						SkillHolder.Items.SortDescriptions.Add(new SortDescription("Damage", ListSortDirection.Descending));
					}
				}
			}

			// 对ResultHolder也进行重置
			if (ResultHolder != null)
			{
				ResultHolder.ClearValue(HeightProperty);
				ResultHolder.InvalidateMeasure();
				ResultHolder.InvalidateArrange();
			}

			// 强制父容器重新布局
			var parent = Parent as FrameworkElement;
			parent?.InvalidateMeasure();
			parent?.InvalidateArrange();
		}
		catch (Exception ex)
		{
			System.Diagnostics.Debug.WriteLine($"ResetLayout error: {ex.Message}");
		}
	}

	/// <summary>
	/// 重写测量方法，确保每次都能正确计算尺寸
	/// </summary>
	protected override Size MeasureOverride(Size constraint)
	{
		try
		{
			// 清除高度缓存，但保持固定宽度370
			ClearValue(HeightProperty);
			// 不清除宽度，保持固定宽度370
			// ClearValue(WidthProperty);

			// 调用基类的测量方法
			var size = base.MeasureOverride(constraint);

			// 确保ListView能够正确测量
			if (SkillHolder != null && SkillHolder.Items.Count > 0)
			{
				SkillHolder.InvalidateMeasure();
			}

			return size;
		}
		catch (Exception ex)
		{
			System.Diagnostics.Debug.WriteLine($"MeasureOverride error: {ex.Message}");
			return base.MeasureOverride(constraint);
		}
	}

	/// <summary>
	/// 重写排列方法，确保布局正确
	/// </summary>
	protected override Size ArrangeOverride(Size arrangeBounds)
	{
		try
		{
			var size = base.ArrangeOverride(arrangeBounds);

			// 确保ListView能够正确排列
			if (SkillHolder != null)
			{
				SkillHolder.InvalidateArrange();
			}

			return size;
		}
		catch (Exception ex)
		{
			System.Diagnostics.Debug.WriteLine($"ArrangeOverride error: {ex.Message}");
			return base.ArrangeOverride(arrangeBounds);
		}
	}
	#endregion
}