﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Serilog;
using System.Net;
using System.Windows;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.BnsHelper.Resources;
using Xylia.Updater;

namespace Xylia.BnsHelper.Services;
/// <summary>
/// Provides functionality to manage and execute application updates using the AutoUpdater.NET library.
/// </summary>
/// <remarks>This service is responsible for configuring and initiating the update process for the application. 
/// It ensures that updates are downloaded and applied automatically when a newer version is available.  The service
/// uses forced download mode and handles update information parsing and error reporting.</remarks>
internal class UpdateService : IService
{
	public const string APP_NAME = "bns-helper";
	private static Action? _updateCheckCompleteCallback;

	#region Methods
	static UpdateService()
	{
		AutoUpdater.ClearAppDirectory = true;
		AutoUpdater.RemindLaterTimeSpan = 0;
		AutoUpdater.ReportErrors = true;
		AutoUpdater.UpdateMode = Mode.ForcedDownload;
		AutoUpdater.RunUpdateAsAdmin = true; // 确保更新程序以管理员权限运行
		AutoUpdater.ParseUpdateInfoEvent += ParseUpdateInfoEvent;
		AutoUpdater.CheckForUpdateEvent += CheckForUpdateEvent;
	}

	/// <summary>
	/// 设置更新检查完成的回调函数
	/// </summary>
	/// <param name="callback">当确认没有更新时调用的回调函数</param>
	public static void SetUpdateCheckCompleteCallback(Action callback)
	{
		_updateCheckCompleteCallback = callback;
	}

	public void Register()
	{
		AutoUpdater.HttpUserAgent = "bnszs";
		AutoUpdater.Start($"https://tools.bnszs.com/api/update?app={APP_NAME}&version={VersionHelper.InternalVersion}");
	}

	private static void ParseUpdateInfoEvent(ParseUpdateInfoEventArgs args)
	{
		args.UpdateInfo = JsonConvert.DeserializeObject<UpdateInfoEventArgs>(args.RemoteData);
		foreach (var x in JsonConvert.DeserializeObject<JObject>(args.RemoteData)!) Application.Current.Properties[x.Key] = x.Value?.ToString();
	}

	private static void CheckForUpdateEvent(UpdateInfoEventArgs args)
	{
		if (args.CurrentVersion != null)
		{
			var currentVersion = new Version(args.CurrentVersion);
			if (currentVersion > args.InstalledVersion)
			{
				// 有更新可用，下载并退出
				AutoUpdater.DownloadUpdate(args);
				Environment.Exit(0);
			}
			else
			{
				// 没有更新，调用回调函数启动MainWindow
				_updateCheckCompleteCallback?.Invoke();
			}
		}
		else
		{
			Log.Error(args.Error, $"Failed to fetch config: " + args.Error.Message);
			MessageBox.Show(
				args.Error is WebException ? args.Error.Message : StringHelper.Get("Application_UpdateFail"),
				StringHelper.Get("ApplicationName"), MessageBoxButton.OK, MessageBoxImage.Error);

			Environment.Exit(500);
		}
	}
	#endregion
}
