using CommunityToolkit.Mvvm.ComponentModel;
using HandyControl.Interactivity;
using System.ComponentModel;
using System.Windows;
using System.Windows.Input;
using Xylia.BnsHelper.ViewModels;

namespace Xylia.Preview.UI.Views.Dialogs;

[ObservableObject]
[DesignTimeVisible(false)]
public partial class CDKeyDialog
{
    #region Constructor
    readonly CDKeyDialogViewModel _viewModel;

    public CDKeyDialog()
    {
        InitializeComponent();
        CommandBindings.Add(new CommandBinding(ControlCommands.Close, CloseCommand));
        DataContext = _viewModel = new CDKeyDialogViewModel();
        _viewModel.CloseAction = () => CloseAction?.Invoke();
        Loaded += OnLoaded;
    }
    #endregion

    #region Properties
    public bool Result { get; set; }
    public Action? CloseAction { get; set; }
    #endregion

    #region Methods
    private void OnLoaded(object sender, RoutedEventArgs e)
    {
        CDKeyTextBox.Focus();
    }

    private void CloseCommand(object sender, RoutedEventArgs e)
    {
        Result = _viewModel.Result;
        CloseAction?.Invoke();
    }
    #endregion
}
