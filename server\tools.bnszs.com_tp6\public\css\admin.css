/**
 * admin.css
 */


/*
 fixed-layout 固定头部和边栏布局
*/

html,
body {
  height: 100%;
  overflow: hidden;
}

ul {
  margin-top: 0;
}


.admin-content,
.admin-sidebar {
  height: 100%;
  overflow-x: auto;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
}




.admin-icon-yellow {
  color: #ffbe40;
}

.admin-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1500;
  font-size: 1.4rem;
  margin-bottom: 0;
}

.admin-header-list a:hover :after {
  content: none;
}

.admin-main {
  position: relative;
  height: 100%;
  padding-top: 51px;
  background: #f3f3f3;
}

.admin-menu {
  position: fixed;
  z-index: 10;
  bottom: 30px;
  right: 20px;
}

.admin-sidebar {
  width: 260px;
  min-height: 100%;
  float: left;
  border-right: 1px solid #cecece;
}

.admin-sidebar.am-active {
  z-index: 1600;
}

.admin-sidebar-list {
  margin-bottom: 0;
}

.admin-sidebar-list li a {
  color: #5c5c5c;
  padding-left: 24px;
}

.admin-sidebar-list li:first-child {
  border-top: none;
}

.admin-sidebar-sub {
  margin-top: 0;
  margin-bottom: 0;
  box-shadow: 0 16px 8px -15px #e2e2e2 inset;
  background: #ececec;
  padding-left: 24px;
}

.admin-sidebar-sub li:first-child {
  border-top: 1px solid #dedede;
}

.admin-sidebar-panel {
  margin: 10px;
}

.admin-content {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  background: #fff;
}




.admin-content-body {
  -webkit-box-flex: 1;
  -webkit-flex: 1 0 auto;
  -ms-flex: 1 0 auto;
  flex: 1 0 auto;
}

.admin-content-footer {
  font-size: 85%;
  color: #777;
}

.admin-content-list {
  border: 1px solid #e9ecf1;
  margin-top: 0;
}

.admin-content-list li {
  border: 1px solid #e9ecf1;
  border-width: 0 1px;
  margin-left: -1px;
}

.admin-content-list li:first-child {
  border-left: none;
}

.admin-content-list li:last-child {
  border-right: none;
}

.admin-content-table a {
  color: #535353;
}
.admin-content-file {
  margin-bottom: 0;
  color: #666;
}

.admin-content-file p {
  margin: 0 0 5px 0;
  font-size: 1.4rem;
}

.admin-content-file li {
  padding: 10px 0;
}

.admin-content-file li:first-child {
  border-top: none;
}

.admin-content-file li:last-child {
  border-bottom: none;
}

.admin-content-file li .am-progress {
  margin-bottom: 4px;
}

.admin-content-file li .am-progress-bar {
  line-height: 14px;
}

.admin-content-task {
  margin-bottom: 0;
}

.admin-content-task li {
  padding: 5px 0;
  border-color: #eee;
}

.admin-content-task li:first-child {
  border-top: none;
}

.admin-content-task li:last-child {
  border-bottom: none;
}

.admin-task-meta {
  font-size: 1.2rem;
  color: #999;
}

.admin-task-bd {
  font-size: 1.4rem;
  margin-bottom: 5px;
}

.admin-content-comment {
  margin-bottom: 0;
}

.admin-content-comment .am-comment-bd {
  font-size: 1.4rem;
}

.admin-content-pagination {
  margin-bottom: 0;
}
.admin-content-pagination li a {
  padding: 4px 8px;
}

@media only screen and (min-width: 641px) {
  .admin-sidebar {
    display: block;
    position: static;
    background: none;
  }

  .admin-offcanvas-bar {
    position: static;
    width: auto;
    background: none;
    -webkit-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    overflow-y: visible;
    min-height: 100%;
  }
  .admin-offcanvas-bar:after {
    content: none;
  }
}

@media only screen and (max-width: 640px) {
  .admin-sidebar {
    width: inherit;
  }

  .admin-offcanvas-bar {
    background: #f3f3f3;
  }

  .admin-offcanvas-bar:after {
    background: #BABABA;
  }

  .admin-sidebar-list a:hover, .admin-sidebar-list a:active{
    -webkit-transition: background-color .3s ease;
    -moz-transition: background-color .3s ease;
    -ms-transition: background-color .3s ease;
    -o-transition: background-color .3s ease;
    transition: background-color .3s ease;
    background: #E4E4E4;
  }

  .admin-content-list li {
    padding: 10px;
    border-width: 1px 0;
    margin-top: -1px;
  }

  .admin-content-list li:first-child {
    border-top: none;
  }

  .admin-content-list li:last-child {
    border-bottom: none;
  }

  .admin-form-text {
    text-align: left !important;
  }

}

/*
* user.html css
*/
.user-info {
  margin-bottom: 15px;
}

.user-info .am-progress {
  margin-bottom: 4px;
}

.user-info p {
  margin: 5px;
}

.user-info-order {
  font-size: 1.4rem;
}

/*
* errorLog.html css
*/

.error-log .am-pre-scrollable {
  max-height: 40rem;
}

/*
* table.html css
*/

.table-main {
  font-size: 1.4rem;
  padding: .5rem;
}

.table-main button {
  background: #fff;
}

.table-check {
  width: 30px;
}

.table-id {
  width: 50px;
}

@media only screen and (max-width: 640px) {
  .table-select {
    margin-top: 10px;
    margin-left: 5px;
  }
}

/*
gallery.html css
*/

.gallery-list li {
  padding: 10px;
}

.gallery-list a {
  color: #666;
}

.gallery-list a:hover {
  color: #3bb4f2;
}

.gallery-title {
  margin-top: 6px;
  font-size: 1.4rem;
}

.gallery-desc {
  font-size: 1.2rem;
  margin-top: 4px;
}

/*
 404.html css
*/

.page-404 {
  background: #fff;
  border: none;
  width: 200px;
  margin: 0 auto;
}


input, button {
    border: none;
   outline: none;
}

.tl-price-input{
    width: 100%;
    border: 1px solid #ccc;
    padding: 7px 0;
    background: #F4F4F7;
    border-radius: 3px;
    padding-left:5px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
    -webkit-transition: border-color ease-in-out .15s,-webkit-box-shadow ease-in-out .15s;
    -o-transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s;
    transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s
}
.tl-price-input:focus{
    border-color: #66afe9;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(102,175,233,.6);
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(102,175,233,.6)
}

.ant-btn {
    line-height: 1.499;
    position: relative;
    display: inline-block;
    font-weight: 400;
    white-space: nowrap;
    text-align: center;
    background-image: none;
    border: 1px solid transparent;
    -webkit-box-shadow: 0 2px 0 rgba(0,0,0,0.015);
    box-shadow: 0 2px 0 rgba(0,0,0,0.015);
    cursor: pointer;
    -webkit-transition: all .3s cubic-bezier(.645, .045, .355, 1);
    transition: all .3s cubic-bezier(.645, .045, .355, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    height: 32px;
    padding: 0 15px;
    font-size: 14px;
    border-radius: 4px;
    color: rgba(0,0,0,0.65);
    background-color: #fff;
    border-color: #d9d9d9;
}

.ant-btn-primary {
    color: #fff;
    background-color: #1890ff;
    border-color: #1890ff;
    text-shadow: 0 -1px 0 rgba(0,0,0,0.12);
    -webkit-box-shadow: 0 2px 0 rgba(0,0,0,0.045);
    box-shadow: 0 2px 0 rgba(0,0,0,0.045);
}
.ant-btn-red {
    color: #fff;
    background-color: #FF5A44;
    border-color: #FF5A44;
    text-shadow: 0 -1px 0 rgba(0,0,0,0.12);
    -webkit-box-shadow: 0 2px 0 rgba(0,0,0,0.045);
    box-shadow: 0 2px 0 rgba(0,0,0,0.045);
}





/* 浏览器宽度小于1000px时隐藏图片 */ 
@media screen and (max-width: 1000px) { 
   .bg { display : None } 
   .cv { display : None }    
   .title { display : None }
} 



.Invalid{
    font-weight: bold;
    color:#FF0066 !important;
}

.Invalid a:link { 
    color:#FF0066 !important;
} 

.Invalid a:visited { 
    color:#FF0033 !important;
} 

.Invalid a:hover { 
    color:#FF0066 !important;
} 

.Invalid a:active { 
    color:#FF0033 !important;
} 

.ModeSelect {
   height:30px;
   width:150px;
   text-align:center;
   text-align-last: center;
}




.Expir {
    text-decoration:line-through;
}






.slider-container {
  width: 300px;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
}
.slider-container .back-bar {
  height: 10px;
  position: relative;
}
.slider-container .back-bar .selected-bar {
  position: absolute;
  height: 100%;
}
.slider-container .back-bar .pointer {
  position: absolute;
  width: 10px;
  height: 10px;
  background-color: red;
  cursor: col-resize;
  opacity: 1;
  z-index: 2;
}
.slider-container .back-bar .pointer.last-active {
  z-index: 3;
}
.slider-container .back-bar .pointer-label {
  position: absolute;
  top: -17px;
  font-size: 8px;
  background: white;
  white-space: nowrap;
  line-height: 1;
}
.slider-container .back-bar .focused {
  z-index: 10;
}
.slider-container .clickable-dummy {
  cursor: pointer;
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 1;
}
.slider-container .scale {
  top: 2px;
  position: relative;
}
.slider-container .scale span {
  position: absolute;
  height: 5px;
  border-left: 1px solid #999;
  font-size: 0;
}
.slider-container .scale ins {
  font-size: 9px;
  text-decoration: none;
  position: absolute;
  left: 0;
  top: 5px;
  color: #999;
  line-height: 1;
}
.slider-container.slider-readonly .clickable-dummy,
.slider-container.slider-readonly .pointer {
  cursor: auto;
}
.theme-green .back-bar {
  height: 15px;
  border-radius: 2px;
  background-color: #eeeeee;
  background-color: #e7e7e7;
  background-image: -moz-linear-gradient(top, #eeeeee, #dddddd);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#eeeeee), to(#dddddd));
  background-image: -webkit-linear-gradient(top, #eeeeee, #dddddd);
  background-image: -o-linear-gradient(top, #eeeeee, #dddddd);
  background-image: linear-gradient(to bottom, #eeeeee, #dddddd);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffeeeeee', endColorstr='#ffdddddd', GradientType=0);
}
.theme-green .back-bar .selected-bar {
  border-radius: 2px;
  background-color: #a1fad0;
  background-image: -moz-linear-gradient(top, #FABDE3, #E67BD6);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#FABDE3), to(#E67BD6));
  background-image: -webkit-linear-gradient(top, #FABDE3, #E67BD6);
  background-image: -o-linear-gradient(top, #FABDE3, #E67BD6);
  background-image: linear-gradient(to bottom, #FABDE3, #E67BD6);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FABDE3', endColorstr='#E67BD6', GradientType=0);
}
.theme-green .back-bar .pointer {
  width: 14px;
  height: 25px;
  top: -5px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  /*border-radius: 10px;*/
  /*border: 1px solid #AAA;*/
  background-color: #e7e7e7;
  background-image: -moz-linear-gradient(top, #eeeeee, #dddddd);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#eeeeee), to(#dddddd));
  background-image: -webkit-linear-gradient(top, #eeeeee, #dddddd);
  background-image: -o-linear-gradient(top, #eeeeee, #dddddd);
  background-image: linear-gradient(to bottom, #eeeeee, #dddddd);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffeeeeee', endColorstr='#fdddddd', GradientType=0);
}
.theme-green .back-bar .pointer-label {
  color: #999;
}
.theme-green .back-bar .focused {
  color: #333;
}
.theme-green .scale span {
  border-left: 1px solid #e5e5e5;
}
.theme-green .scale ins {
  color: #999;
}
.theme-blue .back-bar {
  height: 5px;
  border-radius: 2px;
  background-color: #eeeeee;
  background-color: #e7e7e7;
  background-image: -moz-linear-gradient(top, #eeeeee, #dddddd);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#eeeeee), to(#dddddd));
  background-image: -webkit-linear-gradient(top, #eeeeee, #dddddd);
  background-image: -o-linear-gradient(top, #eeeeee, #dddddd);
  background-image: linear-gradient(to bottom, #eeeeee, #dddddd);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffeeeeee', endColorstr='#ffdddddd', GradientType=0);
}
.theme-blue .back-bar .selected-bar {
  border-radius: 2px;
  background-color: #92c1f9;
  background-image: -moz-linear-gradient(top, #b1d1f9, #64a8f9);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#b1d1f9), to(#64a8f9));
  background-image: -webkit-linear-gradient(top, #b1d1f9, #64a8f9);
  background-image: -o-linear-gradient(top, #b1d1f9, #64a8f9);
  background-image: linear-gradient(to bottom, #b1d1f9, #64a8f9);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffb1d1f9', endColorstr='#ff64a8f9', GradientType=0);
}
.theme-blue .back-bar .pointer {
  width: 14px;
  height: 14px;
  top: -5px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  border-radius: 10px;
  border: 1px solid #AAA;
  background-color: #e7e7e7;
  background-image: -moz-linear-gradient(top, #eeeeee, #dddddd);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#eeeeee), to(#dddddd));
  background-image: -webkit-linear-gradient(top, #eeeeee, #dddddd);
  background-image: -o-linear-gradient(top, #eeeeee, #dddddd);
  background-image: linear-gradient(to bottom, #eeeeee, #dddddd);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffeeeeee', endColorstr='#ffdddddd', GradientType=0);
}
.theme-blue .back-bar .pointer-label {
  color: #999;
}
.theme-blue .back-bar .focused {
  color: #333;
}
.theme-blue .scale span {
  border-left: 1px solid #e5e5e5;
}
.theme-blue .scale ins {
  color: #999;
}
.demo button{
  appearance:none;
  -webkit-appearance:none;
  padding: 8px 15px;
  border:none;   
  color: #fff;
  font-size: 1em; 
  border-radius: 5px;
  background: #aaa;
}


.data a {
  color: inherit;
}