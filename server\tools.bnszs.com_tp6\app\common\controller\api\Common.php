<?php

namespace app\common\controller\api;

use app\common\controller\ApiBase;
use think\App;
use think\Request;
use think\facade\Cache;
use think\facade\Session;
use app\common\service\RedisService;
use app\common\model\Team as Team;
use app\guidebook\model\Clock as Clock;

class Common extends ApiBase
{
    public function __construct(App $app, Request $request) {
        parent::__construct($app, $request);
    }

    /**
     * 获取团队信息
     */
    public function team() {
        $teams = Team::GetTeams();
        $ret = [];
            
        foreach ($teams as $key => $value) {
            if (isset($value['uin'])) {
                $value['head'] = "http://q1.qlogo.cn/g?b=qq&nk=".$value['uin']."&s=100";
                unset($value['uin']);
            }
                
            if ($value['type'] == 'author') {
                $ret['author'][] = $value;
            } else {
                $ret['friends'][] = $value;
            }
        }

        return json($ret);
    }

    /**
     * 获取时间表信息
     */
    public function schedule() {
        $type = $this->request->param('server', 'ZTX');
        $ret = Clock::getClockData($type);
        return json($ret);
    }

    /**
     * 健康检查
     */
    public function health() {
        try {
            $health = [
                'status' => 'ok',
                'timestamp' => time(),
                'load' => $this->getSystemLoad(),
                'memory' => [
                    'used' => memory_get_usage(true),
                    'peak' => memory_get_peak_usage(true)
                ],
                'redis' => RedisService::ping() ? 'connected' : 'disconnected'
            ];
            
            return json(['code' => 1, 'data' => $health]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '健康检查失败', 'error' => $e->getMessage()]);
        }
    }

    /**
     * 获取版本信息
     */
    public function version() {
        $version = [
            'app' => 'BNS Tools',
            'version' => '6.0.0',
            'framework' => 'ThinkPHP 6.0',
            'php' => PHP_VERSION,
            'build_time' => date('Y-m-d H:i:s')
        ];
        
        return json(['code' => 1, 'data' => $version]);
    }

    /**
     * 获取统计信息
     */
    public function statistics() {
        try {
            $stats = [
                'online_users' => RedisService::get('bns:online_count') ?: 0,
                'total_requests' => Cache::get('total_requests') ?: 0,
                'cache_hits' => Cache::get('cache_hits') ?: 0,
                'last_update' => time()
            ];
            
            return json(['code' => 1, 'data' => $stats]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取统计失败', 'error' => $e->getMessage()]);
        }
    }

    /**
     * 获取系统负载
     */
    private function getSystemLoad() {
        try {
            if (function_exists('sys_getloadavg')) {
                $load = sys_getloadavg();
                return round($load[0], 2);
            }
            return 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * 清理缓存
     */
    public function clearCache() {
        try {
            Cache::clear();
            return json(['code' => 1, 'msg' => '缓存清理成功']);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '缓存清理失败', 'error' => $e->getMessage()]);
        }
    }
}
