﻿using CommunityToolkit.Mvvm.Input;
using System.Diagnostics;
using System.Windows.Media.Imaging;

namespace Xylia.BnsHelper.Models;
public struct Team
{
	public TeamMember[] Author { get; set; }
	public TeamMember[] Friends { get; set; }
}

public struct TeamMember
{
	public string Name { get; set; }
	public string Slogan { get; set; }
	public string Strengths { get; set; }
	public string Status { get; set; }
	public string Software { get; set; }
	public string Software_Status { get; set; }
	public string Url { get; set; }
	public string Head { get; set; }

	public readonly BitmapFrame? HeadImg
	{
		get
		{
			try { return BitmapFrame.Create(new Uri(Head), BitmapCreateOptions.None, BitmapCacheOption.Default); }
			catch { return null; }
		}
	}

	public readonly IRelayCommand OpenLinkCommand => new RelayCommand(OpenLink);

	readonly void OpenLink()
	{
		// check url
		if (!string.IsNullOrEmpty(Url)) Process.Start("explorer.exe", Url);
	}
}
