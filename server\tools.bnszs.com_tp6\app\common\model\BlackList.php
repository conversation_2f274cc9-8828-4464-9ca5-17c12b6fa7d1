<?php
namespace app\common\model;

use think\Model;

class BlackList extends Model
{
    protected $pk = 'id';
    protected $table = 'bns_blacklist';

    /**
     * 获取黑名单列表
     */
    static public function GetList() {
        $Model = new static();
        $Info = $Model->select()->toArray();
        return $Info;
    }
    
    /**
     * 检查是否在黑名单中
     */
    public static function isBlacklisted($type, $value) {
        return static::where(['type' => $type, 'value' => $value])->find() ? true : false;
    }
    
    /**
     * 添加到黑名单
     */
    public static function addToBlacklist($type, $value, $reason = '') {
        $data = [
            'type' => $type,
            'value' => $value,
            'reason' => $reason,
            'create_time' => time()
        ];
        
        return static::create($data);
    }
    
    /**
     * 从黑名单移除
     */
    public static function removeFromBlacklist($type, $value) {
        return static::where(['type' => $type, 'value' => $value])->delete();
    }
}
