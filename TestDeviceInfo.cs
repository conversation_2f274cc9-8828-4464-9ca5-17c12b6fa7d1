using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Management;
using System.Net.NetworkInformation;
using System.Text;

namespace DeviceInfoTest
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== 设备信息收集测试 ===");
            
            var info = GatherSystemInfo();
            
            Console.WriteLine($"CPU: {info.CPU}");
            Console.WriteLine($"Memory: {info.Memory}");
            Console.WriteLine($"Motherboard: {info.Motherboard}");
            Console.WriteLine($"Disk: {info.Disk}");
            Console.WriteLine($"MAC Address: {info.MACAddress}");
            
            var combined = $"{info.CPU}|{info.Memory}|{info.Motherboard}|{info.Disk}|{info.MACAddress}";
            Console.WriteLine($"\n组合信息: {combined}");
            
            using var sha256 = System.Security.Cryptography.SHA256.Create();
            var hash = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(combined));
            var deviceFingerprint = Convert.ToHexString(hash).ToLower();
            Console.WriteLine($"设备指纹: {deviceFingerprint}");
            
            // 测试多次运行的一致性
            Console.WriteLine("\n=== 一致性测试 ===");
            for (int i = 0; i < 3; i++)
            {
                var testInfo = GatherSystemInfo();
                var testCombined = $"{testInfo.CPU}|{testInfo.Memory}|{testInfo.Motherboard}|{testInfo.Disk}|{testInfo.MACAddress}";
                var testHash = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(testCombined));
                var testFingerprint = Convert.ToHexString(testHash).ToLower();
                Console.WriteLine($"测试 {i + 1}: {testFingerprint} {(testFingerprint == deviceFingerprint ? "✓" : "✗")}");
            }
        }

        private static SystemInfo GatherSystemInfo()
        {
            var info = new SystemInfo();

            // 使用更稳定的设备信息收集方法
            info.CPU = GetCPUInfo();
            info.Memory = GetMemoryInfo();
            info.Motherboard = GetMotherboardInfo();
            info.Disk = GetDiskInfo();
            info.MACAddress = GetMACAddress();

            // 最终验证 - 如果所有信息都是默认值，生成唯一标识符
            if (IsAllDefaultValues(info))
            {
                GenerateUniqueDeviceInfo(info);
            }

            return info;
        }

        private static string GetCPUInfo()
        {
            // 方法1: WMI查询
            try
            {
                using var searcher = new ManagementObjectSearcher("SELECT Name, ProcessorId FROM Win32_Processor");
                foreach (ManagementObject obj in searcher.Get())
                {
                    var name = obj["Name"]?.ToString();
                    var processorId = obj["ProcessorId"]?.ToString();
                    if (!string.IsNullOrEmpty(name))
                    {
                        // 如果有ProcessorId，添加到名称中增加唯一性
                        return !string.IsNullOrEmpty(processorId) ? $"{name}-{processorId[..8]}" : name;
                    }
                }
            }
            catch { }

            // 方法2: 环境变量
            try
            {
                var processorIdentifier = Environment.GetEnvironmentVariable("PROCESSOR_IDENTIFIER");
                var processorArchitecture = Environment.GetEnvironmentVariable("PROCESSOR_ARCHITECTURE");
                if (!string.IsNullOrEmpty(processorIdentifier))
                {
                    return $"{processorIdentifier}-{processorArchitecture}";
                }
            }
            catch { }

            // 最后备用方案
            return $"CPU-{Environment.ProcessorCount}Core-{Environment.MachineName}";
        }

        private static string GetMemoryInfo()
        {
            // 方法1: WMI查询
            try
            {
                using var searcher = new ManagementObjectSearcher("SELECT TotalPhysicalMemory FROM Win32_ComputerSystem");
                foreach (ManagementObject obj in searcher.Get())
                {
                    if (obj["TotalPhysicalMemory"] != null)
                    {
                        var totalMemory = Convert.ToUInt64(obj["TotalPhysicalMemory"]);
                        return $"{totalMemory / (1024 * 1024 * 1024)} GB";
                    }
                }
            }
            catch { }

            // 方法2: GC内存信息
            try
            {
                var totalMemory = GC.GetTotalMemory(false) / (1024 * 1024);
                return $"GC-{totalMemory} MB";
            }
            catch { }

            return $"MEM-{Environment.WorkingSet / (1024 * 1024)} MB";
        }

        private static string GetMotherboardInfo()
        {
            // 方法1: WMI查询主板信息
            try
            {
                using var searcher = new ManagementObjectSearcher("SELECT Product, Manufacturer, SerialNumber FROM Win32_BaseBoard");
                foreach (ManagementObject obj in searcher.Get())
                {
                    var manufacturer = obj["Manufacturer"]?.ToString();
                    var product = obj["Product"]?.ToString();
                    var serialNumber = obj["SerialNumber"]?.ToString();
                    
                    if (!string.IsNullOrEmpty(manufacturer) && !string.IsNullOrEmpty(product))
                    {
                        var result = $"{manufacturer} {product}";
                        if (!string.IsNullOrEmpty(serialNumber) && serialNumber.Length > 4)
                        {
                            result += $"-{serialNumber[..4]}";
                        }
                        return result;
                    }
                }
            }
            catch { }

            return $"MB-{Environment.MachineName}-{Environment.OSVersion.Platform}";
        }

        private static string GetDiskInfo()
        {
            // 方法1: WMI查询硬盘信息
            try
            {
                using var searcher = new ManagementObjectSearcher("SELECT Model, Size, SerialNumber FROM Win32_DiskDrive WHERE MediaType='Fixed hard disk media'");
                foreach (ManagementObject obj in searcher.Get())
                {
                    var model = obj["Model"]?.ToString();
                    var serialNumber = obj["SerialNumber"]?.ToString();
                    var size = obj["Size"] != null ? Convert.ToUInt64(obj["Size"]) / (1024 * 1024 * 1024) : 0;
                    
                    if (!string.IsNullOrEmpty(model))
                    {
                        var result = $"{model} ({size} GB)";
                        if (!string.IsNullOrEmpty(serialNumber) && serialNumber.Length > 4)
                        {
                            result += $"-{serialNumber[..4]}";
                        }
                        return result;
                    }
                }
            }
            catch { }

            return $"DISK-{Environment.SystemDirectory[0]}-{Environment.MachineName}";
        }

        private static string GetMACAddress()
        {
            // 方法1: 获取物理网卡MAC地址（排除虚拟网卡）
            try
            {
                var networkInterfaces = NetworkInterface.GetAllNetworkInterfaces()
                    .Where(nic => nic.OperationalStatus == OperationalStatus.Up &&
                                 nic.NetworkInterfaceType != NetworkInterfaceType.Loopback &&
                                 nic.NetworkInterfaceType != NetworkInterfaceType.Tunnel &&
                                 !nic.GetPhysicalAddress().Equals(PhysicalAddress.None) &&
                                 !nic.Description.ToLower().Contains("virtual") &&
                                 !nic.Description.ToLower().Contains("vmware") &&
                                 !nic.Description.ToLower().Contains("hyper-v"))
                    .OrderBy(nic => nic.Name)
                    .ToArray();

                if (networkInterfaces.Length > 0)
                {
                    var macAddress = networkInterfaces[0].GetPhysicalAddress().ToString();
                    if (macAddress.Length == 12)
                    {
                        return string.Join(":", Enumerable.Range(0, 6)
                            .Select(i => macAddress.Substring(i * 2, 2)));
                    }
                    return macAddress;
                }
            }
            catch { }

            // 方法2: 生成基于系统信息的伪MAC地址
            try
            {
                var systemInfo = $"{Environment.MachineName}{Environment.UserName}{Environment.OSVersion}{Environment.ProcessorCount}";
                using var sha1 = System.Security.Cryptography.SHA1.Create();
                var hash = sha1.ComputeHash(System.Text.Encoding.UTF8.GetBytes(systemInfo));
                var macBytes = hash.Take(6).ToArray();
                // 设置本地管理位，确保不与真实MAC冲突
                macBytes[0] = (byte)(macBytes[0] | 0x02);
                return string.Join(":", macBytes.Select(b => b.ToString("X2")));
            }
            catch { }

            return "02:00:00:00:00:00"; // 本地管理的MAC地址格式
        }

        private static bool IsAllDefaultValues(SystemInfo info)
        {
            return (info.CPU.StartsWith("Unknown") || info.CPU.StartsWith("CPU-")) &&
                   (info.Memory.StartsWith("Unknown") || info.Memory.StartsWith("MEM-") || info.Memory.StartsWith("GC-")) &&
                   (info.Motherboard.StartsWith("Unknown") || info.Motherboard.StartsWith("MB-")) &&
                   (info.Disk.StartsWith("Unknown") || info.Disk.StartsWith("DISK-")) &&
                   (info.MACAddress == "00:00:00:00:00:00" || info.MACAddress == "02:00:00:00:00:00");
        }

        private static void GenerateUniqueDeviceInfo(SystemInfo info)
        {
            // 生成基于多个系统属性的唯一标识符
            var uniqueId = Guid.NewGuid().ToString("N")[..8];
            var timestamp = DateTimeOffset.Now.ToUnixTimeSeconds().ToString();
            
            // 收集更多系统信息用于生成唯一性
            var systemData = new StringBuilder();
            systemData.Append(Environment.MachineName);
            systemData.Append(Environment.UserName);
            systemData.Append(Environment.OSVersion.ToString());
            systemData.Append(Environment.ProcessorCount);
            systemData.Append(Environment.SystemDirectory);
            systemData.Append(Environment.WorkingSet);
            systemData.Append(uniqueId);
            systemData.Append(timestamp);

            var combinedData = systemData.ToString();
            using var sha256 = System.Security.Cryptography.SHA256.Create();
            var hash = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(combinedData));
            var hashString = Convert.ToHexString(hash).ToLower();

            // 生成唯一的设备信息
            info.CPU = $"CPU-{hashString[..8]}-{Environment.ProcessorCount}Core";
            info.Memory = $"MEM-{hashString[8..16]}-{Environment.WorkingSet / (1024 * 1024)}MB";
            info.Motherboard = $"MB-{hashString[16..24]}-{Environment.MachineName}";
            info.Disk = $"DISK-{hashString[24..32]}-{Environment.SystemDirectory[0]}";
            
            // 生成唯一MAC地址
            var macBytes = hash.Take(6).ToArray();
            macBytes[0] = (byte)(macBytes[0] | 0x02); // 设置本地管理位
            info.MACAddress = string.Join(":", macBytes.Select(b => b.ToString("X2")));
        }

        private class SystemInfo
        {
            public string CPU { get; set; } = string.Empty;
            public string Memory { get; set; } = string.Empty;
            public string Motherboard { get; set; } = string.Empty;
            public string Disk { get; set; } = string.Empty;
            public string MACAddress { get; set; } = string.Empty;
        }
    }
}
