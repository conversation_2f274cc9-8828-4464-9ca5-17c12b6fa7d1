﻿using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Interop;
using Vanara.PInvoke;
using static Xylia.Preview.Data.Models.ChatChannelOption;

namespace Xylia.BnsHelper.Common.Helpers;
internal class WindowHelper
{
	public readonly HwndSource Source;
	private readonly Action RemoveHook;

	public WindowHelper(Window window, HwndSourceHook WndProc)
	{
		Source = HwndSource.FromHwnd(new WindowInteropHelper(window).Handle);
		Source.AddHook(WndProc);
		RemoveHook = () => Source.RemoveHook(WndProc);
		window.Closed += (sender, e) => RemoveHook();
	}

	public static nint GetGameWindow()
	{
		//var process = Process.GetProcessesByName("BNSR");
		//if (process.Length == 0) throw new Exception(StringHelper.Get("Exception_NoRunningGame"));

		//Hwnd = process.MainWindowHandle;
		return (nint)User32.FindWindow("UnrealWindow", null);
	}

	public static void SendMessage(nint hwnd, string text, CategorySeq category = CategorySeq.Info)
	{
		if (!SettingHelper.Default.AllowMessage) return;

		var cds = new COPYDATASTRUCTSTR()
		{
			cbData = text.Length * 2 + 1,
			lpData = text,
			dwData = (int)category
		};

		User32.SendMessage(hwnd, User32.WindowMessage.WM_COPYDATA, 0x10, ref cds);
	}
}

internal struct COPYDATASTRUCTSTR
{
	public long dwData;
	public int cbData;
	[MarshalAs(UnmanagedType.LPWStr)]
	public string lpData;
}

public enum AppMessage
{
	None,
	Register,
	UnRegister,
}
