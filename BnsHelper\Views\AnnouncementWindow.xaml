<hc:Window x:Class="Xylia.BnsHelper.Views.AnnouncementWindow"
           xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
           xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
           xmlns:hc="https://handyorg.github.io/handycontrol"
           Title="{DynamicResource AnnouncementWindow_Title}"
           Height="600" 
           Width="800"
           WindowStartupLocation="CenterOwner"
           ShowInTaskbar="False"
           ResizeMode="CanResize">

    <Window.Resources>
        <!-- 公告类型颜色转换器 -->
        <Style x:Key="AnnouncementTypeStyle" TargetType="Border">
            <Setter Property="CornerRadius" Value="3"/>
            <Setter Property="Padding" Value="6,2"/>
            <Setter Property="Margin" Value="0,0,8,0"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding Type}" Value="Info">
                    <Setter Property="Background" Value="#E3F2FD"/>
                    <Setter Property="BorderBrush" Value="#2196F3"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="Warning">
                    <Setter Property="Background" Value="#FFF3E0"/>
                    <Setter Property="BorderBrush" Value="#FF9800"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="Update">
                    <Setter Property="Background" Value="#E8F5E8"/>
                    <Setter Property="BorderBrush" Value="#4CAF50"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="Maintenance">
                    <Setter Property="Background" Value="#FFEBEE"/>
                    <Setter Property="BorderBrush" Value="#F44336"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="{DynamicResource RegionBrush}" Padding="16,12">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <Path Data="{StaticResource BellGeometry}" 
                          Fill="{DynamicResource PrimaryBrush}" 
                          Width="20" Height="20" 
                          Stretch="Uniform" 
                          Margin="0,0,8,0"/>
                    <TextBlock Text="{DynamicResource AnnouncementWindow_Title}"
                               FontSize="16" 
                               FontWeight="Bold" 
                               VerticalAlignment="Center"/>
                    <TextBlock Text="{Binding UnreadCount, StringFormat={DynamicResource AnnouncementWindow_UnreadCount}}"
                               FontSize="12" 
                               Foreground="{DynamicResource DangerBrush}" 
                               VerticalAlignment="Center" 
                               Margin="8,0,0,0"
                               Visibility="{Binding HasUnread, Converter={StaticResource Boolean2VisibilityConverter}}"/>
                </StackPanel>

                <Button Grid.Column="1" 
                        Content="{DynamicResource Button_MarkAllAsRead}"
                        Style="{StaticResource ButtonPrimary}"
                        Command="{Binding MarkAllAsReadCommand}"
                        Visibility="{Binding HasUnread, Converter={StaticResource Boolean2VisibilityConverter}}"/>
            </Grid>
        </Border>

        <!-- 公告列表 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="16">
            <ItemsControl ItemsSource="{Binding Announcements}">
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <Border Background="{DynamicResource RegionBrush}" 
                                CornerRadius="8" 
                                Margin="0,0,0,12" 
                                Padding="16"
                                BorderThickness="1"
                                BorderBrush="{DynamicResource BorderBrush}">
                            <Border.Style>
                                <Style TargetType="Border">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsRead}" Value="False">
                                            <Setter Property="BorderBrush" Value="{DynamicResource PrimaryBrush}"/>
                                            <Setter Property="BorderThickness" Value="2"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </Border.Style>
                            
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- 标题和类型 -->
                                <Grid Grid.Row="0" Margin="0,0,0,8">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                                        <Border Style="{StaticResource AnnouncementTypeStyle}" BorderThickness="1">
                                            <TextBlock Text="{Binding TypeText}" 
                                                       FontSize="10" 
                                                       FontWeight="Bold"/>
                                        </Border>
                                        <TextBlock Text="{Binding Title}" 
                                                   FontSize="14" 
                                                   FontWeight="Bold" 
                                                   VerticalAlignment="Center"/>
                                        <Ellipse Width="8" Height="8" 
                                                 Fill="{DynamicResource DangerBrush}" 
                                                 Margin="8,0,0,0"
                                                 Visibility="{Binding IsRead, Converter={StaticResource Boolean2VisibilityConverter}, ConverterParameter=Reverse}"/>
                                    </StackPanel>
                                    
                                    <TextBlock Grid.Column="1" 
                                               Text="{Binding FormattedPublishTime}" 
                                               FontSize="11" 
                                               Foreground="{DynamicResource SecondaryTextBrush}" 
                                               VerticalAlignment="Center"/>
                                </Grid>

                                <!-- 分隔线 -->
                                <Separator Grid.Row="1" Margin="0,0,0,8"/>

                                <!-- 内容 -->
                                <TextBlock Grid.Row="2" 
                                           Text="{Binding Content}" 
                                           TextWrapping="Wrap" 
                                           LineHeight="20" 
                                           Margin="0,0,0,12"/>

                                <!-- 操作按钮 -->
                                <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Right">
                                    <Button Content="{DynamicResource Button_MarkAsRead}"
                                            Style="{StaticResource ButtonDefault}"
                                            FontSize="11"
                                            Padding="12,4"
                                            Command="{Binding DataContext.MarkAsReadCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                            CommandParameter="{Binding Id}"
                                            Visibility="{Binding IsRead, Converter={StaticResource Boolean2VisibilityConverter}, ConverterParameter=Reverse}"/>
                                </StackPanel>
                            </Grid>
                        </Border>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>

        <!-- 底部按钮 -->
        <Border Grid.Row="2" Background="{DynamicResource RegionBrush}" Padding="16,12" BorderThickness="0,1,0,0" BorderBrush="{DynamicResource BorderBrush}">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="{DynamicResource Button_Refresh}"
                        Style="{StaticResource ButtonDefault}" 
                        Command="{Binding RefreshCommand}" 
                        Margin="0,0,8,0"/>
                <Button Content="{DynamicResource Button_Close}"
                        Style="{StaticResource ButtonPrimary}" 
                        IsCancel="True" 
                        Click="CloseButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</hc:Window>
