﻿using NAudio.Wave;
using NAudio.Wave.SampleProviders;
using System.IO;

namespace Xylia.BnsHelper.Models;
internal class AudioSteamReader : WaveStream, ISampleProvider
{
	private WaveStream readerStream;

	private readonly SampleChannel sampleChannel;

	private readonly int destBytesPerSample;

	private readonly int sourceBytesPerSample;

	private readonly long length;

	private readonly object lockObject;

	public string FileName { get; }

	/// <summary>
	/// WaveFormat of this stream
	/// </summary>
	public override WaveFormat WaveFormat => sampleChannel.WaveFormat;

	/// <summary>
	/// Length of this stream (in bytes)
	/// </summary>
	public override long Length => length;

	/// <summary>
	/// Position of this stream (in bytes)
	/// </summary>
	public override long Position
	{
		get
		{
			return SourceToDest(readerStream.Position);
		}
		set
		{
			lock (lockObject)
			{
				readerStream.Position = DestToSource(value);
			}
		}
	}

	/// <summary>
	/// Gets or Sets the Volume of this AudioFileReader. 1.0f is full volume
	/// </summary>
	public float Volume
	{
		get
		{
			return sampleChannel.Volume;
		}
		set
		{
			sampleChannel.Volume = value;
		}
	}

	/// <summary>
	/// Initializes a new instance of AudioFileReader
	/// </summary>
	/// <param name="stream"></param>
	/// <param name="extension"></param>
	public AudioSteamReader(Stream stream, string extension = "mp3")
	{
		lockObject = new object();
		FileName = null;
		CreateReaderStream(stream, extension);
		sourceBytesPerSample = readerStream.WaveFormat.BitsPerSample / 8 * readerStream.WaveFormat.Channels;
		sampleChannel = new SampleChannel(readerStream, forceStereo: false);
		destBytesPerSample = 4 * sampleChannel.WaveFormat.Channels;
		length = SourceToDest(readerStream.Length);
	}

	/// <summary>
	/// Creates the reader stream, supporting all filetypes in the core NAudio library,	and ensuring we are in PCM format
	/// </summary>
	/// <param name="stream"></param>
	/// <param name="extension"></param>
	private void CreateReaderStream(Stream stream, string extension)
	{
		if (extension.Equals("wav", StringComparison.OrdinalIgnoreCase))
		{
			readerStream = new WaveFileReader(stream);
			if (readerStream.WaveFormat.Encoding != WaveFormatEncoding.Pcm && readerStream.WaveFormat.Encoding != WaveFormatEncoding.IeeeFloat)
			{
				readerStream = WaveFormatConversionStream.CreatePcmStream(readerStream);
				readerStream = new BlockAlignReductionStream(readerStream);
			}
		}
		else if (extension.Equals("mp3", StringComparison.OrdinalIgnoreCase))
		{
			readerStream = new Mp3FileReader(stream);
		}
		else if (extension.Equals("aiff", StringComparison.OrdinalIgnoreCase) || extension.Equals("aif", StringComparison.OrdinalIgnoreCase))
		{
			readerStream = new AiffFileReader(stream);
		}
		else
		{
			throw new NotSupportedException("Sound is not supported!");
		}
	}

	/// <summary>
	/// Reads from this wave stream
	/// </summary>
	/// <param name="buffer">Audio buffer</param>
	/// <param name="offset">Offset into buffer</param>
	/// <param name="count">Number of bytes required</param>
	/// <returns>Number of bytes read</returns>
	public override int Read(byte[] buffer, int offset, int count)
	{
		WaveBuffer waveBuffer = new WaveBuffer(buffer);
		int count2 = count / 4;
		return Read(waveBuffer.FloatBuffer, offset / 4, count2) * 4;
	}

	/// <summary>
	/// Reads audio from this sample provider
	/// </summary>
	/// <param name="buffer">Sample buffer</param>
	/// <param name="offset">Offset into sample buffer</param>
	/// <param name="count">Number of samples required</param>
	/// <returns><Number of samples read/returns>
	public int Read(float[] buffer, int offset, int count)
	{
		lock (lockObject)
		{
			return sampleChannel.Read(buffer, offset, count);
		}
	}

	/// <summary>
	/// Helper to convert source to dest bytes
	/// </summary>
	/// <param name="sourceBytes"></param>
	/// <returns></returns>
	private long SourceToDest(long sourceBytes)
	{
		return destBytesPerSample * (sourceBytes / sourceBytesPerSample);
	}

	/// <summary>
	///  Helper to convert dest to source bytes
	/// </summary>
	/// <param name="destBytes"></param>
	/// <returns></returns>
	private long DestToSource(long destBytes)
	{
		return sourceBytesPerSample * (destBytes / destBytesPerSample);
	}

	/// <summary>
	/// Disposes this AudioFileReader
	/// </summary>
	protected override void Dispose(bool disposing)
	{
		if (disposing && readerStream != null)
		{
			readerStream.Dispose();
			readerStream = null;
		}

		base.Dispose(disposing);
	}
}
