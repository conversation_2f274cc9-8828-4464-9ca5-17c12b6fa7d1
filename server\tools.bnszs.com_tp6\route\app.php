<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
use think\facade\Route;

// 基础路由
Route::get('/', function() {
    return redirect('/ingame/bs/bs/error');
});

/* 助手接口 */
Route::group('api', function() {
    Route::rule('/config','\\app\\common\\controller\\Api@config');
    Route::rule('/saveBnsTop','\\app\\common\\controller\\Api@saveBnsTop');
    Route::rule('/getBnsTop','\\app\\common\\controller\\Api@getBnsTop');
    Route::rule('/getface','\\app\\common\\controller\\Api@getface');
    Route::rule('/question','\\app\\common\\controller\\Api@question');
    Route::rule('/saveQuestion','\\app\\common\\controller\\Api@saveQuestion');
    Route::rule('/LogReport','\\app\\common\\controller\\Api@LogReport');
    Route::rule('/LogReportHtml','\\app\\common\\controller\\Api@LogReportHtml');
    Route::rule('/TokenUpdate','\\app\\common\\controller\\Api@TokenUpdate');
    Route::rule('/gateInfo','\\app\\common\\controller\\Api@gateInfo');

    Route::rule('/download','\\app\\common\\controller\\Api@download');
    Route::rule('/team','\\app\\common\\controller\\api\\Common@team');
    Route::rule('/health','\\app\\common\\controller\\api\\Common@health');
    Route::rule('/version','\\app\\common\\controller\\api\\Common@version');
    Route::rule('/statistics','\\app\\common\\controller\\api\\Common@statistics');
    Route::rule('/ranking/PowerTop','\\app\\common\\controller\\api\\Ranking@PowerTop');
    Route::rule('/ranking/InviteTop','\\app\\common\\controller\\api\\Ranking@InviteTop');
    //Route::rule('/schedule','\\app\\common\\controller\\api\\Common@schedule');
    Route::rule('/update','\\app\\service\\controller\\Update@Main');
    Route::rule('/shop','\\app\\common\\controller\\Api@getShopData');
});

// 其他路由
Route::rule('bmg','\\app\\ingame\\controller\\Character\\Resource@bmg');
Route::rule('search/openapi/suggest.jsp','\\app\\ingame\\controller\\Character\\CharacterInfo@SuggestUser');

// comm-htdocs/js/ams/actDesc/391/596391/gmi_act.desc.js
Route::rule('comm-htdocs/js/ams/actDesc/:id/:act_id/gmi_act.desc.js','\\app\\common\\controller\\Api@jsApi');
Route::rule('activity','\\app\\common\\controller\\Api@activity');

return [];