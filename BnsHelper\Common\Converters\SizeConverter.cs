﻿using System.Globalization;
using System.Windows.Data;
using System.Windows.Markup;
using Xylia.Preview.Common.Extension;

namespace Xylia.BnsHelper.Common.Converters;
public class SizeConverter : MarkupExtension, IValueConverter
{
	public override object ProvideValue(IServiceProvider serviceProvider) => this;

	public object? Convert(object value, Type targetType, object? parameter, CultureInfo? culture)
	{
		return value switch
		{
			long size => BinaryExtension.GetReadableSize(size),
			_ => value,
		};
	}

	public object ConvertBack(object value, Type targetType, object? parameter, CultureInfo? culture) => throw new NotImplementedException();
}