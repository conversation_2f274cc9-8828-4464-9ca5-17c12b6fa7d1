<?php

namespace app\common\controller;

use app\common\controller\BaseController;
use think\App;
use think\Request;
use think\facade\Db;
use think\facade\Session;
use think\facade\Validate;
use think\facade\Cache;
use think\facade\View;
use voku\helper\HtmlDomParser;
use app\common\service\RedisService;
use app\ingame\model\BnsTop as TopModel;
use app\common\model\BlackList as BlackListModel;
use app\common\model\BnsUserInfo as BnsUserInfoModel;
use Exception;

class Api extends BaseController
{
    private $globalbnsDomain = 'https://';
    private $globalserverId = 0;
    private $globalareaId = 0;

    public function __construct(App $app, Request $request) {
        parent::__construct($app, $request);
    }


    /**
     * 保存BNS排行榜数据
     */
    public function saveBnsTop() {
        try {
            $data = $this->request->param();
            
            if (empty($data)) {
                return json(['code' => 0, 'msg' => '数据不能为空']);
            }
            
            // 这里应该有保存逻辑
            return json(['code' => 1, 'msg' => '保存成功']);
            
        } catch (Exception $e) {
            return json(['code' => 0, 'msg' => '保存失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 获取BNS排行榜数据
     */
    public function getBnsTop() {
        try {
            $serverId = $this->request->param('server_id', 0);
            $limit = $this->request->param('limit', 100);
            
            $data = TopModel::GetPowerTop($limit, $serverId);
            
            return json(['code' => 1, 'data' => $data]);
            
        } catch (Exception $e) {
            return json(['code' => 0, 'msg' => '获取失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 获取角色头像
     */
    public function getface() {
        try {
            $serverId = $this->request->param('server_id');
            $roleName = $this->request->param('role_name');
            
            if (empty($serverId) || empty($roleName)) {
                return json(['code' => 0, 'msg' => '参数不完整']);
            }
            
            $character = BnsUserInfoModel::detail($serverId, $roleName);
            
            if (!$character) {
                return json(['code' => 0, 'msg' => '角色不存在']);
            }
            
            return json(['code' => 1, 'data' => $character]);
            
        } catch (Exception $e) {
            return json(['code' => 0, 'msg' => '获取失败: ' . $e->getMessage()]);
        }
    }



    //加密
    static function ssl_encrypt($option) {
        $encryptMethod = 'aes-256-cbc';
        $iv = "BfbaxmvjZwnPlGuH";
        $key = md5($iv);
        $encrypted = openssl_encrypt($option, $encryptMethod, $key, 0, $iv);
        return $encrypted;
    }
    //解密
    static function ssl_decrypt($option) {
        $encryptMethod = 'aes-256-cbc';
        $iv = "BfbaxmvjZwnPlGuH";
        $key = md5($iv);
        $decrypted = openssl_decrypt($option, $encryptMethod, $key, 0, $iv);
        return $decrypted;
    }
    
    public function BlackList() {
        $BlackList = BlackListModel::GetList();
        $BlackListStr = json_encode($BlackList);
        echo self::ssl_encrypt($BlackListStr);
    }


    //返回角色是否是302
    public function RoleIs302() {

        //获取大区url
        $this->initParams();

        $params = Request::param();

        if (isset($params['btwaf'])) {
            unset($params['btwaf']);
        }

        $rule = [
            'c|角色名字' => 'require',
            's|服务器ID' => 'number',
        ];

        $validate = Validate::make($rule);

        if (!$validate->check($params)) {
            return self::ResultJson(['code' => 1, 'msg' => $validate->getError()]);
        }



        //获取提交参数
        $roleName = Request::get('c', null);
        $serverId = Request::get('s', $this->globalserverId);
        $tid      = Request::get('t', null);

        //获取角色信息
        $url = $this->globalbnsDomain . '/ingame/bs/character/profile?' . http_build_query($params);
        $profile = self::curlGetForm($url, $stateCode);


        if (empty($profile) || $stateCode != 200) {
            if ($tid == null) return self::ResultJson(['code' => $stateCode, 'msg' => '角色异常']);
            else  return "$('.tid[data-id=" . $tid . "]').parent().addClass('Invalid');";
        } else {
            if ($tid == null) return self::ResultJson(['code' => 0, 'msg' => '角色可以正常访问']);
            else  return "console.log('返回：" . $tid . " 角色存在')";
        }
    }




    /**
     * 日志报告
     */
    public function LogReport() {
        //return;
        include_once(EXTEND_PATH . "Rc4.php");
        $iFlowId = Request::post('iFlowId', null);
        $sign_base64 = Request::post('sign', null);
        $uin = Request::post('uin', null);
        $access = true;
        if (bnszs_api_check()) {
            $access = true;
        }
        if (!$access) {
            return json(['code' => 1, 'msg' => '接口非法请求']);
        }
        $sign = base64_decode($sign_base64);
        
        $reportText = rc4b($sign);

        $reportText = mb_convert_encoding($reportText, "UTF-8", "GBK");
        
        $report = json_decode($reportText, true);
        
        return $this->doLogReport($uin, $iFlowId, $report);
    }

    function doLogReport($uin, $iFlowId, $report) {

        if (!is_array($report)) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }

        switch ($iFlowId) {
            case '242849':
                
                cache("InviteTop", json_encode($report));
                break;
                //答题领取直升券活动 收集题库
            case '734515':
                $question = $report['modRet']['jData']['questions'];
                foreach ($question as $k => $v) {
                    $D = Db::name('bns_question')->where(['iQuestionId' => $v['iQuestionId']])->find();

                    if (!$D) {
                        $InsertData = $v;
                        unset($InsertData['aAnswer']);
                        $InsertData['aAnswer_A'] = $v['aAnswer']['A'];
                        $InsertData['aAnswer_B'] = $v['aAnswer']['B'];
                        $InsertData['aAnswer_C'] = $v['aAnswer']['C'];
                        $InsertData['aAnswer_D'] = $v['aAnswer']['D'];
                        $result = Db::name('bns_question')->insert($InsertData);
                    }
                }
                break;
            // 白青挑战向前冲 答题
            case '933028':
            case 'question_s2':
                $time = date("Y-m-d H:i:s", time());
                if(isset($report['modRet'])){
                    $modRet = $report['modRet'];
                }else{
                    $modRet = $report;
                }
                
                $sOutValue1 = $modRet['sOutValue1'];
                $sOutValue2 = $modRet['sOutValue2'];
                $vQuestionData = json_decode($sOutValue2,true);
                
                $iQuestionId = $vQuestionData[0]['iQuestionId'];
                $vQuestion = $vQuestionData[0]['vQuestion'];
                
                $D = Db::name('bns_question_s2')->where(['iQuestionId' => $iQuestionId])->find();
                if (!$D) {
                    $InsertData['vQuestion'] = $vQuestion;
                    $InsertData['iQuestionId'] = $iQuestionId;
                    $InsertData['iSelectMax'] = 1;
                    $InsertData['iSelectMin'] = 1;
                    $InsertData['vOptionCeil'] = 'D';
                    $InsertData['vOptionFloor'] = 'A';
                    $InsertData['rid'] = $sOutValue1;
                    
                    foreach ($vQuestionData as $k => $v) {
                            $InsertData['aAnswer_'.$v['iAnswerId']] = $v['vAnswer'];
                    }
                    $result = Db::name('bns_question_s2')->insert($InsertData);
                }
                
                
                break;
                //答题领取直升券活动 收集答案和分数
            case '734514':
            case 'question_s1':
                $time = date("Y-m-d H:i:s", time());
                $sAnswer = $report['sAnswer'];
                $result = $report['result'];
                $uin = $report['uin'];


                $tkTime =  $report['tkTime'] ?? null;
                $notFullMarks =  $report['notFullMarks'] ?? 0;

                $sAnswer_md5 = md5($sAnswer);
                $DB_name_main = $iFlowId == '734514' ? 'bns_question_answer_main' : 'bns_question_s1_answer_main';
                $DB_name = $iFlowId == '734514' ? 'bns_question_answer' : 'bns_question_s1_answer';

                Db::name("bns_question_s1_uin")->insert(['uin' => $uin, 'ip' => request()->ip(), 'tkTime' => $tkTime, 'notFullMarks' => $notFullMarks]);


                //一模一样的答案和分数不入库
                if ($notFullMarks == 0) {

                    $D = Db::name($DB_name_main)->where(['answerId' => $sAnswer_md5])->find();
                    if (!$D) {
                        //存储到主表
                        $InsertMainData = [];
                        $InsertMainData['answerId'] = $sAnswer_md5;
                        $InsertMainData['point'] = $result;
                        $InsertMainData['create_time'] = $time;
                        $InsertMainData['uin'] = $uin;
                        $InsertMainData = Db::name($DB_name_main)->insert($InsertMainData);


                        //存储到子表
                        $InsertData = [];
                        $sAnswer_Array = explode("|", $sAnswer);
                        foreach ($sAnswer_Array as $k => $v) {

                            $InsertData[$k]['answerId'] = $sAnswer_md5;
                            $sAnswer_tmp = explode(":", $v);
                            if (isset($sAnswer_tmp[0])) {
                                $InsertData[$k]['iQuestionId'] = $sAnswer_tmp[0];
                            }
                            $InsertData[$k]['aAnswer'] = "";
                            if (isset($sAnswer_tmp[1])) {
                                $InsertData[$k]['aAnswer'] = $sAnswer_tmp[1];
                            }

                            $InsertData[$k]['create_time'] = $time;
                        }
                        $result = Db::name($DB_name)->insertAll($InsertData);
                    }
                }
                break;
            case '979270':
                // 灵小团子
                $time = date("Y-m-d H:i:s", time());
                if(isset($report['modRet'])){
                    $modRet = $report['modRet'];
                }else{
                    $modRet = $report;
                }
                
                $jData = $modRet['jData'];
                
                $qId = $jData['qId'];
                $qahold = $jData['qahold'];
                $question = $jData['question'];
                
                $D = Db::name('bns_question_s3')->where(['qId' => $qId])->find();
                if (!$D) {
                    $InsertData['question'] = $question;
                    $InsertData['qahold'] = $qahold;
                    $InsertData['qId'] = $qId;
                    $result = Db::name('bns_question_s3')->insert($InsertData);
                }
                break;

                //角色战力及信息
            case '717459':
                $date_time  = date("Y-m-d H:i:s", time());
                $roleids = $report['modRet']['jData']['list'];
                foreach ($roleids as $key => $value) {
                    $update_sql = "";
                    $insert_sql = "";


                    $name = urldecode(urldecode($value['name']));
                    $pcid = $value['newpcid'];


                    $RoleId = Db::name('bnsuserinfo')->where(['newpcid' => $pcid])->count();


                    if ($RoleId) {
                        //存在Roleid 做更新 和 检查是否改名
                        $UserInfo = Db::query("SELECT `iuin`,`name`,`worldid` FROM `bnsuserinfo` WHERE `newpcid` = \"{$pcid}\" LIMIT 1");
                        if ($UserInfo) {
                            //取出之前的名字
                            $uin  = $UserInfo[0]["iuin"];
                            $role_name  = $UserInfo[0]["name"];
                            $role_world = $UserInfo[0]["worldid"];
                        }

                        //存在role == 传递的role name 
                        if (isset($role_name) && ($role_name != $name)) {

                            //如果改名记录表中不存在此角色，则先存储最初的角色名
                            if (!Db::name('rolename_log')->where(['role_id' => $pcid])->count()) {
                                $Insert = Db::execute("insert into `rolename_log`  (`uin`,`role_id`,`role_name`,`create_time`) 
                                                       values ('$uin','{$pcid}','$role_name','$date_time')");
                            }

                            //再存储新的名字
                            $Insert = Db::execute("insert into `rolename_log`  (`uin`,`role_id`,`role_name`,`create_time`) 
                                                   values ('$uin','{$pcid}','$name','$date_time')");

                            //更新角色信息      
                            Db::execute("UPDATE `bns_profile` SET `roleName` = '$name' WHERE `newpcid` = \"{$pcid}\"");
                            Db::execute("UPDATE `bns_character_like` SET `target_roleName` = '$name' WHERE `target_roleName` = '$role_name' AND `target_serverId` = $role_world");
                            Db::execute("UPDATE `bns_character_board` SET `target_roleName` = '$name' WHERE `target_roleName` = '$role_name' AND `target_serverId` = $role_world");
                        }


                        //update 操作
                        $update_sql = "update `bnsuserinfo` set `name` = \"{$name}\",`account_level` = \"{$value['account_level']}\",`allscore` = \"{$value['allscore']}\",`dtEventTimeStamp` = \"{$value['dtEventTimeStamp']}\",`hyday_count` = \"{$value['hyday_count']}\",`iuin` = \"{$value['iuin']}\",`job` = \"{$value['job']}\",`lastplaystarttime` = \"{$value['lastplaystarttime']}\",`level` = \"{$value['level']}\",`lsday` = \"{$value['lsday']}\",`mastery_level` = \"{$value['mastery_level']}\",`paixu` = \"{$value['paixu']}\",`registrationtime` = \"{$value['registrationtime']}\",`sex` = \"{$value['sex']}\",`statis_date` = \"{$value['statis_date']}\",`uin_createtime` = \"{$value['uin_createtime']}\",`uin_lastlogintime` = \"{$value['uin_lastlogintime']}\",`uin_lastlogouttime` = \"{$value['uin_lastlogouttime']}\",`worldid` = \"{$value['worldid']}\",`update_time` = '{$date_time}' where `newpcid` = \"{$pcid}\";";
                    } else {
                        //不存在 直接入库
                        $insert_sql = "insert into `bnsuserinfo`  
	                       (`account_level`,`allscore`,`dtEventTimeStamp`,`hyday_count`,`iuin`,`job`,`lastplaystarttime`,`level`,`lsday`,`mastery_level`,`name`,`newpcid`,`paixu`,`registrationtime`,`sex`,`statis_date`,`uin_createtime`,`uin_lastlogintime`,`uin_lastlogouttime`,`worldid`,`update_time`)
	                       values (\"{$value['account_level']}\",\"{$value['allscore']}\",\"{$value['dtEventTimeStamp']}\",\"{$value['hyday_count']}\",\"{$value['iuin']}\",\"{$value['job']}\",\"{$value['lastplaystarttime']}\",\"{$value['level']}\",\"{$value['lsday']}\",\"{$value['mastery_level']}\",\"{$name}\",\"{$value['newpcid']}\",\"{$value['paixu']}\",\"{$value['registrationtime']}\",\"{$value['sex']}\",\"{$value['statis_date']}\",\"{$value['uin_createtime']}\",\"{$value['uin_lastlogintime']}\",\"{$value['uin_lastlogouttime']}\",\"{$value['worldid']}\",\"{$date_time}\");";
                    }


                    if (strlen($update_sql) > 0) {
                        $update = Db::execute($update_sql);
                    }

                    if (strlen($insert_sql) > 0) {
                        $insert = Db::execute($insert_sql);
                    }
                }
                break;
                // 存储玩家装备信息 个人中心pk接口
            case '780147':
                $date_time  = date("Y-m-d H:i:s", time());
                $myData = $report['modRet']['jData']['my']; // my 是自己的装备信息  other是他人信息
                $myData = json_encode($myData);
                //存一下my  存一下other
                if(!isset($report['request'])){
                    //这里需要把请求体数据塞到sign 的 request 里      。加一个参数
                    return self::ResultJson(['code' => 1, 'msg' => '日志获取失败']);
                }
                $request = $report['request']; // 这个是请求体的数据 我只要大区和roleid
                 
                $newpcid = $request['iArea'].'_'.$request['iRoleId'];
                $has = Db::name('equipMap')->where(['newpcid' => $newpcid])->find();
                
                $update_sql = "";
                $insert_sql = "";
                if($has){
                    $update_sql = "update `equipMap` set `my` = \"{$myData}\" ,`times` = '{$date_time}' where `newpcid` = \"{$newpcid}\";";
                }else{
                    $insert_sql = "insert into `equipMap`  
	                       (`my`,`newpcid`,`times`)
	                       values (\"{$myData}\",\"{$newpcid}\",\"{$date_time}\");";
                }
                
                if (strlen($update_sql) > 0) {
                    $update = Db::execute($update_sql);
                }

                if (strlen($insert_sql) > 0) {
                    $insert = Db::execute($insert_sql);
                }
                
                break;
            default:
                // code...
                break;
        }
        return json(['code' => 0, 'msg' => '日志获取成功']);
    }
    
    public function LogReportJS() {
        //return;
        $iFlowId = Request::post('iFlowId', null);
        $uin = Request::post('uin', null);
        $report = Request::post('sign', null);
        //$reportText = Request::post('sign', null);
        //$report = json_decode($reportText,true);
        return $this->doLogReport($uin, $iFlowId, $report);
    }


    public function jsApi() {
        $id = $this->request->param('id');
        $actId = $this->request->param('act_id');
        $url = "https://bns.qq.com/comm-htdocs/js/ams/actDesc/$id/$actid/gmi_act.desc.js";
        $res = file_get_contents($url);
        return $res;
    }

    public function activity() {
        return View::fetch(app()->getAppPath() . 'common/view/activity.html');
    }

    public function getShopData() {
        $type = $this->request->param('type');
        if($type === '1') {
            $token = 'ztxShop';
            $url = 'https://apps.game.qq.com/daoju/igw/main?_service=goods.hs.res&_act_id=32060&_biz_code=neo&set=2&acctype=itop';
        } else {
            $token = 'zncgShop';
            $url = 'https://apps.game.qq.com/daoju/igw/main?_service=goods.hs.res&_act_id=35923&_biz_code=neo&set=2&acctype=itop';
        }

        $origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';
        
        // 验证是否为 bnszs.com 子域
        if (preg_match('/^https?:\/\/([^.]+\.)?bnszs\.com$/', $origin)) {
            header("Access-Control-Allow-Origin: $origin");
            header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
            header('Access-Control-Allow-Headers: DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range');
        }
        
        // 处理 OPTIONS 请求
        if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
            header('Access-Control-Max-Age: 1728000');
            header('Content-Length: 0');
            header('Content-Type: text/plain');
            exit(0);
        }

        $redis = Cache::store('redis')->handler();
        $cache = $redis->get($token);
        
        if(!$cache || true) {
            $shop_data = file_get_contents($url);
            $shop_data_array = json_decode($shop_data,true);
            
            if ($shop_data_array['result'] != 0){
                return self::ResultJson(['code' => 1, 'msg' => '获取失败','data'=>[] ]);
            }
            
            $sfilePath = $shop_data_array['data']['sfilePath'];
            $goods = file_get_contents($sfilePath);
            $goods = str_replace("var goods_data=","",$goods);
            
            $redis->setex($token, 3600, $goods);
        }else{
            $goods = $cache;
        }
        
        $goods = json_decode($goods,true);
        return json(['code' => 0, 'msg' => '获取成功','data'=>$goods]);
    }
}