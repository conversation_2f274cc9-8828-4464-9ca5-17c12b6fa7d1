<?php /*a:1:{s:96:"E:\Build\C++\bnspatch\src\BnsPlugin\server\tools.bnszs.com_tp6\app\manage\view\manage\login.html";i:1747138549;}*/ ?>
<!DOCTYPE html>
<html>
<head lang="cn">
	<meta charset="UTF-8">
	<title>剑灵小助手</title>
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<meta name="format-detection" content="telephone=no">
	<meta name="renderer" content="webkit">
	<meta http-equiv="Cache-Control" content="no-siteapp" />
	<link rel="icon shortcut" href="/favicon.ico" type="image/x-icon">
	<link rel="stylesheet" href="/css/manage.css?version=2025021501" />
	
	<script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/3.6.0/jquery.min.js"></script>
	<script src="https://static-**********.cos.ap-shanghai.myqcloud.com/web_html/assets/layer/layer.js"></script>

	<style>
		/* 修改颜色主题 */
		:root {
			--primary-color: #00a8ff;
			--primary-hover: #0097e6;
			--text-color: #2f3542;
			--error-color: #ff4757;
		}

		.header {
		  text-align: center;
		}
		.header h1 {
		  font-size: 200%;
		  color: var(--text-color);
		  margin-top: 30px;
		}
		
		.header .have-account {
			font-size: 16px;
			float: right;
			margin-top: 55px;
			color: #999
		}
		
		.header .have-account a {
			color: var(--primary-color);
			text-decoration: none;
			cursor: pointer;
		}
		
		.header .have-account a:hover {
			color: var(--primary-hover);
			text-decoration: underline;
		}

		/* 表单容器样式 */
		.form-container {
			position: relative;
			background: #fff;
			padding: 40px;
			border-radius: 8px;
			box-shadow: 0 2px 15px rgba(0,0,0,0.1);
			margin-bottom: 30px;
			width: 100%;
			box-sizing: border-box;
		}

		/* 输入框组样式 */
		.input-group {
			position: relative;
			margin-bottom: 25px;
		}

		.input-group label {
			display: block;
			margin-bottom: 8px;
			color: var(--text-color);
			font-weight: 500;
		}

		.input-group input {
			width: 100%;
			padding: 12px 15px;
			border: 1px solid #ddd;
			border-radius: 4px;
			transition: all 0.3s ease;
			font-size: 16px;
		}

		.input-group input:focus {
			border-color: var(--primary-color);
			box-shadow: 0 0 5px rgba(0,168,255,0.2);
		}

		/* 验证码组样式 */
		.verify-code-group {
			display: flex;
			gap: 15px;
		}

		.verify-code-group input {
			flex: 1;
		}

		.verify-code-group button {
			padding: 12px 20px;
			white-space: nowrap;
		}

		/* 按钮样式 */
		.btn-primary {
			background: var(--primary-color);
			color: #fff;
			border: none;
			padding: 12px 30px;
			border-radius: 4px;
			cursor: pointer;
			transition: all 0.3s ease;
			font-size: 16px;
			font-weight: 500;
		}

		.btn-primary:hover {
			background: var(--primary-hover);
		}

		/* 按钮禁用样式 */
		.btn-primary:disabled {
			background: #ccc !important;
			cursor: not-allowed;
			opacity: 0.7;
		}

		/* 注册按钮初始状态 */
		#do_register:disabled {
			background: #ccc !important;
			cursor: not-allowed;
			opacity: 0.7;
		}

		/* 错误消息样式 */
		.error-message {
			color: #ff4757;
			font-size: 12px;
			margin-top: 5px;
			display: none;
		}

		.error-message.show {
			display: block;
		}

		/* 表单页脚样式 */
		.form-footer {
			text-align: right;
			margin-top: 35px;
			display: flex;
			justify-content: flex-end;
			gap: 10px;
		}

		.form-footer button {
			min-width: 120px;
			padding: 15px 20px;
		}

		/* 登录表单特定样式 */
		#loginForm {
			max-width: 400px;
			margin: 0 auto;
		}

		/* 注册按钮特定样式 */
		#register {
			background: var(--primary-color);
			transition: all 0.3s ease;
		}

		#register:hover {
			background: var(--primary-hover);
		}

		/* 验证码提示样式 */
		.verify-code-tip {
			color: #ff4757;
			font-size: 12px;
			margin-top: 5px;
		}

		/* 响应式调整 */
		@media (max-width: 768px) {
			.am-g {
				padding: 0;
				min-height: calc(100vh - 120px);
			}

			.am-u-lg-6.am-u-md-8.am-u-sm-centered {
				width: 90%;
				max-width: none;
			}

			.form-container {
				padding: 30px 20px;
			}

			.form-footer button {
				width: 100%;
				margin: 5px 0;
				padding: 15px 20px;
				font-size: 16px;
			}
		}

		/* 修改 body 样式，防止出现滚动条 */
		body {
			margin: 0;
			padding: 0;
			min-height: 100vh;
			overflow: hidden;
		}

		/* 修改主容器样式 */
		.am-g {
			display: flex;
			align-items: center;
			justify-content: center;
			min-height: calc(100vh - 100px); /* 减去footer高度 */
			padding: 0 20px;
			margin: 0;
		}

		/* 修改表单容器样式 */
		.am-u-lg-6.am-u-md-8.am-u-sm-centered {
			width: 100%;
			max-width: 500px;
		}

		/* footer 样式调整 */
		footer {
			position: fixed;
			bottom: 0;
			left: 0;
			width: 100%;
			background-color: #fff;
			padding: 15px 0;
			border-top: 1px solid #eee;
			text-align: center;
			z-index: 1000;
			height: 80px; /* 固定footer高度 */
		}

		footer a {
			color: #666;
			text-decoration: none;
			margin: 0 15px;
			transition: color 0.3s ease;
		}

		footer a:hover {
			color: var(--primary-color);
			text-decoration: none;
		}

		/* 标题样式优化 */
		.form-title {
			font-size: 24px;
			margin-bottom: 30px;
			color: var(--text-color);
		}

		/* 按钮组样式优化 */
		.form-footer {
			margin-top: 35px;
		}

		.btn-primary {
			padding: 12px 30px;
			font-size: 16px;
		}
	</style>
</head>
<body>
	<div class="am-g">
		<div class="am-u-lg-6 am-u-md-8 am-u-sm-centered">
			<form method="post" class="am-form form-container" id="loginForm">
				<h3 class="form-title" style="text-align:center;">剑灵小助手账号登录</h3>
				<div class="input-group">
					<label for="username">账号</label>
					<input type="text" name="username" id="username" placeholder="请输入认证账号 不知道可以输QQ号" tabindex="1">
					<div class="error-message" id="username_error"></div>
				</div>
				<div class="input-group">
					<label for="password">密码</label>
					<input type="password" name="password" id="password" placeholder="请输入密码（不是你QQ密码！！！）" tabindex="2">
					<div class="error-message" id="userpassword_error"></div>
				</div>

				<!-- <div class="verifyimgArea" id="verifyimgArea"><img class="verifyimg" id="verifyimg" title="看不清，换一张"> <a tabindex="4" href="javascript:void(0);" class="verifyimg_tips">看不清，换一张</a> -->


				<div class="form-footer">
					<button type="button" id="register" class="btn-primary">注册 / 找回</button>
					<button type="button" id="login" class="btn-primary">登 录</button>
				</div>
			</form>
			<form method="post" class="am-form form-container" id="registerForm" style="display:none;">
				<h3 class="form-title">用户注册 / 找回密码</h3>
				<div class="input-group">
					<label for="reg_email">QQ邮箱</label>
					<div class="verify-code-group">
						<input type="text" name="email" id="reg_email" placeholder="请输入QQ号" oninput="value=value.replace(/[^\d]/g,'')">
						<label>@qq.com</span>
					</div>

					<div class="error-message" id="email_error"></div>
				</div>
				<div class="input-group">
					<label for="verify_code">验证码</label>
					<div class="verify-code-group">
						<input type="text" name="verify_code" id="verify_code" placeholder="请输入验证码">
						<button type="button" class="btn-primary" id="send_code">发送验证码</button>
					</div>
					<div class="error-message" id="verify_code_error"></div>
				</div>
				<div class="input-group step2">
					<label for="reg_password">密码</label>
					<input type="password" name="password" id="reg_password" placeholder="请输入密码（至少6位）">
					<div class="error-message" id="password_error"></div>
				</div>
				<div class="input-group step2">
					<label for="reg_confirm_password">确认密码</label>
					<input type="password" name="confirm_password" id="reg_confirm_password" placeholder="请再次输入密码">
					<div class="error-message" id="confirm_password_error"></div>
				</div>
				<div class="form-footer">
					<button type="button" id="back_to_login" class="btn-primary">返回</button>
					<button type="button" id="do_register" class="btn-primary">确定</button>
				</div>
			</form>
		</div>

		<footer>
			<div>
				<span><a href="//www.bnszs.com" style="color: #666; text-decoration: none; margin: 0 15px;">返回首页</a></span>
				<span><a target="view_window" href="//weibo.com/bnszs" style="color: #666; text-decoration: none; margin: 0 15px;">关注我们</a></span>
			</div>
			<div style="font-size: 16px; padding-top: 10px; color: #999;">剑灵小助手 2025</div>
		</footer>
	</div>
</body>
</html>
<script>
	var _hmt = _hmt || [];
	    (function() {
	      var hm = document.createElement("script");
	      hm.src = "https://hm.baidu.com/hm.js?00e000ae4edf31394d2153c309efbdec";
	      var s = document.getElementsByTagName("script")[0];
	      s.parentNode.insertBefore(hm, s);
	    })();
	    
	
	function getQueryVariable(variable) {
	    var query = window.location.search.substring(1);
	    var vars = query.split("&");
	    for (var i=0;i<vars.length;i++) {
	        var pair = vars[i].split("=");
	        if(pair[0] == variable) return pair[1];
	    }
	}
	    
	    
	document.onkeydown = function (e) {
	    var theEvent = window.event || e;
	    var code = theEvent.keyCode || theEvent.which || theEvent.charCode;
	    if (code == 13) {
	        $('#login').click();
	    }
	}  
	
	//设置参数    
	$('#username').val(getQueryVariable("uin")); 
	    
	//登录事件
	$('#login').click(function() {
		$('.error-message').hide();
		var username = $('#username').val();
		var password = $('#password').val();
		var hasError = false;

		if(!username) {
			$('#username_error').text('请输入QQ号').show();
			hasError = true;
		}
		if(!password) {
			$('#userpassword_error').text('请输入密码').show();
			hasError = true;
		}

		if(hasError) return;

		$.ajax({
			type: "POST",
			dataType: "json",
			url: window.location.href,
			data: {
				"username": username,
				"password": password,
				"sign": "<?=md5('BNSZS'.time())?>",
			},
			success: function (result) {
				if(result.code == 1) {
					location.href = getQueryVariable('callback') ?? "/manage/center";
				} else {
					layer.msg(result.msg || '登录失败', {icon: 2, time: 5000});
				}
			},
			error: function() {
				layer.msg("数据提交异常，请联系管理员处理", {icon: 2});
			}
		});
	});
	
	//注册事件
	$('#register').click(function() {
		// layer.msg('嘻嘻嘻 邮件已经发送频繁了 暂时不开 晚点再说', {icon: 2});
		// return false;
		$('#loginForm').hide();
		$('#registerForm').show();
		$('#registerForm .step2').hide();
		initSendCodeButton(); // 初始化发送验证码按钮状态
	});

	$('#back_to_login').click(function() {
		$('#registerForm').hide();
		$('#loginForm').show();
		localStorage.removeItem('verifyCodeEndTime'); // 清除倒计时
	});

	// 初始化发送验证码按钮状态
	function initSendCodeButton() {
		var btn = $('#send_code');
		var endTime = localStorage.getItem('verifyCodeEndTime');
		
		if (endTime) {
			var now = Date.now();
			var endTimeValue = parseInt(endTime);
			
			if (now < endTimeValue) {
				// 如果倒计时未结束，继续倒计时
				btn.prop('disabled', true);
				startCountdown(btn, endTimeValue);
			} else {
				// 如果倒计时已结束，重置按钮
				localStorage.removeItem('verifyCodeEndTime');
				btn.prop('disabled', false);
				btn.text('发送验证码');
			}
		} else {
			// 没有倒计时，重置按钮
			btn.prop('disabled', false);
			btn.text('发送验证码');
		}
	}

	// 开始倒计时
	function startCountdown(btn, endTime) {
		// 清除可能存在的旧定时器
		if (window.countdownTimer) {
			clearInterval(window.countdownTimer);
		}
		
		function updateCountdown() {
			var now = Date.now();
			if (now >= endTime) {
				clearInterval(window.countdownTimer);
				btn.prop('disabled', false);
				btn.text('发送验证码');
				localStorage.removeItem('verifyCodeEndTime');
				return;
			}
			
			var timeLeft = Math.floor((endTime - now) / 1000);
			var minutes = Math.floor(timeLeft / 60);
			var seconds = timeLeft % 60;
			btn.text(minutes + '分' + seconds + '秒后重试');
		}
		
		// 立即更新一次
		updateCountdown();
		
		// 设置新的定时器
		window.countdownTimer = setInterval(updateCountdown, 1000);
	}

	// 页面加载时初始化按钮状态
	$(document).ready(function() {
		// 清除所有倒计时相关的状态
		localStorage.removeItem('verifyCodeEndTime');
		var btn = $('#send_code');
		btn.prop('disabled', false);
		btn.text('发送验证码');
		
		// 清除可能存在的定时器
		if (window.countdownTimer) {
			clearInterval(window.countdownTimer);
		}
	});

	// 发送验证码
	$('#send_code').click(function() {
		var email = $('#reg_email').val();
		if (!validateEmail(email)) {
			layer.msg('请输入正确的QQ邮箱格式（QQ号@qq.com）', {icon: 2});
			return;
		}
		
		var btn = $(this);
		btn.prop('disabled', true);
		
		$.ajax({
			type: "POST",
			url: "/manage/sendcode",
			data: { "email": email + '@qq.com' },
			success: function(result) {
				if(result.code == 1) {
					layer.msg('验证码发送成功', {icon: 1});
					var endTime = Date.now() + 900000;
					localStorage.setItem('verifyCodeEndTime', endTime);
					startCountdown(btn, endTime);
					$("#registerForm .form-title").text(result.mode ? "正在找回密码" : "用户注册");
				} else {
					layer.msg(result.msg || '发送失败', {icon: 2});
					btn.prop('disabled', false);
				}
			},
			error: function() {
				layer.msg('网络错误，请稍后重试', {icon: 2});
				btn.prop('disabled', false);
			}
		});
	});


	// 表单验证函数
	function validateEmail(email) {
		//return /^\d+@qq\.com$/.test(email);
		return /^\d+$/.test(email);
	}

	function validatePassword(password) {
		return password && password.length >= 6;
	}

	function validateForm() {
		let isValid = true;
		const email = $('#reg_email').val();
		const password = $('#reg_password').val();
		const confirmPassword = $('#reg_confirm_password').val();
		const verifyCode = $('#verify_code').val();
		const hasVerificationCodeSent = $('#send_code').text().includes('秒后重试');

		// 清除所有错误提示
		$('.error-message').hide();

		// 验证邮箱
		if (!email) {
			$('#email_error').text('请输入QQ邮箱').show();
			isValid = false;
		} else if (!validateEmail(email)) {
			$('#email_error').text('请输入正确的QQ邮箱格式（QQ号@qq.com）').show();
			isValid = false;
		}

		// 验证验证码
		if (!verifyCode) {
			$('#verify_code_error').text('请输入验证码').show();
			isValid = false;
		}

		// 验证是否发送过验证码
		if (!hasVerificationCodeSent) {
			$('#verify_code_error').text('请先发送验证码').show();
			isValid = false;
		}

		// 验证密码
		if($('#registerForm .step2').is(":visible")) {
			if (!password || password.length < 6) {
				$('#password_error').text('密码长度不能小于6位').show();
				isValid = false;
			}

			if (password === '123456') {
				$('#password_error').text('无法使用此密码').show();
				isValid = false;
			}


			// 验证确认密码
			if (!confirmPassword) {
				$('#confirm_password_error').text('请确认密码').show();
				isValid = false;
			} else if (password !== confirmPassword) {
				$('#confirm_password_error').text('两次输入的密码不一致').show();
				isValid = false;
			}
		}

		return isValid;
	}

	// 实时验证邮箱格式
	$('#reg_email').on('input', function() {
		const email = $(this).val();
		if (email && !validateEmail(email)) {
			$('#email_error').text('请输入正确的QQ邮箱格式（QQ号@qq.com）').show();
		} else {
			$('#email_error').hide();
		}
	});

	// 实时验证密码长度
	$('#reg_password').on('input', function() {
		const password = $(this).val();
		if (password && password.length < 6) {
			$('#password_error').text('密码长度不能小于6位').show();
		} 
		else if (password === '123456') {
			$('#password_error').text('无法使用此密码').show();
		}else {
			$('#password_error').hide();
		}
	});

	// 实时验证确认密码
	$('#reg_confirm_password').on('input', function() {
		const confirmPassword = $(this).val();
		const password = $('#reg_password').val();
		if (confirmPassword && password !== confirmPassword) {
			$('#confirm_password_error').text('两次输入的密码不一致').show();
		} else {
			$('#confirm_password_error').hide();
		}
	});

	// 注册表单提交
	$('#do_register').click(function() {
		// 检查提交信息
		if(!validateForm()) return;
	
		$.ajax({
			type: "POST",
			url: "/manage/register",
			data: {
				"email": $('#reg_email').val() + "@qq.com",
				"password": $('#reg_password').val(),
				"verify_code": $('#verify_code').val()
			},
			success: function(result) {
				switch(result.code) {
					case 0: {
						layer.msg(result.msg || '失败时消息', {icon: 2});
						break;
					}

					case 1: {
						if(result.msg != null) {
							layer.msg(result.msg, {icon: 2});
							return;
						}
							
						$('#registerForm .step2').show();
						break;
					}

					case 2: {
						layer.msg('操作成功！正在跳转登录界面', {icon: 1});
						setTimeout(function() {
							$('#registerForm').hide();
							$('#loginForm').show();
						}, 1500);
						break;
					}
				}
			},
			error: function() {
				layer.msg('网络错误，请稍后重试', {icon: 2});
			}
		});
	});
</script>