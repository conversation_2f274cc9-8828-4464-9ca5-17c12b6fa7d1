﻿using System.Diagnostics;
using System.Text.RegularExpressions;
using Xylia.BnsHelper.Models;
using Xylia.Preview.Data.Engine;

namespace Xylia.BnsHelper.Services.Network.Plugin;
internal class InstantEffectNotification : IPacket
{
	#region Fields
	public Creature? Player;
	public int EffectId;
	public sbyte EffectGroup;
    public string EffectAlias = "";
	#endregion

	#region Methods
	public DataArchiveWriter Create() => new DataArchiveWriter(); 

	public void Read(DataArchive reader)
	{
		var version = reader.Read<ushort>();
		Player = new Creature(reader);
		EffectId = reader.Read<int>();
		EffectGroup = (sbyte)reader.ReadByte();
        EffectAlias = reader.ReadString();
		Debug.WriteLine($"{Player.Name} {EffectId} {EffectAlias}");
	}
    #endregion

    #region Regex
    // 基础伤害模式 - 自己造成伤害
    internal static Regex regex_damage1 = new(@"(?<skill>.+?)命中了?(?<target>.+?)，(造成了|造成)(?<damage>\d+(,\d+)*)点(?<critical>(暴击伤害)|(伤害))", RegexOptions.Compiled);

    // 受到伤害模式 - 被别人攻击
    internal static Regex regex_incomingdamage1 = new(@"(?<target>.+?)被(?<caster>.*)的(?<skill>.*)命中，受到了(?<damage>\d+(,\d+)*)点(?<critical>(暴击伤害)|(伤害))", RegexOptions.Compiled);
    internal static Regex regex_incomingdamage2 = new(@"被(?<caster>.+?)的(?<skill>.+?)命中，受到了(?<damage>\d+(,\d+)*)点(?<critical>(暴击伤害)|(伤害))", RegexOptions.Compiled);

    // 效果伤害模式 - DOT/HOT等
    internal static Regex regex_debuff = new(@"(?<effect>.*?)给(?<target>.+?)造成了(?<damage>\d+(,\d+)*)点伤害。", RegexOptions.Compiled);
    internal static Regex regex_debuff2 = new(@"由于(?<effect>.*?)效果，(?<target>.+?)受到了(?<damage>\d+(,\d+)*)点伤害。", RegexOptions.Compiled);
    internal static Regex regex_effect_damage = new(@"由于(?<skill>.+?)的(?<effect>.+?)效果影响，(?<target>.+?)受到了(?<damage>\d+(,\d+)*)点伤害", RegexOptions.Compiled);

    // 闪避/格挡/反击模式
    internal static Regex regex_dodge1 = new(@"(?<target>.+?)闪避了(?<skill>.+?)。", RegexOptions.Compiled);
    internal static Regex regex_dodge2 = new(@"(?<target>.+?)闪避了(?<caster>.+?)的(?<skill>.+?)。", RegexOptions.Compiled);
    internal static Regex regex_parry = new(@"(?<target>.+?)格挡了(?<caster>.+?)的(?<skill>.+?)。", RegexOptions.Compiled);
    internal static Regex regex_perfect_parry = new(@"(?<target>.+?)完全格挡了(?<caster>.+?)的(?<skill>.+?)。", RegexOptions.Compiled);
    internal static Regex regex_counter = new(@"(?<target>.+?)对(?<caster>.+?)的(?<skill>.+?)进行了反击。", RegexOptions.Compiled);
    internal static Regex regex_resist = new(@"(?<target>.+?)抵抗了(?<caster>.+?)的(?<skill>.+?)。", RegexOptions.Compiled);
    internal static Regex regex_bounce = new(@"(?<target>.+?)反制了(?<caster>.+?)的(?<skill>.+?)。", RegexOptions.Compiled);

    // 格挡但受伤模式
    internal static Regex regex_parry_damage = new(@"虽然(?<target>.+?)格挡了(?<caster>.+?)的(?<skill>.+?)，但还是受到了(?<damage>\d+(,\d+)*)点(?<critical>(暴击伤害)|(伤害))", RegexOptions.Compiled);
    internal static Regex regex_perfect_parry_damage = new(@"虽然(?<target>.+?)完全格挡了(?<caster>.+?)的(?<skill>.+?)，但还是受到了(?<damage>\d+(,\d+)*)点(?<critical>(暴击伤害)|(伤害))", RegexOptions.Compiled);
    internal static Regex regex_counter_damage = new(@"虽然(?<target>.+?)对(?<caster>.+?)的(?<skill>.+?)进行了反击，但还是受到了(?<damage>\d+(,\d+)*)点(?<critical>(暴击伤害)|(伤害))", RegexOptions.Compiled);

    // 治疗模式
    internal static Regex regex_heal = new(@"由于(?<skill>.+?)的?效果影响，(?<target>.+?)恢复了(?<heal>\d+(,\d+)*)点(?<type>(生命)|(内力))", RegexOptions.Compiled);
    internal static Regex regex_heal2 = new(@"由于(?<skill>.+?)恢复了(?<heal>\d+(,\d+)*)点(?<type>(生命)|(内力))", RegexOptions.Compiled);
    internal static Regex regex_effect_heal = new(@"(?<effect>.+?)给(?<target>.+?)恢复了(?<heal>\d+(,\d+)*)点(?<type>(生命)|(内力))", RegexOptions.Compiled);

    // 内力伤害模式
    internal static Regex regex_sp_damage = new(@"(?<skill>.+?)命中了?(?<target>.+?)，造成了(?<damage>\d+(,\d+)*)点内力伤害", RegexOptions.Compiled);
    internal static Regex regex_incoming_sp_damage = new(@"(?<target>.+?)被(?<caster>.+?)的(?<skill>.+?)命中，受到了(?<damage>\d+(,\d+)*)点内力伤害", RegexOptions.Compiled);
    internal static Regex regex_effect_sp_damage = new(@"由于(?<effect>.+?)效果，(?<target>.+?)受到了(?<damage>\d+(,\d+)*)点内力伤害", RegexOptions.Compiled);

    // 吸收模式 (生命/内力吸收)
    internal static Regex regex_hp_drain = new(@"(?<target>.+?)被(?<caster>.+?)的(?<skill>.+?)命中，受到了(?<damage>\d+(,\d+)*)点(?<critical>(暴击伤害)|(伤害))，并且被?吸收了(?<drain>\d+(,\d+)*)点?生命", RegexOptions.Compiled);
    internal static Regex regex_sp_drain = new(@"(?<target>.+?)被(?<caster>.+?)的(?<skill>.+?)命中，受到了(?<damage>\d+(,\d+)*)点(?<critical>(暴击伤害)|(伤害))，并且被?吸收了(?<drain>\d+(,\d+)*)点?内力", RegexOptions.Compiled);
    internal static Regex regex_hp_sp_drain = new(@"(?<target>.+?)被(?<caster>.+?)的(?<skill>.+?)命中，受到了?(?<damage>\d+(,\d+)*)点?(?<critical>(暴击伤害)|(伤害))，并且被?吸收了(?<hp_drain>\d+(,\d+)*)点?生命并?吸收了(?<sp_drain>\d+(,\d+)*)点?内力", RegexOptions.Compiled);

    // 效果相关模式 (附加效果、抵抗效果、解除效果)
    internal static Regex regex_attach_effect = new(@"(?<skill>.+?)命中了?(?<target>.+?)，造成了(?<effect>.+?)效果", RegexOptions.Compiled);
    internal static Regex regex_resist_effect = new(@"(?<skill>.+?)命中了?(?<target>.+?)，但抵抗了(?<effect>.+?)效果", RegexOptions.Compiled);
    internal static Regex regex_remove_effect = new(@"(?<skill>.+?)命中了?(?<target>.+?)，?解除了(?<effect>.+?)效果", RegexOptions.Compiled);

    // 死亡/濒死模式
    internal static Regex regex_death = new(@"(?<target>.+?)受到(?<caster>.+?)的(?<skill>.+?)的?影响，死亡了", RegexOptions.Compiled);
    internal static Regex regex_exhaustion = new(@"(?<target>.+?)受到(?<caster>.+?)的(?<skill>.+?)的?影响，陷入了濒死状态", RegexOptions.Compiled);
    internal static Regex regex_effect_death = new(@"由于(?<effect>.+?)效果，(?<target>.+?)死亡了", RegexOptions.Compiled);
    internal static Regex regex_effect_exhaustion = new(@"由于(?<effect>.+?)效果，(?<target>.+?)陷入了濒死状态", RegexOptions.Compiled);

    // 第三人称视角模式 (观察其他人战斗)
    internal static Regex regex_other_hit_damage = new(@"(?<target>.+?)被(?<caster>.+?)的(?<skill>.+?)命中，受到了(?<damage>\d+(,\d+)*)点(?<critical>(暴击伤害)|(伤害))", RegexOptions.Compiled);
    internal static Regex regex_other_parry = new(@"(?<target>.+?)格挡了(?<caster>.+?)的(?<skill>.+?)", RegexOptions.Compiled);
    internal static Regex regex_other_perfect_parry = new(@"(?<target>.+?)完全格挡了(?<caster>.+?)的(?<skill>.+?)", RegexOptions.Compiled);
    internal static Regex regex_other_counter = new(@"(?<target>.+?)对(?<caster>.+?)的(?<skill>.+?)进行了反击", RegexOptions.Compiled);
    internal static Regex regex_other_dodge = new(@"(?<target>.+?)闪避了(?<caster>.+?)的(?<skill>.+?)", RegexOptions.Compiled);
    internal static Regex regex_other_resist = new(@"(?<target>.+?)抵抗了(?<caster>.+?)的(?<skill>.+?)", RegexOptions.Compiled);
    internal static Regex regex_other_bounce = new(@"(?<target>.+?)反制了(?<caster>.+?)的(?<skill>.+?)", RegexOptions.Compiled);

    // 第三人称格挡但受伤
    internal static Regex regex_other_parry_damage = new(@"(?<target>.+?)格挡了(?<caster>.+?)的(?<skill>.+?)，但受到了(?<damage>\d+(,\d+)*)点(?<critical>(暴击伤害)|(伤害))", RegexOptions.Compiled);
    internal static Regex regex_other_perfect_parry_damage = new(@"(?<target>.+?)完全格挡了(?<caster>.+?)的(?<skill>.+?)，但受到了(?<damage>\d+(,\d+)*)点(?<critical>(暴击伤害)|(伤害))", RegexOptions.Compiled);
    internal static Regex regex_other_counter_damage = new(@"(?<target>.+?)对(?<caster>.+?)的(?<skill>.+?)进行了反击，但受到了(?<damage>\d+(,\d+)*)点(?<critical>(暴击伤害)|(伤害))", RegexOptions.Compiled);

    // 第三人称治疗
    internal static Regex regex_other_heal = new(@"由于(?<skill>.+?)的效果影响，(?<target>.+?)恢复了(?<heal>\d+(,\d+)*)点(?<type>(生命)|(内力))", RegexOptions.Compiled);
    internal static Regex regex_other_effect_heal = new(@"由于(?<effect>.+?)效果，(?<target>.+?)恢复了(?<heal>\d+(,\d+)*)点(?<type>(生命)|(内力))", RegexOptions.Compiled);

    // 第三人称效果伤害
    internal static Regex regex_other_effect_damage = new(@"由于(?<effect>.+?)效果，(?<target>.+?)受到了(?<damage>\d+(,\d+)*)点伤害", RegexOptions.Compiled);
    internal static Regex regex_other_effect_sp_damage = new(@"由于(?<effect>.+?)效果，(?<target>.+?)受到了(?<damage>\d+(,\d+)*)点内力伤害", RegexOptions.Compiled);

    // 第三人称死亡/濒死
    internal static Regex regex_other_death = new(@"受到(?<caster>.+?)的(?<skill>.+?)，(?<target>.+?)死亡了", RegexOptions.Compiled);
    internal static Regex regex_other_exhaustion = new(@"受到(?<caster>.+?)的(?<skill>.+?)，(?<target>.+?)陷入了濒死状态", RegexOptions.Compiled);
    internal static Regex regex_other_effect_death = new(@"由于(?<effect>.+?)效果，(?<target>.+?)死亡了", RegexOptions.Compiled);
    internal static Regex regex_other_effect_exhaustion = new(@"由于(?<effect>.+?)效果，(?<target>.+?)陷入了濒死状态", RegexOptions.Compiled);
    #endregion
}
