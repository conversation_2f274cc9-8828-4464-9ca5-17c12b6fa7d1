<?php
namespace app\manage\model;

use think\Exception;
use think\Model;
use think\facade\Cache;
use think\facade\Request;
use app\manage\model\UserWhiteList as UserWhiteList;
use app\common\helper\Mail as Mail;

class User extends Model
{
    protected $table = 'user';

    /**
     * 创建Web账号信息
     */
    public static function AddWeb($uin, $password, $admin = 0) {
        $user = static::where("uin", $uin)->find();
        if($admin && $user) throw new Exception("当前用户已经存在！");
        if(!$admin && (empty($password) || strlen($password) < 6)) throw new Exception("密码长度至少需要6位");

        // 已存在账号时修改密码，不存在时创建
        if($user) {
            $user->password = password_hash($password, PASSWORD_BCRYPT);
            $user->save();
        } else {
            $user = new static();
            $user->uin  = $uin;
            $user->name = $uin;
            $user->email = $uin . '@qq.com';
            $user->password = !empty($password) ? password_hash($password, PASSWORD_BCRYPT) : NULL;
            $user->save();
            static::AddLog($user->id, 'create', NULL, $admin);

            // 发送邮件
            if($admin) {
                $template = [
                    'subject' => '账号激活通知',
                    'body' => "
                        <div style='background-color: #f6f6f6; padding: 20px;'>
                             <div style='max-width: 600px; margin: 0 auto; background-color: #fff; padding: 20px; border-radius: 5px; box-shadow: 0 0 10px rgba(0,0,0,0.1);'>
                                <h2 style='color: #333; text-align: center; margin-bottom: 20px;'>剑灵小助手</h2>
                                <div style='border-top: 1px solid #eee; border-bottom: 1px solid #eee; padding: 20px 0; margin: 20px 0;'>
                                    <p style='color: #666; font-size: 16px;'>尊敬的用户您好，请您尽快完成<a href='https://tools.bnszs.com/manage/login#register' target='_blank'>账号激活</a>！</p>
                                </div>
                                <p style='color: #999; font-size: 12px; text-align: center;'>本邮件由系统自动发送，请勿回复</p>
                            </div>
                        </div>",
                    'alt' => "剑灵小助手\n账号激活通知"
                ];
                Mail::SendTo($user->email, $template);
            }
        }

        return $user;
    }

    /**
     * 通过Token登录
     */
    public static function LoginByToken($name, $realm, $data) {
        $user = static::where('name', $name)->find();
        if (!$user) {
            throw new Exception('用户不存在');
        }

        // 验证摘要认证
        $A1 = md5($name . ':' . $realm . ':' . $user->password);
        $A2 = md5($_SERVER['REQUEST_METHOD'] . ':' . $data['uri']);
        $valid_response = md5($A1 . ':' . $data['nonce'] . ':' . $data['nc'] . ':' . $data['cnonce'] . ':' . $data['qop'] . ':' . $A2);

        if ($data['response'] != $valid_response) {
            throw new Exception('认证失败');
        }

        return $user;
    }

    /**
     * Web登录
     */
    public static function LoginWeb($uin, $password) {
        $user = static::where('uin', $uin)->find();
        
        if (!$user) {
            throw new Exception('用户不存在');
        }
        
        if (!password_verify($password, $user->password)) {
            throw new Exception('密码错误');
        }
        
        // 更新登录时间
        $user->last_login_time = time();
        $user->save();
        
        static::AddLog($user->id, 'login', null, 0);
        
        return $user;
    }

    /**
     * 添加用户日志
     */
    public static function AddLog($userId, $action, $data = null, $admin = 0) {
        $log = [
            'user_id' => $userId,
            'action' => $action,
            'data' => $data ? json_encode($data) : null,
            'admin' => $admin,
            'ip' => request()->ip(),
            'create_time' => time()
        ];
        
        // 这里可以添加到日志表
        // UserLog::create($log);
    }

    /**
     * 获取用户信息
     */
    public static function getUserInfo($uin) {
        return static::where('uin', $uin)->find();
    }

    /**
     * 更新用户信息
     */
    public function updateInfo($data) {
        foreach ($data as $key => $value) {
            if (in_array($key, ['name', 'email', 'avatar'])) {
                $this->$key = $value;
            }
        }
        return $this->save();
    }

    /**
     * 修改密码
     */
    public function changePassword($oldPassword, $newPassword) {
        if (!password_verify($oldPassword, $this->password)) {
            throw new Exception('原密码错误');
        }
        
        if (strlen($newPassword) < 6) {
            throw new Exception('新密码长度至少需要6位');
        }
        
        $this->password = password_hash($newPassword, PASSWORD_BCRYPT);
        return $this->save();
    }

    /**
     * 重置密码
     */
    public static function resetPassword($uin, $newPassword) {
        $user = static::where('uin', $uin)->find();
        if (!$user) {
            throw new Exception('用户不存在');
        }
        
        if (strlen($newPassword) < 6) {
            throw new Exception('密码长度至少需要6位');
        }
        
        $user->password = password_hash($newPassword, PASSWORD_BCRYPT);
        $user->save();
        
        static::AddLog($user->id, 'reset_password', null, 1);
        
        return $user;
    }

    /**
     * 检查用户权限
     */
    public function hasPermission($permission) {
        // 这里可以实现权限检查逻辑
        return true;
    }

    /**
     * 获取用户统计信息
     */
    public static function getStats() {
        return [
            'total_users' => static::count(),
            'active_users' => static::where('last_login_time', '>', time() - 86400 * 30)->count(),
            'new_users_today' => static::where('create_time', '>', strtotime('today'))->count()
        ];
    }

    /**
     * 获取用户权限过期时间
     * @param int $uid 用户ID
     * @param string $type 权限类型
     * @return int 过期时间戳，-1表示永久，0表示无权限
     */
    public static function GetExpiration($uid, $type) {
        try {
            $cache = "user_expir_$type";
            $redis = Cache::store('redis')->handler();
            if($redis->hExists($cache,$uid)) return $redis->hGet($cache,$uid);

            // 如果缓存中找不到就去查数据库
            $time = 0;
            $logs = \think\facade\Db::table('user_log')->where(['uid'=>$uid,'type'=>'cdkey'])->order('id')->select();
            if($logs) {
                foreach ($logs as $log) {
                    //计算起始时间戳
                    $startTime = strtotime($log["time"]);
                    if ($time > $startTime) $startTime = $time;

                    $cdkey = \think\facade\Db::table('bns_cdkey')->alias('c')->where(['type'=>$type,'c.cdkey'=>$log["extra"]])->join('bns_cdkey_customize t','t.cdkey = c.cdkey')->find();
                    if ($cdkey) {
                       switch ($cdkey['timeType']) {
                           case 'duration': $time = strtotime("+{$cdkey['duration']} Day", $startTime); break;
                           case 'fixed': {
                               if(empty($cdkey['fixed'])) {
                                   $time = -1;
                                   break 2;
                               }

                               $time = max($time, strtotime($cdkey['fixed']));
                               break;
                           }
                       }
                   }
               }
            }

            $redis->hSet($cache,$uid,$time);
            return $time;
        } catch (\Exception $e) {
            // 如果Redis或数据库出错，返回0（无权限）
            return 0;
        }
    }
}
