﻿using System.Net.Sockets;

namespace Xylia.BnsHelper.Services.Network;
public abstract class BaseSession : IDisposable
{
    #region Fields
    protected Socket? _socket;
    #endregion

    #region Methods
    /// <summary>
    /// Establishes a connection to the underlying resource or service.
    /// </summary>
    /// <remarks>This method must be implemented by derived classes to define the specific connection logic.
    /// Ensure that any required preconditions, such as configuration or initialization, are met before calling this
    /// method.</remarks>
    protected abstract void Connect();

    /// <summary>
    /// Closes the underlying socket connection and releases associated resources.
    /// </summary>
    /// <remarks>This method ensures that the socket is properly closed and disposed.  Override this method in
    /// a derived class to implement additional cleanup logic if necessary.</remarks>
    protected virtual void Close()
    {
        _socket?.Close();
        _socket?.Dispose();
        _socket = null;
    }

    /// <summary>
    /// Gets a value indicating whether the socket is currently connected.
    /// </summary>
    /// <remarks>This property checks the connection state of the underlying socket. If the socket is null or
    /// an error occurs while  determining the connection state, the property returns <see langword="false"/>.</remarks>
    public bool IsConnected
    {
        get
        {
            try
            {
                return _socket != null && _socket.Connected;
            }
            catch
            {
                return false;
            }
        }
    }

    /// <summary>
    /// Attempts to reconnect by closing the current connection and establishing a new one.
    /// </summary>
    /// <remarks>This method performs a best-effort attempt to reconnect. If an exception occurs during the
    /// process, it is caught and the method returns <see langword="false"/>.</remarks>
    /// <returns><see langword="true"/> if the reconnection is successful; otherwise, <see langword="false"/>.</returns>
    public bool TryReconnect()
    {
        try
        {
            Close();
            Connect();
            return IsConnected;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Releases all resources used by the current instance of the class.
    /// </summary>
    /// <remarks>This method closes and disposes the underlying socket, if it exists, and suppresses
    /// finalization  to optimize garbage collection. After calling this method, the instance should no longer be
    /// used.</remarks>
    public virtual void Dispose()
    {
        Close();

        GC.SuppressFinalize(this);
    }
    #endregion
}
