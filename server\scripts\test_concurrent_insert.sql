-- 测试并发插入的SQL脚本
-- 用于验证主键冲突修复是否有效

-- 清理测试数据
DELETE FROM user_draw WHERE uid = 9999 AND schedule = 4;

-- 查看当前表结构
DESCRIBE user_draw;

-- 查看主键约束
SHOW INDEX FROM user_draw WHERE Key_name = 'PRIMARY';

-- 测试正常插入
INSERT INTO user_draw (uid, schedule, extra, day, point, number, today, time) 
VALUES (9999, 4, 0, 1, 1, 0, 0, NOW());

-- 验证插入成功
SELECT * FROM user_draw WHERE uid = 9999 AND schedule = 4;

-- 尝试重复插入（应该失败）
-- INSERT INTO user_draw (uid, schedule, extra, day, point, number, today, time) 
-- VALUES (9999, 4, 0, 1, 1, 0, 0, NOW());

-- 测试 INSERT ... ON DUPLICATE KEY UPDATE 语法
INSERT INTO user_draw (uid, schedule, extra, day, point, number, today, time) 
VALUES (9999, 4, 0, 2, 2, 1, 1, NOW())
ON DUPLICATE KEY UPDATE 
    day = VALUES(day),
    point = VALUES(point),
    number = VALUES(number),
    today = VALUES(today),
    time = VALUES(time);

-- 验证更新结果
SELECT * FROM user_draw WHERE uid = 9999 AND schedule = 4;

-- 清理测试数据
DELETE FROM user_draw WHERE uid = 9999 AND schedule = 4;

-- 显示测试完成
SELECT 'Test completed successfully' as result;
