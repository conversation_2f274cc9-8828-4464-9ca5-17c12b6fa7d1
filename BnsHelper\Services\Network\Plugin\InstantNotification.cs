﻿using Xylia.Preview.Data.Engine;
using static Xylia.Preview.Data.Models.ChatChannelOption;

namespace Xylia.BnsHelper.Services.Network.Plugin;
internal class InstantNotification : IPacket
{
	#region Fields
	public CategorySeq Category;
	public string? Text;
	#endregion

	#region Methods
	public DataArchiveWriter Create() => new DataArchiveWriter(); // 响应包返回空Writer

	public void Read(DataArchive reader)
	{
		Category = (CategorySeq)reader.Read<byte>();
		Text = reader.ReadString();
    }
    #endregion
}
