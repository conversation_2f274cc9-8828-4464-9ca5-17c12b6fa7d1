<?php

namespace app\ingame\controller\Character;

use app\common\controller\BaseController;
use think\App;
use think\Request;
use think\facade\Db;
use think\facade\Session;
use think\facade\Validate;
use think\facade\View;
use app\ingame\model\BnsTop as BnsTopModel;

class Message extends RedirectBase
{
    private $m_sRoleName = '';
    private $m_iServerId = 0;

    public function __construct(App $app, Request $request)
    {
        parent::__construct($app, $request);
        
        //获取自身信息
        $this->m_sRoleName = Session::get('self_roleName', null);
        $this->m_iServerId = Session::get('self_serverId', null);
    }

    /**
     * 错误页面
     */
    public function ErrorPage() 
    {
        //获取每个职业最高战力
        $jobTopHtml = "";
        $jobTop = BnsTopModel::GetJobTop();
        
        //获取职业信息
        $jobs = $this->getJobArray();
        
        if($jobTop){
            foreach ($jobTop as $item){
                $job = "";
                $jobKey = "";
                $jobIcon = "";
                
                foreach($jobs as $info) {
                    if($info['id'] == $item['job']) {
                        $job = $info["name"];
                        $jobKey = $info["code"];
                        break;
                    }
                }
                    
                $jobTopHtml = (empty($jobTopHtml)?'':$jobTopHtml.($item['job']=='8'?'<br/>':' | ')).'<span data-server="'.$item['worldid'].'" data-name="'.$item['name'].'" job="'.$jobKey.'" ><img '.($jobIcon?('src="'.$jobIcon.'"'):"").'/> '.$job.'</span>';
            }
        }
        
        return View::fetch('Character/error', [
            'jobTop' => $jobTopHtml
        ]);
    }

    /**
     * 消息中心首页
     */
    public function index()
    {
        if (empty($this->m_sRoleName) || empty($this->m_iServerId)) {
            return redirect('/manage/login');
        }

        // 获取未读消息数量
        $unreadCount = $this->getUnreadCount();
        
        // 获取最近消息
        $recentMessages = $this->getRecentMessages();

        return View::fetch('Character/message_center', [
            'unread_count' => $unreadCount,
            'recent_messages' => $recentMessages,
            'role_name' => $this->m_sRoleName,
            'server_id' => $this->m_iServerId
        ]);
    }

    /**
     * 发送消息
     */
    public function send()
    {
        if (empty($this->m_sRoleName) || empty($this->m_iServerId)) {
            return json(['code' => 1, 'msg' => '请先登录']);
        }

        if ($this->request->isPost()) {
            $toRoleName = $this->request->post('to_role_name', '');
            $toServerId = $this->request->post('to_server_id', 0);
            $content = $this->request->post('content', '');
            $title = $this->request->post('title', '');

            // 验证参数
            $validate = Validate::make([
                'to_role_name' => 'require',
                'to_server_id' => 'require|number',
                'content' => 'require|max:1000',
                'title' => 'require|max:100'
            ]);

            $data = [
                'to_role_name' => $toRoleName,
                'to_server_id' => $toServerId,
                'content' => $content,
                'title' => $title
            ];

            if (!$validate->check($data)) {
                return json(['code' => 1, 'msg' => $validate->getError()]);
            }

            // 不能给自己发消息
            if ($toRoleName == $this->m_sRoleName && $toServerId == $this->m_iServerId) {
                return json(['code' => 1, 'msg' => '不能给自己发消息']);
            }

            // 检查今日发送次数限制
            $todayCount = Db::name('character_messages')
                ->where('from_role_name', $this->m_sRoleName)
                ->where('from_server_id', $this->m_iServerId)
                ->where('create_time', '>=', strtotime('today'))
                ->count();

            if ($todayCount >= 20) {
                return json(['code' => 1, 'msg' => '今日发送消息次数已达上限']);
            }

            // 保存消息
            $messageData = [
                'from_role_name' => $this->m_sRoleName,
                'from_server_id' => $this->m_iServerId,
                'to_role_name' => $toRoleName,
                'to_server_id' => $toServerId,
                'title' => $title,
                'content' => $content,
                'is_read' => 0,
                'create_time' => time()
            ];

            $result = Db::name('character_messages')->insert($messageData);

            if ($result) {
                return json(['code' => 0, 'msg' => '发送成功']);
            } else {
                return json(['code' => 1, 'msg' => '发送失败']);
            }
        }

        return View::fetch('Character/send_message');
    }

    /**
     * 获取消息列表
     */
    public function getMessages()
    {
        if (empty($this->m_sRoleName) || empty($this->m_iServerId)) {
            return json(['code' => 1, 'msg' => '请先登录']);
        }

        $type = $this->request->get('type', 'received'); // received, sent
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 20);

        $offset = ($page - 1) * $limit;

        $where = [];
        if ($type == 'received') {
            $where['to_role_name'] = $this->m_sRoleName;
            $where['to_server_id'] = $this->m_iServerId;
        } else {
            $where['from_role_name'] = $this->m_sRoleName;
            $where['from_server_id'] = $this->m_iServerId;
        }

        $messages = Db::name('character_messages')
            ->where($where)
            ->order('create_time desc')
            ->limit($offset, $limit)
            ->select();

        $total = Db::name('character_messages')
            ->where($where)
            ->count();

        return json([
            'code' => 0,
            'data' => [
                'list' => $messages,
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ]
        ]);
    }

    /**
     * 查看消息详情
     */
    public function view()
    {
        if (empty($this->m_sRoleName) || empty($this->m_iServerId)) {
            return redirect('/manage/login');
        }

        $messageId = $this->request->get('id', 0);
        
        if (empty($messageId)) {
            abort(404);
        }

        $message = Db::name('character_messages')
            ->where('id', $messageId)
            ->find();

        if (!$message) {
            abort(404);
        }

        // 检查权限（只能查看自己的消息）
        $canView = false;
        if ($message['to_role_name'] == $this->m_sRoleName && 
            $message['to_server_id'] == $this->m_iServerId) {
            $canView = true;
            
            // 标记为已读
            if (!$message['is_read']) {
                Db::name('character_messages')
                    ->where('id', $messageId)
                    ->update(['is_read' => 1, 'read_time' => time()]);
            }
        } elseif ($message['from_role_name'] == $this->m_sRoleName && 
                  $message['from_server_id'] == $this->m_iServerId) {
            $canView = true;
        }

        if (!$canView) {
            abort(403);
        }

        return View::fetch('Character/message_detail', [
            'message' => $message
        ]);
    }

    /**
     * 删除消息
     */
    public function delete()
    {
        if (empty($this->m_sRoleName) || empty($this->m_iServerId)) {
            return json(['code' => 1, 'msg' => '请先登录']);
        }

        $messageId = $this->request->post('id', 0);
        
        if (empty($messageId)) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }

        $message = Db::name('character_messages')
            ->where('id', $messageId)
            ->find();

        if (!$message) {
            return json(['code' => 1, 'msg' => '消息不存在']);
        }

        // 检查权限（只能删除自己的消息）
        $canDelete = false;
        if ($message['to_role_name'] == $this->m_sRoleName && 
            $message['to_server_id'] == $this->m_iServerId) {
            $canDelete = true;
        } elseif ($message['from_role_name'] == $this->m_sRoleName && 
                  $message['from_server_id'] == $this->m_iServerId) {
            $canDelete = true;
        }

        if (!$canDelete) {
            return json(['code' => 1, 'msg' => '没有权限']);
        }

        $result = Db::name('character_messages')
            ->where('id', $messageId)
            ->delete();

        if ($result) {
            return json(['code' => 0, 'msg' => '删除成功']);
        } else {
            return json(['code' => 1, 'msg' => '删除失败']);
        }
    }

    /**
     * 获取未读消息数量
     */
    private function getUnreadCount()
    {
        return Db::name('character_messages')
            ->where('to_role_name', $this->m_sRoleName)
            ->where('to_server_id', $this->m_iServerId)
            ->where('is_read', 0)
            ->count();
    }

    /**
     * 获取最近消息
     */
    private function getRecentMessages($limit = 10)
    {
        return Db::name('character_messages')
            ->where('to_role_name', $this->m_sRoleName)
            ->where('to_server_id', $this->m_iServerId)
            ->order('create_time desc')
            ->limit($limit)
            ->select();
    }

    /**
     * 获取职业数组
     */
    private function getJobArray()
    {
        $data = [];
        $data[0] = ["id" => 1 ,"code" => "blademaster","name" => "剑士"];
        $data[1] = ["id" => 2 ,"code" => "kungfufighter","name" => "拳师"];
        $data[2] = ["id" => 3 ,"code" => "forcemaster","name" => "气功师"];
        $data[3] = ["id" => 4 ,"code" => "shooter","name" => "枪手"];
        $data[4] = ["id" => 5 ,"code" => "destroyer","name" => "力士"];
        $data[5] = ["id" => 6 ,"code" => "summoner","name" => "召唤师"];
        $data[6] = ["id" => 7 ,"code" => "assassin","name" => "刺客"];
        $data[7] = ["id" => 8 ,"code" => "bladedancer","name" => "灵剑士"];
        $data[8] = ["id" => 9 ,"code" => "warlock","name" => "咒术师"];
        $data[9] = ["id" => 10 ,"code" => "soulfighter","name" => "气宗"];
        $data[10] = ["id" => 11 ,"code" => "warrior","name" => "斗士"];
        $data[11] = ["id" => 12 ,"code" => "archer","name" => "弓手"];
        $data[12] = ["id" => 14 ,"code" => "thunderer","name" => "星术师"];
        $data[13] = ["id" => 15 ,"code" => "dualblader","name" => "双剑士"];
        $data[14] = ["id" => 16 ,"code" => "bard","name" => "乐师"];
        $data[15] = ["id" => 17 ,"code" => "xxxx","name" => "未定义"];
        
        return $data;
    }
}
