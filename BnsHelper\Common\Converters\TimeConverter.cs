﻿using System.Globalization;
using System.Text;
using System.Windows.Data;
using System.Windows.Markup;
using Xylia.BnsHelper.Resources;

namespace Xylia.BnsHelper.Common.Converters;
public class TimeConverter : MarkupExtension, IValueConverter
{
	public override object ProvideValue(IServiceProvider serviceProvider) => this;

	public object? Convert(object value, Type targetType, object? parameter, CultureInfo? culture)
	{
		switch(value)
		{
			case long time: return DateTimeOffset.FromUnixTimeMilliseconds(time).LocalDateTime;
			case TimeSpan time:
				var builder = new StringBuilder();
				if (time.Days > 0) builder.Append(time.Days + StringHelper.Get("Text.Time_Day"));
				if (time.Hours > 0) builder.Append(time.Hours + StringHelper.Get("Text.Time_Hour"));
				if (time.Minutes > 0) builder.Append(time.Minutes + StringHelper.Get("Text.Time_Minute"));
				if (time.Seconds >= 0) builder.Append(time.Seconds + StringHelper.Get("Text.Time_Second"));

				return builder.ToString();

			default: return value;
		}
	}

	public object ConvertBack(object value, Type targetType, object? parameter, CultureInfo? culture)
	{
		throw new NotImplementedException();
	}
}