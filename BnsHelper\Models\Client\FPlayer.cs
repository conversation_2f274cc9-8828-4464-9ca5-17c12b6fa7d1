﻿using CommunityToolkit.Mvvm.Input;
using Newtonsoft.Json;
using Serilog;
using System.Collections;
using System.Collections.Concurrent;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using Xylia.BnsHelper.Common.Converters;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.BnsHelper.Services.Network.Plugin;
using Xylia.BnsHelper.ViewModels;
using Xylia.Preview.Common.Extension;
using Xylia.Preview.Data.Models.Sequence;

namespace Xylia.BnsHelper.Models;
[JsonConverter(typeof(FPlayerConverter))]
internal partial class FPlayer(FPlayerCollection? owner, string? name, bool self = false) : Collection<FEffectEvent>, INotifyPropertyChanged
{
	#region Fields
	public FPlayerCollection Owner { get; set; } = owner;

	public DateTime? StartTime;
	public DateTime EndTime;
	public double Seconds => StartTime.HasValue ? Math.Ceiling((EndTime - StartTime.Value).TotalSeconds) : 0;   //ElapsedTime

	private readonly Dictionary<string, FEffectResult> hash = [];
	public ObservableCollection<FEffectResult> Skills { get; } = [];

	private readonly Dictionary<string, FTargetResult> targetHash = [];
	public ObservableCollection<FTargetResult> Targets { get; } = [];

	public bool Self => self;
	public string? Name { get; set; } = name;
	public JobSeq Job { get; set; } = self ? JobSeq.PcMax : default;

    public bool Mode => Owner!.Count > 1;
    public long TotalDamage => this.Where(x => x.Damage > 0).Sum(x => x.Damage); // 只统计正数伤害，排除治疗
	public long DamagePerSec => Math.Max(0, (long)(TotalDamage / Seconds));
	public double DamageRate => 1d * TotalDamage / Owner!.TotalDamage;
	public double DamageRate2 => 1d * TotalDamage / Owner!.MaxDamage;

	public int BlockedCount;
	public double BlockedRate => 1d * BlockedCount / this.Count;

	public int DodgeCount;
	public double DodgeRate => 1d * DodgeCount / this.Count;
	#endregion

	#region Methods
	protected override void InsertItem(int index, FEffectEvent item)
	{
		base.InsertItem(index, item);

		// update time
		EndTime = item.Time;
		StartTime ??= EndTime;

		// 通知时间相关属性更新
		OnPropertyChanged(nameof(Seconds));

		// update skill result
		if (!hash.TryGetValue(item.Skill, out var skill))
		{
			skill = new FEffectResult() { Owner = this, Name = item.Skill };
			hash.Add(item.Skill, skill);
			Skills.Add(skill);
		}

		// 只有正数伤害才计入技能伤害统计，治疗效果不计入
		if (item.Damage > 0)
		{
			skill.Damage += item.Damage;
		}
		skill.HitCount += 1;
		if (item.Type == SkillResultSeq.CriticalHit) skill.CriticalHitCount += 1;
		if (item.Type == SkillResultSeq.Parry)
		{
			BlockedCount += 1;
			skill.BlockedCount += 1;
		}
		if (item.Type == SkillResultSeq.Dodge)
		{
            DodgeCount += 1;
			skill.DodgeCount += 1;
		}

		// update target result
		if (!string.IsNullOrEmpty(item.Target))
		{
			if (!targetHash.TryGetValue(item.Target, out var target))
			{
				target = new FTargetResult() { Owner = this, Name = item.Target };
				targetHash.Add(item.Target, target);
				Targets.Add(target);
			}

			// 更新目标时间信息
			target.EndTime = item.Time;
			target.StartTime ??= item.Time;

			// 只有正数伤害才计入目标伤害统计，治疗效果不计入
			if (item.Damage > 0) target.Damage += item.Damage;
			target.HitCount += 1;
			if (item.Type == SkillResultSeq.CriticalHit) target.CriticalHitCount += 1;
			if (item.Type == SkillResultSeq.Dodge) target.DodgeCount += 1;
			if (item.Type == SkillResultSeq.Parry) target.ParryCount += 1;
		}
	}

	public void Refresh()
	{
		// always refresh time
		if (!SettingHelper.Default.EncounterMode) EndTime = DateTime.Now;

		OnPropertyChanged(nameof(TotalDamage));
		OnPropertyChanged(nameof(Seconds));
		OnPropertyChanged(nameof(DamagePerSec));
		OnPropertyChanged(nameof(BlockedRate));
		OnPropertyChanged(nameof(DodgeRate));
		Skills.ForEach(x => x.Refresh());
		Targets.ForEach(x => x.Refresh());
	}

	[RelayCommand]
	private void ToWorld()
	{
		if (Name is null) return;

		WindowHelper.SendMessage(DamageMeterViewModel.gHwnd, $"<font name=\"00008130.UI.Hypertext_PC\"><link id='pc:{Name}'>{Name}</link></font>");
	}
	#endregion

	#region Interface
	public event PropertyChangedEventHandler? PropertyChanged;

	public void OnPropertyChanged(string name)
	{
		PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
	}
	#endregion
}

[JsonConverter(typeof(FPlayerCollectionConverter))]
internal class FPlayerCollection : Collection<FPlayer>, INotifyPropertyChanged
{
	#region Fields
	internal bool IsHistory;
	private readonly Dictionary<string, FPlayer> hash = [];              //快速获取统计对象
	private readonly ConcurrentDictionary<string, Creature> hash2 = [];  //快速获取玩家信息，因为目前通过解析消息文本方式

    public DateTime? StartTime;
	public DateTime EndTime;
	public DateTime LastCombatTime { get; private set; }
	public TimeSpan TimeSpan => StartTime.HasValue ? (EndTime - StartTime.Value) : TimeSpan.Zero;

	public long TotalDamage;
	public long MaxDamage => Count == 0 ? 0 : this.Max(x => x.TotalDamage); // TotalDamage已经只包含正数伤害
	public IEnumerable? View
	{
		get
		{
			if (Count == 0) return null;

			var players = this.Where(x => x.Job != JobSeq.JobNone);

			// Apply target filtering if a specific target is selected
			if (TargetFilter != null && !string.IsNullOrEmpty(TargetFilter))
			{
				// Check if it's a specific target (not the "all targets" option)
				bool isAllTargets = TargetFilter.Contains(',') || TargetFilter == "全部目标";
				if (!isAllTargets)
				{
					// Filter players who have damage to this specific target (只考虑正数伤害)
					players = players.Where(p => p.Targets.Any(t => t.Name == TargetFilter && t.Damage > 0));
				}
			}

			return players.OrderByDescending(x => x.TotalDamage);
		}
	}

	// Target filter property
	public string? TargetFilter { get; set; }

	private FPlayer? _default;
	public FPlayer? Default
	{
		get => _default;
		set
		{
			_default = value;
			OnPropertyChanged(nameof(Default));
		}
	}
	#endregion

	#region Methods
	protected override void RemoveItem(int index)
	{
		var palyer = this[index];
		hash.Remove(palyer.Name!);
		base.RemoveItem(index);
	}

	protected override void ClearItems()
	{
		IsHistory = false;

		// 保存当前默认玩家的名称和职业信息
		string? savedName = _default?.Name;
		JobSeq savedJob = _default?.Job ?? JobSeq.PcMax;

		_default = null;
		StartTime = null;
		EndTime = default;
		LastCombatTime = default; // 重置LastCombatTime，避免频道切换后的自动暂停
		TotalDamage = 0;
		hash.Clear();
		base.ClearItems();

		// 如果之前有真实的用户名称，则保留它；否则使用默认值"YOU"
		string playerName = !string.IsNullOrEmpty(savedName) && savedName != "YOU" ? savedName : "YOU";
		Add(Default = new FPlayer(this, playerName, true) { Job = savedJob });
		this.Refresh();
	}

	public void Add(FEffectEvent item)
	{
		// create player
		FPlayer? player;
		if (item.Caster is null)
		{
			ArgumentNullException.ThrowIfNull(Default);
			player = Default;
		}
		else if (!hash.TryGetValue(item.Caster, out player))
		{
            // 跳过对象
            var creature = hash2.GetValueOrDefault(item.Caster);
            if (creature is null) return;

            player = new FPlayer(this, item.Caster) { Job = creature.Job };
			hash.Add(player.Name, player);
			Add(player);
		}

		// append data
		EndTime = LastCombatTime = item.Time;
		StartTime ??= LastCombatTime;
		// 只有正数伤害才计入总伤害统计，治疗效果不计入
		if (item.Damage > 0) TotalDamage += item.Damage;

		player.Add(item);
	}

	public void Add(InstantEffectNotification item)
	{
        if (item.Player is null) return;

		hash2.TryAdd(item.Player.Name, item.Player);
	}

	public void Refresh()
	{
		if (IsHistory) return;

		EndTime = DateTime.Now;
		OnPropertyChanged(nameof(View));
		OnPropertyChanged(nameof(TimeSpan));
	}

	public void Save(int zone)
	{
		try
        {
            // 检查是否禁用战斗记录日志
            if (SettingHelper.Default.DisableBattleLog) return;
            if (IsHistory || !StartTime.HasValue || TimeSpan.TotalSeconds < 10) return;

			var logs = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
			Directory.CreateDirectory(logs);
			File.WriteAllText(Path.Combine(logs, $"act_{zone}_{StartTime.Value.Ticks}.json"), JsonConvert.SerializeObject(this, Formatting.Indented));
		}
		catch (Exception e)
		{
			Log.Error(e, "Save act log failed.");
		}
	}
	#endregion

	#region Interface
	public event PropertyChangedEventHandler? PropertyChanged;

	public void OnPropertyChanged(string name)
	{
		PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
	}
	#endregion
}
