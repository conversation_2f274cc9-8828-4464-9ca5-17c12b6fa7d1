<?php

namespace app\guidebook\controller;

use app\common\controller\BaseController;
use think\App;
use think\Request;
use think\facade\View;
use app\guidebook\model\Article;
use app\guidebook\model\Clock;

class Index extends BaseController {
    
    public function __construct(App $app, Request $request) {
        parent::__construct($app, $request);
    }
    
    /**
     * 向导书首页
     */
    public function index() {
        return View::fetch('index', [
            'data' => Article::GetHome()
        ]);
    }

    /**
     * 查看文章
     */
    public function view() {
        $title = $this->request->get('title', null);
        $viewData = [];
        if ($title == '时钟') {
            $ret = Clock::getClockData('ZTX');
            $viewData['timeList'] = $ret;
        }

        try {
            return View::fetch('Article/' . $title, $viewData);
        }
        catch(\Exception $e) {
            abort(500);
        }
    }

    /**
     * 排行榜页面
     */
    public function top() {
        // 这里可以调用bns应用的排行榜API
        return View::fetch('bnsTop');
    }
    
    /**
     * 邀请排行榜
     */
    public function inviteTop() {
        return View::fetch('bnsInviteTop');
    }
    
    /**
     * 邀请排行榜JSON数据
     */
    public function inviteTopJson() {
        // 返回JSON数据
        return json(['code' => 1, 'data' => []]);
    }
}
