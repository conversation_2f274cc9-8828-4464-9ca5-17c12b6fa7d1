@echo off

echo ========================================
echo    BNSZS Server Windows Build
echo ========================================
echo.

:: Check Go environment
echo [1/5] Checking Go environment...
go version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Go not found, please install Go 1.19 or higher
    pause
    exit /b 1
)
echo OK: Go environment check passed

:: Check current directory
echo [2/5] Checking current directory...
if not exist "cmd\main.go" (
    echo ERROR: Please run this script in the server directory
    echo Current directory: %CD%
    echo Required file: cmd\main.go
    pause
    exit /b 1
)
echo OK: Directory check passed

:: Create output directory
echo [3/5] Creating output directory...
if not exist "bin" mkdir "bin"
echo OK: Output directory created

:: Set build parameters
set CGO_ENABLED=0
set GOOS=windows
set GOARCH=amd64
set VERSION=1.0.0

:: Build server
echo [4/5] Building server...
echo Target platform: Windows AMD64
echo Version: %VERSION%
echo.

go build -ldflags "-s -w" -o "bin\server.exe" ".\cmd\main.go"

if errorlevel 1 (
    echo ERROR: Build failed
    pause
    exit /b 1
)
echo OK: Server build successful

:: Run
cd bin
server.exe
pause