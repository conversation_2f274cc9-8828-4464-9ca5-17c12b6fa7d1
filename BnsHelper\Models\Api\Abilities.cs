﻿namespace Xylia.Preview.UI.GameUI.Scene.Game_CharacterInfo.Api;
public class Abilities
{
	public Ability base_ability;
	public Ability equipped_ability;
	public PointAbility? point_ability;
	public Ability total_ability;

	public struct Ability
	{
		public int abnormal_attack_power_rate;
		public int abnormal_attack_power_value;
		public int abnormal_attack_power_value_equip;
		public int abnormal_defend_power_rate;
		public int abnormal_defend_power_value;
		public int aoe_defend_damage_reduce_rate;
		public int aoe_defend_power_value;
		public int attack_attribute_rate;
		public int attack_attribute_value;
		public int attack_attribute_value_equip;
		public int attack_concentrate_value;
		public int attack_counter_damage_rate;
		public int attack_critical_damage_rate;
		public int attack_critical_damage_value;
		public int attack_critical_damage_value_equip;
		public int attack_critical_rate;
		public int attack_critical_value;
		public int attack_damage_modify_diff;
		public int attack_damage_modify_rate;
		public int attack_defend_pierce_rate;
		public int attack_hit_rate;
		public int attack_hit_value;
		public int attack_parry_pierce_rate;
		public int attack_perfect_parry_damage_rate;
		public int attack_pierce_value;
		public int attack_power_value;
		public int attack_stiff_duration_level;
		public int boss_attack_power_value;
		public int boss_defend_power_rate;
		public int boss_defend_power_value;
		public int counter_damage_reduce_rate;
		public int defend_critical_damage_rate;
		public int defend_critical_rate;
		public int defend_critical_value;
		public int defend_damage_modify_diff;
		public int defend_damage_modify_rate;
		public int defend_dodge_rate;
		public int defend_dodge_value;
		public int defend_parry_rate;
		public int defend_parry_reduce_rate;
		public int defend_parry_value;
		public int defend_physical_damage_reduce_rate;
		public int defend_power_value;
		public int defend_stiff_duration_level;
		public int guard_gauge;
		public int hate_power_rate;
		public int hate_power_value;
		public int heal_power_diff;
		public int heal_power_rate;
		public int heal_power_value;
		public int hp_regen;
		public int hp_regen_combat;
		public int int_aoe_defend_damage_reduce_rate;
		public int int_aoe_defend_power_value;
		public int int_attack_concentrate_value;
		public int int_attack_counter_damage_rate;
		public int int_attack_critical_damage_rate;
		public int int_attack_critical_rate;
		public int int_attack_critical_value;
		public int int_attack_damage_modify_diff;
		public int int_attack_damage_modify_rate;
		public int int_attack_defend_pierce_rate;
		public int int_attack_hit_rate;
		public int int_attack_hit_value;
		public int int_attack_parry_pierce_rate;
		public int int_attack_perfect_parry_damage_rate;
		public int int_attack_pierce_value;
		public int int_attack_power_value;
		public int int_attack_stiff_duration_level;
		public int int_counter_damage_reduce_rate;
		public int int_defend_critical_damage_rate;
		public int int_defend_critical_rate;
		public int int_defend_critical_value;
		public int int_defend_damage_modify_diff;
		public int int_defend_damage_modify_rate;
		public int int_defend_dodge_rate;
		public int int_defend_dodge_value;
		public int int_defend_parry_rate;
		public int int_defend_parry_reduce_rate;
		public int int_defend_parry_value;
		public int int_defend_physical_damage_reduce_rate;
		public int int_defend_power_value;
		public int int_defend_stiff_duration_level;
		public int int_hate_power_rate;
		public int int_hate_power_value;
		public int int_heal_power_diff;
		public int int_heal_power_rate;
		public int int_heal_power_value;
		public long int_hp_regen;
		public long int_hp_regen_combat;
		public long int_max_hp;
		public int int_perfect_parry_damage_reduce_rate;
		public int max_hp;
		public int pc_attack_power_value;
		public int pc_defend_power_rate;
		public int pc_defend_power_value;
		public int perfect_parry_damage_reduce_rate;
		public int pve_boss_level_npc_attack_power_equip_min_and_max;
		public int pve_boss_level_npc_defend_power_equip_value;
		public int pvp_attack_power_equip_min_and_max;
		public int pvp_defend_power_equip_value;

		public static Ability operator +(Ability a, Ability b) => new()
		{
			abnormal_attack_power_rate = a.abnormal_attack_power_rate + b.abnormal_attack_power_rate,
			abnormal_attack_power_value = a.abnormal_attack_power_value + b.abnormal_attack_power_value,
			abnormal_attack_power_value_equip = a.abnormal_attack_power_value_equip + b.abnormal_attack_power_value_equip,
			abnormal_defend_power_rate = a.abnormal_defend_power_rate + b.abnormal_defend_power_rate,
			abnormal_defend_power_value = a.abnormal_defend_power_value + b.abnormal_defend_power_value,
			aoe_defend_damage_reduce_rate = a.aoe_defend_damage_reduce_rate + b.aoe_defend_damage_reduce_rate,
			aoe_defend_power_value = a.aoe_defend_power_value + b.aoe_defend_power_value,
			attack_attribute_rate = a.attack_attribute_rate + b.attack_attribute_rate,
			attack_attribute_value = a.attack_attribute_value + b.attack_attribute_value,
			attack_attribute_value_equip = a.attack_attribute_value_equip + b.attack_attribute_value_equip,
			attack_concentrate_value = a.attack_concentrate_value + b.attack_concentrate_value,
			attack_counter_damage_rate = a.attack_counter_damage_rate + b.attack_counter_damage_rate,
			attack_critical_damage_rate = a.attack_critical_damage_rate + b.attack_critical_damage_rate,
			attack_critical_damage_value = a.attack_critical_damage_value + b.attack_critical_damage_value,
			attack_critical_damage_value_equip = a.attack_critical_damage_value_equip + b.attack_critical_damage_value_equip,
			attack_critical_rate = a.attack_critical_rate + b.attack_critical_rate,
			attack_critical_value = a.attack_critical_value + b.attack_critical_value,
			attack_damage_modify_diff = a.attack_damage_modify_diff + b.attack_damage_modify_diff,
			attack_damage_modify_rate = a.attack_damage_modify_rate + b.attack_damage_modify_rate,
			attack_defend_pierce_rate = a.attack_defend_pierce_rate + b.attack_defend_pierce_rate,
			attack_hit_rate = a.attack_hit_rate + b.attack_hit_rate,
			attack_hit_value = a.attack_hit_value + b.attack_hit_value,
			attack_parry_pierce_rate = a.attack_parry_pierce_rate + b.attack_parry_pierce_rate,
			attack_perfect_parry_damage_rate = a.attack_perfect_parry_damage_rate + b.attack_perfect_parry_damage_rate,
			attack_pierce_value = a.attack_pierce_value + b.attack_pierce_value,
			attack_power_value = a.attack_power_value + b.attack_power_value,
			attack_stiff_duration_level = a.attack_stiff_duration_level + b.attack_stiff_duration_level,
			boss_attack_power_value = a.boss_attack_power_value + b.boss_attack_power_value,
			boss_defend_power_rate = a.boss_defend_power_rate + b.boss_defend_power_rate,
			boss_defend_power_value = a.boss_defend_power_value + b.boss_defend_power_value,
			counter_damage_reduce_rate = a.counter_damage_reduce_rate + b.counter_damage_reduce_rate,
			defend_critical_damage_rate = a.defend_critical_damage_rate + b.defend_critical_damage_rate,
			defend_critical_rate = a.defend_critical_rate + b.defend_critical_rate,
			defend_critical_value = a.defend_critical_value + b.defend_critical_value,
			defend_damage_modify_diff = a.defend_damage_modify_diff + b.defend_damage_modify_diff,
			defend_damage_modify_rate = a.defend_damage_modify_rate + b.defend_damage_modify_rate,
			defend_dodge_rate = a.defend_dodge_rate + b.defend_dodge_rate,
			defend_dodge_value = a.defend_dodge_value + b.defend_dodge_value,
			defend_parry_rate = a.defend_parry_rate + b.defend_parry_rate,
			defend_parry_reduce_rate = a.defend_parry_reduce_rate + b.defend_parry_reduce_rate,
			defend_parry_value = a.defend_parry_value + b.defend_parry_value,
			defend_physical_damage_reduce_rate = a.defend_physical_damage_reduce_rate + b.defend_physical_damage_reduce_rate,
			defend_power_value = a.defend_power_value + b.defend_power_value,
			defend_stiff_duration_level = a.defend_stiff_duration_level + b.defend_stiff_duration_level,
			guard_gauge = a.guard_gauge + b.guard_gauge,
			hate_power_rate = a.hate_power_rate + b.hate_power_rate,
			hate_power_value = a.hate_power_value + b.hate_power_value,
			heal_power_diff = a.heal_power_diff + b.heal_power_diff,
			heal_power_rate = a.heal_power_rate + b.heal_power_rate,
			heal_power_value = a.heal_power_value + b.heal_power_value,
			hp_regen = a.hp_regen + b.hp_regen,
			hp_regen_combat = a.hp_regen_combat + b.hp_regen_combat,
			int_aoe_defend_damage_reduce_rate = a.int_aoe_defend_damage_reduce_rate + b.int_aoe_defend_damage_reduce_rate,
			int_aoe_defend_power_value = a.int_aoe_defend_power_value + b.int_aoe_defend_power_value,
			int_attack_concentrate_value = a.int_attack_concentrate_value + b.int_attack_concentrate_value,
			int_attack_counter_damage_rate = a.int_attack_counter_damage_rate + b.int_attack_counter_damage_rate,
			int_attack_critical_damage_rate = a.int_attack_critical_damage_rate + b.int_attack_critical_damage_rate,
			int_attack_critical_rate = a.int_attack_critical_rate + b.int_attack_critical_rate,
			int_attack_critical_value = a.int_attack_critical_value + b.int_attack_critical_value,
			int_attack_damage_modify_diff = a.int_attack_damage_modify_diff + b.int_attack_damage_modify_diff,
			int_attack_damage_modify_rate = a.int_attack_damage_modify_rate + b.int_attack_damage_modify_rate,
			int_attack_defend_pierce_rate = a.int_attack_defend_pierce_rate + b.int_attack_defend_pierce_rate,
			int_attack_hit_rate = a.int_attack_hit_rate + b.int_attack_hit_rate,
			int_attack_hit_value = a.int_attack_hit_value + b.int_attack_hit_value,
			int_attack_parry_pierce_rate = a.int_attack_parry_pierce_rate + b.int_attack_parry_pierce_rate,
			int_attack_perfect_parry_damage_rate = a.int_attack_perfect_parry_damage_rate + b.int_attack_perfect_parry_damage_rate,
			int_attack_pierce_value = a.int_attack_pierce_value + b.int_attack_pierce_value,
			int_attack_power_value = a.int_attack_power_value + b.int_attack_power_value,
			int_attack_stiff_duration_level = a.int_attack_stiff_duration_level + b.int_attack_stiff_duration_level,
			int_counter_damage_reduce_rate = a.int_counter_damage_reduce_rate + b.int_counter_damage_reduce_rate,
			int_defend_critical_damage_rate = a.int_defend_critical_damage_rate + b.int_defend_critical_damage_rate,
			int_defend_critical_rate = a.int_defend_critical_rate + b.int_defend_critical_rate,
			int_defend_critical_value = a.int_defend_critical_value + b.int_defend_critical_value,
			int_defend_damage_modify_diff = a.int_defend_damage_modify_diff + b.int_defend_damage_modify_diff,
			int_defend_damage_modify_rate = a.int_defend_damage_modify_rate + b.int_defend_damage_modify_rate,
			int_defend_dodge_rate = a.int_defend_dodge_rate + b.int_defend_dodge_rate,
			int_defend_dodge_value = a.int_defend_dodge_value + b.int_defend_dodge_value,
			int_defend_parry_rate = a.int_defend_parry_rate + b.int_defend_parry_rate,
			int_defend_parry_reduce_rate = a.int_defend_parry_reduce_rate + b.int_defend_parry_reduce_rate,
			int_defend_parry_value = a.int_defend_parry_value + b.int_defend_parry_value,
			int_defend_physical_damage_reduce_rate = a.int_defend_physical_damage_reduce_rate + b.int_defend_physical_damage_reduce_rate,
			int_defend_power_value = a.int_defend_power_value + b.int_defend_power_value,
			int_defend_stiff_duration_level = a.int_defend_stiff_duration_level + b.int_defend_stiff_duration_level,
			int_hate_power_rate = a.int_hate_power_rate + b.int_hate_power_rate,
			int_hate_power_value = a.int_hate_power_value + b.int_hate_power_value,
			int_heal_power_diff = a.int_heal_power_diff + b.int_heal_power_diff,
			int_heal_power_rate = a.int_heal_power_rate + b.int_heal_power_rate,
			int_heal_power_value = a.int_heal_power_value + b.int_heal_power_value,
			int_hp_regen = a.int_hp_regen + b.int_hp_regen,
			int_hp_regen_combat = a.int_hp_regen_combat + b.int_hp_regen_combat,
			int_max_hp = a.int_max_hp + b.int_max_hp,
			int_perfect_parry_damage_reduce_rate = a.int_perfect_parry_damage_reduce_rate + b.int_perfect_parry_damage_reduce_rate,
			max_hp = a.max_hp + b.max_hp,
			pc_attack_power_value = a.pc_attack_power_value + b.pc_attack_power_value,
			pc_defend_power_rate = a.pc_defend_power_rate + b.pc_defend_power_rate,
			pc_defend_power_value = a.pc_defend_power_value + b.pc_defend_power_value,
			perfect_parry_damage_reduce_rate = a.perfect_parry_damage_reduce_rate + b.perfect_parry_damage_reduce_rate,
			pve_boss_level_npc_attack_power_equip_min_and_max = a.pve_boss_level_npc_attack_power_equip_min_and_max + b.pve_boss_level_npc_attack_power_equip_min_and_max,
			pve_boss_level_npc_defend_power_equip_value = a.pve_boss_level_npc_defend_power_equip_value + b.pve_boss_level_npc_defend_power_equip_value,
			pvp_attack_power_equip_min_and_max = a.pvp_attack_power_equip_min_and_max + b.pvp_attack_power_equip_min_and_max,
			pvp_defend_power_equip_value = a.pvp_defend_power_equip_value + b.pvp_defend_power_equip_value,
		};
	}

	public class PointAbility
	{
		public int attack_attribute_value;
		public int attack_power_value;
		public int defend_power_value;
		public int defense_point;
		public int max_hp;
		public int offense_point;
	}
}