package service

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"udp-server/server/pkg/metrics"

	"github.com/redis/go-redis/v9"
)

// CacheService 缓存服务接口
type CacheService interface {
	Set(key string, value interface{}) error
	Get(key string) (interface{}, error)
	Delete(key string) error
	BatchSet(items map[string]interface{}) error
	BatchGet(keys []string) (map[string]interface{}, error)
	BatchDelete(keys []string) error
	// Hash操作
	HSet(key, field string, value interface{}) error
	HGet(key, field string) (interface{}, error)
	HExists(key, field string) (bool, error)
	HIncrBy(key, field string, incr int64) (int64, error)
	HDel(key string, fields ...string) error
	// 分布式锁
	SetNX(key string, value interface{}, expiration time.Duration) (bool, error)
}

// RedisCache Redis缓存实现
type RedisCache struct {
	client     *redis.Client
	metrics    *metrics.Metrics
	maxRetries int
}

// NewRedisCache 创建Redis缓存实例
func NewRedisCache(addr string, password string, db int, metrics *metrics.Metrics) (*RedisCache, error) {
	client := redis.NewClient(&redis.Options{
		Addr:     addr,
		Password: password,
		DB:       db,
	})

	// 测试连接
	ctx := context.Background()
	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %v", err)
	}

	return &RedisCache{
		client:     client,
		metrics:    metrics,
		maxRetries: 3,
	}, nil
}

// Set 设置缓存
func (r *RedisCache) Set(key string, value interface{}) error {
	ctx := context.Background()
	data, err := json.Marshal(value)
	if err != nil {
		return err
	}

	var lastErr error
	for i := 0; i < r.maxRetries; i++ {
		err := r.client.Set(ctx, key, data, 0).Err()
		if err == nil {
			return nil
		}
		lastErr = err
		time.Sleep(time.Duration(i+1) * 100 * time.Millisecond)
	}
	return fmt.Errorf("failed after %d retries: %v", r.maxRetries, lastErr)
}

// Get 获取缓存
func (r *RedisCache) Get(key string) (interface{}, error) {
	ctx := context.Background()
	var lastErr error
	for i := 0; i < r.maxRetries; i++ {
		data, err := r.client.Get(ctx, key).Bytes()
		if err == nil {
			var value interface{}
			if err := json.Unmarshal(data, &value); err != nil {
				return nil, err
			}
			r.metrics.RecordCacheHit()
			return value, nil
		}
		lastErr = err
		time.Sleep(time.Duration(i+1) * 100 * time.Millisecond)
	}
	r.metrics.RecordCacheMiss()
	return nil, fmt.Errorf("failed after %d retries: %v", r.maxRetries, lastErr)
}

// Delete 删除缓存
func (r *RedisCache) Delete(key string) error {
	ctx := context.Background()
	var lastErr error
	for i := 0; i < r.maxRetries; i++ {
		err := r.client.Del(ctx, key).Err()
		if err == nil {
			return nil
		}
		lastErr = err
		time.Sleep(time.Duration(i+1) * 100 * time.Millisecond)
	}
	return fmt.Errorf("failed after %d retries: %v", r.maxRetries, lastErr)
}

// BatchSet 批量设置缓存
func (r *RedisCache) BatchSet(items map[string]interface{}) error {
	ctx := context.Background()
	pipe := r.client.Pipeline()
	for key, value := range items {
		data, err := json.Marshal(value)
		if err != nil {
			return err
		}
		pipe.Set(ctx, key, data, 0)
	}
	_, err := pipe.Exec(ctx)
	return err
}

// BatchGet 批量获取缓存
func (r *RedisCache) BatchGet(keys []string) (map[string]interface{}, error) {
	ctx := context.Background()
	pipe := r.client.Pipeline()
	cmds := make([]*redis.StringCmd, len(keys))
	for i, key := range keys {
		cmds[i] = pipe.Get(ctx, key)
	}
	_, err := pipe.Exec(ctx)
	if err != nil && err != redis.Nil {
		return nil, err
	}

	result := make(map[string]interface{})
	for i, cmd := range cmds {
		if cmd.Err() == redis.Nil {
			r.metrics.RecordCacheMiss()
			continue
		}
		data, err := cmd.Bytes()
		if err != nil {
			continue
		}
		var value interface{}
		if err := json.Unmarshal(data, &value); err != nil {
			continue
		}
		result[keys[i]] = value
		r.metrics.RecordCacheHit()
	}
	return result, nil
}

// BatchDelete 批量删除缓存
func (r *RedisCache) BatchDelete(keys []string) error {
	ctx := context.Background()
	return r.client.Del(ctx, keys...).Err()
}

// HSet 设置Hash字段
func (r *RedisCache) HSet(key, field string, value interface{}) error {
	ctx := context.Background()
	data, err := json.Marshal(value)
	if err != nil {
		return err
	}
	return r.client.HSet(ctx, key, field, data).Err()
}

// HGet 获取Hash字段
func (r *RedisCache) HGet(key, field string) (interface{}, error) {
	ctx := context.Background()
	data, err := r.client.HGet(ctx, key, field).Bytes()
	if err != nil {
		if err == redis.Nil {
			return nil, fmt.Errorf("field not found")
		}
		return nil, err
	}
	var value interface{}
	if err := json.Unmarshal(data, &value); err != nil {
		return nil, err
	}
	return value, nil
}

// HExists 检查Hash字段是否存在
func (r *RedisCache) HExists(key, field string) (bool, error) {
	ctx := context.Background()
	return r.client.HExists(ctx, key, field).Result()
}

// HIncrBy Hash字段增量操作
func (r *RedisCache) HIncrBy(key, field string, incr int64) (int64, error) {
	ctx := context.Background()
	return r.client.HIncrBy(ctx, key, field, incr).Result()
}

// HDel 删除Hash字段
func (r *RedisCache) HDel(key string, fields ...string) error {
	ctx := context.Background()
	return r.client.HDel(ctx, key, fields...).Err()
}

// SetNX 分布式锁设置
func (r *RedisCache) SetNX(key string, value interface{}, expiration time.Duration) (bool, error) {
	ctx := context.Background()
	data, err := json.Marshal(value)
	if err != nil {
		return false, err
	}
	return r.client.SetNX(ctx, key, data, expiration).Result()
}

// Close 关闭Redis连接
func (r *RedisCache) Close() error {
	return r.client.Close()
}

// LocalCache 本地缓存实现
type LocalCache struct {
	cache   *sync.Map
	metrics *metrics.Metrics
}

// NewLocalCache 创建本地缓存实例
func NewLocalCache(metrics *metrics.Metrics) *LocalCache {
	return &LocalCache{
		cache:   &sync.Map{},
		metrics: metrics,
	}
}

// Set 设置缓存
func (l *LocalCache) Set(key string, value interface{}) error {
	l.cache.Store(key, value)
	return nil
}

// Get 获取缓存
func (l *LocalCache) Get(key string) (interface{}, error) {
	if value, ok := l.cache.Load(key); ok {
		l.metrics.RecordCacheHit()
		return value, nil
	}
	l.metrics.RecordCacheMiss()
	return nil, fmt.Errorf("key not found")
}

// Delete 删除缓存
func (l *LocalCache) Delete(key string) error {
	l.cache.Delete(key)
	return nil
}

// BatchSet 批量设置缓存
func (l *LocalCache) BatchSet(items map[string]interface{}) error {
	for key, value := range items {
		l.cache.Store(key, value)
	}
	return nil
}

// BatchGet 批量获取缓存
func (l *LocalCache) BatchGet(keys []string) (map[string]interface{}, error) {
	result := make(map[string]interface{})
	for _, key := range keys {
		if value, ok := l.cache.Load(key); ok {
			result[key] = value
			l.metrics.RecordCacheHit()
		} else {
			l.metrics.RecordCacheMiss()
		}
	}
	return result, nil
}

// BatchDelete 批量删除缓存
func (l *LocalCache) BatchDelete(keys []string) error {
	for _, key := range keys {
		l.cache.Delete(key)
	}
	return nil
}

// HSet 设置Hash字段（本地缓存简单实现）
func (l *LocalCache) HSet(key, field string, value interface{}) error {
	hashKey := key + ":" + field
	l.cache.Store(hashKey, value)
	return nil
}

// HGet 获取Hash字段（本地缓存简单实现）
func (l *LocalCache) HGet(key, field string) (interface{}, error) {
	hashKey := key + ":" + field
	if value, ok := l.cache.Load(hashKey); ok {
		l.metrics.RecordCacheHit()
		return value, nil
	}
	l.metrics.RecordCacheMiss()
	return nil, fmt.Errorf("field not found")
}

// HExists 检查Hash字段是否存在（本地缓存简单实现）
func (l *LocalCache) HExists(key, field string) (bool, error) {
	hashKey := key + ":" + field
	_, ok := l.cache.Load(hashKey)
	return ok, nil
}

// HIncrBy Hash字段增量操作（本地缓存简单实现）
func (l *LocalCache) HIncrBy(key, field string, incr int64) (int64, error) {
	hashKey := key + ":" + field
	var current int64 = 0
	if value, ok := l.cache.Load(hashKey); ok {
		if v, ok := value.(int64); ok {
			current = v
		}
	}
	newValue := current + incr
	l.cache.Store(hashKey, newValue)
	return newValue, nil
}

// HDel 删除Hash字段（本地缓存简单实现）
func (l *LocalCache) HDel(key string, fields ...string) error {
	for _, field := range fields {
		hashKey := key + ":" + field
		l.cache.Delete(hashKey)
	}
	return nil
}

// SetNX 分布式锁设置（本地缓存简单实现，不支持真正的分布式锁）
func (l *LocalCache) SetNX(key string, value interface{}, expiration time.Duration) (bool, error) {
	if _, ok := l.cache.Load(key); ok {
		return false, nil // 已存在
	}
	l.cache.Store(key, value)
	// 注意：本地缓存不支持过期时间，这里只是简单实现
	return true, nil
}

// DualCache 双缓存服务
type DualCache struct {
	redisCache *RedisCache
	localCache *LocalCache
	useRedis   bool
	syncPeriod time.Duration
	stopChan   chan struct{}
	mu         sync.RWMutex
	metrics    *metrics.Metrics
}

// NewDualCache 创建双缓存服务实例
func NewDualCache(redisAddr string, redisPassword string, redisDB int, syncPeriod time.Duration, metrics *metrics.Metrics) (*DualCache, error) {
	redisCache, err := NewRedisCache(redisAddr, redisPassword, redisDB, metrics)
	if err != nil {
		return &DualCache{
			localCache: NewLocalCache(metrics),
			useRedis:   false,
			syncPeriod: syncPeriod,
			stopChan:   make(chan struct{}),
			metrics:    metrics,
		}, nil
	}

	return &DualCache{
		redisCache: redisCache,
		localCache: NewLocalCache(metrics),
		useRedis:   true,
		syncPeriod: syncPeriod,
		stopChan:   make(chan struct{}),
		metrics:    metrics,
	}, nil
}

// Set 设置缓存
func (d *DualCache) Set(key string, value interface{}) error {
	d.mu.Lock()
	defer d.mu.Unlock()

	if d.useRedis {
		if err := d.redisCache.Set(key, value); err != nil {
			d.useRedis = false
			return d.localCache.Set(key, value)
		}
		return nil
	}
	return d.localCache.Set(key, value)
}

// Get 获取缓存
func (d *DualCache) Get(key string) (interface{}, error) {
	d.mu.RLock()
	defer d.mu.RUnlock()

	if d.useRedis {
		value, err := d.redisCache.Get(key)
		if err != nil {
			d.useRedis = false
			return d.localCache.Get(key)
		}
		return value, nil
	}
	return d.localCache.Get(key)
}

// Delete 删除缓存
func (d *DualCache) Delete(key string) error {
	d.mu.Lock()
	defer d.mu.Unlock()

	if d.useRedis {
		if err := d.redisCache.Delete(key); err != nil {
			d.useRedis = false
			return d.localCache.Delete(key)
		}
		return nil
	}
	return d.localCache.Delete(key)
}

// BatchSet 批量设置缓存
func (d *DualCache) BatchSet(items map[string]interface{}) error {
	d.mu.Lock()
	defer d.mu.Unlock()

	if d.useRedis {
		if err := d.redisCache.BatchSet(items); err != nil {
			d.useRedis = false
			return d.localCache.BatchSet(items)
		}
		return nil
	}
	return d.localCache.BatchSet(items)
}

// BatchGet 批量获取缓存
func (d *DualCache) BatchGet(keys []string) (map[string]interface{}, error) {
	d.mu.RLock()
	defer d.mu.RUnlock()

	if d.useRedis && d.redisCache != nil {
		values, err := d.redisCache.BatchGet(keys)
		if err != nil {
			d.useRedis = false
			return d.localCache.BatchGet(keys)
		}
		return values, nil
	}
	return d.localCache.BatchGet(keys)
}

// BatchDelete 批量删除缓存
func (d *DualCache) BatchDelete(keys []string) error {
	d.mu.Lock()
	defer d.mu.Unlock()

	if d.useRedis {
		if err := d.redisCache.BatchDelete(keys); err != nil {
			d.useRedis = false
			return d.localCache.BatchDelete(keys)
		}
		return nil
	}
	return d.localCache.BatchDelete(keys)
}

// HSet 设置Hash字段
func (d *DualCache) HSet(key, field string, value interface{}) error {
	d.mu.Lock()
	defer d.mu.Unlock()

	if d.useRedis {
		if err := d.redisCache.HSet(key, field, value); err != nil {
			d.useRedis = false
			return d.localCache.HSet(key, field, value)
		}
		return nil
	}
	return d.localCache.HSet(key, field, value)
}

// HGet 获取Hash字段
func (d *DualCache) HGet(key, field string) (interface{}, error) {
	d.mu.RLock()
	defer d.mu.RUnlock()

	if d.useRedis {
		value, err := d.redisCache.HGet(key, field)
		if err != nil {
			d.useRedis = false
			return d.localCache.HGet(key, field)
		}
		return value, nil
	}
	return d.localCache.HGet(key, field)
}

// HExists 检查Hash字段是否存在
func (d *DualCache) HExists(key, field string) (bool, error) {
	d.mu.RLock()
	defer d.mu.RUnlock()

	if d.useRedis {
		exists, err := d.redisCache.HExists(key, field)
		if err != nil {
			d.useRedis = false
			return d.localCache.HExists(key, field)
		}
		return exists, nil
	}
	return d.localCache.HExists(key, field)
}

// HIncrBy Hash字段增量操作
func (d *DualCache) HIncrBy(key, field string, incr int64) (int64, error) {
	d.mu.Lock()
	defer d.mu.Unlock()

	if d.useRedis {
		result, err := d.redisCache.HIncrBy(key, field, incr)
		if err != nil {
			d.useRedis = false
			return d.localCache.HIncrBy(key, field, incr)
		}
		return result, nil
	}
	return d.localCache.HIncrBy(key, field, incr)
}

// HDel 删除Hash字段
func (d *DualCache) HDel(key string, fields ...string) error {
	d.mu.Lock()
	defer d.mu.Unlock()

	if d.useRedis {
		if err := d.redisCache.HDel(key, fields...); err != nil {
			d.useRedis = false
			return d.localCache.HDel(key, fields...)
		}
		return nil
	}
	return d.localCache.HDel(key, fields...)
}

// SetNX 分布式锁设置
func (d *DualCache) SetNX(key string, value interface{}, expiration time.Duration) (bool, error) {
	d.mu.Lock()
	defer d.mu.Unlock()

	if d.useRedis {
		result, err := d.redisCache.SetNX(key, value, expiration)
		if err != nil {
			d.useRedis = false
			return d.localCache.SetNX(key, value, expiration)
		}
		return result, nil
	}
	return d.localCache.SetNX(key, value, expiration)
}

// StartSync 启动同步
func (d *DualCache) StartSync(syncFunc func() error) {
	go func() {
		ticker := time.NewTicker(d.syncPeriod)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				if err := syncFunc(); err != nil {
					d.mu.Lock()
					d.useRedis = false
					d.mu.Unlock()
				} else {
					d.mu.Lock()
					if !d.useRedis {
						d.useRedis = true
					}
					d.mu.Unlock()
				}
			case <-d.stopChan:
				return
			}
		}
	}()
}

// StopSync 停止同步
func (d *DualCache) StopSync() {
	close(d.stopChan)
}

// 以下方法实现cache.Cache接口

// GetCache 获取缓存（实现cache.Cache接口）
func (d *DualCache) GetCache(key string, value interface{}) error {
	result, err := d.Get(key)
	if err != nil {
		return err
	}

	// 使用JSON反序列化到目标对象
	data, err := json.Marshal(result)
	if err != nil {
		return err
	}

	return json.Unmarshal(data, value)
}

// SetCache 设置缓存（实现cache.Cache接口）
func (d *DualCache) SetCache(key string, value interface{}, expiration time.Duration) error {
	// DualCache的Set方法不支持过期时间，这里忽略expiration参数
	return d.Set(key, value)
}

// DeleteCache 删除缓存（实现cache.Cache接口）
func (d *DualCache) DeleteCache(key string) error {
	return d.Delete(key)
}

// Close 关闭缓存连接（实现cache.Cache接口）
func (d *DualCache) Close() error {
	d.StopSync()
	if d.redisCache != nil {
		return d.redisCache.Close()
	}
	return nil
}
