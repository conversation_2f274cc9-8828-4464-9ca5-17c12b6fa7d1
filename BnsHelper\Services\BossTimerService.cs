using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Windows;
using System.Windows.Threading;
using Xylia.BnsHelper.Models;

namespace Xylia.BnsHelper.Services;

/// <summary>
/// BOSS倒计时器服务
/// </summary>
public class BossTimerService : IDisposable
{
    #region Singleton
    private static readonly Lazy<BossTimerService> _instance = new(() => new BossTimerService());
    public static BossTimerService Instance => _instance.Value;
    #endregion

    #region Fields
    private readonly Dictionary<int, BossTimer> _timers = new();
    private readonly DispatcherTimer _updateTimer;
    private readonly object _lockObject = new();
    #endregion

    #region Properties
    /// <summary>
    /// 活跃的倒计时器列表（按剩余时间排序）
    /// </summary>
    public ObservableCollection<BossTimer> ActiveTimers { get; } = new();

    /// <summary>
    /// 倒计时器更新事件
    /// </summary>
    public event EventHandler? TimersUpdated;
    #endregion

    #region Constructor
    private BossTimerService()
    {
        _updateTimer = new DispatcherTimer
        {
            Interval = TimeSpan.FromSeconds(1)
        };
        _updateTimer.Tick += OnUpdateTimerTick;
        _updateTimer.Start();
    }
    #endregion

    #region Methods
    /// <summary>
    /// 添加或更新BOSS倒计时器
    /// </summary>
    /// <param name="channel">频道号</param>
    /// <param name="type">计时器类型</param>
    public void AddOrUpdateTimer(int channel, BossTimerType type)
    {
        if (channel <= 0)
        {
            Debug.WriteLine($"[BossTimer] 无效的频道号: {channel}");
            return;
        }

        lock (_lockObject)
        {
            // 移除该频道的旧计时器
            if (_timers.ContainsKey(channel))
            {
                var oldTimer = _timers[channel];
                _timers.Remove(channel);

                // 从UI集合中移除
                try
                {
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        var uiTimer = ActiveTimers.FirstOrDefault(t => t.Channel == channel);
                        if (uiTimer != null)
                        {
                            ActiveTimers.Remove(uiTimer);
                        }
                    });
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"[BossTimer] UI更新失败: {ex.Message}");
                }
            }

            // 添加新计时器
            var newTimer = new BossTimer(channel, type);
            _timers[channel] = newTimer;

            try
            {
                Application.Current.Dispatcher.Invoke(() =>
                {
                    ActiveTimers.Add(newTimer);

                    // 如果有多个倒计时器，重新排序
                    if (ActiveTimers.Count > 1)
                    {
                        var sortedTimers = ActiveTimers
                            .OrderBy(t => t.State == BossTimerState.Finished ? 1 : 0) // 已完成的排在后面
                            .ThenBy(t => t.RemainingTime) // 然后按剩余时间排序
                            .ToList();
                        ActiveTimers.Clear();
                        foreach (var timer in sortedTimers)
                        {
                            ActiveTimers.Add(timer);
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[BossTimer] UI更新失败: {ex.Message}");
            }

            Debug.WriteLine($"[BossTimer] 添加倒计时器: 频道{channel}, 类型{type}, 持续{newTimer.TotalDuration}秒");
        }

        TimersUpdated?.Invoke(this, EventArgs.Empty);
    }

    /// <summary>
    /// 添加或更新BOSS倒计时器（根据服务器ID和区域ID自动确定类型）
    /// </summary>
    /// <param name="channel">频道号</param>
    /// <param name="serverId">服务器ID</param>
    /// <param name="zoneId">区域ID</param>
    /// <param name="isMutant">是否为变异体</param>
    public void AddOrUpdateTimer(int channel, int serverId, int zoneId, bool isMutant = false)
    {
        var timerType = DetermineTimerType(serverId, zoneId, isMutant);
        AddOrUpdateTimer(channel, timerType);
    }

    /// <summary>
    /// 根据服务器ID和区域ID确定BOSS倒计时器类型
    /// </summary>
    /// <param name="serverId">服务器ID</param>
    /// <param name="zoneId">区域ID</param>
    /// <param name="isMutant">是否为变异体</param>
    /// <returns>BOSS倒计时器类型</returns>
    private BossTimerType DetermineTimerType(int serverId, int zoneId, bool isMutant)
    {
        // 新服务器组 1001~1999
        if (serverId >= 1001 && serverId <= 1999)
        {
            // 特殊区域 5200/5295/5500
            if (zoneId == 5200 || zoneId == 5295 || zoneId == 5500)
            {
                return isMutant ? BossTimerType.ZTX_SpecialMutant : BossTimerType.ZTX_SpecialNormal;
            }
            else
            {
                // 新服务器组普通区域不会出现变异体
                return BossTimerType.ZTX_Normal;
            }
        }
        // 原服务器组 2001~2999
        else if (serverId >= 2001 && serverId <= 2999)
        {
            return isMutant ? BossTimerType.ZNCS_Mutant : BossTimerType.ZNCS_Normal;
        }
        else
        {
            // 默认使用原服务器组的逻辑
            return isMutant ? BossTimerType.ZNCS_Mutant : BossTimerType.ZNCS_Normal;
        }
    }

    /// <summary>
    /// 移除指定频道的倒计时器
    /// </summary>
    /// <param name="channel">频道号</param>
    public void RemoveTimer(int channel)
    {
        lock (_lockObject)
        {
            if (_timers.ContainsKey(channel))
            {
                _timers.Remove(channel);

                Application.Current.Dispatcher.Invoke(() =>
                {
                    var uiTimer = ActiveTimers.FirstOrDefault(t => t.Channel == channel);
                    if (uiTimer != null)
                    {
                        ActiveTimers.Remove(uiTimer);
                    }
                });

                Debug.WriteLine($"[BossTimer] 移除倒计时器: 频道{channel}");
            }
        }

        TimersUpdated?.Invoke(this, EventArgs.Empty);
    }

    /// <summary>
    /// 清除所有倒计时器
    /// </summary>
    public void ClearAllTimers()
    {
        lock (_lockObject)
        {
            _timers.Clear();
            Application.Current.Dispatcher.Invoke(() =>
            {
                ActiveTimers.Clear();
            });
            Debug.WriteLine("[BossTimer] 清除所有倒计时器");
        }

        TimersUpdated?.Invoke(this, EventArgs.Empty);
    }

    /// <summary>
    /// 获取指定频道的倒计时器
    /// </summary>
    /// <param name="channel">频道号</param>
    /// <returns>倒计时器，如果不存在则返回null</returns>
    public BossTimer? GetTimer(int channel)
    {
        lock (_lockObject)
        {
            return _timers.TryGetValue(channel, out var timer) ? timer : null;
        }
    }

    /// <summary>
    /// 获取所有活跃的倒计时器
    /// </summary>
    /// <returns>活跃的倒计时器列表</returns>
    public List<BossTimer> GetActiveTimers()
    {
        lock (_lockObject)
        {
            return _timers.Values.Where(t => !t.IsExpired).ToList();
        }
    }
    #endregion

    #region Private Methods
    /// <summary>
    /// 重新排序活跃倒计时器（按剩余时间升序）
    /// </summary>
    private void SortActiveTimers()
    {
        try
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                if (ActiveTimers.Count <= 1) return;

                // 获取当前排序后的倒计时器列表
                // 排序规则：已完成的排在最后，活跃的按剩余时间排序
                var sortedTimers = ActiveTimers
                    .OrderBy(t => t.State == BossTimerState.Finished ? 1 : 0) // 已完成的排在后面
                    .ThenBy(t => t.RemainingTime) // 然后按剩余时间排序
                    .ToList();

                // 检查是否需要重新排序
                bool needsReordering = false;
                for (int i = 0; i < sortedTimers.Count; i++)
                {
                    if (i >= ActiveTimers.Count || ActiveTimers[i].Channel != sortedTimers[i].Channel)
                    {
                        needsReordering = true;
                        break;
                    }
                }

                // 如果需要重新排序，则重新排列集合
                if (needsReordering)
                {
                    // 使用Move方法来重新排序，避免清空和重新添加
                    for (int i = 0; i < sortedTimers.Count; i++)
                    {
                        var targetTimer = sortedTimers[i];
                        var currentIndex = ActiveTimers.IndexOf(targetTimer);
                        if (currentIndex != i && currentIndex >= 0)
                        {
                            ActiveTimers.Move(currentIndex, i);
                        }
                    }

                    #if DEBUG
                    Debug.WriteLine($"[BossTimer] 倒计时器已重新排序: {string.Join(", ", ActiveTimers.Select(t => $"频道{t.Channel}({t.RemainingTime}s)"))}");
                    #endif
                }
            });
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[BossTimer] 排序失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 更新计时器
    /// </summary>
    private void OnUpdateTimerTick(object? sender, EventArgs e)
    {
        lock (_lockObject)
        {
            // 更新所有活跃倒计时器的UI显示
            foreach (var timer in _timers.Values)
            {
                if (!timer.IsExpired)
                {
                    timer.NotifyPropertyChanged();
                }
            }

            // 移除已过期的计时器
            var expiredChannels = _timers.Where(kvp => kvp.Value.IsExpired)
                                        .Select(kvp => kvp.Key)
                                        .ToList();

            foreach (var channel in expiredChannels)
            {
                _timers.Remove(channel);
                Debug.WriteLine($"[BossTimer] 倒计时器已过期: 频道{channel}");
            }

            // 在UI线程上移除过期的UI元素
            if (expiredChannels.Count > 0)
            {
                Application.Current.Dispatcher.Invoke(() =>
                {
                    foreach (var channel in expiredChannels)
                    {
                        var uiTimer = ActiveTimers.FirstOrDefault(t => t.Channel == channel);
                        if (uiTimer != null)
                        {
                            ActiveTimers.Remove(uiTimer);
                        }
                    }
                });
            }

            // 重新排序倒计时器（按剩余时间升序）
            if (_timers.Count > 1)
            {
                SortActiveTimers();
            }

            // 如果有计时器被移除，触发更新事件
            if (expiredChannels.Count > 0)
            {
                TimersUpdated?.Invoke(this, EventArgs.Empty);
            }
        }
    }
    #endregion

    #region IDisposable
    public void Dispose()
    {
        if (_updateTimer != null)
        {
            _updateTimer.Stop();
            _updateTimer.Tick -= OnUpdateTimerTick;
        }

        ClearAllTimers();
    }
    #endregion
}
