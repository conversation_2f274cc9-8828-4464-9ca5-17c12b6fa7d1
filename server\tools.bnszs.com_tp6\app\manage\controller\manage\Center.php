<?php

namespace app\manage\controller\manage;

use app\common\controller\BaseController;
use think\App;
use think\Request;
use think\facade\Db;
use think\facade\Session;
use think\facade\View;
use think\facade\Cache;
use think\Exception;
use app\manage\model\User as User;
use app\manage\model\CDkey as CDkey;
use app\manage\model\Lucky as Lucky;

class Center extends BaseController 
{
    private $uid = 0;

    public function __construct(App $app, Request $request) {
        parent::__construct($app, $request);
        
        $this->uid = Session::get("user");
        if($this->uid == 0 || $this->uid == null) {
            return redirect("/bns/manage/login?callback=/bns/manage/center");
        }
    }

    /**
     * 用户中心主页
     */
    public function index() {
        $user = User::find($this->uid);
        $stats = $this->getUserStats();
        
        return View::fetch('Manage/center_main', [
            'user' => $user,
            'stats' => $stats
        ]);
    }

    /**
     * 主要功能处理
     */
    public function main() {
        if ($this->request->isPost()) {
            try {
                $mode = $this->request->post('mode', '');
                $data = $this->request->post('data', '');
                
                switch ($mode) {
                    case "cdkey": 
                        $result = CDkey::Activate($data, $this->uid);
                        return json(['code' => 1, 'msg' => "激活成功", 'data' => $result]);

                    case "name":
                        User::ModifyName($this->uid, $data);
                        return json(['code' => 1, 'msg' => "用户名修改成功"]);

                    case "password": 
                        User::ModifyPassword($this->uid, $data);
                        Session::clear();
                        return json(['code' => 1, 'msg' => "密码修改成功，请重新登录"]);

                    case "token": 
                        $token = User::CreateToken($this->uid);
                        return json(['code' => 1, 'msg' => "Token生成成功", 'data' => $token]);

                    case "lucky":
                        $result = Lucky::Draw($this->uid);
                        return json(['code' => 1, 'msg' => "抽奖成功", 'data' => $result]);

                    default:
                        return json(['code' => 0, 'msg' => "无效的操作"]);
                }
            } catch (Exception $e) {
                return json(['code' => 0, 'msg' => $e->getMessage()]);
            }
        }

        return $this->index();
    }

    /**
     * CDKey激活
     */
    public function activateCDKey() {
        if ($this->request->isPost()) {
            $cdkey = $this->request->post('cdkey', '');
            
            if (empty($cdkey)) {
                return json(['code' => 0, 'msg' => 'CDKey不能为空']);
            }

            try {
                $result = CDkey::Activate($cdkey, $this->uid);
                return json(['code' => 1, 'msg' => 'CDKey激活成功', 'data' => $result]);
            } catch (Exception $e) {
                return json(['code' => 0, 'msg' => $e->getMessage()]);
            }
        }

        return View::fetch('Manage/activate_cdkey');
    }

    /**
     * 修改用户名
     */
    public function changeName() {
        if ($this->request->isPost()) {
            $newName = $this->request->post('new_name', '');
            
            if (empty($newName)) {
                return json(['code' => 0, 'msg' => '新用户名不能为空']);
            }

            if (strlen($newName) < 3 || strlen($newName) > 20) {
                return json(['code' => 0, 'msg' => '用户名长度必须在3-20个字符之间']);
            }

            try {
                User::ModifyName($this->uid, $newName);
                return json(['code' => 1, 'msg' => '用户名修改成功']);
            } catch (Exception $e) {
                return json(['code' => 0, 'msg' => $e->getMessage()]);
            }
        }

        $user = User::find($this->uid);
        return View::fetch('Manage/change_name', ['user' => $user]);
    }

    /**
     * 修改密码
     */
    public function changePassword() {
        if ($this->request->isPost()) {
            $oldPassword = $this->request->post('old_password', '');
            $newPassword = $this->request->post('new_password', '');
            $confirmPassword = $this->request->post('confirm_password', '');
            
            if (empty($oldPassword) || empty($newPassword) || empty($confirmPassword)) {
                return json(['code' => 0, 'msg' => '所有字段都不能为空']);
            }

            if ($newPassword !== $confirmPassword) {
                return json(['code' => 0, 'msg' => '两次输入的新密码不一致']);
            }

            if (strlen($newPassword) < 6) {
                return json(['code' => 0, 'msg' => '新密码长度不能少于6位']);
            }

            try {
                // 验证旧密码
                $user = User::find($this->uid);
                if (!password_verify($oldPassword, $user['password'])) {
                    return json(['code' => 0, 'msg' => '原密码错误']);
                }

                User::ModifyPassword($this->uid, $newPassword);
                Session::clear();
                return json(['code' => 1, 'msg' => '密码修改成功，请重新登录']);
            } catch (Exception $e) {
                return json(['code' => 0, 'msg' => $e->getMessage()]);
            }
        }

        return View::fetch('Manage/change_password');
    }

    /**
     * 生成API Token
     */
    public function generateToken() {
        try {
            $token = User::CreateToken($this->uid);
            return json(['code' => 1, 'msg' => 'Token生成成功', 'data' => $token]);
        } catch (Exception $e) {
            return json(['code' => 0, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 抽奖功能
     */
    public function lucky() {
        if ($this->request->isPost()) {
            try {
                $result = Lucky::Draw($this->uid);
                return json(['code' => 1, 'msg' => '抽奖成功', 'data' => $result]);
            } catch (Exception $e) {
                return json(['code' => 0, 'msg' => $e->getMessage()]);
            }
        }

        $luckyInfo = Lucky::getUserInfo($this->uid);
        return View::fetch('Manage/lucky', ['lucky_info' => $luckyInfo]);
    }

    /**
     * 我的CDKey记录
     */
    public function myCDKeys() {
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 20);
        
        $cdkeys = CDkey::getUserCDKeys($this->uid, $page, $limit);
        
        return View::fetch('Manage/my_cdkeys', [
            'cdkeys' => $cdkeys
        ]);
    }

    /**
     * 账户安全
     */
    public function security() {
        $user = User::find($this->uid);
        $loginLogs = $this->getLoginLogs();
        
        return View::fetch('Manage/security', [
            'user' => $user,
            'login_logs' => $loginLogs
        ]);
    }

    /**
     * 个人设置
     */
    public function settings() {
        if ($this->request->isPost()) {
            $settings = $this->request->post();
            
            try {
                User::updateSettings($this->uid, $settings);
                return json(['code' => 1, 'msg' => '设置保存成功']);
            } catch (Exception $e) {
                return json(['code' => 0, 'msg' => $e->getMessage()]);
            }
        }

        $user = User::find($this->uid);
        return View::fetch('Manage/user_settings', ['user' => $user]);
    }

    /**
     * 获取用户统计信息
     */
    private function getUserStats() {
        return [
            'total_cdkeys' => CDkey::getUserCDKeyCount($this->uid),
            'used_cdkeys' => CDkey::getUserUsedCDKeyCount($this->uid),
            'lucky_points' => Lucky::getUserPoints($this->uid),
            'login_count' => User::getLoginCount($this->uid),
            'last_login' => User::getLastLoginTime($this->uid)
        ];
    }

    /**
     * 获取登录日志
     */
    private function getLoginLogs($limit = 10) {
        return Db::name('user_login_logs')
            ->where('user_id', $this->uid)
            ->order('login_time DESC')
            ->limit($limit)
            ->select();
    }

    /**
     * 账户注销
     */
    public function deleteAccount() {
        if ($this->request->isPost()) {
            $password = $this->request->post('password', '');
            $confirm = $this->request->post('confirm', '');
            
            if ($confirm !== 'DELETE') {
                return json(['code' => 0, 'msg' => '请输入DELETE确认删除']);
            }

            try {
                $user = User::find($this->uid);
                if (!password_verify($password, $user['password'])) {
                    return json(['code' => 0, 'msg' => '密码错误']);
                }

                User::deleteAccount($this->uid);
                Session::clear();
                return json(['code' => 1, 'msg' => '账户删除成功']);
            } catch (Exception $e) {
                return json(['code' => 0, 'msg' => $e->getMessage()]);
            }
        }

        return View::fetch('Manage/delete_account');
    }

    /**
     * 数据导出
     */
    public function exportData() {
        try {
            $data = User::exportUserData($this->uid);
            
            header('Content-Type: application/json');
            header('Content-Disposition: attachment; filename="user_data_' . date('Y-m-d') . '.json"');
            
            echo json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            exit;
        } catch (Exception $e) {
            return json(['code' => 0, 'msg' => $e->getMessage()]);
        }
    }
}
