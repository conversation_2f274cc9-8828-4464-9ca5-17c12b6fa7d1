<?php

namespace app\ingame\controller\Character;

use app\common\controller\BaseController;
use think\App;
use think\Request;
use think\facade\Validate;
use think\facade\Session;
use voku\helper\HtmlDomParser;

class RedirectBase extends BaseController
{
    //当前重定向主域名
    public $globalbnsDomain = 'https://';
    
    //当前服务器ID
    public $globalserverId = 1911;
    
    //当前大区ID
    public $globalareaId = 0;
    
    //当前查看的角色名称
    public $roleName = "";

    public function __construct(App $app, Request $request) {
        parent::__construct($app, $request);
    }

    //初始化重定向参数
    public function InitRedirectParm() {
        //获取提交参数
        $this->roleName = urldecode($this->request->get('c', null));
        $this->globalserverId = $this->request->get('s', $this->globalserverId);
        $this->globalareaId = self::serverId2AreaId($this->globalserverId);
        $this->globalbnsDomain = sprintf('https://%dgate.bns.qq.com', $this->globalareaId);
    }
    
    //执行重定向或代理请求 - 兼容TP5的实现方式
    public function RedirectUrl($jumpaddress = null, $domain = false, $proxy = false) {
        //获取提交参数
        $this->InitRedirectParm();

        //返回请求链接
        if(empty($jumpaddress)) $jumpaddress = $this->request->url();

        //传递参数，对于已存在参数的路径不进行传递
        else if(strpos($jumpaddress,'&') === false) {
            $jumpaddress .= substr(strrchr($this->request->url(),'?'),0);
        }

        //拼接完整链接
        $url = $domain ? $jumpaddress : ($this->globalbnsDomain . $jumpaddress);

        if($domain) return $url;

        // 如果是代理模式，直接返回curl结果（类似TP5）
        if($proxy) {
            $stateCode = null;
            $result = self::curlGetForm($url, $stateCode, $this->request->header('Cookie'));

            // 记录调试信息
            trace("RedirectUrl - URL: " . $url, 'info');
            trace("RedirectUrl - HTTP Code: " . $stateCode, 'info');

            if($result === false || $stateCode !== 200) {
                trace("RedirectUrl - Failed with code: " . $stateCode, 'error');
                return response(json_encode(['error' => 'Failed to fetch data', 'http_code' => $stateCode]), 503, [
                    'Access-Control-Allow-Origin' => '*',
                    'Content-Type' => 'application/json; charset=utf-8'
                ]);
            }

            // 直接返回原始结果，设置CORS头
            return response($result, 200, [
                'Access-Control-Allow-Origin' => '*',
                'Access-Control-Allow-Methods' => 'GET, POST, OPTIONS',
                'Access-Control-Allow-Headers' => 'Content-Type',
                'Content-Type' => 'application/json; charset=utf-8'
            ]);
        }

        //执行重定向
        header("Location: $url");
        exit;
    }

    //代理请求到BNS API - 解决CORS问题
    public function ProxyUrl($jumpaddress = null) {
        return $this->RedirectUrl($jumpaddress, false, true);
    }
    
    //服务器ID转大区ID
    public static function serverId2AreaId($serverId) {
        return substr($serverId, 0, 2);
    }
    
    //大区ID转服务器名称
    public static function areaId2Name($areaId) {
        $areaNames = [
            1 => '电信一区',
            2 => '电信二区', 
            3 => '电信三区',
            4 => '电信四区',
            5 => '联通一区',
            6 => '联通二区',
            7 => '联通三区',
            8 => '联通四区'
        ];
        
        return $areaNames[$areaId] ?? '未知大区';
    }

}
