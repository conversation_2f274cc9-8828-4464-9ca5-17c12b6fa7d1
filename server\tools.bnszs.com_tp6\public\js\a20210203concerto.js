console.clear();

console.group("%c剑灵小助手","color:#009a61; font-size: 36px; font-weight: 400");

// 个人获奖记录初始化
amsCfg_741865 = {
    'activityId': '361653', // AMS活动号
    "flowId": "741865",  //流程号
    "moduleId": "391935",// 模块实例号
    "extParam": {"pageNo": 1, "pageSize": 10},
    //提交成功的回调 （必选）
    'success': function (data) {
        //获取模块接口对象
        var giftModule = window['myGiftList_741865'];
        need(["util.jquery", "util.template", "biz.widget.base"], function ($, TemplateManager, widget) {
            //如果第一次点击，则先显示弹出框，在渲染页面
            if ($("#tbody_741865").length == 0) {
                widget.dialog({
                    "content": $("#tpl_dialog_741865").html(),
                    width: 600,
                    topOffset: -200,
                    "complete": function () {
                        plan(data);
                        giftModule.renderPanel(data);
                    }
                });
            } else {
                //如果是翻页的情况，直接渲染页面
                plan(data);
                giftModule.renderPanel(data);
            }
        });
    }
};


function log(t){
    console.log(t['dtGetPackageTime'] + "   QQ:"+t['sExtend5'] + "  游戏Id:"+t['sRoleName'] + "   助力:%c " + t['iPackageNum']+"","color:red;font-size:16px;");
}
function A(data){
    var d = data['myGiftList'];
    d.forEach(function (item) {
        if (item['sPackageName'] === "助力值") {
            log(item);
        }
    });
}
function B(data){
    var d = data['myGiftList'];
    var thisObj = [];
    d.forEach(function (item) {
        if (item['sPackageName'] === "助力值") {
            thisObj.push({time:item['dtGetPackageTime'],qq:item['sExtend5'],gameName:item['sRoleName'],number:item['iPackageNum']})
        }
    });
    if (thisObj.length === 0) {
        console.info("当前页暂无数据，请翻页");
    }else{
        console.table(thisObj);
    }
　　
}

function plan(data){
    //A(data);
    B(data);
}

amsSubmit(361653, 741865);