<?php
namespace app\ingame\model;

use think\facade\Db;
use think\facade\Cache;

class BnsTop
{
    /**
     * 获取战力排行榜
     */
    public static function GetPowerTop($count = 100) {
        $ranking = Db::query("
            SELECT name,worldid,iuin,job,level,mastery_level,newpcid,allscore as score
            FROM bnsuserinfo
            ORDER BY allscore DESC
            LIMIT $count");

       return $ranking;
    }

    /**
     * 获取全区每个职业第一
     */
    static public function GetJobTop() {
        $jobTop = Db::query("SELECT i.allscore,i.name,i.worldid,i.job FROM(
            SELECT max(allscore) allscore,job FROM bnsuserinfo GROUP BY job
            )a,bnsuserinfo i WHERE i.allscore = a.allscore and i.job =a.job ORDER BY i.job");
        if(!empty($jobTop)){
           return $jobTop;
        }else{
           return;
        }
    }

    /**
     * 获取角色装备分数 - 高性能优化版本
     */
    static public function GetAllscore($serverId, $roleName)
    {
        // 先尝试直接查询指定服务器的角色（最快）
        $allscore = Db::query("
            SELECT allscore,worldid,newpcid,name,job
            FROM bnsuserinfo
            WHERE worldid = ? AND name = ?
            ORDER BY allscore DESC
            LIMIT 1", [$serverId, $roleName]);

        // 如果直接查询没有结果，再查询同大区的角色
        if(empty($allscore)) {
            // 先获取服务器前缀，然后使用 LIKE 查询（避免函数索引问题）
            $serverPrefix = Db::query("
                SELECT LEFT(newpcid,2) as prefix
                FROM bnsuserinfo
                WHERE worldid = ?
                LIMIT 1", [$serverId]);

            if (!empty($serverPrefix)) {
                $prefix = $serverPrefix[0]['prefix'];
                $allscore = Db::query("
                    SELECT allscore,worldid,newpcid,name,job
                    FROM bnsuserinfo
                    WHERE newpcid LIKE ? AND name = ?
                    ORDER BY allscore DESC
                    LIMIT 1", [$prefix . '%', $roleName]);
            }
        }

        if(!empty($allscore)){
            return $allscore[0];
        }else{
            return null;
        }
    }

    /**
     * 获取分数排名_高性能优化版本
     */
    static public function GetAllRankingByAllscore_new($allscore, $newpcid = null, $worldid = null, $job = null)
    {
        // 构建优化的WHERE条件，使用 LIKE 替代 LEFT 函数以提高索引效率
        $conditions = ["allscore > ?"];
        $params = [$allscore];

        if (!empty($newpcid)) {
            $prefix = substr($newpcid, 0, 2);
            $conditions[] = "newpcid LIKE ?";
            $params[] = $prefix . '%';
        }

        if (!empty($worldid)) {
            $prefix = substr($worldid, 0, 2);
            $conditions[] = "worldid LIKE ?";
            $params[] = $prefix . '%';
        }

        if (!empty($job)) {
            $conditions[] = "job = ?";
            $params[] = $job;
        }

        $whereClause = implode(' AND ', $conditions);

        // 使用原生SQL进行高性能查询
        $result = Db::query("SELECT ? as allscore, COUNT(*) + 1 as zbrank
                            FROM bnsuserinfo
                            WHERE {$whereClause}", array_merge([$allscore], $params));

        if (!empty($result)) {
            return [
                'allscore' => (int)$allscore,
                'zbrank' => (int)$result[0]['zbrank']
            ];
        }

        return [
            'allscore' => (int)$allscore,
            'zbrank' => 1
        ];
    }

    /**
     * 获取角色排名 - 高性能缓存优化版本
     */
    static public function GetAllRankingByIDName($serverId, $roleName, $newpcid = null, $worldid = null, $job = null)
    {
        // 多级缓存策略：内存缓存 + Redis Hash
        static $memoryCache = [];
        $memoryKey = "{$serverId}_{$roleName}";

        // 第一级：内存缓存（进程内缓存，最快）
        if (isset($memoryCache[$memoryKey])) {
            $cacheData = $memoryCache[$memoryKey];
            if ($cacheData['expire'] > time()) {
                return $cacheData['data'];
            }
            unset($memoryCache[$memoryKey]);
        }

        // 第二级：Redis Hash 缓存
        $hashKey = "ranking_server_{$serverId}";
        $field = $roleName;

        $cached = Cache::hGet($hashKey, $field);
        if ($cached !== false && $cached !== null) {
            $data = json_decode($cached, true);
            // 同时写入内存缓存（5分钟）
            $memoryCache[$memoryKey] = [
                'data' => $data,
                'expire' => time() + 300
            ];
            return $data;
        }

        // 缓存未命中，执行数据库查询
        $allscore = static::GetAllscore($serverId, $roleName);

        if(!empty($allscore)){
            $ranking = static::GetAllRankingByAllscore_new(
                $allscore['allscore'],
                empty($newpcid) ? $allscore['newpcid'] : $newpcid,
                empty($worldid) ? $allscore['worldid'] : $worldid,
                empty($job) ? $allscore['job'] : $job
            );

            if (!empty($ranking)) {
                // 写入 Redis Hash 缓存（1小时）
                Cache::hSet($hashKey, $field, json_encode($ranking));
                Cache::expire($hashKey, 3600);

                // 写入内存缓存（5分钟）
                $memoryCache[$memoryKey] = [
                    'data' => $ranking,
                    'expire' => time() + 300
                ];
            }

            return $ranking;
        }else{
            return null;
        }
    }

    /**
     * 批量获取服务器角色排名 - Redis Hash 批量操作
     */
    static public function GetBatchRankingByIDNames($serverId, $roleNames)
    {
        if (empty($roleNames) || !is_array($roleNames)) {
            return [];
        }

        $hashKey = "ranking_server_{$serverId}";
        $results = [];
        $missedRoles = [];

        // 批量从 Redis Hash 获取
        $cachedData = \think\facade\Cache::hMGet($hashKey, $roleNames);

        foreach ($roleNames as $roleName) {
            if (isset($cachedData[$roleName]) && $cachedData[$roleName] !== false) {
                $results[$roleName] = json_decode($cachedData[$roleName], true);
            } else {
                $missedRoles[] = $roleName;
            }
        }

        // 处理缓存未命中的角色
        if (!empty($missedRoles)) {
            $batchData = [];
            foreach ($missedRoles as $roleName) {
                $allscore = static::GetAllscore($serverId, $roleName);
                if (!empty($allscore)) {
                    $ranking = static::GetAllRankingByAllscore_new(
                        $allscore['allscore'],
                        $allscore['newpcid'],
                        $allscore['worldid'],
                        $allscore['job']
                    );
                    if (!empty($ranking)) {
                        $results[$roleName] = $ranking;
                        $batchData[$roleName] = json_encode($ranking);
                    }
                } else {
                    $results[$roleName] = null;
                }
            }

            // 批量写入 Redis Hash
            if (!empty($batchData)) {
                Cache::hMSet($hashKey, $batchData);
                Cache::expire($hashKey, 3600);
            }
        }

        return $results;
    }

    /**
     * 清除服务器排名缓存
     */
    static public function clearServerRankingCache($serverId)
    {
        $hashKey = "ranking_server_{$serverId}";
        return Cache::delete($hashKey);
    }

    /**
     * 清除特定角色排名缓存
     */
    static public function clearRoleRankingCache($serverId, $roleName)
    {
        $hashKey = "ranking_server_{$serverId}";
        return Cache::hDel($hashKey, $roleName);
    }

    /**
     * 高性能角色排名查询 - 专为频繁查询优化
     */
    static public function GetRankingFast($serverId, $roleName)
    {
        // 使用预编译查询和索引优化
        $sql = "
            SELECT
                u1.allscore,
                (SELECT COUNT(*) FROM bnsuserinfo u2
                 WHERE u2.allscore > u1.allscore
                 AND (u2.worldid = ? OR LEFT(u2.newpcid,2) = LEFT(u1.newpcid,2))
                ) + 1 as zbrank,
                u1.worldid,
                u1.newpcid,
                u1.name,
                u1.job
            FROM bnsuserinfo u1
            WHERE (u1.worldid = ? OR LEFT(u1.newpcid,2) = (
                SELECT LEFT(newpcid,2) FROM bnsuserinfo WHERE worldid = ? LIMIT 1
            ))
            AND u1.name = ?
            ORDER BY u1.allscore DESC
            LIMIT 1
        ";

        $result = Db::query($sql, [$serverId, $serverId, $serverId, $roleName]);

        if (!empty($result)) {
            return [
                'allscore' => $result[0]['allscore'],
                'zbrank' => $result[0]['zbrank']
            ];
        }

        return null;
    }

    /**
     * 获取数据库连接统计信息（用于性能监控）
     */
    static public function getDbStats()
    {
        $stats = Db::query("SHOW STATUS LIKE 'Connections'");
        $queries = Db::query("SHOW STATUS LIKE 'Questions'");

        return [
            'connections' => $stats[0]['Value'] ?? 0,
            'queries' => $queries[0]['Value'] ?? 0,
            'timestamp' => time()
        ];
    }
}
