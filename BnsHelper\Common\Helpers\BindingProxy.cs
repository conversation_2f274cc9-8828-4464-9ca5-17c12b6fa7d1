﻿using System.Windows;

namespace Xylia.BnsHelper.Common.Helpers;
public class BindingProxy : Freezable
{
	protected override Freezable CreateInstanceCore()
	{
		return new BindingProxy();
	}

	public static readonly DependencyProperty DataProperty = DependencyProperty.Register("Data",
		typeof(object), typeof(BindingProxy), new UIPropertyMetadata(null));

	public object Data
	{
		get { return (object)GetValue(DataProperty); }
		set { SetValue(DataProperty, value); }
	}
}
