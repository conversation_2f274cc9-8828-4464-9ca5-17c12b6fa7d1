{layout name="manage/template" /}
<style>
.admin-content {
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.page-header {
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.page-header h2 {
    margin: 0;
    color: #333;
    font-size: 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.online-count {
    font-size: 14px;
    color: #666;
    background: #f8f9fa;
    padding: 5px 10px;
    border-radius: 4px;
}

.user-form {
    max-width: 600px;
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
}

.form-control {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    transition: all 0.3s;
}

.form-control:focus {
    border-color: #00a8ff;
    box-shadow: 0 0 5px rgba(0,168,255,0.2);
}

.btn-group {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.btn {
    padding: 10px 20px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    transition: all 0.3s;
}

.btn-primary {
    background: #00a8ff;
    color: #fff;
}

.btn-primary:hover {
    background: #0097e6;
}

.modal-content {
    border-radius: 8px;
}

.modal-header {
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.modal-body .form-group {
    margin-bottom: 20px;
}

.tutorial-section {
    margin-top: 40px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.tutorial-section h3 {
    color: #333;
    margin-bottom: 20px;
}

.tutorial-content {
    color: #666;
    line-height: 1.6;
}

.tutorial-step {
    margin-bottom: 20px;
    padding: 15px;
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.tutorial-step h4 {
    color: #00a8ff;
    margin-bottom: 10px;
}

.tutorial-step p {
    margin-bottom: 10px;
}

.tutorial-note {
    background: #fff3cd;
    color: #856404;
    padding: 10px;
    border-radius: 4px;
    margin-top: 15px;
}

/* 自定义layer弹窗样式 */
.layui-layer-custom {
    background-color: #fff;
    border-radius: 8px;
}

.layui-layer-custom .layui-layer-title {
    background: #0e90d2 !important;
    color: #fff !important;
    border-radius: 8px 8px 0 0;
    border-bottom: none;
}

.layui-layer-custom .layui-layer-btn {
    border-top: 1px solid #eee;
    background: #f8f9fa;
    border-radius: 0 0 8px 8px;
}

.layui-layer-custom .layui-layer-btn a {
    background: #0e90d2;
    border-color: #0e90d2;
    color: #fff;
}

.layui-layer-custom .layui-layer-btn .layui-layer-btn1 {
    background: #fff;
    color: #0e90d2;
    border-color: #0e90d2;
}

.layui-layer-custom .layui-layer-content {
    padding: 20px 20px 20px 45px !important;
    position: relative;
}

.layui-layer-custom .layui-layer-ico {
    left: 15px !important;
}

.layui-layer-custom .layui-layer-close {
    color: #fff;
}

.form-value {
    background: #f8f9fa;
    padding: 10px 15px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    color: #333;
    font-size: 15px;
    min-height: 42px;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.form-value:hover {
    background: #fff;
    border-color: #0e90d2;
    box-shadow: 0 0 0 2px rgba(14,144,210,0.1);
}
</style>

<div class="admin-content">
	<div class="page-header">
		<h2>
			用户中心
			<span class="online-count">在线用户数：{$online}</span>
		</h2>
	</div>

	<div class="user-form">
		<div class="form-group">
			<label class="form-label">站点账号</label>
			<div class="form-value">{$user->uid}</div>
		</div>
		<div class="form-group">
			<label class="form-label">昵称/QQ</label>
			<div class="form-value">{$user->name}</div>
		</div>
		<div class="form-group">
			<label class="form-label">绑定的游戏账号</label>
			<div class="form-value">{$user->uin}</div>
		</div>
		<!--<div class="btn-group">-->
		<!--	<button type="button" class="btn btn-primary" onclick="javascript:submitDraw()">每日签到</button>-->
		<!--	<button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#profile">修改密码</button>-->
  <!--          {if $old}<button type="button" class="btn btn-primary" onclick="javascript:submitToken()">获得登录凭证</button>{/if}-->
		<!--</div>-->

		<div class="form-group" style="margin-top: 20px;">
			<label class="form-label">CDkey</label>
			<div class="input-group">
				<input type="text" id="cdkey" class="form-control" placeholder="输入CDkey">
				<button type="button" class="btn btn-primary" onclick="javascript:submitCDkey()">提交</button>
			</div>
			<small id="cdkey_msg" class="form-text text-muted"></small>
		</div>
	</div>

	<!-- 教程部分 -->
	<div class="tutorial-section">
	    <h2>剑灵小助手 官网 -> <a href="//www.bnszs.com" target="_blank">www.bnszs.com</a> <h2>
		<h3>剑灵小助手3.0 (外置秒伤工具)使用指南</h3>
		<div class="tutorial-content">
            <div class="tutorial-step">
                <p> 新版剑灵小助手处于半成品状态，难以保证用户体验。</p>
                <p> 而且毕竟是用爱发电，更新时间无法固定，无法估量真正完善版本需要的时间。</p>
                <p> 但是又有很多人说想用，外加先前部分人的恶意诋毁。</p>
                <p> 因此我们暂时决定需要一定权限才能使用，3月1日起签到规则将会变更为2+3。</p>
            </div>

			<div class="tutorial-step">
				<h4>1. 账号设置与权限获取</h4>
				<p>1. 成功登录后，请先修改密码</p>
				<p>2. 点击"签到"按钮：</p>
				<p> ● 连续签到2天，有99%概率获得3天秒伤使用权限，如果未中奖可以再点一次</p>
				<p> ● 后续每天坚持签到可延续权限</p>
				<p> ● 签到获取到权限后可以直接生成【登录凭证】</p>
				<p> ● 获得【登录凭证】后可以跳过步骤2直接查看3</p>
			</div>

			<div class="tutorial-step">
				<h4>2. 有CDK如何权限激活与使用</h4>
				<p>1. 在CDK栏中输入CDkey并提交</p>
				<p>2. 点击"登录凭证"，系统会自动复制到剪贴板</p>
			</div>

			<div class="tutorial-step">
				<h4>3. 剑灵小助手配置</h4>
				<p>1. 打开剑灵小助手</p>
				<p>2. 输入用户名（即站点账号）和登录凭证（注意：不是网页密码）</p>
				<p> ● 若非最新版，登录后会自动更新</p>
				<p> ● 若提示服务器拒绝登录，请重新下载最新版本</p>
                <p> ● 若出现无限更新现象，原因是自动更新时不会删除旧版本程序，请到原安装目录启动 "剑灵小助手.exe"</p>
			</div>

			<div class="tutorial-step">
				<h4>4. 游戏文件配置</h4>
				<p class="text-danger"><strong>一定一定一定一定要关掉游戏再修改！！！！</strong></p>
				<p>1. 点击左下角设置按钮</p>
				<p>2. 点击右侧放大镜，选择剑灵怀旧服文件夹</p>
				<p>3. 返回初始页面，选择需要修改的内容</p>
				<p>4. 点击右上角"修改"按钮，等待"修改成功"提示</p>
			</div>

			<div class="tutorial-step">
				<h4>5. 游戏内使用说明</h4>
				<p>1. 进入游戏后，点击左侧最后一个按钮查看秒伤</p>
				<p>2. 在白青山脉英雄副本与极限挑战副本中，会显示全队秒伤（以百分比形式呈现）</p>
                <p>3. 将鼠标悬停在玩家名称上方，可显示详细统计信息</p>
				<p>4. 通过设置按钮可查看历史秒伤记录</p>
				<p>5. 不要开着秒伤到处跑，会崩溃 （如频繁出现此现象，请在设置中取消勾选 "允许剑灵小助手向您发送消息"）</p>
			</div>

			<div class="tutorial-note">
				<h4>6. 注意事项</h4>
				<p>● 本工具为免费软件，由雪姨用爱发电</p>
				<p>● 请勿以恶意揣测开发者意图</p>
				<p>● 如对安全性存疑，请不要使用</p>
				<p>● 雪姨小姐姐刚接触做这个项目，已经很棒了！</p>
			</div>
		</div>
	</div>
</div>

<!-- 公告 -->
<div class="modal" id="notice">
	<div class="modal-dialog modal-dialog-centered">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title" id="myModalLabel">通知</h4>
				<button type="button" class="btn-close" data-bs-dismiss="modal"></button>
			</div>
			<div class="modal-body">
			</div>
			<div class="modal-footer">
				<button type="button" class="am-btn am-btn-primary" data-bs-dismiss="modal">
					<span class="glyphicon glyphicon-ok-circle"></span> 我知道了
				</button>
			</div>
		</div>
	</div>
</div>
<!-- 个人资料 -->
<div class="modal fade" id="profile">
	<div class="modal-dialog modal-dialog-centered">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">修改密码 (<span style="color:red;font-weight:bold;">请勿在本站使用常用敏感密码</span>)</h4>
				<button type="button" class="btn-close" data-bs-dismiss="modal"></button>
			</div>
			<div class="modal-body">
				<div class="form-group">
					<label>密码</label>
					<input type="text" class="form-control" id="password" placeholder="输入密码" value>
				</div>
				<div class="form-group">
					<label>确认密码</label>
					<input type="text" class="form-control" id="password2" placeholder="再次输入密码" value>
				</div>
			</div>
			<div class="modal-footer">
				<button type="button" class="am-btn am-btn-default" data-bs-dismiss="modal">
					<span class="glyphicon glyphicon-remove"></span> 关闭
				</button>
				<button type="button" id="btn_submit" class="am-btn am-btn-primary" data-bs-dismiss="modal" onclick="submitPass();">
					<span class="glyphicon glyphicon-floppy-disk"></span> 提交
				</button>
			</div>
		</div>
	</div>
</div>

<script>

layer.alert("新版本3.1.0 支持巅峰服，打开助手官网下载即可使用!");

function showNotice($html) {
    layer.open({
        type: 1,
        title: '通知',
        closeBtn: 1,
        shadeClose: false,
        skin: 'layui-layer-custom',
        btn: ['我知道了'],
        content: '<div style="padding: 20px;">' + $html + '</div>'
    });
}

function submitPass() {
    layer.confirm('即将修改站内密码，是否确认?', {
        btn: ['确认','取消'],
        title: '确认修改',
        skin: 'layui-layer-custom'
    }, function(index){
        $password = $("#profile #password").val();
        if ($password == '') { 
            layer.alert('密码不能为空', {
                skin: 'layui-layer-custom',
                closeBtn: 1,
                anim: 1,
                icon: 2,
                btn: ['确定']
            });
            return; 
        }
        else if ($password != $("#profile #password2").val()) { 
            layer.alert('前后输入不一致', {
                skin: 'layui-layer-custom',
                closeBtn: 1,
                anim: 1,
                icon: 2,
                btn: ['确定']
            });
            return; 
        }
        
        $.ajax({
            type: "POST",  
            dataType: "json", 
            url: window.location.href, 
            data: { "mode": "password", "data": $password },
            success: function (result) {
                layer.alert('您的密码已成功修改为' + $password, {
                    skin: 'layui-layer-custom',
                    closeBtn: 1,
                    anim: 1,
                    icon: 1,
                    btn: ['确定']
                }, function(){
                    location.reload();
                });
            },
            error: function() {
                layer.alert('提交失败,未知错误', {
                    skin: 'layui-layer-custom',
                    closeBtn: 1,
                    anim: 1,
                    icon: 2,
                    btn: ['确定']
                });
            }
        });
        layer.close(index);
    });
}

function submitName() {
    layer.confirm('即将修改账号昵称，是否确认提交?', {
        btn: ['确认','取消'],
        title: '确认修改'
    }, function(index){
        $.ajax({
            type: "POST",  
            dataType: "json", 
            url: window.location.href, 
            data: { "mode": "name", "data": $("#name2").val() },
            success: function (result) {
                layer.alert('修改成功', {
                    skin: 'layui-layer-custom',
                    closeBtn: 1,
                    anim: 1,
                    icon: 1,
                    btn: ['确定']
                }, function(){
                    location.reload();
                });
            },
            error: function() {
                layer.alert('提交失败,未知错误', {
                    skin: 'layui-layer-custom',
                    closeBtn: 1,
                    anim: 1,
                    icon: 2,
                    btn: ['确定']
                });
            }
        });
        layer.close(index);
    });
}
    
function submitCDkey() {
    layer.confirm('即将尝试激活，是否确认提交?', {
        btn: ['确认','取消'],
        title: '确认激活'
    }, function(index){
        $.ajax({
            type: "POST",  
            dataType: "json",  
            url: window.location.href, 
            data: { "mode": "cdkey", "data": $("#cdkey").val() },
            success: function (result) {
                layer.alert(result.msg, {
                    skin: 'layui-layer-custom',
                    closeBtn: 1,
                    anim: 1,
                    icon: result.code == 1 ? 1 : 2,
                    btn: ['确定']
                });
            },
            error: function() {
                layer.alert('提交失败,未知错误', {
                    skin: 'layui-layer-custom',
                    closeBtn: 1,
                    anim: 1,
                    icon: 2,
                    btn: ['确定']
                });
            }
        });
        layer.close(index);
    });
}

function submitToken() {
    $.ajax({
        type: "POST",  
        dataType: "json",  
        url: window.location.href, 
        data: { "mode": "token" },
        success: function (result) {
            if(result.code == 0) {
                layer.alert(result.msg, {
                    skin: 'layui-layer-custom',
                    closeBtn: 1,
                    anim: 1,
                    icon: 2,
                    btn: ['确定']
                }, function(){
                    window.location.reload();
                });
                return;
            }

            var aux = document.createElement("input"); 
            aux.setAttribute("value", result.data); 
            document.body.appendChild(aux); 
            aux.select();
            document.execCommand("copy");
            document.body.removeChild(aux);

            layer.alert("本次的登录凭证已复制到粘贴板<br>请复制到剑灵小助手3.0以上版本的密码框部分<br>过期时间为 " + new Date(result.time * 1000).toLocaleString() + "，过期后无法继续登录。", {
                skin: 'layui-layer-custom',
                closeBtn: 1,
                anim: 1,
                icon: 1,
                btn: ['确定']
            });
        },
        error: function() {
            layer.alert('提交失败,未知错误', {
                skin: 'layui-layer-custom',
                closeBtn: 1,
                anim: 1,
                icon: 2,
                btn: ['确定']
            });
        }
    });
}

function submitDraw() {
    $.ajax({
        type: "POST",  
        dataType: "json",  
        url: window.location.href, 
        data: { "mode": "draw", "type": 1 },
        success: function (result) {
            var msg = result.msg;
            if (result.count != null && result.count != 0) 
                msg += "（剩余额外" + result.count + "次）";

            layer.alert(msg, {
                skin: 'layui-layer-custom',
                closeBtn: 1,
                anim: 1,
                icon: result.code == 1 ? 1 : 2,
                btn: ['确定']
            });
        },
        error: function() {
            layer.alert('提交失败,未知错误', {
                skin: 'layui-layer-custom',
                closeBtn: 1,
                anim: 1,
                icon: 2,
                btn: ['确定']
            });
        }
    });
}

function getStorage(name) {
    return localStorage.getItem(name);
}

function setStorage(name, value) {
    localStorage.setItem(name, value);
}

function showDailyNotice() {
    var today = new Date().toISOString().split('T')[0];
    var lastShow = getStorage('lastShowNotice');
    var notShowToday = getStorage('notShowTodayNotice');
    
    if (lastShow !== today && notShowToday !== today) {
        layer.open({
            type: 1,
            title: '每日使用提示',
            closeBtn: 1,
            shadeClose: false,
            skin: 'layui-layer-custom',
            btn: ['我知道了', '今日不再提示'],
            content: '<div style="padding: 20px;">' +
                    '<h4 style="color: #0e90d2; margin-bottom: 15px;">欢迎使用剑灵小助手</h4>' +
                    '<p style="margin-bottom: 10px;">1. 通过各种途径获得使用权限后</p>' +
                    '<p style="margin-bottom: 10px;">2. 在小助手3.0以上版本[账号框]输入当前账号</p>' +
                    '<p style="margin-bottom: 10px;">3. 点击<span style="color:red;font-weight:bold;">[获得登录凭证]并复制到[密码框]</span></p>' +
                    '<p style="margin-bottom: 10px;">4. 完成以上步骤即可使用</p>' +
                    '<div style="margin-top: 15px; padding: 10px; background: #fff3cd; color: #856404; border-radius: 4px;">' +
                    '注意：请勿将账号借给他人使用，如有异常将被永久封禁</div>' +
                    '</div>',
            yes: function(index){
                // 记录今天已经显示过
                setStorage('lastShowNotice', today);
                layer.close(index);
            },
            btn2: function(index){
                // 设置今天不再显示
                setStorage('notShowTodayNotice', today);
                layer.close(index);
                return false;
            }
        });
    }
}

// 页面加载完成后显示每日提示
$(document).ready(function() {
    showDailyNotice();
});
</script>