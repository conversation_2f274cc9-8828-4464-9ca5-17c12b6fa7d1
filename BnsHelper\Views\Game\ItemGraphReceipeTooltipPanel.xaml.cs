﻿using System.Windows;
using System.Windows.Media;
using Xylia.Preview.Data.Models;
using Xylia.Preview.UI.Controls;
using Xylia.Preview.UI.Extensions;
using static Xylia.Preview.Data.Models.ItemImprove;
using static Xylia.Preview.Data.Models.ItemImproveOptionList;

namespace Xylia.Preview.UI.GameUI.Scene.Game_Tooltip2;
public partial class ItemGraphReceipeTooltipPanel
{
	public ItemGraphReceipeTooltipPanel()
	{
		InitializeComponent();
	}

	#region Methods
	protected override void OnInitialized(EventArgs e)
	{
		base.OnInitialized(e);

		ItemGraphReceipeTooltipPanel_2.Visibility =
		ItemGraphReceipeTooltipPanel_3.Visibility =
		ItemGraphReceipeTooltipPanel_4.Visibility = Visibility.Collapsed;
	}

	protected override void OnDataChanged(DependencyPropertyChangedEventArgs e)
	{
		if (e.NewValue is not ItemGraph.Edge edge) return;

		// ItemGraphReceipeTooltipPanel_1 
		var MainItem = edge.Recipe.MainItem!;
		ItemGraphReceipeTooltipPanel_1_Title.String.LabelText = edge.Title;
		ItemGraphReceipeTooltipPanel_1_ItemIcon.SetExpansionImageProperties("IconImage", MainItem.FrontIcon?.GetImage());
		ItemGraphReceipeTooltipPanel_1_ItemIcon.SetExpansionImageProperties("UnusableImage", MainItem.UnusableImage);
		ItemGraphReceipeTooltipPanel_1_ItemIcon.SetExpansionVisibleFlag("SearchedImage", false);
		ItemGraphReceipeTooltipPanel_1_ItemIcon.SetExpansionImageProperties("CanSaleItem", MainItem.CanSaleItemImage);
		ItemGraphReceipeTooltipPanel_1_ItemIcon.SetExpansionVisibleFlag("SymbolImage", false);
		ItemGraphReceipeTooltipPanel_1_ItemIcon.SetExpansionVisibleFlag("SymbolImage_Chacked", false);
		ItemGraphReceipeTooltipPanel_1_ItemIcon.SetExpansionText("StackableLabel", edge.Recipe.MainItemCount.ToString());
		ItemGraphReceipeTooltipPanel_1_ItemIcon_Name.String.LabelText = edge.Recipe.MainItem.ItemNameOnly;
		ItemGraphReceipeTooltipPanel_1_ItemIcon_Desc.String.LabelText = null;
		ItemGraphReceipeTooltipPanel_1_Price.String.LabelText = edge.Recipe.Money.Money;
		ItemGraphReceipeTooltipPanel_1_DiscountPrice.String.LabelText = (edge.Recipe.Money * ItemGraphRouteHelper.Discount).Money;
		ItemGraphReceipeTooltipPanel_1_Guide.String.LabelText = edge.Recipe.Guide + string.Format(" ({0:P0})" , edge.Recipe.Probability);
		ItemGraphReceipeTooltipPanel_Guide.SetVisiable(false);

		// MeterialItem
		var MeterialItemHolder = ItemGraphReceipeTooltipPanel_1_MeterialItemHolder;
		if (MeterialItemHolder != null)
		{
			for (int i = 0; i < 8; i++)
			{
				var item = edge.Recipe.SubItem[i];
				var count = edge.Recipe.SubItemCount[i];

				// check item
				var widget = MeterialItemHolder.GetChild<BnsCustomImageWidget>("ItemGraphReceipeTooltipPanel_1_MeterialItem_" + (i + 1), true)!;
				if (!widget.SetVisiable(item is not null)) continue;

				widget.Foreground = new SolidColorBrush(Colors.White);
				widget.SetExpansionImageProperties("IconImage", item!.FrontIcon?.GetImage());
				widget.SetExpansionImageProperties("UnusableImage", item.UnusableImage);
				widget.SetExpansionVisibleFlag("SearchedImage", false);
				widget.SetExpansionImageProperties("CanSaleItem", MainItem.CanSaleItemImage);
				widget.SetExpansionVisibleFlag("SymbolImage", false);
				widget.SetExpansionVisibleFlag("SymbolImage_Chacked", false);
				widget.SetExpansionText("StackableLabel", count.ToString());
			}
		}

		// ImproveOption 
		var startItem = edge.StartItem.Value;
		var ImproveId = startItem.Attributes.Get<int>("improve-id");
		var ImproveLevel = startItem.Attributes.Get<sbyte>("improve-level");
		var Improve = startItem.Provider.GetTable<ItemImprove>()[new ItemImproveKey(ImproveId, ImproveLevel)];
		if (Improve != null && Improve.SuccessOptionListId != 0)
		{
			var ImproveOptionList = startItem.Provider.GetTable<ItemImproveOptionList>()[new ItemImproveOptionListKey(Improve.SuccessOptionListId)];
			ItemGraphReceipeTooltipPanel_OptionList.ItemsSource = ImproveOptionList.GetOptions((sbyte)(ImproveLevel + 1));
		}
	}
	#endregion
}