﻿using System.IO;
using System.Net.Http;

namespace Xylia.BnsHelper.Common.Helpers;
internal static class HttpHelper
{
	public static async Task<Stream> DownloadAsync(string? url)
	{
		ArgumentNullException.ThrowIfNull(url);

		var client = new HttpClient();
		client.DefaultRequestHeaders.Add("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.63 Safari/537.36");

		var response = await client.GetAsync(url);
		response.EnsureSuccessStatusCode();

		return await response.Content.ReadAsStreamAsync();
	}
}
