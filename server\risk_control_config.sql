-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.1.1
-- https://www.phpmyadmin.net/
--
-- 主机： localhost
-- 生成日期： 2025-07-01 15:30:57
-- 服务器版本： 5.7.44-log
-- PHP 版本： 8.0.26

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `bns`
--

-- --------------------------------------------------------

--
-- 表的结构 `risk_control_config`
--

CREATE TABLE `risk_control_config` (
  `id` bigint(20) UNSIGNED NOT NULL COMMENT '主键ID',
  `config_key` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置键',
  `config_value` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置值',
  `description` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '配置描述',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='风控配置表';

--
-- 转存表中的数据 `risk_control_config`
--

INSERT INTO `risk_control_config` (`id`, `config_key`, `config_value`, `description`, `created_at`, `updated_at`) VALUES
(1, 'max_qq_per_device_per_day', '3', '同设备每日最大登录QQ数量', '2025-06-25 17:19:00', '2025-06-26 20:51:20'),
(2, 'max_qq_per_ip_per_day', '3', '同IP每日最大登录QQ数量', '2025-06-25 17:19:00', '2025-06-28 10:43:47'),
(3, 'max_login_attempts_per_hour', '20', '每小时最大登录尝试次数', '2025-06-25 17:19:00', '2025-06-25 17:19:00'),
(4, 'enable_new_device_check', 'true', '是否启用新设备检查', '2025-06-25 17:19:00', '2025-06-25 17:19:00'),
(5, 'enable_location_check', 'false', '是否启用异地登录检查', '2025-06-25 17:19:00', '2025-06-25 17:19:00'),
(6, 'enable_risk_control', 'true', '是否启用风控系统', '2025-06-25 17:19:00', '2025-06-25 17:19:00');

--
-- 转储表的索引
--

--
-- 表的索引 `risk_control_config`
--
ALTER TABLE `risk_control_config`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_config_key` (`config_key`);

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `risk_control_config`
--
ALTER TABLE `risk_control_config`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID', AUTO_INCREMENT=7;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
