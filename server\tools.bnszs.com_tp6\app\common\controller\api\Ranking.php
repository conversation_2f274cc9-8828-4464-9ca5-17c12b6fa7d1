<?php

namespace app\common\controller\api;

use app\common\controller\ApiBase;
use think\App;
use think\Request;
use think\facade\Cache;
use app\ingame\model\BnsTop;

class Ranking extends ApiBase
{
    public function __construct(App $app, Request $request) {
        parent::__construct($app, $request);
    }

    /**
     * 战力排行榜页面
     */
    public function PowerTop() {
        return view('Guidebook/bnsTop');
    }
    
    /**
     * 邀请排行榜页面
     */
    public function InviteTop() {
        return view('Guidebook/bnsInviteTop');
    }
    
    /**
     * 邀请排行榜JSON数据
     */
    public function InviteTopJson() {
        $InviteTopJsonStr = Cache::get("InviteTop");
        if ($InviteTopJsonStr != "") {
            $InviteTop_Array = json_decode($InviteTopJsonStr, true);
            return json($InviteTop_Array);
        }
        
        return json(['code' => 0, 'msg' => '暂无数据']);
    }
    
    /**
     * 战力排行榜测试接口
     */
    public function PowerTop_Test() {
        return json(['code' => 1, 'data' => BnsTop::GetPowerTop(1000)]);
    }
    
    /**
     * 获取战力排行榜数据
     */
    public function getPowerRanking() {
        $serverId = $this->request->param('server_id', 0);
        $areaId = $this->request->param('area_id', 0);
        $count = $this->request->param('count', 100);
        
        // 尝试从缓存获取
        $cacheKey = "power_ranking_{$serverId}_{$areaId}_{$count}";
        $ranking = Cache::get($cacheKey);
        
        if (!$ranking) {
            try {
                $ranking = BnsTop::GetPowerTop($count, $serverId, $areaId);
                Cache::set($cacheKey, $ranking, 300); // 缓存5分钟
            } catch (\Exception $e) {
                return json(['code' => 0, 'msg' => '获取排行榜失败', 'error' => $e->getMessage()]);
            }
        }
        
        return json(['code' => 1, 'data' => $ranking]);
    }
    
    /**
     * 获取职业排行榜
     */
    public function getJobRanking() {
        // 尝试从缓存获取
        $cacheKey = "job_ranking";
        $ranking = Cache::get($cacheKey);
        
        if (!$ranking) {
            try {
                $ranking = BnsTop::GetJobTop();
                Cache::set($cacheKey, $ranking, 600); // 缓存10分钟
            } catch (\Exception $e) {
                return json(['code' => 0, 'msg' => '获取职业排行榜失败', 'error' => $e->getMessage()]);
            }
        }
        
        return json(['code' => 1, 'data' => $ranking]);
    }
}
