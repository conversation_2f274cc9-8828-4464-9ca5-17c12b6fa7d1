namespace Xylia.BnsHelper.Models;
internal class ActivityInfo
{
    #region Fields
    /// <summary>
    /// 活动是否进行中
    /// </summary>
    public bool IsActive => DateTime.Now >= StartTime && DateTime.Now <= EndTime;

    /// <summary>
    /// 活动开始时间
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 活动结束时间
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 签到权限恢复时间
    /// </summary>
    public DateTime SignInResumeTime { get; set; }

    /// <summary>
    /// 活动标题
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// 活动描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
    #endregion

    #region Methods
    private static ActivityInfo? _instance;

    /// <summary>
    /// 获取活动信息
    /// </summary>
    public static ActivityInfo Instance
    {
        get
        {
            return _instance ??= new ActivityInfo
            {
                StartTime = new DateTime(2025, 6, 25, 0, 0, 0),
                EndTime = new DateTime(2025, 7, 10, 23, 59, 59),
                SignInResumeTime = new DateTime(2025, 7, 8, 12, 0, 0),
                Title = "战斗统计功能限时免费体验",
                Description = "🎉 战斗统计功能限时体验中！7月10日起恢复签到获得方式"
            };
        }
    }
    #endregion
}
