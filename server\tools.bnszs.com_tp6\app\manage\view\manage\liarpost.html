{layout name="manage/template" /}

<div class="admin-content">
	<div class="admin-content-body">
		<div class="am-cf am-padding am-padding-bottom-0">
			<div class="am-fl am-cf"><strong class="am-text-primary am-text-lg"><?php if(session('admin')) echo('骗子信息'); else echo('举报骗子'); ?></strong></div>
		</div>
		<hr />
		<div class="am-g">
		</div>
		<div class="am-u-sm-12 am-u-md-8 am-u-md-pull">
			<form class="am-form am-form-horizontal" action="" id="form" method="post" onsubmit="return toVaild()">
				<input type="hidden" name="id" value=<?=$liar_info['id']?>>
				<div class="am-form-group">
					<label for="server" class="am-u-sm-3 am-form-label">大区</label>
					<div class="am-select am-u-sm-9">
						<select id="areaSelect1" name="server">
						</select>
					</div>
				</div>
				<div class="am-form-group">
					<label for="area" class="am-u-sm-3 am-form-label">服务器</label>
					<div class="am-select am-u-sm-9">
						<select id="serverSelect1" name="area">
						</select>
						<small id="serverSelect1_msg"></small>
					</div>
				</div>
				<div class="am-form-group">
					<label for="username" class="am-u-sm-3 am-form-label">游戏ID</label>
					<div class="am-u-sm-9">
						<input type="text" id="rolename" name="rolename" value="<?php if(session('admin')) echo($liar_info['rolename']); ?>" placeholder="骗子的游戏昵称" required>
						<small id="rolename_msg"></small>
					</div>
				</div> <?php if(session('admin')) { ?> <div class="am-form-group">
					<label for="msg" class="am-u-sm-3 am-form-label">关联角色</label>
					<div class="am-u-sm-9">
						<ul id="searchinfo" style="list-style: none; ">
						</ul>
					</div>
				</div>
				<!-- 角色查询上红字显示骗子行为 -->
				<div class="am-form-group">
					<label for="msg" class="am-u-sm-3 am-form-label">简介</label>
					<div class="am-u-sm-9">
						<input type="text" id="msg" name="msg" value="<?=$liar_info['msg']?>" placeholder="用于在角色查询系统中显示，若不设置则显示行骗手段。">
					</div>
				</div> <?php } ?> <div class="am-form-group">
					<label for="gold" class="am-u-sm-3 am-form-label">受骗金币</label>
					<div class="am-u-sm-9">
						<input type="number" id="gold" name="gold" value="<?php if(session('admin')) echo($liar_info['gold']); ?>" placeholder="受骗金币 没有可以不填">
					</div>
				</div>
				<div class="am-form-group">
					<label for="money" class="am-u-sm-3 am-form-label">受骗金额</label>
					<div class="am-u-sm-9">
						<input type="number" id="money" name="money" value="<?php if(session('admin')) echo($liar_info['money']); ?>" placeholder="受骗金额 没有可以不填">
					</div>
				</div>
				<div class="am-form-group">
					<label for="qq" class="am-u-sm-3 am-form-label">骗子QQ号</label>
					<div class="am-u-sm-9">
						<input type="number" id="qq" name="qq" value="<?php if(session('admin')) echo($liar_info['qq']); ?>" placeholder="骗子QQ号 没有可以不填">
					</div>
				</div>
				<div class="am-form-group">
					<label for="wechat" class="am-u-sm-3 am-form-label">骗子微信号</label>
					<div class="am-u-sm-9">
						<input type="text" id="wechat" name="wechat" value="<?php if(session('admin')) echo($liar_info['wechat']); ?>" placeholder="骗子的微信号 没有可以不填">
					</div>
				</div>
				<div class="am-form-group">
					<label for="tel" class="am-u-sm-3 am-form-label">骗子手机号</label>
					<div class="am-u-sm-9">
						<input type="number" id="tel" name="tel" value="<?php if(session('admin')) echo($liar_info['tel']); ?>" placeholder="骗子的手机号码 没有可以不填">
					</div>
				</div>
				<div class="am-form-group">
					<label for="alipay" class="am-u-sm-3 am-form-label">骗子支付宝号</label>
					<div class="am-u-sm-9">
						<input type="text" id="alipay" name="alipay" value="<?php if(session('admin')) echo($liar_info['alipay']); ?>" placeholder="骗子的支付宝号码 没有可以不填">
					</div>
				</div>
				<div class="am-form-group">
					<label for="url" class="am-u-sm-3 am-form-label">证据链接</label>
					<div class="am-u-sm-9">
						<input type="text" id="url" name="url" class="js-pattern-url" value="<?php if(session('admin')) echo($liar_info['url']); ?>" placeholder="证据链接 可以在 www.bnszs.com 发布">
						<small id="url_msg"></small>
					</div>
				</div>
				<div class="am-form-group">
					<label for="technique" class="am-u-sm-3 am-form-label">行骗手段</label>
					<div class="am-u-sm-9">
						<textarea name="technique" id="technique" placeholder="请详细描述该骗子行骗手段，并填写骗子同账号下其它角色ID（可通过装备查询界面输入骗子ID查询）" required><?php if(session('admin')) echo($liar_info['technique']); ?></textarea>
					</div>
				</div>
				<div class="am-form-group">
					<label for="up" class="am-u-sm-3 am-form-label">举报人联系方式</label>
					<div class="am-u-sm-9">
						<input type="text" id="up" name="up" value="<?php if(session('admin')) echo($liar_info['up']); ?>" placeholder="请输入您的联系方式 一定要真实 必填 可以是QQ 微信 或者 YY等" required>
					</div>
				</div> <?php //if(session('admin')) { ?> <!-- 设置骗子类型 -->
				<div class="am-form-group" group="liarType">
					<label for="up" class="am-u-sm-3 am-form-label">类型</label>
					<div class="am-u-sm-9">
						<label style="margin-right:10px;"><input type="radio" name="type" value="1" checked>交易类别</label>
						<label style="margin-right:10px;"><input type="radio" name="type" value="2">副本类别</label>
					</div>
				</div> <?php //} ?> <div class="am-form-group">
					<label for="pics" class="am-u-sm-3 am-form-label">截图</label>
					<div class="am-u-sm-9">
						<textarea name="pics" id="pics" placeholder="截图 可以是行骗过程 可以是骗子装备 最好是游戏全屏截图"><?php if(session('admin')) echo($liar_info['pics']); ?></textarea>
						<small>上传图片：<a href="https://imgchr.com/" target="_blank">点开传图地址</a>--点Browse选择图片--点upload上传--选择复制HTML信息贴到这里即可</small>
					</div>
				</div>
				<div class="am-form-group">
					<div class="am-u-sm-9 am-u-sm-push-3">
						<button type="button" id="submit" disabled class="am-btn am-btn-primary">提交</button>
					</div>
				</div>
			</form>
		</div>
	</div>
</div>
<script src="//tools.bnszs.com/res/js/bns.server.js?v=2020050706"></script>
<script>
	//获取服务器信息
	initServer("areaSelect1","serverSelect1","<?=$liar_info['server']?>","<?=$liar_info['area_name']?>");
	
	$(".am-form-group[group=liarType] input[type=radio]").each(function(){
	    if($(this).val() == "<?=$liar_info['type']?>"){
	        $(this).attr('checked','true');   	
	    }
	});
	
	$("#rolename").blur(function(){
	  	var area_id = $("#serverSelect1").val();
	  	var rolename = $("#rolename").val();
	  	
	    if (area_id == null || area_id == undefined || area_id == '') {
	      $("#serverSelect1_msg").html("服务器还没有选择");
	      $("#serverSelect1_msg").css("color","red");
	      $('#submit').attr("disabled",true);
	      return false;
	    }else{
	    	$("#serverSelect1_msg").html("");
	      	$('#submit').attr("disabled",false);
	    }
	    
	    
	    if (rolename == ""){
	      $("#rolename_msg").html("请输入骗子昵称");
	      $("#rolename_msg").css("color","red");
	      $('#submit').attr("disabled",true);
	      return false;
	    }else{
	    	$("#rolename_msg").html("");
	      	$('#submit').attr("disabled",false);
	    }
		
	  	var url = "<a target='_blank' href='https://tools.bnszs.com/ingame/bs/character/profile?s="+ area_id +"&c="+ rolename +"'> 装备详情 </a>";
		$("#rolename_msg").html("点击查看角色是否存在" + url);
	  	$("#rolename_msg").css("color","green");
	  	// $('#submit').attr("disabled",false);
	  	//校验骗子数据库中是否存在
	  	liarcheck(area_id,rolename);
		
	  	//信息填写正确进行角色身份是否存在验证.
	  	// $.ajax({
	   //   url:'//liar.bnszs.com/search.php',
	   //   type:"POST",
	   //   data:{serverId:area_id,roleName:rolename},
	   //   success: function(data) {
	   //     var data = JSON.parse( data );
	   //     if(data.data.level && data.data.level != null){
	   //       	var url = "<a target='_blank' href='https://tools.bnszs.com/ingame/bs/character/profile?s="+ area_id +"&c="+ rolename +"'> 装备详情 </a>";
	   //     	$("#rolename_msg").html("角色查询存在,点击查看" + url);
	   //   		$("#rolename_msg").css("color","green");
	   //       	// $('#submit').attr("disabled",false);
	   //       	//校验骗子数据库中是否存在
	   //       	liarcheck(area_id,rolename);
	   //     }else{
	   //       	$("#rolename_msg").html("角色查询不存在,请确认提交游戏ID是否正确");
	   //   		$("#rolename_msg").css("color","red");
	   //     	// $('#submit').attr("disabled",true);
	   //     }
	   //   }
	   // });
	   
	   //查询关联角色
	    $.ajax({
	      url:'/tools/api/getAllUser?s='+ area_id +'&c='+ rolename,
	      type:"GET",
	      success: function(data) {
	        var data = JSON.parse( data );
	        if(data.count > 0){
	          	var searchinfo = "";
	          	searchinfo = "<li>社交昵称:"+data.data.account_name+"<li>"
	          	for (var k = 0, length = data.data.roleName.length; k < length; k++) {
	               searchinfo = searchinfo+"<li>["+k+"] <a target='_blank' href='https://tools.bnszs.com/ingame/bs/character/profile?s="+ area_id +"&c="+ data.data.roleName[k] +"'>"+ data.data.roleName[k] +"</a><li>"
	            } 
	        	$("#searchinfo").html(searchinfo);
	
	        }else{
	            $("#searchinfo").html("");
	        //   	$("#rolename_msg").html("角色查询不存在,请确认提交游戏ID是否正确");
	      		// $("#rolename_msg").css("color","red");
	        // 	// $('#submit').attr("disabled",true);
	        }
	      }
	    });	
	  	
	});
	
	$("#url").blur(function(){
	  	var url = $("#url").val();
	  	/* if(!isValidURL(url)){
	    	$("#url_msg").html("证据链接应该为一个http/https链接!");
	      	$("#url_msg").css("color","red");
	      	$('#submit').attr("disabled",true);
	    }else{
	      	$("#url_msg").html("");
	    	$('#submit').attr("disabled",false);
	    } */
	  	
	});

	$("#submit").click(function(){
	    $.ajax({
		    type: "POST",
		    dataType: "json",
		    url: "../post.php" ,
		    data: $("#form").serialize(),
		    success: function (result) {
		    	alert(result.msg);
		    	
		    	
		    	if(result.code == "success"){
		    		location.reload();
		    	} 
	
		    },
		    error : function() {
		        console.log("页面请求失败，请联系管理员处理");
			}
		});
	});
	
	function isValidURL(url){
	    var urlRegExp=/^((https|http)?:\/\/)+[A-Za-z0-9]+\.[A-Za-z0-9]+[\/=\?%\-&_~`@[\]\':+!]*([^<>\"\"])*$/;
	    if(urlRegExp.test(url)){
	        return true;
	    }else{
	        return false;
	    }
	}
	
  function liarcheck(serverId,roleName){
		//身份验证完毕.
	  	$.ajax({
	      url:'//liar.bnszs.com/liarcheck.php',
	      type:"POST",
	      data:{serverId:serverId,roleName:roleName},
	      success: function(data) {
	        //var data = JSON.parse( data );
	        console.log("进行骗子身份识别  " + data); 
	        if(data == "存在"){
	          	var url = "<a target='_blank' href='https://tools.bnszs.com/ingame/bs/character/profile?s="+ serverId +"&c="+ roleName +"'> 装备详情 </a>";
	        	$("#rolename_msg").html("骗子数据库中存在该骗子信息,点击查看" + url + ", 您可以选择继续提交");
	      		$("#rolename_msg").css("color","green");
	        }
	        
	      }
	    });	
	}
	
	function toVaild(){
	      /* var url = $("#url").val();
	      if(!isValidURL(url)){
	        $("#url_msg").html("证据链接应该为一个http/https链接!");
	        $("#url_msg").css("color","red");
	        $('#submit').attr("disabled",true);
	        return false;
	      }else{
	        $("#url_msg").html("");
	        $('#submit').attr("disabled",false);
	      }
	     
	  	  var area_id = $("#serverSelect1").val();
	      var rolename = $("#rolename").val();
	
	      if (area_id == null || area_id == undefined || area_id == '') {
	        console.log("服务器还没有选择");
	        $("#serverSelect1_msg").html("服务器还没有选择");
	        $("#serverSelect1_msg").css("color","red");
	        $('#submit').attr("disabled",true);
	        return false;
	      }else{
	          $("#serverSelect1_msg").html("");
	          $('#submit').attr("disabled",false);
	      }
	      if (rolename == ""){
	        $("#rolename_msg").html("请输入骗子昵称");
	        $("#rolename_msg").css("color","red");
	        $('#submit').attr("disabled",true);
	        return false;
	      }else{
	          $("#rolename_msg").html("");
	          $('#submit').attr("disabled",false);
	      }
	      //数据没有问题 直接提交 */
	      return true;
	 }
</script>