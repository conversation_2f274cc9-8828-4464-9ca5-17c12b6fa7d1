﻿using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.Preview.Common.Extension;

namespace Xylia.BnsHelper.Models;
public partial class HUDConfig(string name, int status = 1) : ObservableObject
{
	#region Properties
	[ObservableProperty] string name = name;
	[ObservableProperty] int status = status;
	#endregion

	#region ViewModel
	internal event EventHandler? Deleted;

	[RelayCommand] [property: Newtonsoft.Json.JsonIgnore]
	void Delete() => Deleted?.Invoke(this, EventArgs.Empty);
	#endregion
}

public class HUDCollection : ObservableCollection<HUDConfig>
{
	const string SECTION = "HUD";

	protected override void InsertItem(int index, HUDConfig item)
	{
		// invoke changed
		item.PropertyChanged += (s, e) => SetItem(IndexOf(item), item);
		item.Deleted += (s, e) => RemoveItem(IndexOf(item));

		SettingHelper.Default.SetValue(item.Status, item.Name, SECTION);
		base.InsertItem(index, item);
	}

	protected override void SetItem(int index, HUDConfig item)
	{
		SettingHelper.Default.RemoveKey(item.Name, SECTION);
		SettingHelper.Default.SetValue(item.Status, item.Name, SECTION);

		base.SetItem(index, item);
	}

	protected override void RemoveItem(int index)
	{
		var item = this[index];
		SettingHelper.Default.RemoveKey(item.Name, SECTION);

		base.RemoveItem(index);
	}


	public static HUDCollection Load()
	{
		var collection = new HUDCollection();

		var keys = SettingHelper.Default[SECTION];
		if (keys.Count == 0)
		{
			collection.Add(new("PlayerExpBarPanel"));
			collection.Add(new("PlayerStatusPanel"));
			collection.Add(new("Feedback_CombatSignal_Panel"));
			collection.Add(new("QuestQuickSlotPanel"));
			collection.Add(new("SystemMenuPanel"));
			collection.Add(new("NotificationMenuPanel"));
			collection.Add(new("ItemBarPanel"));
			collection.Add(new("SprintPanel"));
		}
		else keys.ForEach(item => collection.Add(new HUDConfig(item.KeyName, int.Parse(item.Value))));

		return collection;
	}
}
