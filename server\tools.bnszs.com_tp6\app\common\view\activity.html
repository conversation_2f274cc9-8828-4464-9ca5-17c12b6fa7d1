<!DOCTYPE html>
<html lang="zh-CN">
<head>
	<meta charset="gbk">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>剑灵怀旧服活动日历</title>
	<style>
		/* 重置样式 */
		* {
			margin: 0;
			padding: 0;
			box-sizing: border-box;
		}
		body {
			font-family: "Microsoft YaHei", sans-serif;
			color: #333;
			background-color: #f5f5f5;
			line-height: 1.4;
			overflow-x: hidden;
			transition: background-color 0.3s, color 0.3s;
		}

		/* 深色主题样式 */
		body.dark-theme {
			color: #e0e0e0;
			background-color: #1a1a1a;
		}
		ul {
			list-style: none;
		}
		a {
			text-decoration: none;
			color: inherit;
			display: block;
			transition: opacity 0.3s;
		}
		a:hover {
			opacity: 0.85;
		}
		img {
			max-width: 100%;
			height: auto;
			display: block;
			margin: 0 auto;
		}
		.clearfix:after {
			content: "";
			display: table;
			clear: both;
		}

		/* 容器样式 - 针对1070x675优化 */
		.container {
			max-width: 1070px;
			width: 100%;
			margin: 0 auto;
			padding: 15px;
			min-height: 675px;
			max-height: 675px;
			box-sizing: border-box;
			overflow-y: auto;
		}

		/* 自定义滚动条样式 - 浅色主题 */
		.container::-webkit-scrollbar {
			width: 6px;
		}

		.container::-webkit-scrollbar-track {
			background: #f1f1f1;
			border-radius: 3px;
		}

		.container::-webkit-scrollbar-thumb {
			background: #c1c1c1;
			border-radius: 3px;
		}

		.container::-webkit-scrollbar-thumb:hover {
			background: #a8a8a8;
		}

		/* 深色主题滚动条样式 */
		.dark-theme .container::-webkit-scrollbar-track {
			background: #2a2a2a;
		}

		.dark-theme .container::-webkit-scrollbar-thumb {
			background: #555;
		}

		.dark-theme .container::-webkit-scrollbar-thumb:hover {
			background: #777;
		}
		
		/* 标题区域样式 - 针对1070x675优化 */
		.header-section {
			margin-bottom: 20px;
			text-align: center;
		}

		.page-title {
			color: #1c232b;
			position: relative;
			display: inline-block;
			margin-bottom: 10px;
			padding-bottom: 8px;
			font-size: 24px;
			transition: color 0.3s;
		}

		.page-title:after {
			content: "";
			position: absolute;
			bottom: 0;
			left: 50%;
			transform: translateX(-50%);
			width: 50px;
			height: 2px;
			background: #ff6b6b;
			transition: background 0.3s;
		}

		/* 深色主题标题样式 */
		.dark-theme .page-title {
			color: #f0f0f0;
		}
		
		/* 统计信息样式 - 针对1070x675优化 */
		.stats-container {
			background: white;
			border-radius: 6px;
			padding: 12px;
			margin-bottom: 20px;
			box-shadow: 0 2px 8px rgba(0,0,0,0.05);
			display: flex;
			flex-wrap: wrap;
			justify-content: center;
			gap: 12px;
			transition: background 0.3s, box-shadow 0.3s;
		}

		.stat-item {
			flex: 1;
			min-width: 100px;
			max-width: 140px;
			padding: 8px;
			text-align: center;
			border-radius: 4px;
			background: #f9f9f9;
			transition: background 0.3s;
		}

		.stat-number {
			font-size: 20px;
			font-weight: bold;
			color: #ff6b6b;
			margin-bottom: 3px;
			transition: color 0.3s;
		}

		.stat-label {
			font-size: 12px;
			color: #666;
			transition: color 0.3s;
		}

		/* 深色主题统计信息样式 */
		.dark-theme .stats-container {
			background: #2a2a2a;
			box-shadow: 0 2px 8px rgba(0,0,0,0.3);
		}

		.dark-theme .stat-item {
			background: #3a3a3a;
		}

		.dark-theme .stat-number {
			color: #ff8a8a;
		}

		.dark-theme .stat-label {
			color: #b0b0b0;
		}

		/* 活动列表 - 针对1070x675优化 */
		.active-list {
			display: flex;
			flex-wrap: wrap;
			gap: 15px;
			justify-content: flex-start;
			align-content: flex-start;
		}

		.active-item {
			width: calc(33.333% - 10px); /* 1070宽度下一行显示3个卡片 */
			max-width: 340px;
			background: #fff;
			border-radius: 6px;
			overflow: hidden;
			box-shadow: 0 2px 8px rgba(0,0,0,0.05);
			transition: transform 0.2s, background 0.3s, box-shadow 0.3s;
		}

		.active-item:hover {
			transform: translateY(-3px);
		}

		/* 深色主题活动卡片样式 */
		.dark-theme .active-item {
			background: #2a2a2a;
			box-shadow: 0 2px 8px rgba(0,0,0,0.3);
		}
		
		.active-image {
			width: 100%;
			height: 160px;
			position: relative;
			text-align: center;
			overflow: hidden;
		}

		.active-image img {
			width: 100%;
			height: 100%;
			object-fit: cover;
			transition: transform 0.3s;
		}

		.active-item:hover .active-image img {
			transform: scale(1.05);
		}
		
		.event-info {
			padding: 12px;
		}

		.event-name {
			font-size: 14px;
			margin-bottom: 8px;
			color: #1c232b;
			line-height: 1.3;
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-line-clamp: 2;
			line-clamp: 2;
			-webkit-box-orient: vertical;
			transition: color 0.3s;
		}

		.event-time {
			font-size: 12px;
			color: #666;
			margin-bottom: 8px;
			transition: color 0.3s;
		}

		.event-countdown {
			font-size: 12px;
			color: #ff6b6b;
			display: flex;
			align-items: center;
			transition: color 0.3s;
		}

		/* 深色主题事件信息样式 */
		.dark-theme .event-name {
			color: #f0f0f0;
		}

		.dark-theme .event-time {
			color: #b0b0b0;
		}

		.dark-theme .event-countdown {
			color: #ff8a8a;
		}

		/* 主题切换按钮样式 */
		.theme-toggle {
			position: fixed;
			top: 20px;
			right: 20px;
			width: 40px;
			height: 40px;
			border: none;
			border-radius: 50%;
			background: #fff;
			box-shadow: 0 2px 8px rgba(0,0,0,0.1);
			cursor: pointer;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 18px;
			transition: all 0.3s;
			z-index: 1000;
		}

		.theme-toggle:hover {
			transform: scale(1.1);
			box-shadow: 0 4px 12px rgba(0,0,0,0.15);
		}

		.dark-theme .theme-toggle {
			background: #3a3a3a;
			color: #f0f0f0;
			box-shadow: 0 2px 8px rgba(0,0,0,0.3);
		}

		.dark-theme .theme-toggle:hover {
			box-shadow: 0 4px 12px rgba(0,0,0,0.4);
		}
		
		.time-icon {
			display: inline-block;
			width: 16px;
			height: 16px;
			margin-right: 5px;
			background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ff6b6b'%3E%3Cpath d='M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10 10-4.5 10-10S17.5 2 12 2zm0 18c-4.4 0-8-3.6-8-8s3.6-8 8-8 8 3.6 8 8-3.6 8-8 8zm.5-13H11v6l5.2 3.2.8-1.3-4.5-2.7V7z'/%3E%3C/svg%3E") no-repeat center;
			background-size: contain;
		}

		/* 响应式设计 - 针对1070x675优化 */
		/* 专门针对1070x675比例的优化 */
		@media (min-width: 1070px) and (max-width: 1070px) {
			.container {
				width: 1070px;
				padding: 15px;
			}

			.active-item {
				width: calc(33.333% - 10px);
				max-width: 340px;
			}
		}

		/* 1070宽度以下的适配 */
		@media (max-width: 1069px) {
			.active-item {
				width: calc(50% - 15px); /* 较小屏幕一行显示2个卡片 */
			}
		}

		@media (max-width: 768px) {
			.container {
				padding: 12px;
			}

			.active-item {
				width: 100%; /* 手机端一行只显示一个卡片 */
			}

			.event-name {
				font-size: 13px;
			}

			.page-title {
				font-size: 20px;
			}

			.stat-item {
				min-width: calc(50% - 10px);
			}
		}
	</style>

	<script src="https://cdn.staticfile.org/jquery/1.11.3/jquery.min.js"></script>
	<script src="https://bns.qq.com/webplat/info/news_version3/1298/61630/61631/m22845/index.js"></script>
</head>
<body>
	<!-- 主题切换按钮 -->
	<button class="theme-toggle" id="theme-toggle" onclick="toggleTheme()" title="切换主题">
		🌙
	</button>

	<div class="container">
		<div class="stats-container" id="stats-container">
			<!-- 统计信息将通过JS动态生成 -->
		</div>

		<ul class="active-list" id="active-list"></ul>
	</div>

	<script>
    // 主题切换功能
    function initTheme() {
        // 从URL参数获取主题设置
        const urlParams = new URLSearchParams(window.location.search);
        const style = urlParams.get('style');

        // 应用主题
        if (style === 'dark') {
            document.body.classList.add('dark-theme');
            updateThemeButton(true);
        } else if (style === 'light') {
            document.body.classList.remove('dark-theme');
            updateThemeButton(false);
        } else {
            // 如果没有指定style参数，使用默认浅色主题
            updateThemeButton(false);
        }
    }

    // 手动切换主题
    function toggleTheme() {
        const isDark = document.body.classList.toggle('dark-theme');
        updateThemeButton(isDark);

        // 更新URL参数（可选）
        const url = new URL(window.location);
        url.searchParams.set('style', isDark ? 'dark' : 'light');
        window.history.replaceState({}, '', url);
    }

    // 更新主题切换按钮图标
    function updateThemeButton(isDark) {
        const button = document.getElementById('theme-toggle');
        if (button) {
            button.innerHTML = isDark ? '☀️' : '🌙';
            button.title = isDark ? '切换到浅色主题' : '切换到深色主题';
        }
    }

    var eventsArrange = function(wrapid, data) {
        var wrap = document.getElementById(wrapid);
        var statsContainer = document.getElementById('stats-container');
        
        // 清空容器
        wrap.innerHTML = '';
        
        // 统计信息计数器
        var stats = {
            total: data.length,
            active: 0,
            ended: 0,
            longTerm: 0
        };

        function calCountdownTime(endTime) {
            endTime = endTime.replace(/\-/g, "/");
            var gap = new Date(endTime) - new Date();

            var second = Math.floor(gap/1000),
                day = Math.floor(second/3600/24),
                hourCal = Math.floor(second/3600 - day*24),
                minCal = Math.floor(second/60 - hourCal*60 - day*24*60);
            
            if(gap < 0) {
                stats.ended++;
                return '<span>已结束</span>';
            } else if(day >= 300) {
                stats.longTerm++;
                return '长期有效';
            } else {
                stats.active++;
                return '剩余' + day + '天' + hourCal + '时' + minCal + '分';
            }
        }

        for(var i = 0; i < data.length; i++) {
            data[i].timeGap = calCountdownTime(data[i].endTime);

            var li = document.createElement('li');
            li.className = 'active-item';
            li.innerHTML = 
                '<a href="//bns.qq.com' + data[i].infoPath + '" target="_blank">' +
                    '<div class="active-image">' +
                        '<img src="' + data[i].imageAddr + '" alt="' + data[i].title + '">' +
                    '</div>' +
                    '<div class="event-info">' +
                        '<h3 class="event-name">' + data[i].title + '</h3>' +
                        '<p class="event-time">' + data[i].begTime.replace(/\-/g, ".").slice(0,10) + 
                        ' ~ ' + data[i].endTime.replace(/\-/g, ".").slice(0,10) + '</p>' +
                        '<p class="event-countdown"><i class="time-icon"></i>' + data[i].timeGap + '</p>' +
                    '</div>' +
                '</a>';
            
            wrap.appendChild(li);
        }
        
        // 生成统计信息
        statsContainer.innerHTML = 
            '<div class="stat-item">' +
                '<div class="stat-number">' + stats.total + '</div>' +
                '<div class="stat-label">活动总数</div>' +
            '</div>' +
            '<div class="stat-item">' +
                '<div class="stat-number">' + stats.active + '</div>' +
                '<div class="stat-label">进行中</div>' +
            '</div>' +
            '<div class="stat-item">' +
                '<div class="stat-number">' + stats.ended + '</div>' +
                '<div class="stat-label">已结束</div>' +
            '</div>' +
            '<div class="stat-item">' +
                '<div class="stat-number">' + stats.longTerm + '</div>' +
                '<div class="stat-label">长期活动</div>' +
            '</div>';
    };

    // 处理数据并生成活动列表
    var tempJSON = [];
    for(var i in newsIndexData) {
        var tempObj = {
            begTime: decodeURIComponent(newsIndexData[i].dtBegTime),
            endTime: decodeURIComponent(newsIndexData[i].dtEndTime),
            imageAddr: decodeURIComponent(newsIndexData[i].sInfoImageAddr),
            title: decodeURIComponent(newsIndexData[i].sTitle),
            subContent: decodeURIComponent(newsIndexData[i].sSubContent),
            infoPath: decodeURIComponent(newsIndexData[i].infoPath)
        };
        tempJSON.push(tempObj);
    }
    
    // 页面加载完成后初始化主题和活动列表
    document.addEventListener('DOMContentLoaded', function() {
        initTheme();
        eventsArrange('active-list', tempJSON);
    });

	var _hmt = _hmt || [];
    (function() {
        var hm = document.createElement("script");
        hm.src = "https://hm.baidu.com/hm.js?2b2dc611e614389a26197f09051f463b";
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(hm, s);
    })();
	
	</script>
</body>
</html>