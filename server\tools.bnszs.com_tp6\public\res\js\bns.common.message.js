var Messages = (function (M, $) {
	var messages = {};
	
	M.get = function(key) {
		var message = messages[key];
		if (message == null || message == 'undefined') {
			return "";
		}
		return decodeURIComponent(message.replace(/\+/g, ' '));
	};
	M.init = function(data) {
		messages = data;
	};
	return M;
}(Messages || {}, jQuery));

String.prototype.format = function() {
	var str = this;
	for (var i = 0; i < arguments.length; i++) {
		var reg = new RegExp("\\{"+i+"\\}", "gm");
		str = str.replace(reg, arguments[i]);
	}
	return str;
}