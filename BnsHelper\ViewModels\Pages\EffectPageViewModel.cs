﻿using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Win32;
using Newtonsoft.Json;
using System.IO;
using System.Windows;
using Xylia.BnsHelper.Models;
using Xylia.BnsHelper.Resources;
using Xylia.Preview.UI.Views.Dialogs;

namespace Xylia.BnsHelper.ViewModels.Pages;
internal partial class EffectPageViewModel : ObservableObject
{
	#region Properties		 
	[ObservableProperty] EffectCollection data = EffectCollection.Load();
	#endregion

	#region Methods
	[RelayCommand] void Add() => Data.Add(new EffectConfig(StringHelper.Get("EffectPage_Default")!));
	[RelayCommand] void Reload() => MainWindowViewModel.ReloadConfig();

	[RelayCommand]
	async Task Import()
	{
		var dialog = new OpenFileDialog()
		{
			Filter = "JavaScript Object Notation|*.json",
		};
		if (dialog.ShowDialog() != true) return;

		foreach (var item in JsonConvert.DeserializeObject<EffectCollection>(File.ReadAllText(dialog.FileName))!)
		{
			// check if already exist
			if (Data.Contains(item))
			{
				if (MessageBox.Show(StringHelper.Get("EffectPage_Import_Ask", item.Alias), StringHelper.Get("ApplicationName"),
					MessageBoxButton.YesNo, MessageBoxImage.Asterisk) != MessageBoxResult.Yes) continue;

				Data.Remove(item);
			}

			Data.Add(item);
		}

		await MessageDialog.ShowDialog(StringHelper.Get("EffectPage_Import_Message"));
	}

	[RelayCommand]
	async Task Export()
	{
		var dialog = new SaveFileDialog()
		{
			FileName = "effect",
			Filter = "JavaScript Object Notation|*.json",
		};
		if (dialog.ShowDialog() != true) return;

		File.WriteAllText(dialog.FileName, JsonConvert.SerializeObject(Data, Formatting.Indented));
		await MessageDialog.ShowDialog(StringHelper.Get("EffectPage_Export_Message"));
	}
	#endregion
}