package database

import (
	"fmt"
	"udp-server/server/internal/model"
)

// AutoMigrate 自动执行数据库迁移
func AutoMigrate() error {
	// 先执行SQL命令禁用外键检查
	DB.Exec("SET FOREIGN_KEY_CHECKS = 0")
	defer DB.Exec("SET FOREIGN_KEY_CHECKS = 1")

	// 检查表是否存在，如果不存在则创建
	migrator := DB.Migrator()

	// 按照依赖关系顺序创建表
	tables := []interface{}{
		&model.User{},
		&model.DeviceHistory{},
		&model.Lucky{},
		&model.UserDraw{},
		&model.LuckyReward{},
		&model.CDkey{},
		&model.UserDrawResult{},
		&model.OnlineStatsHistory{},
		&model.RiskEvent{},         // 风控事件表
		&model.RiskControlConfig{}, // 风控配置表
		&model.AdminLog{},          // 管理员日志表
	}

	// 只创建不存在的表，跳过已存在的表以避免索引冲突
	for _, table := range tables {
		if !migrator.HasTable(table) {
			fmt.Printf("Creating table for %T\n", table)
			if err := migrator.CreateTable(table); err != nil {
				return fmt.Errorf("failed to create table %T: %v", table, err)
			}
		} else {
			fmt.Printf("Table for %T already exists, skipping migration\n", table)
		}
	}

	fmt.Println("Database migration completed successfully")
	return nil
}
