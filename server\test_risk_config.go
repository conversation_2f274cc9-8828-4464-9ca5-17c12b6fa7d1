package main

import (
	"fmt"
	"log"
	"udp-server/server/internal/database"
	"udp-server/server/internal/service"
	"udp-server/server/pkg/cache"

	"github.com/spf13/viper"
)

func main() {
	// 初始化配置
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("config")
	viper.AddConfigPath(".")
	viper.AutomaticEnv()

	if err := viper.ReadInConfig(); err != nil {
		log.Fatalf("Error reading config file: %v", err)
	}

	// 初始化数据库
	if err := database.InitDB(); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	// 创建缓存实例
	cacheInstance, err := cache.NewRedisCache(
		fmt.Sprintf("%s:%d", viper.GetString("redis.host"), viper.GetInt("redis.port")),
		viper.GetString("redis.password"),
		viper.GetInt("redis.db"),
		nil,
	)
	if err != nil {
		log.Fatalf("Failed to create cache: %v", err)
	}

	// 创建风控服务
	riskService := service.NewRiskControlService(database.GetDB(), cacheInstance)

	// 测试配置读取
	fmt.Println("=== 风控配置测试 ===")
	
	// 模拟登录风险检查
	err = riskService.CheckLoginRisk(123456789, "test-device-001", "*************")
	if err != nil {
		fmt.Printf("风控检查结果: %v\n", err)
	} else {
		fmt.Println("风控检查通过")
	}

	// 测试配置更新
	fmt.Println("\n=== 测试配置更新 ===")
	err = riskService.UpdateConfig()
	if err != nil {
		fmt.Printf("配置更新失败: %v\n", err)
	} else {
		fmt.Println("配置更新成功")
	}

	fmt.Println("\n测试完成！")
}
