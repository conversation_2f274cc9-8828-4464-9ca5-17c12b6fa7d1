<?php

namespace app\manage\controller\api;

use app\common\controller\BaseController;
use think\App;
use think\Request;
use think\facade\Db;
use think\Exception;

/**
 * 风控API接口
 * 供Go风控服务调用的PHP接口
 */
class RiskControl extends BaseController 
{
    public function __construct(App $app, Request $request) {
        parent::__construct($app, $request);
        
        // 风控接口需要特殊的鉴权
        $this->checkRiskControlAuth();
    }

    /**
     * 风控接口鉴权
     */
    private function checkRiskControlAuth() {
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $authToken = $this->request->header('X-Risk-Control-Token', '');
        
        // 检查User-Agent和Token
        if ($userAgent !== 'bnszs-risk-control' && empty($authToken)) {
            abort(403, 'Unauthorized access to risk control API');
        }
    }

    /**
     * 记录风险事件
     */
    public function recordEvent() {
        try {
            $data = $this->request->param();
            
            // 验证必要参数
            if(!isset($data['event_type']) || !isset($data['severity'])) {
                return json(['code' => 0, 'msg' => '参数错误']);
            }

            // 插入风险事件
            $eventData = [
                'event_type' => $data['event_type'],
                'device_id' => $data['device_id'] ?? '',
                'ip_address' => $data['ip_address'] ?? '',
                'qq_numbers' => $data['qq_numbers'] ?? '',
                'count' => $data['count'] ?? 1,
                'severity' => $data['severity'],
                'status' => 'pending',
                'description' => $data['description'] ?? '',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $result = Db::name('risk_events')->insert($eventData);
            
            if($result) {
                return json(['code' => 1, 'msg' => '记录成功']);
            } else {
                return json(['code' => 0, 'msg' => '记录失败']);
            }

        } catch (Exception $e) {
            return json(['code' => 0, 'msg' => '系统错误: ' . $e->getMessage()]);
        }
    }

    /**
     * 获取风险统计
     */
    public function getStats() {
        try {
            $timeRange = $this->request->param('time_range', '24h');
            
            // 根据时间范围计算开始时间
            switch($timeRange) {
                case '1h':
                    $startTime = date('Y-m-d H:i:s', time() - 3600);
                    break;
                case '24h':
                    $startTime = date('Y-m-d H:i:s', time() - 86400);
                    break;
                case '7d':
                    $startTime = date('Y-m-d H:i:s', time() - 604800);
                    break;
                default:
                    $startTime = date('Y-m-d H:i:s', time() - 86400);
            }

            // 获取统计数据
            $stats = [
                'total_events' => Db::name('risk_events')
                    ->where('created_at', '>=', $startTime)
                    ->count(),
                'high_severity' => Db::name('risk_events')
                    ->where('created_at', '>=', $startTime)
                    ->where('severity', 'high')
                    ->count(),
                'medium_severity' => Db::name('risk_events')
                    ->where('created_at', '>=', $startTime)
                    ->where('severity', 'medium')
                    ->count(),
                'low_severity' => Db::name('risk_events')
                    ->where('created_at', '>=', $startTime)
                    ->where('severity', 'low')
                    ->count(),
                'pending_events' => Db::name('risk_events')
                    ->where('created_at', '>=', $startTime)
                    ->where('status', 'pending')
                    ->count(),
                'time_range' => $timeRange,
                'start_time' => $startTime
            ];

            return json(['code' => 1, 'msg' => '获取成功', 'data' => $stats]);

        } catch (Exception $e) {
            return json(['code' => 0, 'msg' => '系统错误: ' . $e->getMessage()]);
        }
    }

    /**
     * 获取风险事件列表
     */
    public function getEvents() {
        try {
            $page = $this->request->param('page', 1);
            $limit = $this->request->param('limit', 20);
            $severity = $this->request->param('severity', '');
            $status = $this->request->param('status', '');
            
            $query = Db::name('risk_events');
            
            if($severity) {
                $query->where('severity', $severity);
            }
            
            if($status) {
                $query->where('status', $status);
            }
            
            $events = $query->order('created_at', 'desc')
                ->page($page, $limit)
                ->select();
                
            $total = $query->count();

            return json([
                'code' => 1, 
                'msg' => '获取成功', 
                'data' => [
                    'events' => $events,
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit
                ]
            ]);

        } catch (Exception $e) {
            return json(['code' => 0, 'msg' => '系统错误: ' . $e->getMessage()]);
        }
    }

    /**
     * 更新风险事件状态
     */
    public function updateEventStatus() {
        try {
            $eventId = $this->request->param('event_id');
            $status = $this->request->param('status');

            if(!$eventId || !$status) {
                return json(['code' => 0, 'msg' => '参数错误']);
            }

            if(!in_array($status, ['pending', 'processing', 'resolved', 'ignored'])) {
                return json(['code' => 0, 'msg' => '状态值无效']);
            }

            $result = Db::name('risk_events')
                ->where('id', $eventId)
                ->update([
                    'status' => $status,
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

            if($result) {
                return json(['code' => 1, 'msg' => '更新成功']);
            } else {
                return json(['code' => 0, 'msg' => '更新失败']);
            }

        } catch (Exception $e) {
            return json(['code' => 0, 'msg' => '系统错误: ' . $e->getMessage()]);
        }
    }

    /**
     * 检查设备风险等级
     */
    public function checkDeviceRisk() {
        try {
            $deviceId = $this->request->param('device_id');

            if(!$deviceId) {
                return json(['code' => 0, 'msg' => '设备ID不能为空']);
            }

            // 查询该设备的风险事件
            $events = Db::name('risk_events')
                ->where('device_id', $deviceId)
                ->where('created_at', '>=', date('Y-m-d H:i:s', time() - 86400)) // 24小时内
                ->select();

            $riskLevel = 'low';
            $riskScore = 0;

            foreach($events as $event) {
                switch($event['severity']) {
                    case 'high':
                        $riskScore += 10;
                        break;
                    case 'medium':
                        $riskScore += 5;
                        break;
                    case 'low':
                        $riskScore += 1;
                        break;
                }
            }

            // 根据风险分数确定风险等级
            if($riskScore >= 50) {
                $riskLevel = 'high';
            } elseif($riskScore >= 20) {
                $riskLevel = 'medium';
            }

            return json([
                'code' => 1,
                'msg' => '检查完成',
                'data' => [
                    'device_id' => $deviceId,
                    'risk_level' => $riskLevel,
                    'risk_score' => $riskScore,
                    'event_count' => count($events)
                ]
            ]);

        } catch (Exception $e) {
            return json(['code' => 0, 'msg' => '系统错误: ' . $e->getMessage()]);
        }
    }

    /**
     * 健康检查
     */
    public function health() {
        try {
            // 检查数据库连接
            $dbStatus = Db::query('SELECT 1');

            return json([
                'code' => 1,
                'msg' => '风控API服务正常',
                'data' => [
                    'timestamp' => time(),
                    'database' => $dbStatus ? 'connected' : 'disconnected',
                    'version' => 'TP6.0'
                ]
            ]);

        } catch (Exception $e) {
            return json([
                'code' => 0,
                'msg' => '服务异常: ' . $e->getMessage()
            ]);
        }
    }
}
