<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------

// +----------------------------------------------------------------------
// | 缓存设置
// +----------------------------------------------------------------------

return [
    // 默认缓存驱动
    'default' => 'redis',

    // 缓存驱动配置
    'stores' => [
        // Redis缓存
        'redis' => [
            'type'       => 'redis',
            'host'       => '127.0.0.1',
            'port'       => 6379,
            'password'   => '',
            'select'     => 4, // 使用与Go服务相同的数据库4
            'timeout'    => 3600,
            'expire'     => 3600 * 24 * 7, // 缓存时间
            'persistent' => false,
            'prefix'     => '',
            'serialize'  => [],
        ],

        // 文件缓存（备用）
        'file' => [
            'type'       => 'File',
            'path'       => '',
            'prefix'     => '',
            'expire'     => 3600 * 24 * 7,
            'serialize'  => [],
        ],
    ],
];
