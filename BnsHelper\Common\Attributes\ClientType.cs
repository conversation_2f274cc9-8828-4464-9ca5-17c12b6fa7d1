﻿using Xylia.BnsHelper.Common.Helpers;
using Xylia.Preview.Data.Engine.DatData;

namespace Xylia.Preview.Common.Attributes;

[AttributeUsage(AttributeTargets.Class | AttributeTargets.Field | AttributeTargets.Property)]
public class ClientTypeAttribute : Attribute
{
    public ClientType Type;

    public ClientTypeAttribute(ClientType type) => Type = type;

    public static bool Check(ClientType type)
    {
        var publisher = SettingHelper.Default.Publisher;

        return type switch
        {
            ClientType.LIVE => publisher is EPublisher.Tencent,
            ClientType.NEOCLASSIC => publisher is EPublisher.ZTX,
            ClientType.NEOGLOBAL => publisher is EPublisher.ZNCS or EPublisher.ZNCG,
            _ => true,
        };
    }
}

[Flags]
public enum ClientType
{
    All,
    LIVE,
    NEOCLASSIC = 0x100,
    NEOGLOBAL = 0x200,
    NEO = NEOCLASSIC | NEOGLOBAL,
}
