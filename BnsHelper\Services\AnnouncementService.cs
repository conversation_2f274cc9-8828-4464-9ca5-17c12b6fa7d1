using System.Collections.ObjectModel;
using System.Diagnostics;
using System.IO;
using System.Text.Json;
using Xylia.BnsHelper.Models;

namespace Xylia.BnsHelper.Services;

/// <summary>
/// 公告服务
/// </summary>
public class AnnouncementService
{
    private static readonly Lazy<AnnouncementService> _instance = new(() => new AnnouncementService());
    public static AnnouncementService Instance => _instance.Value;

    private readonly string _announcementFilePath;
    private readonly string _readStatusFilePath;
    private readonly ObservableCollection<Announcement> _announcements;
    private readonly HashSet<string> _readAnnouncementIds;

    private AnnouncementService()
    {
        var appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "Xylia");
        Directory.CreateDirectory(appDataPath);
        
        _announcementFilePath = Path.Combine(appDataPath, "announcements.json");
        _readStatusFilePath = Path.Combine(appDataPath, "read_announcements.json");
        
        _announcements = new ObservableCollection<Announcement>();
        _readAnnouncementIds = new HashSet<string>();
        
        LoadReadStatus();
        LoadAnnouncements();
    }

    /// <summary>
    /// 所有公告
    /// </summary>
    public ObservableCollection<Announcement> Announcements => _announcements;

    /// <summary>
    /// 未读公告数量
    /// </summary>
    public int UnreadCount => _announcements.Count(a => !a.IsRead);

    /// <summary>
    /// 加载公告
    /// </summary>
    public void LoadAnnouncements()
    {
        try
        {
            if (!File.Exists(_announcementFilePath))
            {
                // 创建默认公告文件
                CreateDefaultAnnouncements();
                return;
            }

            var json = File.ReadAllText(_announcementFilePath);
            var announcementData = JsonSerializer.Deserialize<AnnouncementData[]>(json);

            _announcements.Clear();
            if (announcementData != null)
            {
                foreach (var data in announcementData.OrderByDescending(a => a.PublishTime))
                {
                    var announcement = new Announcement
                    {
                        Id = data.Id,
                        Title = data.Title,
                        Content = data.Content,
                        PublishTime = data.PublishTime,
                        Type = data.Type,
                        IsRead = _readAnnouncementIds.Contains(data.Id)
                    };
                    _announcements.Add(announcement);
                }
            }

            Debug.WriteLine($"[INFO] 加载了 {_announcements.Count} 条公告，未读 {UnreadCount} 条");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 加载公告失败: {ex.Message}");
            CreateDefaultAnnouncements();
        }
    }

    /// <summary>
    /// 标记公告为已读
    /// </summary>
    public void MarkAsRead(string announcementId)
    {
        if (_readAnnouncementIds.Add(announcementId))
        {
            var announcement = _announcements.FirstOrDefault(a => a.Id == announcementId);
            if (announcement != null)
            {
                announcement.IsRead = true;
            }
            SaveReadStatus();
        }
    }

    /// <summary>
    /// 标记所有公告为已读
    /// </summary>
    public void MarkAllAsRead()
    {
        foreach (var announcement in _announcements)
        {
            if (!announcement.IsRead)
            {
                announcement.IsRead = true;
                _readAnnouncementIds.Add(announcement.Id);
            }
        }
        SaveReadStatus();
    }

    /// <summary>
    /// 更新公告（从服务器或更新包获取）
    /// </summary>
    public void UpdateAnnouncements(string newAnnouncementsJson)
    {
        try
        {
            File.WriteAllText(_announcementFilePath, newAnnouncementsJson);
            LoadAnnouncements();
            Debug.WriteLine("[INFO] 公告已更新");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 更新公告失败: {ex.Message}");
        }
    }

    private void LoadReadStatus()
    {
        try
        {
            if (File.Exists(_readStatusFilePath))
            {
                var json = File.ReadAllText(_readStatusFilePath);
                var readIds = JsonSerializer.Deserialize<string[]>(json);
                if (readIds != null)
                {
                    foreach (var id in readIds)
                    {
                        _readAnnouncementIds.Add(id);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 加载已读状态失败: {ex.Message}");
        }
    }

    private void SaveReadStatus()
    {
        try
        {
            var json = JsonSerializer.Serialize(_readAnnouncementIds.ToArray(), new JsonSerializerOptions
            {
                WriteIndented = true
            });
            File.WriteAllText(_readStatusFilePath, json);
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 保存已读状态失败: {ex.Message}");
        }
    }

    private void CreateDefaultAnnouncements()
    {
        var defaultAnnouncements = new[]
        {
            new AnnouncementData
            {
                Id = "welcome_001",
                Title = "欢迎使用 BnsHelper",
                Content = "感谢您使用 BnsHelper！\n\n本工具旨在为剑灵玩家提供便捷的游戏辅助功能。\n\n如有问题或建议，请联系开发团队。",
                PublishTime = DateTime.Now.AddDays(-1),
                Type = AnnouncementType.Info
            },
            new AnnouncementData
            {
                Id = "update_001",
                Title = "系统优化更新",
                Content = "本次更新内容：\n\n1. 优化登录性能，提高响应速度\n2. 简化签到流程，减少网络传输\n3. 修复已知问题，提升稳定性\n\n感谢您的支持！",
                PublishTime = DateTime.Now,
                Type = AnnouncementType.Update
            }
        };

        try
        {
            var json = JsonSerializer.Serialize(defaultAnnouncements, new JsonSerializerOptions
            {
                WriteIndented = true
            });
            File.WriteAllText(_announcementFilePath, json);
            LoadAnnouncements();
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 创建默认公告失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 公告数据结构（用于序列化）
    /// </summary>
    private class AnnouncementData
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public DateTime PublishTime { get; set; }
        public AnnouncementType Type { get; set; }
    }
}
