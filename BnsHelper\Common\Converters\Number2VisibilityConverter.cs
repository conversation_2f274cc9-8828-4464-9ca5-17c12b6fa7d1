using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace Xylia.BnsHelper.Common.Converters;
internal class Number2VisibilityConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value == null) return Visibility.Collapsed;
        
        var number = System.Convert.ToInt32(value);
        var threshold = parameter != null ? System.Convert.ToInt32(parameter) : 1;
        
        return number >= threshold ? Visibility.Visible : Visibility.Collapsed;
    }

    public object? ConvertBack(object value, Type targetType, object parameter, CultureInfo culture) => throw new NotImplementedException();
}
