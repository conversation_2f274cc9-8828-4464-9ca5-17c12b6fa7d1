-- BnsTop 性能优化 - 数据库索引建议
-- 执行这些SQL语句来优化 bnsuserinfo 表的查询性能

-- 1. 为 allscore 字段创建降序索引（用于排名查询）
CREATE INDEX idx_bnsuserinfo_allscore_desc ON bnsuserinfo (allscore DESC);

-- 2. 为 worldid + name 创建复合索引（用于角色查找）
CREATE INDEX idx_bnsuserinfo_worldid_name ON bnsuserinfo (worldid, name);

-- 3. 为 name + allscore 创建复合索引（用于角色排名查询）
CREATE INDEX idx_bnsuserinfo_name_allscore ON bnsuserinfo (name, allscore DESC);

-- 4. 为 newpcid 创建普通索引（MySQL 5.7+ 支持前缀索引）
CREATE INDEX idx_bnsuserinfo_newpcid ON bnsuserinfo (newpcid(2));

-- 5. 为 worldid 创建普通索引
CREATE INDEX idx_bnsuserinfo_worldid ON bnsuserinfo (worldid(2));

-- 6. 为 job + allscore 创建复合索引（用于职业排名）
CREATE INDEX idx_bnsuserinfo_job_allscore ON bnsuserinfo (job, allscore DESC);

-- 7. 为多条件查询创建复合索引
CREATE INDEX idx_bnsuserinfo_complex ON bnsuserinfo (allscore DESC, worldid, name, job);

-- 查看当前表的索引状态
SHOW INDEX FROM bnsuserinfo;

-- 分析表统计信息
ANALYZE TABLE bnsuserinfo;

-- 查看表的存储引擎和状态
SHOW TABLE STATUS LIKE 'bnsuserinfo';

-- 性能监控查询
-- 查看慢查询日志设置
SHOW VARIABLES LIKE 'slow_query_log%';
SHOW VARIABLES LIKE 'long_query_time';

-- 查看查询缓存状态
SHOW VARIABLES LIKE 'query_cache%';
SHOW STATUS LIKE 'Qcache%';

-- ========================================
-- 替代方案：如果前缀索引效果不好，可以考虑以下方案
-- ========================================

-- 方案1：添加计算列（MySQL 5.7.6+）
-- ALTER TABLE bnsuserinfo ADD COLUMN newpcid_prefix VARCHAR(2) GENERATED ALWAYS AS (LEFT(newpcid, 2)) STORED;
-- CREATE INDEX idx_bnsuserinfo_newpcid_prefix ON bnsuserinfo (newpcid_prefix);

-- ALTER TABLE bnsuserinfo ADD COLUMN worldid_prefix VARCHAR(2) GENERATED ALWAYS AS (LEFT(worldid, 2)) STORED;
-- CREATE INDEX idx_bnsuserinfo_worldid_prefix ON bnsuserinfo (worldid_prefix);

-- 方案2：手动维护前缀字段
-- ALTER TABLE bnsuserinfo ADD COLUMN newpcid_prefix VARCHAR(2);
-- ALTER TABLE bnsuserinfo ADD COLUMN worldid_prefix VARCHAR(2);
-- UPDATE bnsuserinfo SET newpcid_prefix = LEFT(newpcid, 2), worldid_prefix = LEFT(worldid, 2);
-- CREATE INDEX idx_bnsuserinfo_newpcid_prefix ON bnsuserinfo (newpcid_prefix);
-- CREATE INDEX idx_bnsuserinfo_worldid_prefix ON bnsuserinfo (worldid_prefix);

-- 方案3：优化查询语句，减少 LEFT 函数使用
-- 在应用层预处理前缀，直接使用 LIKE 查询
-- 例如：WHERE newpcid LIKE '01%' 而不是 WHERE LEFT(newpcid,2) = '01'

-- ========================================
-- 性能测试查询
-- ========================================

-- 测试当前查询性能
EXPLAIN SELECT allscore,worldid,newpcid,name,job
FROM bnsuserinfo
WHERE (worldid = 1001 OR LEFT(newpcid,2) = (
    SELECT LEFT(newpcid,2) FROM bnsuserinfo WHERE worldid = 1001 LIMIT 1
))
AND name = '测试角色'
ORDER BY allscore DESC
LIMIT 1;

-- 测试排名查询性能
EXPLAIN SELECT COUNT(*)
FROM bnsuserinfo
WHERE allscore > 100000
AND LEFT(newpcid,2) = '01';

-- 查看表的统计信息
SELECT
    table_name,
    table_rows,
    avg_row_length,
    data_length,
    index_length,
    (data_length + index_length) as total_size
FROM information_schema.tables
WHERE table_schema = DATABASE()
AND table_name = 'bnsuserinfo';
