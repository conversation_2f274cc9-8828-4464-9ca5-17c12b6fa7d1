﻿using System.Diagnostics;
using System.IO;
using System.Text;
using System.Xml.Serialization;

namespace Xylia.BnsHelper.Models;
[XmlRoot("config")]
public class BnsSaved
{
	[XmlElement("user")] public Creature[]? user;
	public IEnumerable<Creature>? User => user?.OrderByDescending(o => o.level).ThenByDescending(o => o.MasteryLevel);


	private static FileSystemWatcher? watcher;

	internal static BnsSaved? Get(Action OnChanged)
	{
		void OnFileChanged(object sender, FileSystemEventArgs e)
		{
			Debug.WriteLine($"{e.ChangeType}: {e.FullPath}");
			OnChanged?.Invoke();
		}

		var path = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "BnS");
		watcher = new FileSystemWatcher(path, "User.xml");
		watcher.Changed += OnFileChanged;
		watcher.Created += OnFileChanged;
		watcher.Deleted += OnFileChanged;
		watcher.EnableRaisingEvents = true;

		return LoadData(Path.Combine(path, "User.xml"));
	}

	private static BnsSaved? LoadData(string path)
	{
		if (!File.Exists(path)) return null;

		using var fs = File.Open(path, FileMode.Open, FileAccess.ReadWrite);
		using var sr = new StreamReader(fs, Encoding.UTF8);

		var serializer = new XmlSerializer(typeof(BnsSaved));
		var result = serializer.Deserialize(sr) as BnsSaved;
		return result;
	}
}

public class Equipment
{
	#region Fields
	[XmlAttribute("id")] public int Id;
	[XmlAttribute("name")] public string? Name;
	#endregion

	#region Methods
	public override string? ToString() => Name;
	#endregion
}