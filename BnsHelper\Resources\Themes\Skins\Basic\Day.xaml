﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:system="clr-namespace:System;assembly=mscorlib">

	<Color x:Key="LightPrimaryColor">#f3fbff</Color>
	
	<Color x:Key="LightDangerColor">#fff6f7</Color>
	<Color x:Key="DangerColor">#db3340</Color>
	<Color x:Key="DarkDangerColor">#db3340</Color>

	<Color x:Key="LightWarningColor">#fffcf5</Color>
	<Color x:Key="WarningColor">#e9af20</Color>
	<Color x:Key="DarkWarningColor">#e9af20</Color>

	<Color x:Key="LightInfoColor">#f1fdff</Color>
	<Color x:Key="InfoColor">#00bcd4</Color>
	<Color x:Key="DarkInfoColor">#00bcd4</Color>

	<Color x:Key="LightSuccessColor">#f3fff6</Color>
	<Color x:Key="SuccessColor">#2db84d</Color>
	<Color x:Key="DarkSuccessColor">#2db84d</Color>

	<Color x:Key="PrimaryTextColor">#212121</Color>
	<Color x:Key="SecondaryTextColor">#757575</Color>
	<Color x:Key="ThirdlyTextColor">#bdbdbd</Color>
	<Color x:Key="ReverseTextColor">#212121</Color>
	<Color x:Key="TextIconColor">White</Color>

	<Color x:Key="BorderColor">#e0e0e0</Color>
	<Color x:Key="SecondaryBorderColor">#757575</Color>
	<Color x:Key="BackgroundColor">#eeeeee</Color>
	<Color x:Key="RegionColor">#ffffff</Color>
	<Color x:Key="SecondaryRegionColor">#eeeeee</Color>
	<Color x:Key="ThirdlyRegionColor">White</Color>
	<Color x:Key="TitleColor">#326cf3</Color>
	<Color x:Key="SecondaryTitleColor">#326cf3</Color>

	<Color x:Key="DefaultColor">White</Color>
	<Color x:Key="DarkDefaultColor">#f5f5f5</Color>

	<Color x:Key="AccentColor">#f8491e</Color>
	<Color x:Key="DarkAccentColor">#f8491e</Color>

	<Color x:Key="DarkMaskColor">#20000000</Color>
	<Color x:Key="DarkOpacityColor">#40000000</Color>
	<system:UInt32 x:Key="BlurGradientValue">0x99FFFFFF</system:UInt32>

	<!-- Additional Brush Resources -->
	<SolidColorBrush x:Key="LightDangerBrush" Color="{StaticResource LightDangerColor}"/>
	<SolidColorBrush x:Key="DangerBrush" Color="{StaticResource DangerColor}"/>
	<SolidColorBrush x:Key="TextIconBrush" Color="{StaticResource TextIconColor}"/>
	<SolidColorBrush x:Key="DarkPrimaryBrush" Color="{StaticResource DarkPrimaryColor}"/>
	<SolidColorBrush x:Key="SecondaryBorderBrush" Color="{StaticResource SecondaryBorderColor}"/>
	<SolidColorBrush x:Key="ThirdlyRegionBrush" Color="{StaticResource ThirdlyRegionColor}"/>
	<SolidColorBrush x:Key="DarkOpacityBrush" Color="{StaticResource DarkOpacityColor}"/>

	
	<!-- AvalonEdit -->
	<Color x:Key="ControlAccentColorKey">#1ba1e2</Color>
	<Color x:Key="EditorBackgroundColor">White</Color>
	<Color x:Key="EditorForegroundColor">Black</Color>
	<Color x:Key="EditorLineNumbersForegroundColor">Black</Color>
	<Color x:Key="EditorNonPrintableCharacterColor">#3F8080FF</Color>
	<Color x:Key="EditorLinkTextForegroundColor">#FF4040FF</Color>
	<Color x:Key="EditorLinkTextBackgroundColor">#00000000</Color>

	<Color x:Key="XML_XmlDeclaration">Blue</Color>
	<Color x:Key="XML_XmlTag">DarkMagenta</Color>
	<Color x:Key="XML_AttributeName">Red</Color>
	<Color x:Key="XML_AttributeValue">Blue</Color>
	<!-- AvalonEdit -->

</ResourceDictionary>