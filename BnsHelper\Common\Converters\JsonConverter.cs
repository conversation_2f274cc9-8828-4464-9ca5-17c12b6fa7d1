﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Xylia.BnsHelper.Models;
using Xylia.Preview.Data.Models.Sequence;

namespace Xylia.BnsHelper.Common.Converters;
internal class FPlayerConverter : JsonConverter<FPlayer>
{
	public override void Write<PERSON><PERSON>(JsonWriter writer, FPlayer? value, JsonSerializer serializer)
	{
		ArgumentNullException.ThrowIfNull(value);

		writer.WriteStartObject();

		writer.WritePropertyName("name");
		writer.WriteValue(value.Name);

		writer.WritePropertyName("job");
		writer.WriteValue(value.Job);

		writer.WritePropertyName("default");
		writer.WriteValue(value.Self);

		writer.WritePropertyName("effect");
		writer.WriteStartArray();
		foreach (var data in value) serializer.Serialize(writer, data);
		writer.WriteEndArray();

		writer.WriteEndObject();
	}

	public override FPlayer ReadJson(JsonReader reader, Type objectType, FPlayer? existingValue, bool hasExistingValue, JsonSerializer serializer)
	{
		var obj = serializer.Deserialize<JObject>(reader)!;
		var name = obj.Value<string>("name");
		var job = (JobSeq)obj.Value<int>("job");
		var self = obj.Value<bool>("default");
		var effect = obj.Value<JArray>("effect")!;

		// Create instance of player
		var player = new FPlayer(null, name, self) { Job = job };
		foreach (var item in effect)
		{
			player.Add(item.ToObject<FEffectEvent>());
		}
	
		return player;
	}
}

internal class FPlayerCollectionConverter : JsonConverter<FPlayerCollection>
{
	public override void WriteJson(JsonWriter writer, FPlayerCollection? value, JsonSerializer serializer)
	{
		ArgumentNullException.ThrowIfNull(value);

		// basic section
		writer.WriteStartObject();
		writer.WritePropertyName("version");
		writer.WriteValue(1);

		// player section
		writer.WritePropertyName("player");
		writer.WriteStartArray();
		foreach (var data in value) serializer.Serialize(writer, data);
		writer.WriteEndArray();

		writer.WriteEndObject();
	}

	public override FPlayerCollection ReadJson(JsonReader reader, Type objectType, FPlayerCollection? existingValue, bool hasExistingValue, JsonSerializer serializer)
	{
		var obj = serializer.Deserialize<JObject>(reader)!;
		var version = obj.Value<int>("version");

		// Create instance of collection
		var collection = new FPlayerCollection() { IsHistory = true };
		var effects = new List<FEffectEvent>();

		foreach (var token in obj.Value<JArray>("player")!)
		{
			var player = token.ToObject<FPlayer>()!;
			player.Owner = collection;
			collection.Add(player);

			// Refresh party stats 
			collection.TotalDamage += player.TotalDamage;
			if (collection.StartTime is null || collection.StartTime > player.StartTime) collection.StartTime = player.StartTime;
			if (collection.EndTime < player.EndTime) collection.EndTime = player.EndTime;
		}

		return collection;
	}
}