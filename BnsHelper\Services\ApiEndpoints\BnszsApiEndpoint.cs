﻿using RestSharp;
using Xylia.BnsHelper.Models;

namespace Xylia.BnsHelper.Services.ApiEndpoints;
internal class BnszsApiEndpoint(RestClient _client)
{
    #region Team
    private static Team? team;
    public Team Team => team ??= GetTeamAsync().GetAwaiter().GetResult();

    private async Task<Team> GetTeamAsync()
    {
        var request = new RestRequest($"https://tools.bnszs.com/api/team");
        var response = await _client.ExecuteAsync<Team>(request).ConfigureAwait(false);
        return response.Data;
    }
    #endregion

    #region Clock
    private static Schedule[]? schedules;
    public Schedule[] Schedules => schedules ??= GetScheduleAsync().GetAwaiter().GetResult();

    private async Task<Schedule[]> GetScheduleAsync()
    {
        // 不再使用远程API
        return [];
    }

    public void RefreshSchedules()
    {

    }
    #endregion
}
