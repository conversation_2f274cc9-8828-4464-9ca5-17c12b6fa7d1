using System.Diagnostics;
using System.IO;
using System.Text.Json;
using Xylia.BnsHelper.Models;

namespace Xylia.BnsHelper.Services;

/// <summary>
/// 日程通知设置管理服务
/// </summary>
public class ScheduleNotificationService
{
    private static readonly Lazy<ScheduleNotificationService> _instance = new(() => new ScheduleNotificationService());
    public static ScheduleNotificationService Instance => _instance.Value;

    private readonly string _settingsFilePath;
    private Dictionary<string, ScheduleNotificationSettings> _settings;

    private ScheduleNotificationService()
    {
        var appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "Xylia");
        Directory.CreateDirectory(appDataPath);
        _settingsFilePath = Path.Combine(appDataPath, "schedule_notifications.json");
        _settings = new Dictionary<string, ScheduleNotificationSettings>();
        LoadSettings();
    }

    /// <summary>
    /// 保存日程通知设置
    /// </summary>
    public void SaveScheduleSettings(Schedule schedule)
    {
        try
        {
            _settings[schedule.UniqueId] = new ScheduleNotificationSettings
            {
                UniqueId = schedule.UniqueId,
                Notify15 = schedule.Notify15,
                Notify5 = schedule.Notify5,
                Notify3 = schedule.Notify3
            };

            SaveSettings();
            Debug.WriteLine($"[INFO] 保存日程通知设置: {schedule.UniqueId}, 15分钟={schedule.Notify15}, 5分钟={schedule.Notify5}, 3分钟={schedule.Notify3}");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 保存日程通知设置失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 加载日程通知设置
    /// </summary>
    public ScheduleNotificationSettings? LoadScheduleSettings(string uniqueId)
    {
        return _settings.TryGetValue(uniqueId, out var settings) ? settings : null;
    }

    /// <summary>
    /// 更新所有日程的通知任务 - 统一通过Schedules属性
    /// </summary>
    public void UpdateScheduleNotifications()
    {
        try
        {
            // 统一方案：通知ScheduleService重新注册通知任务（ScheduleService会通过Schedules属性获取数据）
            ScheduleService.Instance.RefreshNotifications();
            Debug.WriteLine("[INFO] 已更新日程通知任务");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 更新日程通知任务失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 从文件加载设置
    /// </summary>
    private void LoadSettings()
    {
        try
        {
            if (!File.Exists(_settingsFilePath))
            {
                _settings = new Dictionary<string, ScheduleNotificationSettings>();
                return;
            }

            var json = File.ReadAllText(_settingsFilePath);
            var settingsList = JsonSerializer.Deserialize<List<ScheduleNotificationSettings>>(json);

            _settings = settingsList?.ToDictionary(s => s.UniqueId, s => s) ?? new Dictionary<string, ScheduleNotificationSettings>();
            
            Debug.WriteLine($"[INFO] 加载日程通知设置: {_settings.Count} 个设置项");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 加载日程通知设置失败: {ex.Message}");
            _settings = new Dictionary<string, ScheduleNotificationSettings>();
        }
    }

    /// <summary>
    /// 保存设置到文件
    /// </summary>
    private void SaveSettings()
    {
        try
        {
            var settingsList = _settings.Values.ToList();
            var json = JsonSerializer.Serialize(settingsList, new JsonSerializerOptions
            {
                WriteIndented = true
            });

            File.WriteAllText(_settingsFilePath, json);
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 保存日程通知设置到文件失败: {ex.Message}");
        }
    }
}

/// <summary>
/// 日程通知设置数据结构
/// </summary>
public class ScheduleNotificationSettings
{
    public string UniqueId { get; set; } = string.Empty;
    public bool Notify15 { get; set; }
    public bool Notify5 { get; set; } = true;
    public bool Notify3 { get; set; } = true;
}
