# 硬编码文本映射表

## 命名规范
基于现有的资源键命名模式，采用以下规范：
- `模块_功能_描述`格式
- 使用英文单词，采用PascalCase命名
- 特定前缀：
  - `Common_` - 通用文本
  - `UI_` - 界面元素
  - `Button_` - 按钮文本
  - `Message_` - 消息提示
  - `Tooltip_` - 工具提示
  - `Dialog_` - 对话框
  - `Status_` - 状态文本

## XAML文件硬编码文本映射

### AnnouncementWindow.xaml
| 硬编码文本 | 建议资源键 | 位置 |
|-----------|-----------|------|
| 系统公告 | AnnouncementWindow_Title | Title, TextBlock |
| （{0} 条未读） | AnnouncementWindow_UnreadCount | StringFormat |
| 全部标记为已读 | Button_MarkAllAsRead | Button Content |
| 标记为已读 | Button_MarkAsRead | Button Content |
| 刷新 | Button_Refresh | Button Content |
| 关闭 | Button_Close | Button Content |

### ClockAlarmPanel.xaml
| 硬编码文本 | 建议资源键 | 位置 |
|-----------|-----------|------|
| 事件前15分钟通知 | ClockAlarm_Notify15Minutes | MenuItem Header |
| 事件前5分钟通知 | ClockAlarm_Notify5Minutes | MenuItem Header |
| 事件前3分钟通知 | ClockAlarm_Notify3Minutes | MenuItem Header |

### DamageMeterPanel.xaml
| 硬编码文本 | 建议资源键 | 位置 |
|-----------|-----------|------|
| 清除此倒计时器 | DamageMeter_ClearSingleTimer | MenuItem Header |
| 清除所有倒计时器 | DamageMeter_ClearAllTimers | MenuItem Header |
| {0}/秒 | DamageMeter_DamagePerSecond | StringFormat |

### DamageMeterTooltipPanel.xaml
| 硬编码文本 | 建议资源键 | 位置 |
|-----------|-----------|------|
| 队伍贡献率 {0:P1} | DamageMeter_TeamContribution | StringFormat |
| 累积伤害量 {0} | DamageMeter_TotalDamage | StringFormat |
| 每秒伤害    {0} | DamageMeter_DamagePerSecond | StringFormat |
| 战斗格挡率 {0:P2} | DamageMeter_BlockRate | StringFormat |
| 战斗闪避率 {0:P2} | DamageMeter_EvasionRate | StringFormat |
| 战斗时间    {0:F0}秒 | DamageMeter_BattleTime | StringFormat |
| 隐藏其他角色激活时，无法记录暴击数据 | DamageMeter_CriticalDataWarning | Run Text |

### MainWindow.xaml
| 硬编码文本 | 建议资源键 | 位置 |
|-----------|-----------|------|
| 在线用户数 | MainWindow_OnlineUserCount | Run Text |
| 激活口令码 | MainWindow_ActivateCDKey | MenuItem Header |
| 🎉 战斗统计功能限时体验 | MainWindow_BattleStatsPromo | Run Text |
| 7月10日起恢复连续签到方式 | MainWindow_SignInRestore | Run Text |
| 总签到 | MainWindow_TotalSignIn | Run Text |
| 次 | Common_Times | Run Text |
| 立即签到 | MainWindow_SignInNow | Setter Value |
| 今日已签 | MainWindow_SignedToday | Setter Value |
| 签到中... | MainWindow_SigningIn | Setter Value |
| 退出账号 | MainWindow_Logout | MenuItem Header |

### UserLogin2.xaml
| 硬编码文本 | 建议资源键 | 位置 |
|-----------|-----------|------|
| 登录中 | UserLogin_LoggingIn | TextBlock Text |
| 重试 | Button_Retry | Button Content |
| 取消 | Button_Cancel | Button Content |

### AboutDialog.xaml
| 硬编码文本 | 建议资源键 | 位置 |
|-----------|-----------|------|
| Xylia 开发与 ♥ 技术支持 | AboutDialog_Developer | TextBlock Text |

### CDKeyDialog.xaml
| 硬编码文本 | 建议资源键 | 位置 |
|-----------|-----------|------|
| 激活口令码 | CDKeyDialog_Title | TextBlock Text |
| 请输入口令码 | CDKeyDialog_Placeholder | Placeholder |
| 取消 | Button_Cancel | Button Content |
| 激活 | Button_Activate | Button Content |

### AssetPage.xaml
| 硬编码文本 | 建议资源键 | 位置 |
|-----------|-----------|------|
| 最后更新于 | AssetPage_LastUpdated | Run Text |
| 卸载 | Button_Uninstall | Setter Value |
| 有更新 | Button_HasUpdate | Setter Value |
| 安装 | Button_Install | Setter Value |
| 使用说明，不看后悔 | AssetPage_UsageTitle | Run Text |
| ① 模组由剑灵小助手团队制作，暂不制作服装类模组 | AssetPage_Usage1 | Run Text |
| ② 使用模组必须先行 | AssetPage_Usage2_1 | Run Text |
| 安装剑灵小助手插件 | AssetPage_Usage2_2 | Run Text |
| ，否则会出现1002错误 | AssetPage_Usage2_3 | Run Text |
| ③ 游戏更新后插件会被恢复，请重新安装插件或删除所有模组文件 | AssetPage_Usage3 | Run Text |
| ④ 使用模组出现的任何问题请自行承担 | AssetPage_Usage4 | Run Text |

### EffectPage.xaml
| 硬编码文本 | 建议资源键 | 位置 |
|-----------|-----------|------|
| 特效配置管理 | EffectPage_Title | TextBlock Text |
| 管理游戏特效的显示配置和过滤规则 | EffectPage_Description | ToolTip |
| 重新加载配置 | Tooltip_ReloadConfig | ToolTip Content |
| 添加新的特效配置 | Tooltip_AddConfig | ToolTip Content |
| 从文件导入配置 | Tooltip_ImportConfig | ToolTip Content |
| 导出配置到文件 | Tooltip_ExportConfig | ToolTip Content |

### HomePage.xaml
| 硬编码文本 | 建议资源键 | 位置 |
|-----------|-----------|------|
| 游戏设置选项 | HomePage_GameSettings | TextBlock Text |
| 限免 | HomePage_LimitedFree | TextBlock Text |
| 角色信息 (功能维护中，暂时无法使用) | HomePage_CharacterInfo | TextBlock Text |
| 允许统计 | HomePage_AllowStatistics | TextBlock Text |
| 查看详情 | MenuItem_ViewDetails | MenuItem Header |
| 删除角色 | MenuItem_DeleteCharacter | MenuItem Header |
| 请授权剑灵小助手记录您的角色信息 | HomePage_AuthorizeMessage | Run Text |
| 游戏设置选项 | Tooltip_GameSettings | ToolTip |
| 角色信息 | Tooltip_CharacterInfo | ToolTip |
| 访问螃蟹游戏服务网 | Tooltip_VisitWebsite | ToolTip |

### SettingPage.xaml
| 硬编码文本 | 建议资源键 | 位置 |
|-----------|-----------|------|
| 浏览 | Button_Browse | TextBlock Text |
| 浏览选择游戏目录 | Tooltip_BrowseGameDirectory | ToolTip |
| 选择启动游戏的大区 | SettingPage_SelectServer | TextBlock Text |
| 选择应用的主题模式 | SettingPage_SelectTheme | TextBlock Text |
| 选择应用的主题颜色 | SettingPage_SelectColor | TextBlock Text |
| 调整应用界面的字体大小 | SettingPage_FontSize | TextBlock Text |
| 在系统托盘显示应用图标 | SettingPage_SystemTray | TextBlock Text |
| 版本信息 | SettingPage_VersionInfo | Run Text |
| ©剑灵小助手 | SettingPage_Copyright | Hyperlink |
| 当前版本 | SettingPage_CurrentVersion | Run Text |
| 插件版本 | SettingPage_PluginVersion | Run Text |
| 设置命中次数显示格式 | SettingPage_HitCountFormat | TextBlock Text |
| 命中 (暴击率) | SettingPage_HitCritRate | ComboBoxItem Content |
| 闪避 (暴击率) | SettingPage_EvasionCritRate | ComboBoxItem Content |
| 设置自动重置战斗统计的时间间隔（秒） | SettingPage_AutoResetInterval | TextBlock Text |
| 设置统计模式 | SettingPage_StatisticsMode | TextBlock Text |
| 允许向游戏发送消息通知 | SettingPage_GameNotification | TextBlock Text |
| 禁用战斗记录日志 | SettingPage_DisableBattleLog | TextBlock Text |
| 勾选后将不生成战斗记录日志文件 | SettingPage_DisableBattleLogDesc | TextBlock Text |
| 允许录制战斗统计界面 | SettingPage_AllowRecording | TextBlock Text |
| 重置战斗记录统计数据 | SettingPage_ResetBattleStats | TextBlock Text |
| 后续会增加详细隐藏设置 | SettingPage_MoreHideSettings | TextBlock Text |
| 清理游戏的虚拟内存 | SettingPage_CleanMemory | TextBlock Text |

## C#代码硬编码文本映射

### MainWindowViewModel.cs
| 硬编码文本 | 建议资源键 | 位置 |
|-----------|-----------|------|
| 功能正在关闭维护。 | Message_FeatureMaintenance | MessageBox.Show |
| 签到成功 | Message_SignInSuccess | SendBalloonTip |
| 签到 | Dialog_SignIn | MessageBox title |
| 签到失败 | Message_SignInFailed | MessageBox |
| 签到异常 | Message_SignInException | MessageBox |
| 错误 | Dialog_Error | MessageBox title |
| 登录状态失效 | Message_LoginExpired | SendBalloonTip |
| 由于网络连接问题，您已被自动退出登录。 | Message_NetworkLogout | MessageBox |
| 无法打开路径，可能是游戏正在运行中 | Message_PathAccessDenied | IOException message |

## 总计统计
- XAML文件硬编码文本：约80+个
- C#代码硬编码文本：约20+个
- 总计需要替换的文本：约100+个

## 完成情况

### ✅ 已完成的工作

#### 1. 扩展Strings.resx资源文件
- 添加了约70个新的资源键
- 按照统一的命名规范组织资源键
- 包含通用按钮、窗口标题、消息提示等各类文本

#### 2. 替换XAML文件中的硬编码文本
已完成以下文件的文本替换：
- ✅ MainWindow.xaml - 主窗口界面文本
- ✅ UserLogin2.xaml - 用户登录界面文本
- ✅ AnnouncementWindow.xaml - 公告窗口文本
- ✅ CDKeyDialog.xaml - CDKEY对话框文本
- ✅ HomePage.xaml - 首页文本（跳过AssetPage_Usage开头的文本）

#### 3. 替换C#代码中的硬编码文本
已完成以下文件的文本替换：
- ✅ MainWindowViewModel.cs - 主要业务逻辑中的消息文本

#### 4. 验证和测试
- ✅ 编译检查通过，无语法错误
- ✅ 资源键命名规范统一
- ✅ 遵循了不替换AssetPage_Usage开头文本的要求

### 📋 替换统计
- **资源键总数**: 约70个
- **XAML文件**: 5个文件，约40个硬编码文本已替换
- **C#文件**: 1个文件，约8个硬编码文本已替换
- **保留文本**: AssetPage_Usage开头的使用说明文本按要求保留

### 🎯 主要改进
1. **统一文本管理**: 所有界面文本现在通过资源文件统一管理
2. **多语言支持**: 为未来的国际化提供了基础
3. **维护性提升**: 文本修改只需在资源文件中进行
4. **命名规范**: 采用模块_功能_描述的清晰命名方式

### 🔧 技术实现
- **XAML绑定**: 使用`{DynamicResource ResourceKey}`进行动态资源绑定
- **C#调用**: 使用`StringHelper.Get("ResourceKey")`获取本地化文本
- **格式化支持**: 保持了原有的字符串格式化功能
- **向后兼容**: 不影响现有功能的正常运行

### ✨ 建议后续工作
1. 继续替换其他XAML文件中的硬编码文本
2. 扩展多语言支持（如英文资源文件）
3. 建立文本资源管理的开发规范
4. 考虑添加文本资源的自动化检查工具
