{layout name="manage/template" /}

<div class="admin-content">
	<div class="admin-content-body">
		<div class="am-cf am-padding am-padding-bottom-0">
			<div class="am-fl am-cf">
				<strong class="am-text-primary am-text-lg">骗子详情页</strong>
			</div>
		</div>
		<hr />
		<div class="am-g">
			<div class="am-u-sm-12 am-u-sm-centered">
				<div class="am-panel am-panel-primary">
					<div class="am-panel-hd">基本信息</div>
					<div class="am-panel-bd">
						<table class="am-table am-table-bordered">
							<tr>
								<td class="am-primary">大区</td>
								<td><?=$liar_info['server']?></td>
								<td class="am-primary">服务器</td>
								<td><?=$liar_info['area_name']?></td>
								<td class="am-primary">游戏昵称</td>
								<td><a href="<?=ViewEquip($liar_info['area'],$liar_info['rolename'])?>" target="_blank"><?=$liar_info['rolename']?></a></td>
								<td class="am-primary"><?php if($isAdmin) echo("审核状态"); ?></td>
								<td><?php if($isAdmin) echo($states); ?></td>
							</tr>
							<tr>
								<td class="am-primary">QQ</td>
								<td><?=$liar_info['qq']?></td>
								<td class="am-primary">支付宝</td>
								<td><?=$liar_info['alipay']?></td>
								<td class="am-primary">微信</td>
								<td><?=$liar_info['wechat']?></td>
								<td class="am-primary">手机</td>
								<td><?=$liar_info['tel']?></td>
							</tr> <?php if($isAdmin) { ?> <tr>
								<td class="am-primary">举报人</td>
								<td><?=$liar_info['up']?></td>
								<td class="am-primary">举报时间</td>
								<td><?=date("Y-m-d h:i:s",$liar_info['time'])?></td>
								<td class="am-primary">审核时间</td>
								<td><?php if($liar_info['is_shenhe'] != "0") echo(date("Y-m-d h:i:s",$liar_info['checktime'])); else echo("尚未审核"); ?></td>
								<td class="am-primary">审核人</td>
								<td><?=$liar_info['username']?></td>
							</tr> <?php } ?>
						</table>
					</div>
				</div>
				<div class="am-panel am-panel-primary">
					<div class="am-panel-hd">行骗手法,证据以及截图</div>
					<div class="am-panel-bd">
						<p class="am-text-primary">证据</p>
						<blockquote> <?php if(!empty($liar_info['url'])){ ?> <a href="<?=$liar_info['url']?>" target="_blank"><?=$liar_info['url']?></a> <?php  }else{  ?> 公认 <?php  }  ?> </blockquote>
						<p class="am-text-primary">手法</p>
						<blockquote> <?=$liar_info['technique']?> </blockquote>
						<p class="am-text-primary">截图</p> <?=$liar_info['pics']?>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>