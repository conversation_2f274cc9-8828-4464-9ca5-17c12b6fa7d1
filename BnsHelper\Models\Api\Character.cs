﻿namespace Xylia.Preview.UI.GameUI.Scene.Game_CharacterInfo.Api;
public struct Character
{
    public Guid account_id;
	public int id;
	public string name;
	public int server_id;
	public int server_name;
	public string class_name;
	public string clazz;
	public int level;
	public string mastery_faction;
	public string mastery_faction_name;
	public int mastery_level;
	public DateTime last_play_end_time;
	public DateTime last_play_start_time;
	public string guild;
	public string profile_url;
	public bool playing;
}