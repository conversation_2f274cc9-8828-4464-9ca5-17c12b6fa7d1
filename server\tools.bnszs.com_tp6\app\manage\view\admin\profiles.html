{layout name="manage/template" /} 

<div class="admin-content">
	<div class="admin-content-body">
		<div class="am-cf am-padding">
			<div class="am-fl am-cf"><strong class="am-text-primary am-text-lg">首页</strong> / <small>自定义列表</small></div>
		</div>
		<div class="am-cf am-padding">
			<div class="am-fl am-cf">当前共有{$data->total()}份</div>
		</div>
		<hr>
		<div class="am-g">
			<div class="am-u-sm-12 am-u-md-3">
				<div class="am-input-group am-input-group-sm">
					<input type="text" class="am-form-field" id="search_value" placeholder="输入需要查询的信息">
					<span class="am-input-group-btn">
						<button class="am-btn am-btn-default" type="button" id="search" onclick="search();">查询</button>
					</span>
				</div>
			</div>
			<div class="am-select am-u-sm-9">
				<select class="ModeSelect" onchange="changeMode()">
					<option value="">全部</option>
					<option value="1">未审核</option>
					<option value="2">审核退回</option>
				</select>
			</div>
		</div>
		<div class="am-g">
			<div class="am-u-sm-12">
				{$data|raw}
				<table class="am-table am-table-bd am-table-striped admin-content-table">
					<thead>
						<tr>
							<th>ID</th>
							<th>服务器</th>
							<th>角色编号</th>
							<th>角色昵称</th>
							<th>操作</th>
						</tr>
					</thead>
					<tbody>
						{volist name='data' id='item'}
						<tr data-t="{$item.id}" data-s="{$item.server}" data-c="{$item.name}" class="data" style="{switch name="$item.status"}
							{case value="1"}color: green{/case}
							{case value="2"}color: red{/case}
						{/switch}">
							<td>{$item.id}</td>
							<td><a title="点击可搜索同服务器的数据" href="?search={$item.server}">{$item.server}</a></td>
							<td>{$item.newpcid}</td>
							<td><a title="点击可查看装备详情" target="_blank" href="/ingame/bs/character/profile?s={$item.server}&c={$item.name}">{$item.name}</a></td>
							<td>
								<div class="am-btn-group am-btn-group-xs">
								  <a target="_blank" href="/manage/profile?server={$item.server}&name={$item.name}&admin=1" class="am-btn am-btn-default am-btn-xs am-text-secondary"><span class="am-icon-pencil-square-o"></span> 编辑</a>
								</div>
							</td>
						</tr>
						{/volist}
					</tbody>
				</table>
			</div>
		</div>
	</div>
</div>

<script>
	//回车事件绑定
	$('#search_value').bind('keypress', function(event) {
	  if (event.keyCode == "13") {            
	    event.preventDefault(); 
	    //回车执行查询
	    search();
	  }
	});

	function search() {
		var search_value = $("#search_value").val();
		if (search_value == null || search_value == undefined || search_value == '') {
	  	alert("请输入查询条件 如服务器:无日峰 或角色名称 以及任意你知道的信息");return false;
	  }else{
	  	window.location.href = "?search=" + search_value;
	  }
	}

	function changeMode() {
		var mode = $('.ModeSelect').val();
		window.location.href = "?mode=" + mode;
	}

	$(function() {
		var mode = getQueryVariable("mode");
		$(".ModeSelect").val(mode);
	});

	$("tbody tr").each(function() {
	  $.ajax({
	      type: "GET",
	      url: '//tools.bnszs.com/api/roleIs302',
	      data: { s: $(this).data('s'), c: $(this).data('c'), t: $(this).data('t') },
	      dataType: "jsonp", 
	      success: function (result) {
	          console.log(111);
	      }
	  });    
	});
</script>