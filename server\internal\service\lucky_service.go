package service

import (
	"fmt"
	"log"
	"math/rand"
	"strings"
	"time"

	"udp-server/server/internal/database"
	"udp-server/server/internal/model"
	"udp-server/server/pkg/cache"
	"udp-server/server/pkg/metrics"

	"gorm.io/gorm"
)

// CDKeyActivateResult CDkey激活结果
type CDKeyActivateResult struct {
	RequireGroupCheck bool   // 是否需要群验证
	Group             *int64 // 需要验证的群号
}

// LuckyService 签到抽奖服务
type LuckyService struct {
	db                *gorm.DB
	cache             cache.Cache
	hashCache         CacheService // 支持Hash操作的缓存服务
	permissionService *PermissionService
}

// NewLuckyService 创建签到抽奖服务实例
func NewLuckyService(cache cache.Cache, permissionService *PermissionService, redisAddr, redisPassword string, redisDB int) *LuckyService {
	// 创建支持Hash操作的缓存服务
	hashCache, err := NewDualCache(redisAddr, redisPassword, redisDB, time.Minute*5, metrics.NewMetrics())
	if err != nil {
		log.Fatalf("[ERROR] 创建Hash缓存服务失败: %v", err)
	}

	return &LuckyService{
		db:                database.GetDB(),
		cache:             cache,
		hashCache:         hashCache,
		permissionService: permissionService,
	}
}

// DrawRequest 抽奖请求
type DrawRequest struct {
	UID               uint64 `json:"uid"`
	IP                string `json:"ip"`
	DeviceFingerprint string `json:"device_fingerprint"` // 设备指纹，用于设备级别的签到控制
}

// DrawResponse 抽奖响应
type DrawResponse struct {
	Code    int    `json:"code"`
	Message string `json:"msg"`
	Count   int    `json:"count"`
}

// Draw 执行签到抽奖
func (s *LuckyService) Draw(req *DrawRequest) (*DrawResponse, error) {
	// 获取当前活动
	activity, err := s.getCurrentActivity()
	if err != nil {
		return nil, fmt.Errorf("没有正在进行中的活动")
	}

	// 首先检查用户签到操作时间缓存
	if hasSignedToday, err := s.checkUserSignInTimeCache(req.UID, activity.ID); err != nil {
		return nil, err
	} else if hasSignedToday {
		return nil, fmt.Errorf("今天已经签到过啦~")
	}

	// 检查用户签到不可用缓存
	if err := s.checkUserSignInUnavailableCache(req.UID, activity.ID); err != nil {
		return nil, err
	}

	// 检查设备是否今天已经签到
	if req.DeviceFingerprint != "" {
		if err := s.checkDeviceSignIn(req.DeviceFingerprint, activity.ID); err != nil {
			// 缓存设备签到不可用状态
			s.cacheUserSignInUnavailable(req.UID, activity.ID, err.Error())
			return nil, err
		}
	}

	// 获取或创建用户签到数据
	userDraw, err := s.getUserDraw(req.UID, activity.ID)
	if err != nil {
		return nil, err
	}

	// 更新用户签到状态
	if err := s.updateUserDrawStatus(userDraw, activity); err != nil {
		return nil, err
	}

	// 获取可用奖励
	rewards, err := s.getAvailableRewards(activity.ID, userDraw.Point)
	if err != nil {
		return nil, err
	}

	// 计算奖励
	selectedReward, err := s.calculateReward(rewards)
	if err != nil {
		return nil, err
	}

	// 处理奖励
	if err := s.processReward(selectedReward, userDraw, req.UID); err != nil {
		return nil, err
	}

	// 更新用户签到数据（使用 Updates 方法避免复合主键问题）
	if err := s.db.Model(&model.UserDraw{}).Where("uid = ? AND schedule = ?", userDraw.UID, userDraw.Schedule).Updates(map[string]interface{}{
		"extra":  userDraw.Extra,
		"day":    userDraw.Day,
		"point":  userDraw.Point,
		"number": userDraw.Number,
		"today":  userDraw.Today,
		"time":   userDraw.Time,
	}).Error; err != nil {
		return nil, fmt.Errorf("更新用户签到数据失败: %v", err)
	}

	// 签到成功后，缓存操作时间
	s.cacheUserSignInTime(req.UID, activity.ID)

	// 更新用户签到数据哈希缓存
	hashKey := fmt.Sprintf("user_draw_%d", activity.ID)
	field := fmt.Sprintf("%d", req.UID)
	cacheData := s.userDrawToHashData(userDraw)
	if err := s.hashCache.HSet(hashKey, field, cacheData); err != nil {
		log.Printf("[WARN] 更新用户签到数据哈希缓存失败: UID=%d, ActivityID=%d, Error=%v", req.UID, activity.ID, err)
		// 缓存失败不影响主流程
	}

	// 如果连续签到达到3天，清除用户权限缓存以便重新计算
	if userDraw.Day >= 3 && s.permissionService != nil {
		if err := s.permissionService.ClearUserPermissionCache(req.UID, "client"); err != nil {
			log.Printf("[WARN] 清除用户权限缓存失败: UID=%d, Error=%v", req.UID, err)
			// 缓存清理失败不影响主流程
		} else {
			log.Printf("[DEBUG] 用户连续签到%d天，已清除权限缓存: UID=%d", userDraw.Day, req.UID)
		}
	}

	// 记录设备签到状态
	s.recordSignInStatus(req.DeviceFingerprint, activity.ID)

	// 签到成功，清除用户签到不可用缓存
	s.clearUserSignInUnavailableCache(req.UID, activity.ID)

	// 生成返回文本
	text := s.generateResponseText(selectedReward, userDraw)

	return &DrawResponse{
		Code:    1,
		Message: text,
		Count:   userDraw.Extra,
	}, nil
}

// getCurrentActivity 获取当前活动（带缓存）
func (s *LuckyService) getCurrentActivity() (*model.Lucky, error) {
	cacheKey := "current_activity"

	// 先检查缓存
	var cachedActivity model.Lucky
	if err := s.cache.Get(cacheKey, &cachedActivity); err == nil {
		// 检查缓存的活动是否仍然有效
		now := time.Now()
		if cachedActivity.IsActivity &&
			now.After(cachedActivity.StartTime) &&
			now.Before(cachedActivity.EndTime) {
			return &cachedActivity, nil
		}
		// 缓存的活动已过期，删除缓存
		s.cache.Delete(cacheKey)
	}

	// 缓存未命中或已过期，从数据库查询
	var activity model.Lucky
	now := time.Now()

	err := s.db.Where("is_activity = ? AND startTime <= ? AND endTime >= ?",
		true, now, now).First(&activity).Error
	if err != nil {
		return nil, err
	}

	// 计算缓存时间：到活动结束时间，但最多缓存1小时
	cacheDuration := time.Until(activity.EndTime)
	if cacheDuration > time.Hour {
		cacheDuration = time.Hour
	}
	if cacheDuration < time.Minute {
		cacheDuration = time.Minute // 最少缓存1分钟
	}

	// 将结果存入缓存
	if err := s.cache.Set(cacheKey, activity, cacheDuration); err != nil {
		log.Printf("[WARN] 缓存当前活动失败: %v", err)
	}

	return &activity, nil
}

// checkDeviceSignIn 检查设备是否今天已经签到（使用Hash结构）
func (s *LuckyService) checkDeviceSignIn(deviceFingerprint string, activityID uint) error {
	hashKey := fmt.Sprintf("activity_signin_%d", activityID)
	field := fmt.Sprintf("device_%s", deviceFingerprint)

	lastSignTime, err := s.hashCache.HGet(hashKey, field)
	if err != nil {
		// 缓存未命中或错误时跳过检查
		return nil
	}

	if timestamp, ok := lastSignTime.(float64); ok {
		lastTime := time.Unix(int64(timestamp), 0)
		if s.isToday(lastTime) {
			return fmt.Errorf("当前设备今天已经签到过啦~")
		}
	}

	return nil
}

// getUserDraw 获取或创建用户签到数据（使用Hash结构缓存）
func (s *LuckyService) getUserDraw(uid uint64, activityID uint) (*model.UserDraw, error) {
	// 构建哈希键和字段
	hashKey := fmt.Sprintf("user_draw_%d", activityID)
	field := fmt.Sprintf("%d", uid)

	// 先从哈希缓存获取
	cachedData, err := s.hashCache.HGet(hashKey, field)
	if err == nil {
		// 解析缓存数据
		if dataMap, ok := cachedData.(map[string]interface{}); ok {
			if userDraw, success := s.hashDataToUserDraw(dataMap); success {
				log.Printf("[DEBUG] 用户签到数据哈希缓存命中: UID=%d, ActivityID=%d", uid, activityID)
				return userDraw, nil
			}
		}
	}

	// 缓存未命中，从数据库查询或创建
	log.Printf("[DEBUG] 用户签到数据哈希缓存未命中，查询数据库: UID=%d, ActivityID=%d", uid, activityID)
	var userDraw model.UserDraw

	// 使用 INSERT ... ON DUPLICATE KEY UPDATE 语法避免主键冲突
	// 这是处理并发插入的最佳实践
	currentTime := time.Now()

	// 使用原生SQL执行 INSERT ... ON DUPLICATE KEY UPDATE
	sql := `
		INSERT INTO user_draw (uid, schedule, extra, day, point, number, today, time)
		VALUES (?, ?, 0, 1, 1, 0, 0, ?)
		ON DUPLICATE KEY UPDATE
			uid = uid  -- 保持原值，这样可以触发查询但不修改数据
	`

	result := s.db.Exec(sql, uid, activityID, currentTime)
	if result.Error != nil {
		log.Printf("[ERROR] 执行用户签到记录插入失败: UID=%d, ActivityID=%d, Error=%v", uid, activityID, result.Error)
		return nil, result.Error
	}

	// 查询记录（此时记录肯定存在）
	if err := s.db.Where("uid = ? AND schedule = ?", uid, activityID).First(&userDraw).Error; err != nil {
		log.Printf("[ERROR] 查询用户签到记录失败: UID=%d, ActivityID=%d, Error=%v", uid, activityID, err)
		return nil, err
	}

	if result.RowsAffected > 0 {
		log.Printf("[DEBUG] 创建新用户签到记录: UID=%d, ActivityID=%d", uid, activityID)
	} else {
		log.Printf("[DEBUG] 获取现有用户签到记录: UID=%d, ActivityID=%d", uid, activityID)
	}

	log.Printf("[DEBUG] 成功获取用户签到记录: UID=%d, ActivityID=%d", uid, activityID)

	// 将数据存入哈希缓存
	cacheData := s.userDrawToHashData(&userDraw)

	if err := s.hashCache.HSet(hashKey, field, cacheData); err != nil {
		log.Printf("[WARN] 缓存用户签到数据到哈希表失败: UID=%d, ActivityID=%d, Error=%v", uid, activityID, err)
		// 缓存失败不影响主流程
	}

	return &userDraw, nil
}

// userDrawToHashData 将UserDraw结构转换为哈希缓存数据
func (s *LuckyService) userDrawToHashData(userDraw *model.UserDraw) map[string]interface{} {
	return map[string]interface{}{
		"uid":      userDraw.UID,
		"schedule": userDraw.Schedule,
		"extra":    userDraw.Extra,
		"day":      userDraw.Day,
		"point":    userDraw.Point,
		"number":   userDraw.Number,
		"today":    userDraw.Today,
		"time":     userDraw.Time.Format(time.RFC3339),
	}
}

// hashDataToUserDraw 将哈希缓存数据转换为UserDraw结构
func (s *LuckyService) hashDataToUserDraw(dataMap map[string]interface{}) (*model.UserDraw, bool) {
	var userDraw model.UserDraw

	// 从缓存数据中恢复UserDraw结构
	if uidVal, ok := dataMap["uid"].(float64); ok {
		userDraw.UID = uint64(uidVal)
	}
	if schedule, ok := dataMap["schedule"].(float64); ok {
		userDraw.Schedule = uint(schedule)
	}
	if extra, ok := dataMap["extra"].(float64); ok {
		userDraw.Extra = int(extra)
	}
	if day, ok := dataMap["day"].(float64); ok {
		userDraw.Day = int(day)
	}
	if point, ok := dataMap["point"].(float64); ok {
		userDraw.Point = int(point)
	}
	if number, ok := dataMap["number"].(float64); ok {
		userDraw.Number = int(number)
	}
	if today, ok := dataMap["today"].(float64); ok {
		userDraw.Today = int(today)
	}
	if timeStr, ok := dataMap["time"].(string); ok {
		if parsedTime, err := time.Parse(time.RFC3339, timeStr); err == nil {
			userDraw.Time = parsedTime
		} else {
			return nil, false
		}
	}

	return &userDraw, true
}

// checkUserSignInToday 检查用户今天是否已经签到（使用Hash结构）
func (s *LuckyService) checkUserSignInToday(uid uint64, activityID uint) (bool, error) {
	hashKey := fmt.Sprintf("user_draw_%d", activityID)
	field := fmt.Sprintf("%d", uid)

	// 从哈希缓存获取用户签到数据
	cachedData, err := s.hashCache.HGet(hashKey, field)
	if err != nil {
		// 缓存未命中，表示用户今天未签到
		return false, nil
	}

	// 解析缓存数据
	if dataMap, ok := cachedData.(map[string]interface{}); ok {
		if timeStr, ok := dataMap["time"].(string); ok {
			if lastSignTime, err := time.Parse(time.RFC3339, timeStr); err == nil {
				// 检查是否为今天
				return s.isToday(lastSignTime), nil
			}
		}
	}

	return false, nil
}

// clearUserDrawCache 清理用户签到数据哈希缓存
func (s *LuckyService) clearUserDrawCache(uid uint64, activityID uint) error {
	hashKey := fmt.Sprintf("user_draw_%d", activityID)
	field := fmt.Sprintf("%d", uid)

	if err := s.hashCache.HDel(hashKey, field); err != nil {
		log.Printf("[WARN] 清理用户签到数据哈希缓存失败: UID=%d, ActivityID=%d, Error=%v", uid, activityID, err)
		return err
	}
	log.Printf("[DEBUG] 已清理用户签到数据哈希缓存: UID=%d, ActivityID=%d", uid, activityID)
	return nil
}

// updateUserDrawStatus 更新用户签到状态
func (s *LuckyService) updateUserDrawStatus(userDraw *model.UserDraw, activity *model.Lucky) error {
	now := time.Now()

	if userDraw.IsToday() {
		// 今天已经签到过
		if userDraw.Today >= activity.Free {
			if userDraw.Extra == 0 {
				return fmt.Errorf("今天已经签到过啦~")
			}
			userDraw.Extra--
		}
		userDraw.Today++
	} else if userDraw.IsYesterday() {
		// 昨天签到，连续签到
		userDraw.Today = 1
		userDraw.Day++
		userDraw.Point++
	} else {
		// 断签或首次签到
		userDraw.Today = 1
		userDraw.Day = 1
		if activity.IsContinue {
			userDraw.Point = 1 // 重置奖励点数
		} else {
			userDraw.Point++ // 累积奖励点数
		}
	}

	userDraw.Time = now
	return nil
}

// getAvailableRewards 获取可用奖励（带缓存）
func (s *LuckyService) getAvailableRewards(activityID uint, userPoint int) ([]model.LuckyReward, error) {
	cacheKey := fmt.Sprintf("activity_rewards_%d", activityID)

	// 先检查缓存
	var cachedRewards []model.LuckyReward
	if err := s.cache.Get(cacheKey, &cachedRewards); err == nil {
		// 过滤符合用户点数要求的奖励
		return s.filterRewardsByPoint(cachedRewards, userPoint), nil
	}

	// 缓存未命中，从数据库查询
	var rewards []model.LuckyReward
	err := s.db.Where("activity = ? AND is_use = ?", activityID, true).Find(&rewards).Error
	if err != nil {
		return nil, err
	}

	// 将结果存入缓存（缓存24小时）
	if err := s.cache.Set(cacheKey, rewards, 24*time.Hour); err != nil {
		log.Printf("[WARN] 缓存活动奖励失败: ActivityID=%d, Error=%v", activityID, err)
	}

	// 过滤符合用户点数要求的奖励
	return s.filterRewardsByPoint(rewards, userPoint), nil
}

// filterRewardsByPoint 根据用户点数和可用性过滤奖励
func (s *LuckyService) filterRewardsByPoint(rewards []model.LuckyReward, userPoint int) []model.LuckyReward {
	var availableRewards []model.LuckyReward
	for _, reward := range rewards {
		// 检查点数要求和可用性
		if reward.Point <= userPoint && reward.IsAvailable() {
			availableRewards = append(availableRewards, reward)
		}
	}
	return availableRewards
}

// calculateReward 计算奖励
func (s *LuckyService) calculateReward(rewards []model.LuckyReward) (*model.LuckyReward, error) {
	if len(rewards) == 0 {
		return nil, fmt.Errorf("非常抱歉，本次未中奖")
	}

	// 分离保底奖励和随机奖励
	var guardRewards []model.LuckyReward
	var randomRewards []model.LuckyReward

	for _, reward := range rewards {
		if reward.Type == "guard" {
			guardRewards = append(guardRewards, reward)
		} else {
			randomRewards = append(randomRewards, reward)
		}
	}

	// 先检查保底奖励
	for _, reward := range guardRewards {
		randNum := rand.Intn(10000) + 1
		if randNum <= reward.Weight {
			return &reward, nil
		}
	}

	// 随机奖励计算
	if len(randomRewards) > 0 {
		totalWeight := 0
		for _, reward := range randomRewards {
			totalWeight += reward.Weight
		}

		if totalWeight > 0 {
			randNum := rand.Intn(totalWeight) + 1
			cumulative := 0

			for _, reward := range randomRewards {
				cumulative += reward.Weight
				if randNum <= cumulative {
					return &reward, nil
				}
			}
		}
	}

	return nil, fmt.Errorf("非常抱歉，本次未中奖")
}

// processReward 处理奖励
func (s *LuckyService) processReward(reward *model.LuckyReward, userDraw *model.UserDraw, uid uint64) error {
	if reward.IsResetPoint {
		userDraw.Point = 0
	}
	userDraw.Number++

	// 如果有实际奖励
	if reward.Reward != "" {
		// 更新限制次数
		if reward.Limit > 0 {
			if err := s.db.Model(reward).Update("limit_current", reward.LimitCurrent+1).Error; err != nil {
				return err
			}
		}

		// 激活CDKey（签到触发的激活使用管理模式）
		if _, err := s.ActivateCDKey(reward.Reward, uid, true, false); err != nil { // true表示管理模式，false表示无需群验证
			return err
		}

		// 记录抽奖结果
		result := model.UserDrawResult{
			Reward: reward.ID,
			UID:    uid,
		}
		if err := s.db.Create(&result).Error; err != nil {
			return err
		}
	}

	return nil
}

// ActivateCDKey 统一的CDKEY激活方法
// isManagementMode: true表示管理模式（如签到触发），false表示普通模式（如用户手动激活）
// isVerified: 仅在普通模式下有效，表示是否已通过群验证
func (s *LuckyService) ActivateCDKey(cdkeyStr string, uid uint64, isManagementMode bool, isVerified bool) (*CDKeyActivateResult, error) {
	// 提交频率限制
	if !isManagementMode {
		// 检查用户CDkey提交频率限制（防止恶意提交）
		if !isVerified { // 只在第一次提交时检查频率限制
			if s.cache != nil {
				// 使用哈希表存储用户CDkey提交时间戳
				var userCdkeyTimes map[uint64]int64
				err := s.cache.Get("user_cdkey_times", &userCdkeyTimes)
				if err != nil {
					userCdkeyTimes = make(map[uint64]int64)
				}

				// 检查是否在15秒内提交过
				if lastTime, exists := userCdkeyTimes[uid]; exists {
					if time.Now().Unix()-lastTime < 15 {
						log.Printf("[WARN] 用户CDkey提交过于频繁: UID=%d", uid)
						return nil, fmt.Errorf("请求过多，请稍后再试")
					}
				}

				// 更新时间戳
				userCdkeyTimes[uid] = time.Now().Unix()

				// 清理过期的时间戳（超过15秒的）
				currentTime := time.Now().Unix()
				for u, t := range userCdkeyTimes {
					if currentTime-t >= 15 {
						delete(userCdkeyTimes, u)
					}
				}

				// 保存回缓存，设置30秒过期（给清理留余量）
				s.cache.Set("user_cdkey_times", userCdkeyTimes, 30*time.Second)
			}
		}

		// 检查CDkey是否在不存在缓存中（避免重复查询不存在的CDkey）
		if s.cache != nil {
			// 使用哈希表存储无效CDkey
			var invalidCdkeys map[string]int64
			err := s.cache.Get("invalid_cdkeys", &invalidCdkeys)
			if err != nil {
				invalidCdkeys = make(map[string]int64)
			}

			// 检查CDkey是否在无效缓存中且未过期
			if timestamp, exists := invalidCdkeys[cdkeyStr]; exists {
				if time.Now().Unix()-timestamp < 300 { // 5分钟内有效
					log.Printf("[INFO] CDKey在无效缓存中，直接返回不存在: %s", cdkeyStr)
					return nil, fmt.Errorf("口令码不存在")
				} else {
					// 过期了，从缓存中删除
					delete(invalidCdkeys, cdkeyStr)
					// 保存更新后的缓存
					s.cache.Set("invalid_cdkeys", invalidCdkeys, 10*time.Minute)
				}
			}
		}
	}

	// 检查CDKey是否存在
	var cdkey model.CDkey
	if err := s.db.Where("cdkey = ?", cdkeyStr).First(&cdkey).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// 普通模式下将不存在的CDkey加入缓存，避免重复查询（缓存5分钟）
			if !isManagementMode && s.cache != nil {
				// 使用哈希表存储无效CDkey
				var invalidCdkeys map[string]int64
				err := s.cache.Get("invalid_cdkeys", &invalidCdkeys)
				if err != nil {
					invalidCdkeys = make(map[string]int64)
				}

				// 添加当前CDkey到无效缓存
				invalidCdkeys[cdkeyStr] = time.Now().Unix()

				// 清理过期的无效CDkey（超过5分钟的）
				currentTime := time.Now().Unix()
				for key, timestamp := range invalidCdkeys {
					if currentTime-timestamp >= 300 { // 5分钟
						delete(invalidCdkeys, key)
					}
				}

				// 保存回缓存，设置10分钟过期（给清理留余量）
				s.cache.Set("invalid_cdkeys", invalidCdkeys, 10*time.Minute)
			}
			return nil, fmt.Errorf("口令码不存在")
		}
		log.Printf("[ERROR] 查询CDKey失败: %s, Error=%v", cdkeyStr, err)
		return nil, err
	}

	// 管理模式下跳过大部分检查
	if !isManagementMode {
		// 检查CDKey是否可以使用
		if cdkey.StartTime == nil {
			return nil, fmt.Errorf("口令码不存在")
		}
		if cdkey.IsUsed() {
			return nil, fmt.Errorf("口令码已使用完毕")
		}
		if !cdkey.IsActive() {
			return nil, fmt.Errorf("口令码不在有效期内")
		}

		// 检查用户是否已经使用过此CDKey
		var existingLog model.UserLog
		if err := s.db.Where("uid = ? AND type = ? AND extra = ?", uid, "cdkey", cdkeyStr).First(&existingLog).Error; err == nil {
			return nil, fmt.Errorf("你已经使用过此口令码，无法重复使用")
		}

		// 检查批次限制（同一批次的CDKey不能被同一用户重复使用）
		if *cdkey.Batch != 0 {
			var batchUsageCount int64
			err := s.db.Model(&model.UserLog{}).
				Joins("JOIN bns_cdkey ON user_log.extra = bns_cdkey.cdkey").
				Where("user_log.uid = ? AND user_log.type = ? AND bns_cdkey.batch = ?", uid, "cdkey", *cdkey.Batch).
				Count(&batchUsageCount).Error
			if err != nil {
				log.Printf("[ERROR] 检查批次使用情况失败: UID=%d, Batch=%d, Error=%v", uid, *cdkey.Batch, err)
				return nil, fmt.Errorf("系统错误，请稍后重试")
			}

			if batchUsageCount > 0 {
				return nil, fmt.Errorf("你已经使用过相同的口令码，无法重复使用")
			}
		}

		// 如果CDKey需要群验证且用户未验证
		if *cdkey.Group != 0 && !isVerified {
			log.Printf("[INFO] 需要完成群验证: %s, Group=%d", cdkeyStr, *cdkey.Group)
			return &CDKeyActivateResult{
				RequireGroupCheck: true,
				Group:             cdkey.Group,
			}, nil
		}
	}

	// 使用事务确保数据一致性
	err := s.db.Transaction(func(tx *gorm.DB) error {
		// 管理模式下不减少次数，普通模式下需要减少次数
		if !isManagementMode {
			if err := tx.Model(&cdkey).Update("RemainNum", gorm.Expr("RemainNum - 1")).Error; err != nil {
				log.Printf("[ERROR] 更新CDKey剩余次数失败: %s, Error=%v", cdkeyStr, err)
				return err
			}
		} else {
			log.Printf("[DEBUG] 管理模式激活，跳过次数减少: %s", cdkeyStr)
		}

		// 记录用户激活日志
		now := time.Now()
		userLog := model.UserLog{
			UID:   uid,
			Type:  "cdkey",
			Extra: cdkeyStr,
			Time:  now.Format("2006-01-02T15:04:05Z07:00"),
		}
		if err := tx.Create(&userLog).Error; err != nil {
			log.Printf("[ERROR] 创建用户CDKey日志失败: UID=%d, CDKey=%s, Error=%v", uid, cdkeyStr, err)
			return err
		}

		if isManagementMode {
			log.Printf("[INFO] 管理模式CDKey激活成功（无限制）: UID=%d, CDKey=%s, Type=%s", uid, cdkeyStr, cdkey.Type)
		} else {
			log.Printf("[INFO] 普通模式CDKey激活成功: UID=%d, CDKey=%s, Type=%s, RemainNum=%d", uid, cdkeyStr, cdkey.Type, cdkey.RemainNum-1)
		}

		// 清除用户权限缓存，使权限能够重新计算
		if s.permissionService != nil {
			if err := s.permissionService.ClearUserPermissionCache(uid, cdkey.Type); err != nil {
				log.Printf("[WARN] 清除用户权限缓存失败: UID=%d, Type=%s, Error=%v", uid, cdkey.Type, err)
				// 缓存清理失败不影响主流程
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &CDKeyActivateResult{
		RequireGroupCheck: false,
		Group:             nil,
	}, nil
}

// GetUserPermissionExpiration 获取用户权限过期时间（供Handler使用）
func (s *LuckyService) GetUserPermissionExpiration(uid uint64, permissionType string) (int64, error) {
	if s.permissionService != nil {
		return s.permissionService.GetUserPermissionExpiration(uid, permissionType)
	}
	return 0, fmt.Errorf("权限服务不可用")
}

// generateResponseText 生成响应文本
func (s *LuckyService) generateResponseText(reward *model.LuckyReward, userDraw *model.UserDraw) string {
	text := reward.Text
	text = strings.ReplaceAll(text, "{day}", fmt.Sprintf("%d", userDraw.Day))
	text = strings.ReplaceAll(text, "{num}", fmt.Sprintf("%d", userDraw.Number))
	text = strings.ReplaceAll(text, "{point}", fmt.Sprintf("%d", userDraw.Point))
	return text
}

// isToday 检查时间是否为今天
func (s *LuckyService) isToday(t time.Time) bool {
	now := time.Now()
	return t.Year() == now.Year() && t.YearDay() == now.YearDay()
}

// checkUserSignInUnavailableCache 检查用户签到不可用缓存
func (s *LuckyService) checkUserSignInUnavailableCache(uid uint64, activityID uint) error {
	cacheKey := fmt.Sprintf("signin_unavailable_%d_%d", uid, activityID)

	var cachedData struct {
		Date   string `json:"date"`
		Reason string `json:"reason"`
	}

	err := s.cache.Get(cacheKey, &cachedData)
	if err != nil {
		// 缓存未命中，表示没有不可用缓存
		return nil
	}

	// 检查是否为今天的缓存
	today := time.Now().Format("2006-01-02")
	if cachedData.Date == today {
		return fmt.Errorf(cachedData.Reason)
	}

	// 过期缓存，删除
	s.cache.Delete(cacheKey)
	return nil
}

// cacheUserSignInUnavailable 缓存用户签到不可用状态
func (s *LuckyService) cacheUserSignInUnavailable(uid uint64, activityID uint, reason string) {
	cacheKey := fmt.Sprintf("signin_unavailable_%d_%d", uid, activityID)

	cachedData := struct {
		Date   string `json:"date"`
		Reason string `json:"reason"`
	}{
		Date:   time.Now().Format("2006-01-02"),
		Reason: reason,
	}

	// 缓存到明天凌晨过期
	tomorrow := time.Now().AddDate(0, 0, 1)
	tomorrowMidnight := time.Date(tomorrow.Year(), tomorrow.Month(), tomorrow.Day(), 0, 0, 0, 0, tomorrow.Location())
	cacheDuration := time.Until(tomorrowMidnight)

	if err := s.cache.Set(cacheKey, cachedData, cacheDuration); err != nil {
		log.Printf("[WARN] 缓存用户签到不可用状态失败: UID=%d, ActivityID=%d, Error=%v", uid, activityID, err)
	} else {
		log.Printf("[DEBUG] 已缓存用户签到不可用状态: UID=%d, ActivityID=%d, Reason=%s", uid, activityID, reason)
	}
}

// clearUserSignInUnavailableCache 清除用户签到不可用缓存
func (s *LuckyService) clearUserSignInUnavailableCache(uid uint64, activityID uint) {
	cacheKey := fmt.Sprintf("signin_unavailable_%d_%d", uid, activityID)

	if err := s.cache.Delete(cacheKey); err != nil {
		log.Printf("[WARN] 清除用户签到不可用缓存失败: UID=%d, ActivityID=%d, Error=%v", uid, activityID, err)
	} else {
		log.Printf("[DEBUG] 已清除用户签到不可用缓存: UID=%d, ActivityID=%d", uid, activityID)
	}
}

// checkUserSignInTimeCache 检查用户签到操作时间缓存
func (s *LuckyService) checkUserSignInTimeCache(uid uint64, activityID uint) (bool, error) {
	cacheKey := fmt.Sprintf("signin_time_%d_%d", uid, activityID)

	var lastSignTime int64
	err := s.cache.Get(cacheKey, &lastSignTime)
	if err != nil {
		// 缓存未命中，表示今天未签到
		return false, nil
	}

	// 检查缓存的时间是否为今天
	lastTime := time.Unix(lastSignTime, 0)
	isToday := s.isToday(lastTime)

	log.Printf("[DEBUG] 用户签到时间缓存检查: UID=%d, ActivityID=%d, LastSignTime=%d, IsToday=%v",
		uid, activityID, lastSignTime, isToday)

	return isToday, nil
}

// cacheUserSignInTime 缓存用户签到操作时间
func (s *LuckyService) cacheUserSignInTime(uid uint64, activityID uint) {
	cacheKey := fmt.Sprintf("signin_time_%d_%d", uid, activityID)
	now := time.Now().Unix()

	// 缓存到明天凌晨过期
	tomorrow := time.Now().AddDate(0, 0, 1)
	tomorrowMidnight := time.Date(tomorrow.Year(), tomorrow.Month(), tomorrow.Day(), 0, 0, 0, 0, tomorrow.Location())
	cacheDuration := time.Until(tomorrowMidnight)

	if err := s.cache.Set(cacheKey, now, cacheDuration); err != nil {
		log.Printf("[WARN] 缓存用户签到时间失败: UID=%d, ActivityID=%d, Error=%v", uid, activityID, err)
	} else {
		log.Printf("[DEBUG] 已缓存用户签到时间: UID=%d, ActivityID=%d, CacheDuration=%v",
			uid, activityID, cacheDuration)
	}
}

// UserStatusResponse 用户状态响应
type UserStatusResponse struct {
	UID           uint64    `json:"uid"`
	ActivityID    uint      `json:"activity_id"`
	ActivityName  string    `json:"activity_name"`
	Day           int       `json:"day"`
	Point         int       `json:"point"`
	Number        int       `json:"number"`
	Today         int       `json:"today"`
	Extra         int       `json:"extra"`
	LastSignTime  time.Time `json:"last_sign_time"`
	CanSign       bool      `json:"can_sign"`
	FreeRemaining int       `json:"free_remaining"`
	// 客户端期望的字段
	TotalDays      uint32 `json:"total_days"`      // 签到总天数
	AvailableCount uint32 `json:"available_count"` // 今日可用次数
	Message        string `json:"message"`         // 状态消息
}

// AnnouncementResponse 公告响应
type AnnouncementResponse struct {
	ActivityID   uint         `json:"activity_id"`
	ActivityName string       `json:"activity_name"`
	Description  string       `json:"description"`
	StartTime    time.Time    `json:"start_time"`
	EndTime      time.Time    `json:"end_time"`
	Rewards      []RewardInfo `json:"rewards"`
}

// RewardInfo 奖励信息
type RewardInfo struct {
	ID           uint   `json:"id"`
	Name         string `json:"name"`
	Type         string `json:"type"`
	Weight       int    `json:"weight"`
	Point        int    `json:"point"`
	Limit        int    `json:"limit"`
	LimitCurrent int    `json:"limit_current"`
	IsAvailable  bool   `json:"is_available"`
}

// GetUserStatus 获取用户签到状态
func (s *LuckyService) GetUserStatus(uid uint64) (*UserStatusResponse, error) {
	// 获取当前活动
	activity, err := s.getCurrentActivity()
	if err != nil {
		log.Printf("[WARN] 获取当前活动失败: %v", err)
		// 如果没有活动，返回默认状态而不是错误
		return &UserStatusResponse{
			UID:            uid,
			ActivityID:     0,
			ActivityName:   "无活动",
			Day:            0,
			Point:          0,
			Number:         0,
			Today:          0,
			Extra:          0,
			LastSignTime:   time.Time{},
			CanSign:        false,
			FreeRemaining:  0,
			TotalDays:      0,
			AvailableCount: 0,
			Message:        "当前没有进行中的签到活动",
		}, nil
	}

	// 获取用户签到数据
	userDraw, err := s.getUserDraw(uid, activity.ID)
	if err != nil {
		return nil, err
	}

	// 计算是否可以签到
	canSign := true
	freeRemaining := activity.Free - userDraw.Today
	if userDraw.IsToday() && userDraw.Today >= activity.Free && userDraw.Extra == 0 {
		canSign = false
	}
	if freeRemaining < 0 {
		freeRemaining = 0
	}

	// 计算客户端期望的字段
	totalDays := uint32(userDraw.Number)                     // 总签到次数作为总天数
	availableCount := uint32(freeRemaining + userDraw.Extra) // 剩余免费次数 + 额外次数

	return &UserStatusResponse{
		UID:            uid,
		ActivityID:     activity.ID,
		ActivityName:   activity.Name,
		Day:            userDraw.Day,
		Point:          userDraw.Point,
		Number:         userDraw.Number,
		Today:          userDraw.Today,
		Extra:          userDraw.Extra,
		LastSignTime:   userDraw.Time,
		CanSign:        canSign,
		FreeRemaining:  freeRemaining,
		TotalDays:      totalDays,
		AvailableCount: availableCount,
		Message:        "签到状态获取成功",
	}, nil
}

// GetAnnouncement 获取活动公告
func (s *LuckyService) GetAnnouncement() (*AnnouncementResponse, error) {
	// 获取当前活动
	activity, err := s.getCurrentActivity()
	if err != nil {
		return nil, fmt.Errorf("没有正在进行中的活动")
	}

	// 获取奖励列表
	var rewards []model.LuckyReward
	err = s.db.Where("activity = ? AND is_use = ?", activity.ID, true).Find(&rewards).Error
	if err != nil {
		return nil, err
	}

	// 转换奖励信息
	var rewardInfos []RewardInfo
	for _, reward := range rewards {
		rewardInfos = append(rewardInfos, RewardInfo{
			ID:           reward.ID,
			Name:         reward.Text,
			Type:         reward.Type,
			Weight:       reward.Weight,
			Point:        reward.Point,
			Limit:        reward.Limit,
			LimitCurrent: reward.LimitCurrent,
			IsAvailable:  reward.IsAvailable(),
		})
	}

	return &AnnouncementResponse{
		ActivityID:   activity.ID,
		ActivityName: activity.Name,
		Description:  activity.Name, // 使用Name字段作为描述
		StartTime:    activity.StartTime,
		EndTime:      activity.EndTime,
		Rewards:      rewardInfos,
	}, nil
}

// recordSignInStatus 记录设备签到状态（使用Hash结构）
func (s *LuckyService) recordSignInStatus(deviceFingerprint string, activityID uint) {
	hashKey := fmt.Sprintf("activity_signin_%d", activityID)
	currentTime := time.Now().Unix()

	// 记录设备签到状态
	if deviceFingerprint != "" {
		deviceField := fmt.Sprintf("device_%s", deviceFingerprint)
		if err := s.hashCache.HSet(hashKey, deviceField, currentTime); err != nil {
			log.Printf("[WARN] 设置设备签到状态缓存失败: DeviceFingerprint=%s, Error=%v", deviceFingerprint, err)
		}
	}
}

// calculateOptimalExpiry 计算最优的缓存过期时间
func (s *LuckyService) calculateOptimalExpiry() time.Duration {
	now := time.Now()

	// 如果是晚上23:00之后，缓存到明天凌晨1小时后
	if now.Hour() >= 23 {
		tomorrow := now.AddDate(0, 0, 1)
		tomorrowMidnight := time.Date(tomorrow.Year(), tomorrow.Month(), tomorrow.Day(), 1, 0, 0, 0, tomorrow.Location())
		return time.Until(tomorrowMidnight)
	}

	// 否则缓存到今天午夜后1小时
	todayMidnight := time.Date(now.Year(), now.Month(), now.Day()+1, 1, 0, 0, 0, now.Location())
	return time.Until(todayMidnight)
}

// BatchCheckSignInStatus 批量检查签到状态（使用Hash结构）
func (s *LuckyService) BatchCheckSignInStatus(ips []string, deviceFingerprints []string, activityID uint) (map[string]bool, map[string]bool, error) {
	hashKey := fmt.Sprintf("activity_signin_%d", activityID)
	ipResults := make(map[string]bool) // 保留返回值结构以保持接口兼容性，但不再填充数据
	deviceResults := make(map[string]bool)

	// 批量检查设备状态
	if len(deviceFingerprints) > 0 {
		for _, device := range deviceFingerprints {
			field := fmt.Sprintf("device_%s", device)
			if timestamp, err := s.hashCache.HGet(hashKey, field); err == nil {
				if ts, ok := timestamp.(float64); ok {
					lastTime := time.Unix(int64(ts), 0)
					deviceResults[device] = s.isToday(lastTime)
				} else {
					deviceResults[device] = false
				}
			} else {
				deviceResults[device] = false
			}
		}
	}

	return ipResults, deviceResults, nil
}
