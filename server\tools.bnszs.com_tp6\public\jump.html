
<!DOCTYPE html>
<html class="">
    <head>
        <title>领券页面 · 小星星</title>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <meta name="MobileOptimized" content="320">
        <meta name="viewport" content="initial-scale=1.0,user-scalable=no,minimum-scale=1.0, maximum-scale=1.0,width=device-width">
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="black">
        <meta name="format-detection" content="telephone=no, email=no">
        <link rel="apple-touch-icon" href="" data-role="site-logo">
        <script>
            var SOHUZ = {
                page: {site_id: "9224296490", site_name: "小星星淘宝领券", site_url: "http://taobao.duoluosb.com", page_id: "1518788768", page_title: "领券页面", page_type: "0", index_url:"/", is_index_page: false, use_header: true, use_nav: true},
                share:{title: "复制口令", desc: "", pic: "", wxAppId: "unknow"},
            };
            var KZ_PUBLIC = {
                page: {site_id: "9224296490", site_name: "小星星", site_url: "http://taobao.duoluosb.com", page_id: "1518788768", page_title: "领券页面", page_type: "0", index_url:"/", is_index_page: false, use_header: true, use_nav: true},
            share:{title: "复制口令", desc: "", pic: "", wxAppId: "unknow"},
            };
            if (navigator.userAgent.indexOf('KuaiZhaniOSSite') > -1 || navigator.userAgent.indexOf('KuaiZhanAndroidWrapper') > -1) {
                document.getElementsByTagName('html')[0].className += ' no-header no-nav';
            }
        </script>
        <script>SOHUZ.nav = [];</script>

        <meta http-equiv="x-dns-prefetch-control" content="on">
        <link rel="dns-prefetch" href="//cdn.kuaizhan.com">
        <link rel="dns-prefetch" href="//pfile.kuaizhan.com">
        <link rel="dns-prefetch" href="//pic.kuaizhan.com">
        <link rel="stylesheet" type="text/css" href="//cdn.kuaizhan.com/res/mobile/css/ui.css?v=5.0">
        
<!--DTD WML--><meta http-equiv="Cache-Control" content="no-transform"><link id="mobile-css" rel="stylesheet" type="text/css" href="//cdn.kuaizhan.com/res/skin/css/mobile.css?v=5.0"><link id="mod-css" rel="stylesheet" type="text/css" href="//cdn.kuaizhan.com/res/skin/css/mod.css?v=5.0"><link id="portal-css" rel="stylesheet" type="text/css" href="//pfile.kuaizhan.com/files/portal_basic.css"><link id="theme-css-3" rel="stylesheet" type="text/css" href="//cdn.kuaizhan.com/res/skin/themes/theme1/theme.css?v=5.0"><link id="color-css" rel="stylesheet" type="text/css" href="//cdn.kuaizhan.com/t/site-9224296490/site-9224296490.css?v=1568529023"><link id="portal-extra" rel="stylesheet" type="text/css" href="//pfile.kuaizhan.com/files/??5a16c3713607500008a4fae5/latest_version/components/portal_all.css,5be39e7118806700090a5180/latest_version/components/portal_all.css,ernie/latest_version/components/portal_all.css,scrape/latest_version/components/portal_all.css,shop/latest_version/components/portal_all.css,sign/latest_version/components/portal_all.css,survey/latest_version/components/portal_all.css">    </head>
    <body>
        <script src="//cdn.kuaizhan.com/res/skin/js/zepto.min.js?v=5.0"></script>
        <script src="//cdn.kuaizhan.com/bus/static-apps/572aba2ba3fa15f816ef1936/front/js/main.js"></script>
        <div id="phone-main" class="phone-main" style="left: 0px; top: 0px;">
            <div class="mod mod-global-nav nav-header t4 center  no-icon    "  data-component='{&quot;eid&quot;:&quot;system_plugin\/global-nav&quot;,&quot;name&quot;:&quot;mod-global-nav&quot;,&quot;version&quot;:&quot;6.15.188&quot;,&quot;ename&quot;:&quot;system_plugin\/global-nav&quot;,&quot;is_testing&quot;:&quot;&quot;}' style='margin:   ; opacity:1' data-nav-type="t4" data-opacity="1" data-body-class="" data-icon-class=" no-icon " data-role="nav-header"></div><div class="mod-nav-theme t4  no-icon   " data-role="mod-nav-theme"><div class="nav-list-wrapper"><div class="list-section"><ul class="list-items"></ul><span class="list-more" data-role="list-in-box"><a class="icon-more"><span class="ki-t i-add"></span></a></span></div><div class="dropbox"><ul></ul><span class="icon-arrow"><em class="icon-arrow-bd"></em><em class="icon-arrow-bg"></em></span></div></div></div> <div class="nav-screen-mask t4 " data-role="nav-screen-mask"></div>            <div class= "page-w " style="background-color:tranparent;background-size:;"><div id="page-content" class="page-content"><div class="mod mod-js"><script type="text/javascript" src="//lib.baomitu.com/jquery/2.2.4/jquery.min.js"></script><script type="text/javascript" src="//lib.baomitu.com/clipboard.js/1.6.1/clipboard.min.js"></script><script>
function getQueryVariable(variable){
		var query = decodeURIComponent(window.location.search.substring(1));
		var vars = query.split("&");
		for (var i=0;i<vars.length;i++) {
			var pair = vars[i].split("=");
			if(pair[0] == variable){
              if(pair.length > 2){
            	return unescape(pair[1] + '=' + pair[2]);
              }
              return unescape(pair[1]);
            }
		}
		return(false);
	}
var clickurl = decodeURIComponent(window.location.search.substring(window.location.search.indexOf("&url")+5));

var browser = {
    versions: function () {
        var u = navigator.userAgent, app = navigator.appVersion;
        return {         //移动终端浏览器版本信息
            trident: u.indexOf('Trident') > -1, //IE内核
            presto: u.indexOf('Presto') > -1, //opera内核
            webKit: u.indexOf('AppleWebKit') > -1, //苹果、谷歌内核
            gecko: u.indexOf('Gecko') > -1 && u.indexOf('KHTML') == -1, //火狐内核
            mobile: !!u.match(/AppleWebKit.*Mobile.*/), //是否为移动终端
            ios: !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/), //ios终端
            android: u.indexOf('Android') > -1 || u.indexOf('Linux') > -1, //android终端或uc浏览器
            iPhone: u.indexOf('iPhone') > -1, //是否为iPhone或者QQHD浏览器
            iPad: u.indexOf('iPad') > -1, //是否iPad
            webApp: u.indexOf('Safari') == -1 //是否web应该程序，没有头部与底部
        };
    }(),
    language: (navigator.browserLanguage || navigator.language).toLowerCase()
}

 
	
	//复制文本
	var clipboard = new Clipboard('.icopy');
	clipboard.on('success',function(e) {
	  if (e.trigger.disabled == false || e.trigger.disabled == undefined) {
		e.trigger.innerHTML = "复制成功，打开【手tao】购买";
		e.trigger.style.backgroundColor = "#78bd33";
		e.trigger.disabled = true;
		setTimeout(function() {
		  e.trigger.innerHTML = "一键复制";
		  e.trigger.style.backgroundColor = "#fb6a65";
		  e.trigger.disabled = false;
		},
		2000);
	  }
	});
	clipboard.on('error',function(e) {
	  e.trigger.innerHTML = "复制失败,请手动复制";
	  e.trigger.style.backgroundColor = "#b5b4b4";
	});
	
	

	$(function() {
	  
		var np = getQueryVariable("word");
		var np=np.match(/\w+/);
		var np = "("+np+")"
		$("#icopy").attr("data-clipboard-text",np);
		$("#np").html(np);
	
	    var clikurl= getQueryVariable("url");
      	var reg=/^([hH][tT]{2}[pP]:\/\/|[hH][tT]{2}[pP][sS]:\/\/)(([A-Za-z0-9-~]+).)+([A-Za-z0-9-~\/])/;
        if(!reg.test(clikurl)){
        	clikurl = false;
          	$("#pc").css("display",'none');
        }else{
        	var pc_url = "<a href='"+clikurl+"'>点击领取优惠券</a>"
        }
      	
      	$("#pc-url").html(pc_url);
		if (browser.versions.mobile) {//判断是否是移动设备打开。browser代码在下面
	        var ua = navigator.userAgent.toLowerCase();//获取判断用的对象
	        if (ua.match(/MicroMessenger/i) == "micromessenger") {
	                //在微信中打开
	        }else{
				window.location.href=clickurl;
			}
			
		} else {
		      //否则就是PC浏览器打开
              var ua = navigator.userAgent.toLowerCase();//获取判断用的对象
              if(ua.indexOf("wechat")<0){
                  window.location.href=clickurl;
              }
			
		}
		
	var checkpic_preg = /http/;
	var old_pic = getQueryVariable("pic");
	if(old_pic)
	{
		//解64
		old_pic = window.atob(old_pic);
		$("#type-bimg").attr('src',old_pic);
	}else
	{
		var show_pic = getQueryVariable("image");
		if(show_pic.match(checkpic_preg)==null){
			//解64
			show_pic = window.atob(show_pic);
		}
		$("#type-bimg").attr('src',show_pic);
	}

   

    var mall = getQueryVariable("mall");
    if(mall)
	{
		if(mall.match(checkpic_preg)==null){
      //解64
      mall = window.atob(mall);
    }
    if(mall){
        $("#tomall").attr('href',mall);
        $("#tomall").css("display","inline-block");
    }

	}

	
		//自动选中
		var word = document.querySelector('.nomen-proprium');
		document.addEventListener("selectionchange",
		function(e) {
			window.getSelection().selectAllChildren(word);
		},false);
	  
		$("#phone-main").next().hide();
	
	});

</script><div class="mod mod-html"><style type="text/css">
    body {
        background: #eee;
        font-size: 14px;
    }
    .scenograph {
        padding: 16px 16px 0;
    }
    .scenograph #type-bimg {
        box-shadow: 0px 4px 8px rgba(21, 0, 71, .1);
        width: 100%;
    }
    .drugsword {
        margin: 14px;
        margin-top: 16px;
        text-align: center;
    }
    .medicine-bag {
        background: white;
        border: 1px dashed #fb6a65;
        padding: .5rem;
        border-radius: 5px;
    }
    .copy-tip {
        display: flex;
        justify-content: space-around;
        align-items: center;
        background: linear-gradient(to right, #f9c492, #fb6a65);
        color: white;
        font-size: 12px;
        height: 24px;
    }
    .jtone {
        position: relative;
        width: 0;
        height: 0;
        border-top: 12px solid transparent;
        border-left: 10px solid white;
        border-bottom: 12px solid transparent;
    }
    .jtone::before {
        content: "";
        position: absolute;
        top: -12px;
        left: -13px;
        width: 0;
        height: 0;
        border-top: 12px solid transparent;
        border-left: 10px solid #fab189;
        border-bottom: 12px solid transparent;
    }
    .jttwo {
        position: relative;
        width: 0;
        height: 0;
        border-top: 12px solid transparent;
        border-left: 10px solid white;
        border-bottom: 12px solid transparent;
    }
    .jttwo::before {
        content: "";
        position: absolute;
        top: -12px;
        left: -13px;
        width: 0;
        height: 0;
        border-top: 12px solid transparent;
        border-left: 10px solid #fa9b7e;
        border-bottom: 12px solid transparent;
    }
    .jtthree {
        position: relative;
        width: 0;
        height: 0;
        border-top: 12px solid transparent;
        border-left: 10px solid white;
        border-bottom: 12px solid transparent;
    }
    .jtthree::before {
        content: "";
        position: absolute;
        top: -12px;
        left: -13px;
        width: 0;
        height: 0;
        border-top: 12px solid transparent;
        border-left: 10px solid #fa8673;
        border-bottom: 12px solid transparent;
    }
    .icopy {
        background: #fb6a65;
        border-radius: 5px;
        color: white;
        font-size: 14px;
        margin: 16px auto;
        padding: 10px;
        margin-bottom: 2px;
    }
    .nomen-proprium {
        font-size: 12px;
        text-align: left;
        color: #fb6a65;
    }
    .kz-float-layer {
        height: 0px!important;
    }
    .kz-float-layer {
        display: none!important;
    }
</style><div class="scenograph">
    <img id="type-bimg" src="">
</div><div class="drugsword">
    <div class="medicine-bag">
        <div>
            <div id="nomen-proprium" class="nomen-proprium">
                复制红框内信息打开→手TaoAPP←即可
                <span id="np"></span>
            </div>
        </div>
    </div>
    <div class="medicine-bag" id="pc" style="margin-top:13px;">
        <div>
            <div id="pc-q" class="nomen-proprium">
                电脑领优惠券点后面这个
                <span id="pc-url"></span>
            </div>
        </div>
    </div>
    <div type="button" data-clipboard-text="" class="icopy" id="icopy">一键复制</div>
    <div id="copy-tip" class="copy-tip">
        <span>长按框内</span>
        <span class="jtone"></span>
        <span>全选</span>
        <span class="jttwo"></span>
        <span>复制</span>
        <span class="jtthree"></span>
        <span>打开Tao宝APP</span>
    </div>
    <br />
   <div class="medicine-bag">
        <div>
            <div class="nomen-proprium">
                <center>
                  	付款后没有提示（购买成功）的，记得发送订单号给我绑定! <br>
              		<!--<span>每天更新数千款优惠商品</span> <br>-->
              		<span>复制下面的口令</span> <br>
              		<span>今晚0点打开淘宝有1111红包</span> <br>
              		<!--<span>商品库所有商品均有优惠券</span> <br>-->
              		<span>$UdfOYq9rPOU$</span> <br>
                  	<!--<span>点击进入<a href="http://taobao.duoluosb.com">优惠券商城</a></span>-->
                  </center>
            </div>
        </div>
    </div>
</div></div>
</div></div><footer class='kz-footer'><div class="mod mod-text " style="margin:   ;background:">Powered by 搜狐快站</div></footer></div>        </div>
        <div data-role="kz-page-bottom-float-layer" class="kz-page-float-layer kz-page-bottom">
            <div id="kz-page-ad" class="kz-page-ad-inner-layer"></div>
            <div data-role="kz-manage" class="kz-manage"></div>
        </div>
        <script src="//cdn.kuaizhan.com/res/skin/js/lib/require.js?v=5.0"></script>
        <script src="//cdn.kuaizhan.com/res/skin/js/mobile.js?v=5.0"></script>
        <script src="//cdn.kuaizhan.com/res/pageui/js/portal_page.js?v=5.0"></script>
        
<script>
      function kz_load_script(xyUrl, callback){
          var head = document.getElementsByTagName('head')[0];
          var script = document.createElement('script');
          script.type = 'text/javascript';
          script.src = xyUrl;
          //借鉴了jQuery的script跨域方法
          script.onload = script.onreadystatechange = function(){
              if((!this.readyState || this.readyState === "loaded" || this.readyState === "complete")){
                  callback && callback();
                  // Handle memory leak in IE
                  script.onload = script.onreadystatechange = null;
                  if ( head && script.parentNode ) {
                      head.removeChild( script );
                  }
              }
          };
          // Use insertBefore instead of appendChild  to circumvent an IE6 bug.
          head.insertBefore( script, head.firstChild );
      }
      if($('#qr-code-inner').length > 0 ) {
          kz_load_script('//static-1252921496.costj.myqcloud.com/third-party/qrcode.min.js', function() {

            // hack
            $('#qr-code-inner img').remove();

            var qrCodeInnerContent = document.createElement('div');
            qrCodeInnerContent.id = 'qr-code-inner-head';
            $('#qr-code-inner').prepend(qrCodeInnerContent);

            var qrcode = new QRCode(qrCodeInnerContent, {
              text: window.location.href,
              width: 100,
              height: 100,
            });
          });
      }
</script>
<script>
    (function(i,s,o,g,r,a,m) {
        i['KZAnalyticsObject'] = r; i[r] = i[r] || function() {(i[r].q = i[r].q || []).push(arguments);};
        a = s.createElement(o); m = s.getElementsByTagName(o)[0]; a.sync = 1; a.src = g;
        m.parentNode.insertBefore(a, m);
    })(window, document, "script", "//pv.kuaizhan.com/kzcollector.min.js?version=0.3", "kaq");
    (function(z){
        var page = z.page;
        kaq('create', page.site_id);
        kaq('set', 'page_type', page.page_type);
        if (page.page_type == '4' && window.pageData && window.pageData._id) {
            kaq("set", 'traceKey', window.pageData._id);
        } else {
            kaq("set", 'traceKey', page.page_id);
        }
        if (page.page_type == "2") {
            if (page.trace_key) {
                kaq("set", 'traceKey', page.trace_key);
            }
            if (page.is_imported_post) {
                kaq('set', 'is_imported_post', 1);
                kaq('set', 'source_page_id', page.source_page_id);
            } else {
                kaq('set', 'is_imported_post', 0);
                kaq('set', 'source_page_id', 0);
            }
        }
        if (window.$) {
            $("body").on("toolbarad-view", function(e, data) {
                kaq('send', 'event', "global-header", data.action, "plf-ad-click");
            }).on("monitor-ad-bebavior", function() {
                kaq('send', 'event', "stat-ad-behavior", "hidden", page.site_id);
            }).on("click", "[data-trace-label]", function() {
                var t = $(this), trace_category = t.data('trace-category'), trace_label = t.data('trace-label');
                kaq('send', 'event', trace_category, 'click', trace_label);
            });
        }
        kaq('send', 'pageview');
        kaq('send', 'timing', 5);
        z.kaq = kaq;
    })(window.SOHUZ);
</script>
        <script>
                var errorT5Dom = $('.mod-nav-theme.t5.no-icon');
                if (!errorT5Dom.hasClass('line') && !errorT5Dom.hasClass('box')) {
                        errorT5Dom.addClass('line')
                }        
        </script>
    </body>
</html>
