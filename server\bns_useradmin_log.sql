-- 管理员操作日志表
-- 用于记录管理员在风控系统中的各种操作

CREATE TABLE IF NOT EXISTS `bns_useradminlog` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `admin_uid` int(11) NOT NULL COMMENT '管理员UID',
  `admin_username` varchar(50) NOT NULL COMMENT '管理员用户名',
  `action` varchar(100) NOT NULL COMMENT '操作类型',
  `data` text COMMENT '操作数据(JSON格式)',
  `ip` varchar(45) NOT NULL COMMENT '操作IP地址',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_admin_uid` (`admin_uid`),
  KEY `idx_action` (`action`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员操作日志表';

-- 插入一些示例数据（可选）
INSERT INTO `bns_useradminlog` (`admin_uid`, `admin_username`, `action`, `data`, `ip`, `created_at`) VALUES
(1, 'admin', 'system_init', '{"message": "管理员日志表初始化"}', '127.0.0.1', NOW());
