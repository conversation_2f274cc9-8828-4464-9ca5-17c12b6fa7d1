<!DOCTYPE html>
<html lang="cn">
  <head>
	<meta charset="utf-8">
	<meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
	<meta http-equiv="Pragma" content="no-cache">
	<meta http-equiv="Expries" content="0">
	<meta name="viewport" content="width=device-width,height=device-height,inital-scale=1.0,maximum-scale=1.0,user-scalable=no;">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="format-detection" content="telephone=no">
	
    <link rel="stylesheet" href="//down-update.qq.com/bns/static/ingameWeb/ingame/bns/ue4/character/css/character.1.css?v=20210630" media="screen" />
    <link rel="stylesheet" href="/ingame/bns/ue4/character/css/character.css?v=20210830371" media="screen" />
    
    <title>剑灵小助手 - 角色信息</title>
    <style type="text/css">
        @media(max-width:960px){
      
    }
      body{
        background-color:#3b434b;
      }
      
      .container {
        position: absolute;
        /* min-width: 428px; */
        width:auto;
        height: 765px;
        background-color:#3b434b;
        background: none;
        top: 0;
        left: 0;
        border-radius: 10px;
        
      }
      .contents-wrap {
        width: 100%;
      }
      .accessory-wrap{
        display: grid;
        grid-template-columns: 1fr 1fr;
        margin-top: 20px;
      }
      .buttons{
        display: none;
      }
      .charater-view{
        display: none;
      }
      .signature {
          width: 395px;
      }
     
      .cx-mobile-list
      {
        float: none;
        min-width: 364px;
        height: auto;
        margin: 30px auto 45px;
        /* overflow: hidden; */
       
        display: block;
      }
      .contents-wrap {
        /* width: 428px; */
        
        padding: 0 14px 14px;
        margin-bottom: 21px;
        background-color: #1b1e23;
        border-bottom-left-radius: 10px;
        border-bottom-right-radius: 10px;
    }
    
    </style>
    
	<script src="//cdn.staticfile.org/jquery/1.11.3/jquery.min.js"></script>
    <script src='//cdn.staticfile.org/jquery.transit/0.9.9/jquery.transit.min.js' defer></script>   
    <script>
        var _hmt = _hmt || [];
        (function() {
          var hm = document.createElement("script");
          hm.src = "https://hm.baidu.com/hm.js?28827f52ec001469aa650ad3e993c664";
          var s = document.getElementsByTagName("script")[0]; 
          s.parentNode.insertBefore(hm, s);
        })();
    </script>  
  </head>

  {__CONTENT__}

</html>