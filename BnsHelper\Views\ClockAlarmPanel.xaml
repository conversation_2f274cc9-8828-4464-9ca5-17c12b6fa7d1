﻿<hc:Window x:Class="Xylia.BnsHelper.Views.ClockAlarmPanel"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
		xmlns:hc="https://handyorg.github.io/handycontrol"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" mc:Ignorable="d" 
        Title="{DynamicResource ClockAlarmPanel_Title}" ResizeMode="NoResize" SizeToContent="WidthAndHeight" WindowStartupLocation="CenterScreen">
	
	<Grid MinWidth="400">
		<ListBox ItemsSource="{Binding Schedules}">
			<ListBox.Resources>
				<ContextMenu x:Key="ScheduleItemMenu">
					<MenuItem Header="事件前15分钟通知" IsCheckable="True" IsChecked="{Binding Notify15}" />
					<MenuItem Header="事件前5分钟通知" IsCheckable="True" IsChecked="{Binding Notify5}" />
					<MenuItem Header="事件前3分钟通知" IsCheckable="True" IsChecked="{Binding Notify3}" />
				</ContextMenu>
			</ListBox.Resources>
			
			<ListBox.ItemTemplate>
				<DataTemplate>
					<Grid Margin="2 5" ContextMenu="{StaticResource ScheduleItemMenu}">
						<Grid.ColumnDefinitions>
							<ColumnDefinition Width="Auto" />
							<ColumnDefinition Width="*" />
						</Grid.ColumnDefinitions>
						<Grid.RowDefinitions>
							<RowDefinition Height="Auto" />
							<RowDefinition Height="Auto" />
						</Grid.RowDefinitions>

						<TextBlock Text="{Binding DateTime,StringFormat=HH:mm}" FontWeight="Bold" Margin="0 0 10 0" />
						<TextBlock Text="{Binding }" Grid.Column="1" />
						<TextBlock Text="{Binding Zone}" Grid.Column="1" Grid.Row="1" />
					</Grid>
				</DataTemplate>
			</ListBox.ItemTemplate>
		</ListBox>
	</Grid>
</hc:Window>