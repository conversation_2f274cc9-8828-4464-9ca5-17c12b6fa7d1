<?php /*a:2:{s:101:"E:\Build\C++\bnspatch\src\BnsPlugin\server\tools.bnszs.com_tp6\app\manage\view\admin\risk_config.html";i:1751141538;s:99:"E:\Build\C++\bnspatch\src\BnsPlugin\server\tools.bnszs.com_tp6\app\manage\view\manage\template.html";i:1751142117;}*/ ?>
<!DOCTYPE html>
<html>
<head>
  <!-- 项目构建：兔子、0x1ng、Xylia  | 项目创建:2020-06-01  | 项目更新:2025-02-05 -->
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="description" content="剑灵小助手管理系统。">
  <meta name="keywords" content="剑灵骗子,剑灵骗子大全,游戏骗子,剑灵骗子数据库,小助手骗子数据库,小助手提交骗子,小助手自定义,装备查询优化,装备查询自定义,小助手装备查询,剑灵装备查询,剑灵小助手">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="renderer" content="webkit">
  <meta name="apple-mobile-web-app-title" content="Amaze UI" />
  <meta http-equiv="Cache-Control" content="no-siteapp" />

  <title>剑灵小助手管理系统</title>
  <link rel="icon shortcut" href="/favicon.ico" type="image/x-icon">
  <link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/bootstrap/5.1.3/css/bootstrap.css">
  <link rel="stylesheet" href="/css/manage.css?version=2025021504"/>
  <link rel="stylesheet" href="/css/admin.css?version=2021021110">
  <link rel="stylesheet" href="/css/tally.css?version=2021021112">

  <script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/3.6.0/jquery.min.js"></script>
  <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/bootstrap/5.1.3/js/bootstrap.min.js"></script>
  <script src="https://static-1251192097.cos.ap-shanghai.myqcloud.com/web_html/assets/layer/layer.js"></script>
  <script src="/js/amazeui.min.js"></script>
  <script src="/js/manage.js"></script>

  <script>
  // 移动设备侧边栏控制函数 - 全局定义
  function toggleMobileSidebar() {
    var sidebar = document.getElementById('admin-offcanvas');
    var overlay = document.querySelector('.mobile-sidebar-overlay');

    if (!sidebar) {
      return;
    }

    if (sidebar.classList.contains('mobile-show')) {
      hideMobileSidebar();
    } else {
      showMobileSidebar();
    }
  }

  function showMobileSidebar() {
    var sidebar = document.getElementById('admin-offcanvas');
    var overlay = document.querySelector('.mobile-sidebar-overlay');

    if (sidebar) {
      sidebar.classList.add('mobile-show');
    }

    if (overlay) {
      overlay.classList.add('show');
    }
  }

  function hideMobileSidebar() {
    var sidebar = document.getElementById('admin-offcanvas');
    var overlay = document.querySelector('.mobile-sidebar-overlay');

    if (sidebar) {
      sidebar.classList.remove('mobile-show');
    }

    if (overlay) {
      overlay.classList.remove('show');
    }
  }
  </script>

  <style type="text/css">
  	.ripple {
		position: relative;
		overflow: hidden;
	}
			
	.ripple:after {
		content: "";
		display: block;
		position: absolute;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		pointer-events: none;
		background-image: radial-gradient(circle, #666 10%, transparent 10.01%);
		background-repeat: no-repeat;
		background-position: 50%;
		transform: scale(10, 10);
		opacity: 0;
		transition: transform .3s, opacity .5s;
	}
			
	.ripple:active:after {
		transform: scale(0, 0);
		opacity: .3;
		transition: 0s;
	}

	.btn-sign{
		border-width:0px;
		width: 80px;
		height: 80px;
	}
			
	.option {
		width: 200px;
		height: 40px;
		border: 1px solid #cccccc;
		position: relative;
	}

	.option select {
		border: none;
		outline: none;
		width: 100%;
		height: 40px;
		line-height: 40px;
		appearance: none;
		-webkit-appearance: none;
		-moz-appearance: none;
		padding-left: 20px;
	}

	.option:after {
		content: "";
		width: 14px;
		height: 8px;
		background: url(/assets/arrow-down.png) no-repeat center;
		position: absolute;
		right: 20px;
		top: 41%;
		pointer-events: none;
	}

	/* 统计卡片样式 */
	.stats-card {
		background: #f8f9fa;
		border: 1px solid #e9ecef;
		border-radius: 8px;
		padding: 20px;
		text-align: center;
		margin-bottom: 15px;
		transition: all 0.3s ease;
	}

	.stats-card:hover {
		background: #e9ecef;
		transform: translateY(-2px);
		box-shadow: 0 4px 8px rgba(0,0,0,0.1);
	}

	.stats-number {
		font-size: 2.5em;
		font-weight: bold;
		color: #007bff;
		margin-bottom: 5px;
	}

	.stats-label {
		color: #6c757d;
		font-size: 0.9em;
		text-transform: uppercase;
		letter-spacing: 0.5px;
	}

	/* 统计卡片链接样式 */
	.stats-card-link {
		display: block;
		text-decoration: none;
		color: inherit;
	}

	.stats-card-link:hover {
		text-decoration: none;
		color: inherit;
	}

	/* 状态颜色 */
	.status-normal { color: #28a745; }
	.status-banned { color: #dc3545; }
	.status-premium { color: #ffc107; }
	.status-online { color: #17a2b8; }

	/* 移动设备侧边栏优化 */
	@media only screen and (max-width: 640px) {
		#admin-offcanvas {
			position: fixed !important;
			left: -260px !important;
			top: 51px !important;
			bottom: 0 !important;
			z-index: 1600 !important;
			transition: left 0.3s ease !important;
			background: #fff !important;
			box-shadow: 2px 0 5px rgba(0,0,0,0.1) !important;
			width: 260px !important;
			height: calc(100vh - 51px) !important;
			overflow-y: auto !important;
		}

		#admin-offcanvas.mobile-show {
			left: 0 !important;
		}

		/* 确保侧边栏内容可见 */
		#admin-offcanvas .am-offcanvas-bar {
			position: static !important;
			transform: none !important;
			width: 100% !important;
			height: 100% !important;
		}

		.admin-content {
			margin-left: 0 !important;
		}

		.mobile-sidebar-toggle {
			margin-left: 10px;
			background: #1E9FFF !important;
			border-color: #1E9FFF !important;
		}

		.mobile-sidebar-toggle:hover {
			background: #0e7ce8 !important;
			border-color: #0e7ce8 !important;
		}

		/* 遮罩层 */
		.mobile-sidebar-overlay {
			position: fixed;
			top: 51px;
			left: 0;
			right: 0;
			bottom: 0;
			background: rgba(0,0,0,0.5);
			z-index: 1500;
			display: none;
		}

		.mobile-sidebar-overlay.show {
			display: block;
		}
	}
  </style>
</head>
<body>
	<header class="am-topbar am-topbar-inverse admin-header">
	  <div class="am-topbar-brand">
		<strong>剑灵小助手管理系统</strong> <small> 堕络</small>
	  </div>
	  <button class="am-topbar-btn am-topbar-toggle am-btn am-btn-sm am-btn-success am-show-sm-only" data-am-collapse="{target: '#topbar-collapse'}"><span class="am-sr-only">导航切换</span> <span class="am-icon-bars"></span></button>

	  <!-- 移动设备侧边栏切换按钮 -->
	  <button class="am-topbar-btn am-btn am-btn-sm am-btn-secondary am-show-sm-only mobile-sidebar-toggle" onclick="toggleMobileSidebar()">
	    <span class="am-sr-only">菜单</span> <span class="am-icon-navicon"></span>
	  </button>
	  <div class="am-collapse am-topbar-collapse" id="topbar-collapse">
		<ul class="am-nav am-nav-pills am-topbar-nav am-topbar-right admin-header-list">
			<?php if(session("admin")): ?>
			<li class="am-dropdown" data-am-dropdown>
				<a class="am-dropdown-toggle" data-am-dropdown-toggle href="javascript:void(0);" onclick="SwitchPanel('#admin-dropdown-content')">
				  <span class="am-icon-admin"></span> 管理员 <span class="am-icon-caret-down"></span>
				</a>
				<ul class="am-dropdown-content" id="admin-dropdown-content">
				  <li><a href="javascript:void(0);" onclick="window.location.href='/admin/userinfo.php?id=1'"><span class="am-icon-admin"></span> 资料</a></li>
				  <li><a href="javascript:void(0);" onclick="logout()"><span class="am-icon-power-off"></span> 退出</a></li>
				</ul>
			  </li>
			<?php endif; ?>
			<li class="am-hide-sm-only">
				<?php if(session("user")): ?>
				<a href="/manage/center" id="user">
					<span class="am-icon-users"></span>
					<span class="admin-fullText">个人中心</span>
				</a>
				<?php else: ?>
				<a href="/manage/login" id="user">
					<span class="am-icon-users"></span>
					<span class="admin-fullText">登录账号</span>
				</a>
				<?php endif; ?>
			</li>
		</ul>
	  </div>
	</header>

	<div class="am-cf admin-main">
	  <!-- sidebar start -->
	  <div class="admin-sidebar am-offcanvas" id="admin-offcanvas">
		<div class="am-offcanvas-bar admin-offcanvas-bar">
		  <ul class="am-list admin-sidebar-list">
			<li><a href="/manage"><span class="am-icon-home"></span> 系统首页</a></li>
			<!-- <li><a href="/manage/liars"><span class="am-icon-th"></span> 骗子列表 <span class="am-badge am-badge-secondary am-margin-right am-fr"></span></a></li> -->
			<!-- <li>
			  <a href="/manage/liarpost"><span class="am-icon-pencil-square-o"></span> <?php if(session('admin')) echo('提交骗子<span class="am-badge am-badge-secondary am-margin-right am-fr">管理</span>'); 		
				  else echo('举报骗子'); 
			  ?></a>
			</li> -->
			<?php if(isset($_SESSION['admin']) && $_SESSION['admin']): 			try {
				$adminMenuItems = app\manage\model\UserAdmin::GetItems();
				if (!empty($adminMenuItems)) {
					foreach ($adminMenuItems as $item) {?>
						<li><a href="<?php echo $item['url']; ?>"><span class="<?php echo $item['icon']; ?>"></span> <?php echo $item['itemName']; ?></a></li>
					<?php }
				} else { ?>
					<li><a href="#"><span class="am-icon-warning"></span> 暂无管理菜单</a></li>
				<?php }
			} catch (Exception $e) { ?>
				<li><a href="#"><span class="am-icon-exclamation-triangle"></span> 菜单加载失败</a></li>
			<?php } ?>
			<?php endif; ?>
			<li><a href="/manage/profile"><span class="am-icon-gift"></span> 自定义资料</a></li>
			<!-- <li><a href="/manage/help"><span class="am-icon-map-signs"></span> 防骗指南</a></li> -->
			<!-- <li><a href="/manage/choose"><span class="am-icon-paint-brush"></span> 安全测试</a></li> -->
			<?php if(session('user')){ ?> <li><a href="/manage/logout"><span class="am-icon-sign-out"></span> 注销</a></li> <?php } ?>
		  </ul>
		  
		  <div class="am-panel am-panel-default admin-sidebar-panel">
			<div class="am-panel-bd">
			  <p><span class="am-icon-bookmark"></span> 公告</p>
			  <div class="line">
                    <span>萌新必看：</span>
                    <p><a class="home" target="_blank" href="https://docs.qq.com/doc/p/9839e342f1dd89219e2e2980a9a803a42b9d94cf">2.0.1 使用指南</a></p>
			        <p><a class="home" target="_blank" href="https://docs.qq.com/doc/p/7ea1c83b63c8b59b7472c999d15156c4fb843d31">3.0 新版使用指南</a></p>
			        
               </div>
			  
			  <div class="line">
                    <span>剑灵小助手[3.0]：</span>
                    <p>
                    <a class="home" target="_blank" href="https://pan.quark.cn/s/94255b808597">夸克网盘</a>
                    <a class="home" target="_blank" href="https://pan.baidu.com/s/1qcuS-obYbHKBQAuuH6_Bhw?pwd=5210">百度网盘</a>
                    </p>
               </div>
               <div class="line">
                    <span>剑灵小助手[2.0.1]：</span>
                    <p>
                    <a class="home" target="_blank" href="https://pan.quark.cn/s/2ad567e2b816#/list/share">夸克网盘</a>
                    <a class="home" target="_blank" href="https://pan.baidu.com/s/1irL97YxJfR1-UWxkxqXHIQ?pwd=5210">百度网盘</a>
                    <a class="home" target="_blank" href="https://www.lanzoul.com/iUyTU1irepef">蓝奏云</a>
                    </p>
                </div>
			  
			  <div class="texts" style="margin-top: 20px;">
                    <p><span>助手①群：<code>548810086</code></span></p>
                    <p><span>助手②群：<code>563768233</code></span></p>
                    <p><span>助手③群：<code>618986361</code></span></p>
                </div>
			</div>
		  </div>
		</div>
	  </div>

	  <!-- 移动设备侧边栏遮罩层 -->
	  <div class="mobile-sidebar-overlay" onclick="hideMobileSidebar()"></div>

	  

<div class="admin-content">
	<div class="admin-content-body">
		<div class="am-cf am-padding am-padding-bottom-0">
			<div class="am-fl am-cf">
				<strong class="am-text-primary am-text-lg">风控配置</strong> / <small>Risk Control Configuration</small>
			</div>
		</div>
		<hr>
		<!-- 导航栏 -->
		<div class="am-btn-toolbar am-margin-bottom">
			<a href="/manage/admin/risk/dashboard" class="am-btn am-btn-secondary">
				<i class="am-icon-dashboard"></i> 仪表板 </a>
			<a href="/manage/admin/risk/events" class="am-btn am-btn-secondary">
				<i class="am-icon-list"></i> 风险事件 </a>
			<span class="am-btn am-btn-primary am-disabled">
				<i class="am-icon-cog"></i> 风控配置 </span>
		</div>
		<!-- 配置表单 -->
		<div class="am-panel am-panel-default">
			<div class="am-panel-hd">风控规则配置</div>
			<div class="am-panel-bd">
				<form class="am-form am-form-horizontal" method="post">
					<div class="am-form-group">
						<label for="max_qq_per_device_per_day" class="am-u-sm-3 am-form-label"> 同设备每日最大QQ数量: </label>
						<div class="am-u-sm-9">
							<input type="number" id="max_qq_per_device_per_day" name="max_qq_per_device_per_day" value="<?php echo htmlentities((string) $config['max_qq_per_device_per_day']); ?>" class="am-form-field" min="1" max="50" required>
							<small class="am-text-muted"> 当同一设备在一天内登录不同QQ号超过此数量时触发风控 </small>
						</div>
					</div>
					<div class="am-form-group">
						<label for="max_qq_per_ip_per_day" class="am-u-sm-3 am-form-label"> 同IP每日最大QQ数量: </label>
						<div class="am-u-sm-9">
							<input type="number" id="max_qq_per_ip_per_day" name="max_qq_per_ip_per_day" value="<?php echo htmlentities((string) $config['max_qq_per_ip_per_day']); ?>" class="am-form-field" min="1" max="100" required>
							<small class="am-text-muted"> 当同一IP在一天内登录不同QQ号超过此数量时触发风控 </small>
						</div>
					</div>
					<div class="am-form-group">
						<label for="max_login_attempts_per_hour" class="am-u-sm-3 am-form-label"> 每小时最大登录次数: </label>
						<div class="am-u-sm-9">
							<input type="number" id="max_login_attempts_per_hour" name="max_login_attempts_per_hour" value="<?php echo htmlentities((string) $config['max_login_attempts_per_hour']); ?>" class="am-form-field" min="1" max="100" required>
							<small class="am-text-muted"> 当同一QQ号在一小时内登录次数超过此数量时触发风控 </small>
						</div>
					</div>
					<div class="am-form-group">
						<label class="am-u-sm-3 am-form-label">新设备检查:</label>
						<div class="am-u-sm-9">
							<label class="am-radio-inline">
								<input type="radio" name="enable_new_device_check" value="1" <?php if($config['enable_new_device_check'] == 1): ?>checked<?php endif; ?>> 启用 </label>
							<label class="am-radio-inline">
								<input type="radio" name="enable_new_device_check" value="0" <?php if($config['enable_new_device_check'] == 0): ?>checked<?php endif; ?>> 禁用 </label>
							<small class="am-text-muted"> 是否对新设备登录进行额外检查 </small>
						</div>
					</div>
					<div class="am-form-group">
						<label class="am-u-sm-3 am-form-label">异地登录检查:</label>
						<div class="am-u-sm-9">
							<label class="am-radio-inline">
								<input type="radio" name="enable_location_check" value="1" <?php if($config['enable_location_check'] == 1): ?>checked<?php endif; ?>> 启用 </label>
							<label class="am-radio-inline">
								<input type="radio" name="enable_location_check" value="0" <?php if($config['enable_location_check'] == 0): ?>checked<?php endif; ?>> 禁用 </label>
							<small class="am-text-muted"> 是否检查异地登录行为（基于IP地理位置） </small>
						</div>
					</div>
					<div class="am-form-group">
						<div class="am-u-sm-9 am-u-sm-push-3">
							<button type="submit" class="am-btn am-btn-primary">
								<i class="am-icon-save"></i> 保存配置 </button>
							<button type="reset" class="am-btn am-btn-default">
								<i class="am-icon-refresh"></i> 重置 </button>
						</div>
					</div>
				</form>
			</div>
		</div>
		<!-- 配置说明 -->
		<div class="am-panel am-panel-default">
			<div class="am-panel-hd">配置说明</div>
			<div class="am-panel-bd">
				<div class="am-alert am-alert-secondary">
					<h4>风控规则说明:</h4>
					<ul>
						<li><strong>设备QQ数量限制:</strong> 防止同一设备被用于大量不同账号的登录，可能的工作室行为</li>
						<li><strong>IP QQ数量限制:</strong> 防止同一IP地址下大量不同账号登录，可能的代理或工作室行为</li>
						<li><strong>频繁登录限制:</strong> 防止单个账号异常频繁的登录行为，可能的自动化工具</li>
						<li><strong>新设备检查:</strong> 对首次使用的设备进行额外的安全检查</li>
						<li><strong>异地登录检查:</strong> 检测账号在不同地理位置的登录行为</li>
					</ul>
				</div>
				<div class="am-alert am-alert-warning">
					<h4>注意事项:</h4>
					<ul>
						<li>修改配置后需要重启Go风控服务才能生效</li>
						<li>过于严格的配置可能影响正常用户的使用体验</li>
						<li>建议根据实际情况逐步调整配置参数</li>
						<li>所有配置修改都会记录在管理员操作日志中</li>
					</ul>
				</div>
				<div class="am-alert am-alert-success">
					<h4>当前管理员:</h4>
					<p>
						<strong><?php echo htmlentities((string) $admin_info['username']); ?></strong> <?php if($admin_info['super'] == 1): ?> <span class="am-badge am-badge-danger">超级管理员</span> <?php else: ?> <span class="am-badge am-badge-primary">普通管理员</span> <?php endif; ?>
					</p>
				</div>
			</div>
		</div>
	</div>
</div>
<script>
	$(document).ready(function() {
	    // 表单验证
	    $('form').on('submit', function(e) {
	        var deviceLimit = parseInt($('#max_qq_per_device_per_day').val());
	        var ipLimit = parseInt($('#max_qq_per_ip_per_day').val());
	        var loginLimit = parseInt($('#max_login_attempts_per_hour').val());
	
	        if (deviceLimit < 1 || deviceLimit > 50) {
	            alert('设备QQ数量限制必须在1-50之间');
	            e.preventDefault();
	            return false;
	        }
	
	        if (ipLimit < 1 || ipLimit > 100) {
	            alert('IP QQ数量限制必须在1-100之间');
	            e.preventDefault();
	            return false;
	        }
	
	        if (loginLimit < 1 || loginLimit > 100) {
	            alert('登录次数限制必须在1-100之间');
	            e.preventDefault();
	            return false;
	        }
	
	        return confirm('确定要保存这些配置吗？配置修改后需要重启Go风控服务才能生效。');
	    });
	});
</script>
	   
	  <div id="outerdiv" style="position:fixed;top:0;left:0;background:rgba(0,0,0,0.7);z-index:2;width:100%;height:100%;display:none;">
		<div id="innerdiv" style="position:absolute;">
		  <img id="bigimg" style="border:5px solid #fff;" src="" />
		</div>
	  </div>
	  <footer class="admin-content-footer">
		<hr>
		<p class="am-padding-left">© 2018 duoluosb.</p>
	  </footer>
	</div>

	<script>
	// 页面初始化脚本

	// 窗口大小改变时隐藏移动侧边栏
	window.addEventListener('resize', function() {
		if (window.innerWidth > 640) {
			hideMobileSidebar();
		}
	});

	// 页面加载完成后的初始化
	document.addEventListener('DOMContentLoaded', function() {
		// 添加触摸事件支持
		var toggleBtn = document.querySelector('.mobile-sidebar-toggle');
		if (toggleBtn) {
			toggleBtn.addEventListener('touchstart', function(e) {
				e.preventDefault();
				toggleMobileSidebar();
			});
		}
	});
	</script>
</body>

<script>
    var _hmt = _hmt || [];
    (function() {
      var hm = document.createElement("script");
      hm.src = "https://hm.baidu.com/hm.js?00e000ae4edf31394d2153c309efbdec";
      var s = document.getElementsByTagName("script")[0]; 
      s.parentNode.insertBefore(hm, s);
    })();
</script>