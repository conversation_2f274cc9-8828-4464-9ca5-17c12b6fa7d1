﻿using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using RestSharp;
using SharpCompress.Archives;
using SharpCompress.Archives.Zip;
using SharpCompress.Common;
using SharpCompress.Readers;
using System.Diagnostics;
using System.IO;
using System.Text.Json.Serialization;
using System.Xml;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.BnsHelper.Models;

namespace Xylia.BnsHelper.Services.ApiEndpoints;
internal class YoudaoApiEndpoint(RestClient _client)
{
    public YoudaoShareApi GetFiles(long group, string token, string? file = null) => GetFilesAsync(group, token, file).GetAwaiter().GetResult();

    public async Task<YoudaoShareApi> GetFilesAsync(long group, string token, string? file = null)
    {
        var request = new RestRequest($"https://note.youdao.com/yws/api/group/{group}/share?method=get&fileId={file}&shareToken={token}&cstk=gyr0rX62");
        var response = await _client.ExecuteAsync<YoudaoShareApi>(request).ConfigureAwait(false);
        return response.Data with { Owner = this, ShareToken = token };
    }

    public async Task<Stream> DownloadAsync(YoudaoFile file, string token)
    {
        // check local file
        string path = Path.Combine(SettingHelper.Default.DownloadFolder, file.Name);
        if (File.Exists(path))
        {
            return File.Open(path, FileMode.Open);
        }

        // download
        var request = new RestRequest($"https://note.youdao.com/yws/api/group/{file.GroupId}/file/{file.FileId}?method=download&inline=true&shareToken={token}");
        using var ms = await _client.DownloadStreamAsync(request);

        var stream = File.Open(path, FileMode.Create);
        await ms!.CopyToAsync(stream);
        stream.Flush();
        return stream;
    }

    public async Task<string> LoadNoteAsync(YoudaoFile file, string token)
    {
        var request = new RestRequest($"https://note.youdao.com/yws/api/group/{file.GroupId}/note/{file.FileId}?method=get-collabnote&shareToken={token}");
        var response = await _client.ExecuteAsync(request).ConfigureAwait(false);
        return response.Content!;
    }
}

internal struct YoudaoShareApi
{
    #region Properties
    private YoudaoFile[]? children;
    public YoudaoFile[]? Children
    {
        set => children = value;
        get
        {
            if (children is not null)
            {
                var files = new List<YoudaoFile>();
                var settings = new Dictionary<string, ModSetting>();

                foreach (var item in children)
                {
                    item.Owner = this;

                    // get config
                    if (item.Name.EndsWith(".note"))
                    {
                        LoadSetting(item).ForEach(x =>
                        {
                            if (x.Name != null) settings[x.Name] = x;
                        });
                    }
                    else files.Add(item);
                }

                // bind
                foreach (var item in files)
                {
                    if (settings.TryGetValue(item.Name, out var setting)) item.Setting = setting;
                }

                return [.. files];
            }

            return null;
        }
    }

    public YoudaoFile? fileModel;
    public int groupType;
    public object shareEntry;
    public object sharer;
    public string url;
    #endregion

    #region Methods
    [JsonIgnore] internal YoudaoApiEndpoint Owner;
    [JsonIgnore] internal string ShareToken;

    public async readonly Task<Stream> DownloadAsync(YoudaoFile file) => await Owner.DownloadAsync(file, ShareToken).ConfigureAwait(false);
    public async readonly Task<string> LoadNoteAsync(YoudaoFile file) => await Owner.LoadNoteAsync(file, ShareToken).ConfigureAwait(false);

    private readonly List<ModSetting> LoadSetting(YoudaoFile file)
    {
        var doc = new XmlDocument();
        doc.LoadXml(file.Content);

        var xmlnsm = new XmlNamespaceManager(doc.NameTable);
        xmlnsm.AddNamespace("n", "http://note.youdao.com");

        var settings = new List<ModSetting>();
        var strings = new List<string>();
        foreach (XmlElement para in doc.SelectNodes("//n:para", xmlnsm)!)
        {
            var text = para.SelectSingleNode("./n:text", xmlnsm)!.InnerText;
            if (text == "★☆★☆★☆★☆★☆★☆★☆★☆★☆★☆")
            {
                if (strings.Count > 0) settings.Add(new ModSetting(strings));
                strings.Clear();
            }
            else
            {
                strings.Add(text);
            }
        }

        return settings;
    }
    #endregion
}

[DebuggerDisplay("{Name}")]
internal class YoudaoFile : ObservableObject
{
    #region Header
    internal YoudaoShareApi Owner;

    public byte AccessCode { get; set; }
    public byte AuthCode { get; set; }
    public byte AuthType { get; set; }
    public int ChildrenNum { get; set; }
    public int CommentNum { get; set; }
    public long CreateTime { get; set; }
    public bool Deleted { get; set; }
    public bool Dir { get; set; }
    public int DirectoryNum { get; set; }
    public int Domain { get; set; }
    public bool Draft { get; set; }
    public int DraftId { get; set; }
    public bool DraftRemoved { get; set; }
    public int EntryType { get; set; }
    public bool Erased { get; set; }
    public string? FileId { get; set; }
    public bool Forbid { get; set; }
    public long GroupId { get; set; }
    public bool HistoryVersion { get; set; }
    public long LastUpdateTime { get; set; }
    public long ModiftyTime { get; set; }
    public string Name { get; set; }
    public string NotePreview { get; set; }
    public bool OnlyOwnerEdit { get; set; }
    public long ParentId { get; set; }
    public object Properties { get; set; }
    public long Size { get; set; }
    public int SrcFileId { get; set; }
    public object StorageInfo { get; set; }
    public int TbHistVerNO { get; set; }
    public string Title { get; set; }
    public int Version { get; set; }
    #endregion

    #region Propetries
    private string? _content;
    internal string Content => _content ??= Owner.LoadNoteAsync(this).GetAwaiter().GetResult();

    private ModSetting? _setting;
    public ModSetting Setting { set => _setting = value; get => _setting ??= new ModSetting(); }

    public bool IsAvailable => ModRegister.Instance.GetVersion(FileId) == 0;
    public bool IsUpdatable => ModRegister.Instance.GetVersion(FileId) != Version;
    public bool IsNormal => ModRegister.Instance.GetVersion(FileId) == Version;

    public IRelayCommand InstallCommand => new AsyncRelayCommand(Install);

    async Task Install()
    {
        ArgumentNullException.ThrowIfNull(Owner);
        var root = ModRegister.Root;

        // 后续可能需要接入其他工具
        if (!IsNormal)
        {
            using var stream = await Owner.DownloadAsync(this);
            using var archive = ZipArchive.Open(stream, new ReaderOptions() { Password = Setting.Password });

            // get files
            int file = 0;
            foreach (var entry in archive.Entries)
            {
                if (!entry.IsDirectory)
                {
                    // write file
                    var target = Path.Combine(root, Setting.Url!);
                    Directory.CreateDirectory(target);

                    entry.WriteToDirectory(target, new ExtractionOptions()
                    {
                        ExtractFullPath = true,
                        Overwrite = true
                    });

                    ModRegister.Instance.SetValue(Path.Combine(Setting.Url!, entry.Key!), file++.ToString(), FileId);
                }
            }

            // write config
            ModRegister.Instance.SetValue(Name, "name", FileId);
            ModRegister.Instance.SetValue(Version, "version", FileId);

            //var SystemText = new IniSettings(Path.Combine(root, "BNSR\\Binaries\\Win64\\SystemText.ini"));
            //SystemText.SetValue("请更新剑灵小助手插件或删除所有模组文件后再试。", "ErrID_1002", "SystemMessage");
        }
        else
        {
            ModRegister.Instance.Uninstall(FileId);
        }

        // update status
        OnPropertyChanged(nameof(IsAvailable));
        OnPropertyChanged(nameof(IsUpdatable));
        OnPropertyChanged(nameof(IsNormal));
    }
    #endregion
}
