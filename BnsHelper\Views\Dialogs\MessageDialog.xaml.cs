﻿using CommunityToolkit.Mvvm.ComponentModel;
using HandyControl.Controls;
using HandyControl.Interactivity;
using HandyControl.Tools.Extension;
using System.ComponentModel;
using System.Diagnostics;
using System.Windows;
using System.Windows.Input;
using System.Windows.Threading;
using Xylia.BnsHelper.Resources;

namespace Xylia.Preview.UI.Views.Dialogs;
[ObservableObject]
[DesignTimeVisible(false)]
public partial class MessageDialog : IDialogResultable<MessageBoxResult>
{
    #region Fields
    private DispatcherTimer? _autoCloseTimer;
    private DateTime _startTime;
    #endregion

    #region Constructor
    public MessageDialog()
    {
        InitializeComponent();
        CommandBindings.Add(new CommandBinding(ControlCommands.Close, CloseCommand));
        DataContext = this;

        // 初始化按钮可见性和自动关闭计时器
        UpdateButtonVisibility();
        UpdateAutoCloseTimer();
    }
    #endregion

    #region Properties
    public static readonly DependencyProperty MessageProperty = DependencyProperty.Register(nameof(Message), typeof(string), typeof(MessageDialog),
        new FrameworkPropertyMetadata(string.Empty, FrameworkPropertyMetadataOptions.AffectsRender));

    public string Message
    {
        get { return (string)GetValue(MessageProperty); }
        set { SetValue(MessageProperty, value); }
    }

    public static readonly DependencyProperty OkTextProperty = DependencyProperty.Register(nameof(OkText), typeof(string), typeof(MessageDialog),
    new FrameworkPropertyMetadata(string.Empty, FrameworkPropertyMetadataOptions.AffectsRender));

    public string OkText
    {
        get { return (string)GetValue(OkTextProperty); }
        set { SetValue(OkTextProperty, value); }
    }

    public static readonly DependencyProperty ButtonTypeProperty = DependencyProperty.Register(nameof(ButtonType), typeof(MessageBoxButton), typeof(MessageDialog),
        new FrameworkPropertyMetadata(MessageBoxButton.OK, FrameworkPropertyMetadataOptions.AffectsRender, OnButtonTypeChanged));

    public MessageBoxButton ButtonType
    {
        get { return (MessageBoxButton)GetValue(ButtonTypeProperty); }
        set { SetValue(ButtonTypeProperty, value); }
    }

    private static void OnButtonTypeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is MessageDialog dialog)
        {
            dialog.UpdateButtonVisibility();
            dialog.UpdateAutoCloseTimer();
        }
    }

    public static readonly DependencyProperty ShowOkButtonProperty = DependencyProperty.Register(nameof(ShowOkButton), typeof(bool), typeof(MessageDialog),
        new FrameworkPropertyMetadata(true, FrameworkPropertyMetadataOptions.AffectsRender));

    public bool ShowOkButton
    {
        get { return (bool)GetValue(ShowOkButtonProperty); }
        set { SetValue(ShowOkButtonProperty, value); }
    }

    public static readonly DependencyProperty ShowCancelButtonProperty = DependencyProperty.Register(nameof(ShowCancelButton), typeof(bool), typeof(MessageDialog),
        new FrameworkPropertyMetadata(false, FrameworkPropertyMetadataOptions.AffectsRender));

    public bool ShowCancelButton
    {
        get { return (bool)GetValue(ShowCancelButtonProperty); }
        set { SetValue(ShowCancelButtonProperty, value); }
    }

    public static readonly DependencyProperty ShowYesButtonProperty = DependencyProperty.Register(nameof(ShowYesButton), typeof(bool), typeof(MessageDialog),
        new FrameworkPropertyMetadata(false, FrameworkPropertyMetadataOptions.AffectsRender));

    public bool ShowYesButton
    {
        get { return (bool)GetValue(ShowYesButtonProperty); }
        set { SetValue(ShowYesButtonProperty, value); }
    }

    public static readonly DependencyProperty ShowNoButtonProperty = DependencyProperty.Register(nameof(ShowNoButton), typeof(bool), typeof(MessageDialog),
        new FrameworkPropertyMetadata(false, FrameworkPropertyMetadataOptions.AffectsRender));

    public bool ShowNoButton
    {
        get { return (bool)GetValue(ShowNoButtonProperty); }
        set { SetValue(ShowNoButtonProperty, value); }
    }

    public static readonly DependencyProperty AutoCloseMillisecondsProperty = DependencyProperty.Register(nameof(AutoCloseMilliseconds), typeof(int), typeof(MessageDialog),
        new FrameworkPropertyMetadata(0, FrameworkPropertyMetadataOptions.AffectsRender, OnAutoCloseMillisecondsChanged));

    public int AutoCloseMilliseconds
    {
        get { return (int)GetValue(AutoCloseMillisecondsProperty); }
        set { SetValue(AutoCloseMillisecondsProperty, value); }
    }

    private static void OnAutoCloseMillisecondsChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is MessageDialog dialog)
        {
            dialog.UpdateAutoCloseTimer();
        }
    }
    #endregion

    #region Methods
    private void UpdateButtonVisibility()
    {
        switch (ButtonType)
        {
            case MessageBoxButton.OK:
                ShowOkButton = true;
                ShowCancelButton = false;
                ShowYesButton = false;
                ShowNoButton = false;
                break;
            case MessageBoxButton.OKCancel:
                ShowOkButton = true;
                ShowCancelButton = true;
                ShowYesButton = false;
                ShowNoButton = false;
                break;
            case MessageBoxButton.YesNo:
                ShowOkButton = false;
                ShowCancelButton = false;
                ShowYesButton = true;
                ShowNoButton = true;
                break;
            case MessageBoxButton.YesNoCancel:
                ShowOkButton = false;
                ShowCancelButton = true;
                ShowYesButton = true;
                ShowNoButton = true;
                break;
        }
    }

    private void UpdateAutoCloseTimer()
    {
        // MessageBoxButton.OK 且设置了自动关闭毫秒数时启动自动关闭计时器
        if (ButtonType == MessageBoxButton.OK && AutoCloseMilliseconds > 0)
        {
            StartAutoCloseTimer();
        }
        else
        {
            StopTimer();
        }
    }

    private void StartAutoCloseTimer()
    {
        if (_autoCloseTimer != null) return; // 避免重复启动

        _startTime = DateTime.Now;
        _autoCloseTimer = new DispatcherTimer
        {
            Interval = TimeSpan.FromMilliseconds(50) // 每50毫秒检查一次，提高精度
        };
        _autoCloseTimer.Tick += OnTimerTick;
        _autoCloseTimer.Start();
    }

    private void OnTimerTick(object? sender, EventArgs e)
    {
        var elapsed = (DateTime.Now - _startTime).TotalMilliseconds;

        if (elapsed >= AutoCloseMilliseconds)
        {
            _autoCloseTimer?.Stop();
            CloseAction?.Invoke();
            Result = MessageBoxResult.OK; // Auto-close returns OK
        }
    }

    private void StopTimer()
    {
        _autoCloseTimer?.Stop();
        _autoCloseTimer = null;
    }

    private void Ok_Click(object sender, RoutedEventArgs e)
    {
        StopTimer();
        CloseAction?.Invoke();
        Result = MessageBoxResult.OK;
    }

    private void Cancel_Click(object sender, RoutedEventArgs e)
    {
        StopTimer();
        CloseAction?.Invoke();
        Result = MessageBoxResult.Cancel;
    }

    private void Yes_Click(object sender, RoutedEventArgs e)
    {
        StopTimer();
        CloseAction?.Invoke();
        Result = MessageBoxResult.Yes;
    }

    private void No_Click(object sender, RoutedEventArgs e)
    {
        StopTimer();
        CloseAction?.Invoke();
        Result = MessageBoxResult.No;
    }

    private void CloseCommand(object sender, RoutedEventArgs e)
    {
        StopTimer();
        CloseAction?.Invoke();
        Result = MessageBoxResult.Cancel;
    }
    #endregion

    #region Interface
    public MessageBoxResult Result { get; set; }

    public Action? CloseAction { get; set; }

    public static async Task<bool> ShowDialog(string? message, string? btnText = null, int autoCloseMilliseconds = 3000) =>
        await ShowDialog(message, btnText, MessageBoxButton.OK, autoCloseMilliseconds) == MessageBoxResult.OK;

    public static async Task<MessageBoxResult> ShowDialog(string? message, string? btnText, MessageBoxButton button, int autoCloseMilliseconds = 3000)
    {
        try
        {
            // 确保主窗口处于活动状态，这是 HandyControl Dialog 正常显示的关键
            Application.Current.MainWindow?.Activate();

            // 添加超时机制防止无限等待
            var dialog = new MessageDialog()
            {
                Message = message,
                ButtonType = button,
                AutoCloseMilliseconds = autoCloseMilliseconds,
                OkText = btnText ?? StringHelper.Get("Text.Ok")
            };

            // 设置超时，防止无限等待
            var timeoutTask = Task.Delay(15000);
            var dialogTask = Dialog.Show(dialog).GetResultAsync<MessageBoxResult>();

            var completedTask = await Task.WhenAny(dialogTask, timeoutTask);
            if (completedTask == timeoutTask)
            {
                dialog.CloseAction?.Invoke();
                return MessageBoxResult.Cancel;
            }

            return await dialogTask;
        }
        catch (Exception ex)
        {
            // 如果对话框显示失败，记录错误并返回默认值
            Debug.WriteLine($"MessageDialog.ShowDialog failed: {ex.Message}");
            return button == MessageBoxButton.OK ? MessageBoxResult.OK : MessageBoxResult.Cancel;
        }
    }


    #endregion
}
