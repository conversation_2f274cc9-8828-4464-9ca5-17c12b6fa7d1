
<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=Edge" >
        <base href="https://www.bnszs.com/" >
        <title>战力榜 - 剑灵小助手</title>
        <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1">
        <meta name="description" content="剑灵活动小助手官网(www.bnszs.com),剑灵活动助手(剑灵小助手)可以帮助您快速领取游戏礼包、修改游戏DAT、装备查询、模型修改、和谐补丁、捏脸库、login库、等各种功能,本软件永久免费、安全、高效是您的不二选择。">
        <meta name="keywords" content="剑灵助手,剑灵小助手,小助手,剑灵活动,剑灵活动助手,剑灵领取助手,一键领取剑灵活动,剑灵活动工具,剑灵活动一键,剑灵,帮豆,剑灵一键领取奖励,助手官网,剑灵助手官网,剑灵礼包助手,剑灵装备查询">
        <meta name="renderer" content="webkit">
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
        <link rel="icon shortcut" href="favicon.ico">
        <link rel="stylesheet" href="https://security.tencent.com/static/v2.0/css/main.min.css?t=20160310">
        <link rel="stylesheet" href="https://www.layuicdn.com/layui/css/layui.css">
        <!--[if (gte IE 6)&(lte IE 8)]>
        <script type="text/javascript" src="static/plugins/selectivizr.min.js"></script>
        <script type="text/javascript" src="static/plugins/respond.min.js"></script>
        <![endif]-->
        <script type="text/javascript" src="https://security.tencent.com/static/plugins/jquery.js"></script>
    
        <style type="text/css">
            .header>div .userpanel .userpanel_login .login-btn-1 {
                width: 80px;
                margin: 0 2px;
                -webkit-transition: color .2s,border-color .2s,width .5s,height .5s,line-height .5s;
                transition: color .2s,border-color .2s,width .5s,height .5s,line-height .5s;
            }
            .header>div .userpanel .userpanel_login .login-btn-2 {
                width: 80px;
                margin: 0 2px;
                -webkit-transition: color .2s,border-color .2s,width .5s,height .5s,line-height .5s;
                transition: color .2s,border-color .2s,width .5s,height .5s,line-height .5s;
            }

            .header>div .userpanel .userpanel_login .login-qq {margin-left:10px;}

            .header>div .userpanel .userpanel_user .user_username { max-width: 80px; }

            .header>div .nav {
                float: left;
                margin-left: 10px;
            }

            .header>div .header-separator {
                content: '';
                position: absolute;
                width: 1px;
                height: 28px;
                margin-left: 3px;
                margin-top: 31px;
                background-color: #d9d9d9;
                -webkit-transition: height .5s,margin-top .5s;
                transition: height .5s,margin-top .5s;
            }

            .header-scroll>div .header-separator {
                height: 17px;
                margin-top: 18px;
                margin-left: 3px;
                content: '';
                position: absolute;
                width: 1px;
                background-color: #d9d9d9;
                transition: height .5s,margin-top .5s;
            }

            .header-change {
                height:90px;
                line-height:90px;
                float:right;
            }

            .header-change a {
                position: absolute;
                margin-left: 10px;
                text-decoration:none;
            }

            .header-change a:hover{
                color: #4183c4;
            }

            .header-scroll>div .header-change {
                height:50px;
                line-height:50px;
                float:right;
            }

            .header>div .userpanel .userpanel_user .user_menu {width: 185px;}

            .header>div .nav>ul {margin-right:0px;}

            .header>div .nav>ul>li {margin: 0 8px;}

        </style>
    
    </head>
    <script type="text/javascript">
        $(function(){
            
            // 选择导航栏
            var top_tab = 'bnsTop';
            $('.header .nav li[name='+ top_tab +']').attr('class', 'nav_item-active');

            // 导航栏效果
            $(window).scroll(function () {
                if($(this).scrollTop() > 80) {
                    $('.header').addClass('header-scroll');
                } else {
                    $('.header').removeClass('header-scroll');
                }
            });
        });
    </script>
    <body>

        <div class="header">
            <div>
                <div class="logo">
                    <a href="index.php">
                        
                        <div class="logo_name">
                            <p>剑灵小助手</p>
                            <p>www.Bnszs.com</p>
                        </div>
                    </a>
                </div>

                <div class="nav">
                    <ul>
                        <li name="index"><a href="index.php">首页</a></li>
                        <li name="bnsTop"><a href="bnsTop.html">战力榜</a></li>
                       
                        
                    </ul>
                </div>

                <div style="float:right;display:none">

                    <div class="userpanel" style="float:left;">
                        <div class="userpanel_login" style="display:block">
                            <a class="mod-btn mod-btn-black login-btn-1 login-qq" href="javascript:void(0)">QQ 登录</a>
                            <a class="mod-btn mod-btn-black login-btn-2 login-wx" href="javascript:void(0)">微信登录</a>
                        </div>
                        <div class="userpanel_user" style="display:none">
                            
                            <span class="user_username"><i class="i-header-message" style="display:none"></i></span>
                            <i class="user_arrow"></i>
                            <div class="user_menu">
                                <ul class="menu_list">
                                    <li class="list_item list_item-user">
                                        <a href="index.php/user">
                                            <i class="item_icon"></i>
                                            <span class="item_text">个人主页</span>
                                        </a>
                                    </li>
                                    <li class="list_item list_item-message">
                                        <a href="index.php/user/message">
                                            <i class="item_icon"></i>
                                            <span class="item_text">消息中心</span>
                                            <i class="item_icon-message" style="display:none"></i>
                                        </a>
                                    </li>
                                    <li class="list_item list_item-account">
                                        <a href="index.php/user/info">
                                            <i class="item_icon"></i>
                                            <span class="item_text">账户设置</span>
                                        </a>
                                    </li>
                                    <li class="list_item list_item-logout">
                                        <a id="logout" href="javascript:void(0)">
                                            <i class="item_icon"></i>
                                            <span class="item_text">退出</span>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>



                    <div class="header-change">
                        <span class="header-separator"></span>
                        <a href="https://en.security.tencent.com">En</a>
                    </div>

                </div>

                
            </div>
        </div>
<style type="text/css">
    
    .mod-grouptabs {
        width: 550px !important;
    }
    .grouptabs_item {
        width: 110px !important;
    }
    #rank-desc {
        float:right;height:38px;line-height:38px;width:100px;
    }

</style>

<div class="container container-rank">
    <div>
        <!-- section-subnav 标题-->
        <div class="section section-subnav">
            <div class="section_subnav">
                <p class="subnav_title">战力榜</p>
                <div class="nav_myrank" style="display:none">
                    <span>我的排名</span>
                    <a href="javascript:void(0)" ><span class="myrank_num">0</span></a>
                </div>
            </div>
        </div>

        <div class="section section-rank">
            <div class="section_rank">
                <div class="rank_nav">
                    <div class="nav_rankselect">
                        <ul class="mod-grouptabs">
                            <li id="tab-month" class="grouptabs_item grouptabs_item-active">
                                <a href="javascript:msgAlert('暂未开放',5)">月排行榜</a>
                            </li>
                            <li id="tab-year" class="grouptabs_item ">
                                <a href="javascript:msgAlert('暂未开放',5)">年排行榜</a>
                            </li>
                            <li id="tab-all" class="grouptabs_item ">
                                <a href="javascript:msgAlert('暂未开放',5)">总排行榜</a>
                            </li>
                            
                        </ul>
                        <div class="rankselect_form">
                            <div class="form_time" style="display:">
                                <label for="">时间</label>
                                <div>
                                    <select name="" id="select-year">
                                        <option value="2020" selected=true>2020年</option>                                 </select>
                                </div>
                                <div style="display:">
                                    <select name="" id="select-month"><option value="11" selected=true>11月</option><option value="12">12月 - 未开启</option>                                    </select>
                                </div>
                            </div>
                            <div class="form_type" style="display:">
                                <label for="">大区</label>
                                <div>
                                    <select name="" id="select-type">
                                        <option value="all">暂不支持选择</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <a id="rank-desc" class="mod-btn mod-btn-black" style="display:none">榜单说明</a>

                    </div>
                </div>
                <table class="rank_table">
                    <thead>
                        <tr>
                                            <th style="width:80px;text-align:center;">排行</th>
                                            <th style="width:150px;text-align:center;">服务器</th>
                                            <th style="width:250px;text-align:center;">角色名称</th>
                                            <th style="width:120px;text-align:center;">战力值</th>
                                            <th style="width:150px;text-align:center;">等级</th>
                                            <th style="width:100px;text-align:center;">职业</th>
                                        </tr>                        
                    </thead>
                    <tbody id='topList'>
                         
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script src="https://gameact.qq.com/comm-htdocs/js/game_area/bns_server_select.js" charset="gb2312"></script>
<script src="https://www.layuicdn.com/layer/layer.js"></script>
<script type="text/javascript">
    function showEquipPop(id,rolename){
        //https://tools.bnszs.com/ingame/bs/character/profile?s=4115&c=%E5%B0%8F%E6%B4%81%E5%86%AC%E7%9C%A0%E4%BA%86
        var url = "https://tools.bnszs.com/ingame/bs/character/profile?s="+id+"&c="+rolename;
        window.open(url);
    }
    function msgAlert(msg,icon=6){
        layer.alert(msg, {icon: icon});
    }

jQuery(document).ready(function($) {

    // 准备传入模块的数据
    var jobMap={
        1:'剑士',
        2:'拳师',
        3:'气功师',
        4:'枪手',
        5:'力士',
        6:'召唤师',
        7:'刺客',
        8:'灵剑士',
        9:'咒术师',
        10:'气宗',
        11:'斗士',
        12:'弓手',
        14:'星术师'
    }
    
    var listData=[];
    
    //https://tools.bnszs.com/bnsTop1000.json
    $.getJSON("https://tools.bnszs.com/api/top.json",function(ret){
        var loading = layer.msg("加载数据中...",{
				icon:16,
				time:-1
		})
		
		
        if(ret.code !=0 || typeof ret.data=="unedfined"){
            alert("系统正忙，请稍后再试！");
            return;
        }
        
        
        layer.close(loading);
        //整理数据 排序
        var tmpListData= ret.data;
        var maxStatisDate="";
        /* tmpListData.sort(function(a,b){
            if(a.statis_date>=maxStatisDate){
                maxStatisDate=a.statis_date;
            }
            if(b.statis_date>=maxStatisDate){
                maxStatisDate=b.statis_date;
            }
            return a.zbrank-b.zbrank;
        });*/
        
        tmpListData.forEach(function(val){
            if(true || val.statis_date==maxStatisDate){
                listData.push(val);
            }
        });
        
        //渲染到html tr 中
        var html = '';
        listData.forEach(function(val, index){
            html += '<tr>';
            html +=    '<td class="td-rank-'+ (index+1) +'" style="text-align:center;"><i>'+ (index+1) +'</i></td>';
            html +=    '<td style="text-align:center;">'+zoneToName(val.worldid)+'</td>';
            //http://tools.bnszs.com/api/getface?id='+val.id+'
            html +=    '<td style=""><img class="mod-avatar mod-avatar-50" src="//q1.qlogo.cn/g?b=qq&nk='+ val.iuin + '&s=100" alt="" data-src="//q1.qlogo.cn/g?b=qq&nk='+ val.iuin + '&s=100"><a href="javascript:showEquipPop('+val.worldid+',\''+decodeURIComponent(decodeURIComponent(val.name))+'\')">'+decodeURIComponent(decodeURIComponent(val.name))+'</a></td>';
            html +=    '<td style="text-align:center;">'+val.score+'</td>';
            html +=    '<td style="text-align:center;">'+val.level+'级 '+val.mastery_level+'星</td>';
            html +=    '<td style="text-align:center;">'+jobMap[val.job]+'</td>';
            html += '</tr>';
        });
        $("#topList").html(html);
        
        //renderingPage();
        //loadImg()
        layer.alert('排行榜数据加载完毕</br>点击头像后面的名称可查看装备</br>感谢使用剑灵小助手', {icon: 6});
    });
    
        
    function zoneToName(ssn){
        var data=BNSServerSelect.STD_DATA;
        var len=data.length;
        var result = "";
        for(var i=0;i<len;i++)
        {
            var sub_data = data[i].opt_data_array;
            var sub_len = sub_data.length;
            for (var j=0; j<sub_len; j++)
            {
            	if(sub_data[j].v==String(ssn) && sub_data[j].t.indexOf("互通")==-1)
                {
                    result=sub_data[j].t;
                    break;
                }
            }
            if (result != "") {
               	break;
            }
        }
        return result || "";
    }

    
    
    // 页面初始化
    //封装获取元素离上方的高度的函数
    function offsetTop1(obj) {
        var t = obj.offsetTop;
        while (obj.offsetparent) {
            obj = obj.offsetparent;
            t = t + obj.offsetTop;
        }
        return t;
    }

	// 懒加载实现
	function loadImg() {
        var iH = document.documentElement.clientHeight;
        var t = document.documentElement.scrollTop || document.body.scrollTop;
        var img = document.getElementsByTagName("img");
        for (var i = 0; i < img.length; i++) {
            if ((!img[i].bstop) && offsetTop1(img[i]) < (iH + t)) {
                //注意  真正在页面上使用一定要加开关,不加开关每次条件符合时都会重新请求服务器，还不如不用懒加载
                //理解不了啥意思的同学，可以把img[i].bstop删去试试
                //同时不要用offsetTop  
                //因为offsetTop是获取离已定位的父元素的top值  
                //所以自己封装一个计算offsetTop的函数
                var src = img[i].getAttribute("data-src");
                img[i].src = src;
                img[i].bstop = true;
            }
        }
    }
	
	//添加滚动事件
    window.onscroll = function () {
        //loadImg()
    }

});

</script>

        <!-- 确认弹窗 -->
        <div id="base-confirm" class="mod-popup mod-popup-report">
            <div class="popup_mask">
            </div>
            <div class="popup_body">
                <div class="body_content">
                    <p class="content_title"></p>
                    <p class="content_text"></p>
                </div>
                <div class="body_btns">
                    <a class="mod-btn mod-btn-blue" href="javascript:void(0)">确定</a>
                    <a class="mod-btn mod-btn-black" href="javascript:void(0)">取消</a>
                </div>
                <i class="i-close"></i>
            </div>
        </div>

        <!-- 提示弹窗 -->
        <div id="base-alert" class="mod-popup mod-popup-success">
            <div class="popup_mask">
            </div>
            <div class="popup_body">
                <div class="body_content">
                    <p class="content_pic"></p>
                    <p class="content_text">提示弹窗</p>
                </div>
                <div class="i-close"></div>
            </div>
        </div>

        <!-- 登录弹窗 -->
        <div id="base-login-box" class="mod-popup mod-popup-report">
            <div class="popup_mask">
            </div>
            <div class="popup_body">
                <div class="body_content">
                    <p class="content_title">登录提醒</p>
                    <p class="content_text" style="text-align:center;">
                        系统检测到您还未登录或登录态已失效，请先登录
                    </p>
                    <div class="body_btns">
                        <a href="" class="mod-btn mod-btn-black login-qq" style="border-color:#007cfa;color:#007cfa">QQ 登录</a>
                        <a href="" class="mod-btn mod-btn-black login-wx" style="border-color:#007cfa;color:#007cfa">微信 登录</a>
                    </div>
                </div>
                <i class="i-close"></i>
            </div>
        </div>


        <style type="text/css">
            #base-tip-box .popup_body {
                width: 500px;
                height: 300px;
                margin-left: -250px;
                margin-top: -150px;
            }
            #base-tip-box .content_text {
                text-align:left;
                margin-left:2px;
            }
            #base-tip-box .tip-btn-confirm {
                color: #fff;
            }
            #base-tip-box .tip-btn-close {
                color: #fff;
                background-color: #cce5fe;
                cursor: pointer;
            }
            #base-tip-box .tip-btn-close:hover{
                border-bottom-color: #fff !important;
            }
            #base-tip-box .body_btns {
                margin: 0px auto 0;
            }
        </style>

        <!-- tip弹窗 -->
        <div id="base-tip-box" class="mod-popup mod-popup-report">
            <div class="popup_mask">
            </div>
            <div class="popup_body">
                <div class="body_content">
                    <p class="content_title">Bnszs调查问卷</p>
                    <div class="content_text">
                        
                        <div style="font-size:15px;color: #333;">
                            为了更好和大家互动，Bnszs诚邀您填写问卷，我们将在提交联系方式的参与者中随机抽取部分幸运儿，赠送精美礼品！
                        </div>

                        <div class="body_btns" style="margin-top:15px;margin-bottom:15px;">
                            <a class="mod-btn mod-btn-blue tip-btn-confirm" target="_blank" href="">马上填写</a>
                            <a class="mod-btn mod-btn-disabled-blue tip-btn-close tip-close" href="javascript:void(0)">下次再填</a>
                        </div>

                        <div style="">
                            如您已在其他渠道填写过问卷，请勾选：
                            <input class="input-checkbox" type="checkbox">
                            忽略，不再弹出
                        </div>

                        <div style="margin-top:25px;float:right;">
                            如有疑问，请点击咨询：<a href="#">QQ</a>
                        </div>
                        
                    </div>
                </div>
                <i class="i-close tip-close"></i>
            </div>
        </div>


        <!-- footer -->
        <div class="footer">
            <div>
                <div class="footer_about">
                    <p class="about_link">
                        <a href="https://www.bnszs.com/" target="_blank">剑灵小助手</a>
                    </p>
                    <p class="about_copyright">
                        <span>Copyright &copy; 2017 - 2020 Bnszs. All Rights Reserved</span>
                        <span>剑灵小助手版权所有</span>
                    </p>
                </div>

                <div class="footer_contact">
                    <a class="contact_icon-email" href="mailto:<EMAIL>"><i></i><span>专用邮箱</span>|</a>
                    <a class="contact_icon-weibo-sina" href="https://weibo.com/bnszs" target="_blank"><i></i><span>新浪微博</span>|</a>
                    <!--<a class="contact_icon-weixin mod-qrcode" href="javascript:void(0)">-->
                    <!--    <i></i><span>微信公众号</span>-->
                    <!--    <div class="qrcode">-->
                    <!--        <div class="qrcode_pic">-->
                    <!--            <img src="uploadimg_dir/other/Bnszs-qrcode.jpg" alt="">-->
                    <!--        </div>-->
                    <!--        <span class="qrcode_text">扫一扫关注 Bnszs 公众号</span>-->
                    <!--    </div>-->
                    <!--</a>-->
                </div>
            </div>
        </div>
    </body>

</html>

<script>
var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?00e000ae4edf31394d2153c309efbdec";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
})();
</script>


