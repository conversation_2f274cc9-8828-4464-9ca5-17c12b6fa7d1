package service

import (
	"fmt"
	"log"
	"time"

	"gorm.io/gorm"

	"udp-server/server/internal/model"
	"udp-server/server/pkg/cache"
)

// StatsService 统计服务
type StatsService struct {
	db           *gorm.DB
	cache        cache.Cache
	authService  *AuthService
	heartbeatSvc *HeartbeatService
	riskService  *RiskControlService
}

// NewStatsService 创建统计服务
func NewStatsService(db *gorm.DB, cache cache.Cache, authService *AuthService, heartbeatSvc *HeartbeatService, riskService *RiskControlService) *StatsService {
	return &StatsService{
		db:           db,
		cache:        cache,
		authService:  authService,
		heartbeatSvc: heartbeatSvc,
		riskService:  riskService,
	}
}

// SaveCurrentStats 保存当前统计数据到历史记录
func (s *StatsService) SaveCurrentStats() error {
	now := time.Now()

	// 获取认证服务统计
	authStats := s.authService.GetStats()

	// 获取心跳服务统计
	heartbeatStats := s.heartbeatSvc.GetStats()

	// 创建历史记录
	statsHistory := &model.OnlineStatsHistory{
		OnlineCount:     len(s.authService.GetOnlineUsers()),
		AuthActiveUsers: s.safeIntConvert(authStats["active_users"]),
		AuthTotalTokens: s.safeIntConvert(authStats["total_tokens"]),
		HeartbeatActive: s.safeIntConvert(heartbeatStats["active_devices"]),
		HeartbeatTotal:  s.safeIntConvert(heartbeatStats["total_devices"]),
		Timestamp:       now.Unix(),
	}

	// 保存到数据库
	if err := s.db.Create(statsHistory).Error; err != nil {
		return fmt.Errorf("保存统计历史记录失败: %v", err)
	}

	log.Printf("[INFO] 已保存统计历史记录: 在线用户=%d, 认证用户=%d, 心跳设备=%d",
		statsHistory.OnlineCount, statsHistory.AuthActiveUsers, statsHistory.HeartbeatActive)

	// 更新风控配置（每小时执行一次）
	if s.riskService != nil {
		if err := s.riskService.UpdateConfig(); err != nil {
			log.Printf("[ERROR] 更新风控配置失败: %v", err)
		}
	}

	return nil
}

// GetHistoryStats 获取历史统计数据
func (s *StatsService) GetHistoryStats(period string, limit int) ([]model.OnlineStatsHistory, error) {
	var stats []model.OnlineStatsHistory
	var interval time.Duration

	// 根据周期确定查询间隔
	switch period {
	case "hour":
		interval = time.Hour
	case "day":
		interval = 24 * time.Hour
	default:
		interval = time.Hour
	}

	// 计算开始时间
	startTime := time.Now().Add(-time.Duration(limit) * interval).Unix()

	// 查询历史数据
	err := s.db.Where("timestamp >= ?", startTime).
		Order("timestamp ASC").
		Limit(limit).
		Find(&stats).Error

	if err != nil {
		return nil, fmt.Errorf("查询历史统计数据失败: %v", err)
	}

	return stats, nil
}

// GetHistoryStatsGrouped 获取按时间分组的历史统计数据
func (s *StatsService) GetHistoryStatsGrouped(period string, limit int) ([]model.OnlineStatsHistory, error) {
	var stats []model.OnlineStatsHistory
	var groupBy string
	var interval time.Duration

	// 根据周期确定分组方式和间隔
	switch period {
	case "hour":
		// 按小时分组，取每小时的平均值
		groupBy = "FROM_UNIXTIME(timestamp, '%Y-%m-%d %H:00:00')"
		interval = time.Hour
	case "day":
		// 按天分组，取每天的平均值
		groupBy = "FROM_UNIXTIME(timestamp, '%Y-%m-%d')"
		interval = 24 * time.Hour
	default:
		groupBy = "FROM_UNIXTIME(timestamp, '%Y-%m-%d %H:00:00')"
		interval = time.Hour
	}

	// 计算开始时间
	startTime := time.Now().Add(-time.Duration(limit) * interval).Unix()

	// 查询分组统计数据
	err := s.db.Select(fmt.Sprintf(`
		AVG(online_count) as online_count,
		AVG(auth_active_users) as auth_active_users,
		AVG(auth_total_tokens) as auth_total_tokens,
		AVG(heartbeat_active) as heartbeat_active,
		AVG(heartbeat_total) as heartbeat_total,
		UNIX_TIMESTAMP(%s) as timestamp
	`, groupBy)).
		Where("timestamp >= ?", startTime).
		Group(groupBy).
		Order("timestamp ASC").
		Limit(limit).
		Find(&stats).Error

	if err != nil {
		return nil, fmt.Errorf("查询分组历史统计数据失败: %v", err)
	}

	return stats, nil
}

// CleanupOldStats 清理过期的统计数据
func (s *StatsService) CleanupOldStats() error {
	// 保留最近30天的数据
	thirtyDaysAgo := time.Now().AddDate(0, 0, -30).Unix()

	result := s.db.Where("timestamp < ?", thirtyDaysAgo).Delete(&model.OnlineStatsHistory{})
	if result.Error != nil {
		return fmt.Errorf("清理过期统计数据失败: %v", result.Error)
	}

	if result.RowsAffected > 0 {
		log.Printf("[INFO] 已清理过期统计数据: 删除记录数=%d", result.RowsAffected)
	}

	return nil
}

// StartStatsCollector 启动统计数据收集器
func (s *StatsService) StartStatsCollector() {
	// 每小时保存一次统计数据
	ticker := time.NewTicker(1 * time.Hour)

	go func() {
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				if err := s.SaveCurrentStats(); err != nil {
					log.Printf("[ERROR] 保存统计数据失败: %v", err)
				}
			}
		}
	}()

	log.Printf("[INFO] 统计数据收集器已启动，每小时保存一次数据")
}

// StartStatsCleanup 启动统计数据清理任务
func (s *StatsService) StartStatsCleanup() {
	// 每天凌晨3点清理过期数据
	now := time.Now()
	nextCleanup := time.Date(now.Year(), now.Month(), now.Day()+1, 3, 0, 0, 0, now.Location())
	if now.Hour() < 3 {
		nextCleanup = time.Date(now.Year(), now.Month(), now.Day(), 3, 0, 0, 0, now.Location())
	}

	ticker := time.NewTicker(24 * time.Hour)

	go func() {
		defer ticker.Stop()

		// 等待到第一次执行时间
		time.Sleep(time.Until(nextCleanup))

		log.Printf("[INFO] 启动统计数据清理任务，每天凌晨3点执行")

		for {
			select {
			case <-ticker.C:
				log.Printf("[INFO] 开始执行统计数据清理任务")
				if err := s.CleanupOldStats(); err != nil {
					log.Printf("[ERROR] 统计数据清理任务失败: %v", err)
				} else {
					log.Printf("[INFO] 统计数据清理任务完成")
				}
			}
		}
	}()
}

// safeIntConvert 安全地将interface{}转换为int
func (s *StatsService) safeIntConvert(value interface{}) int {
	if value == nil {
		return 0
	}

	switch v := value.(type) {
	case int:
		return v
	case int64:
		return int(v)
	case float64:
		return int(v)
	case string:
		// 尝试解析字符串为数字
		if num, err := fmt.Sscanf(v, "%d", new(int)); err == nil && num == 1 {
			var result int
			fmt.Sscanf(v, "%d", &result)
			return result
		}
		return 0
	default:
		log.Printf("[WARN] 无法转换类型 %T 到 int，使用默认值 0", value)
		return 0
	}
}
