﻿using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CUE4Parse.UE4.Pak;
using Microsoft.Win32;
using Serilog;
using System.Diagnostics;
using System.IO;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.BnsHelper.Models;
using Xylia.BnsHelper.Resources;
using Xylia.BnsHelper.Services;
using Xylia.BnsHelper.Services.ApiEndpoints;
using Xylia.Preview.UI.Views.Dialogs;

namespace Xylia.BnsHelper.ViewModels.Pages;
internal partial class AssetPageViewModel : ObservableObject
{
	#region Properties
	private static string Paks => Path.Combine(SettingHelper.Default.Game.FullPath, "Content\\Paks");

	public static string ModFolder
	{
		get
		{
			var mods = Path.Combine(Paks, "~mods");
			Directory.CreateDirectory(mods);
			return mods;
		}
	}

	[ObservableProperty] YoudaoShareApi _folders = ApiEndpointService.YoudaoApi.GetFiles(94630173, "40D1763C6D104AE0AC9E1E2452AFC830");
	[ObservableProperty] YoudaoShareApi? _folder;
	[ObservableProperty] YoudaoFile? _selectedMod;

	YoudaoFile? _selectedFolder;
	public YoudaoFile? SelectedFolder
	{
		get => _selectedFolder;
		set
		{
			_selectedFolder = value;
			Folder = value is null ? null : ApiEndpointService.YoudaoApi.GetFiles(value.GroupId, value.Owner.ShareToken, value.FileId);
		}
	}

	// 工具集合
	static YoudaoShareApi Test1 => ApiEndpointService.YoudaoApi.GetFiles(95959837, "7EB0B2112FB4426EA4E8A5F61891A83A");
	#endregion

	#region Methods
	[RelayCommand] void OpenModFolder() => Process.Start("Explorer", ModFolder);

	[RelayCommand]
	async Task PackFont()
	{
		var dialog = new OpenFileDialog()
		{
			Filter = "TrueType Font|*.ttf|TrueType Collection|*.ttc",
		};
		if (dialog.ShowDialog() != true) return;

		try
		{
			var reader = new MyPakFileReader("BNSR/Content");
			reader.Add(dialog.FileName, "Fonts/FZY3K.ufont");
			reader.Save(Path.Combine(ModFolder, "bnszs_Font_P"), Path.Combine(Paks, "Pak0-Local.sig"));

			await MessageDialog.ShowDialog(StringHelper.Get("AssetPage_RepackSuccess"));
		}
		catch (Exception ex)
		{
			Log.Error(ex, "Package");
			await MessageDialog.ShowDialog(StringHelper.Get("AssetPage_RepackFailed", ex.Message), null, -1);
		}
	}
	#endregion


	public void GetAll()
	{
		var dic = new Dictionary<string, string>();

		foreach (var mod in ModRegister.Instance.Sections.Select(s => new ModSetting(s)))
		{
			mod.Files.ForEach(f => dic[f] = mod.Name!);
		}

		foreach (var a in Directory.GetFiles(ModFolder, "*.pak"))
		{
			Debug.WriteLine(a);
		}
	}
}
