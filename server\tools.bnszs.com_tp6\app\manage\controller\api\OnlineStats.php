<?php
namespace app\manage\controller\api;

use app\common\controller\ApiBase;
use think\App;
use think\Request;
use think\facade\Config;
use app\common\service\RedisService;

/**
 * 在线统计API控制器
 */
class OnlineStats extends ApiBase
{
    public function __construct(App $app, Request $request)
    {
        parent::__construct($app, $request);
    }

    /**
     * 获取当前在线统计
     */
    public function current()
    {
        try {
            // 获取在线用户数
            $onlineCount = RedisService::get('bns:online_count') ?: 0;

            // 获取详细统计数据
            $onlineStatsJson = RedisService::get('bns:online_stats');
            $onlineStats = $onlineStatsJson ? json_decode($onlineStatsJson, true) : [];
            
            $data = [
                'online_count' => (int)$onlineCount,
                'auth_active_users' => $onlineStats['auth_active_users'] ?? 0,
                'auth_total_tokens' => $onlineStats['auth_total_tokens'] ?? 0,
                'heartbeat_active' => $onlineStats['heartbeat_active'] ?? 0,
                'heartbeat_total' => $onlineStats['heartbeat_total'] ?? 0,
                'last_update' => $onlineStats['last_update'] ?? time(),
                'timestamp' => time()
            ];
            
            return $this->json($data);
            
        } catch (\Exception $e) {
            $this->log('获取在线统计失败: ' . $e->getMessage());
            return $this->json([], 0, '获取统计数据失败');
        }
    }

    /**
     * 健康检查
     */
    public function health()
    {
        try {
            // 检查关键数据是否存在
            $hasOnlineCount = RedisService::has('bns:online_count');
            $hasOnlineStats = RedisService::has('bns:online_stats');

            return $this->json([
                'status' => 'ok',
                'redis_connected' => true,
                'has_online_count' => (bool)$hasOnlineCount,
                'has_online_stats' => (bool)$hasOnlineStats,
                'timestamp' => time()
            ]);

        } catch (\Exception $e) {
            return $this->json([
                'status' => 'error',
                'redis_connected' => false
            ], 0, '健康检查失败: ' . $e->getMessage());
        }
    }


}
