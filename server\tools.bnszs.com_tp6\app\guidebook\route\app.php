<?php
// +----------------------------------------------------------------------
// | Guidebook应用路由配置
// +----------------------------------------------------------------------

use think\facade\Route;

// 向导书首页
Route::rule('/', 'Index/index');
Route::rule('/index', 'Index/index');

// 文章查看
Route::rule('/view', 'Index/view');

// 排行榜相关
Route::rule('/top', 'Index/top');
Route::rule('/InviteTop', 'Index/inviteTop');
Route::rule('/InviteTopJson', 'Index/inviteTopJson');

// API路由组
Route::group('api', function() {
    Route::get('/top.json', 'Api/topJson');
});

return [];
