package main

import (
	"fmt"
	"log"
	"sync"
	"time"

	"udp-server/server/internal/database"
	"udp-server/server/internal/model"
	"udp-server/server/internal/service"
	"udp-server/server/pkg/cache"
	"udp-server/server/pkg/config"

	"gorm.io/gorm"
)

func main() {
	// 初始化配置
	if err := config.Init("../config/config.yaml"); err != nil {
		log.Fatalf("配置初始化失败: %v", err)
	}

	// 初始化数据库
	if err := database.Init(); err != nil {
		log.Fatalf("数据库初始化失败: %v", err)
	}

	// 初始化缓存
	hashCache := cache.NewHashCache()

	// 创建LuckyService实例
	luckyService := service.NewLuckyService(database.DB, hashCache, nil)

	// 测试参数
	testUID := uint64(9999)
	testActivityID := uint(4)

	// 清理测试数据
	database.DB.Where("uid = ? AND schedule = ?", testUID, testActivityID).Delete(&model.UserDraw{})
	log.Printf("清理测试数据完成")

	// 并发测试
	var wg sync.WaitGroup
	concurrency := 10
	results := make([]error, concurrency)

	log.Printf("开始并发测试，并发数: %d", concurrency)

	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()
			
			// 模拟获取用户签到数据
			userDraw, err := getUserDrawSafely(luckyService, testUID, testActivityID)
			results[index] = err
			
			if err != nil {
				log.Printf("协程 %d 失败: %v", index, err)
			} else {
				log.Printf("协程 %d 成功: UID=%d, Schedule=%d, Day=%d", 
					index, userDraw.UID, userDraw.Schedule, userDraw.Day)
			}
		}(i)
	}

	wg.Wait()

	// 统计结果
	successCount := 0
	errorCount := 0
	for i, err := range results {
		if err != nil {
			log.Printf("结果 %d: 错误 - %v", i, err)
			errorCount++
		} else {
			successCount++
		}
	}

	log.Printf("测试完成: 成功=%d, 失败=%d", successCount, errorCount)

	// 验证数据库中只有一条记录
	var count int64
	database.DB.Model(&model.UserDraw{}).Where("uid = ? AND schedule = ?", testUID, testActivityID).Count(&count)
	log.Printf("数据库中记录数量: %d", count)

	if count == 1 && errorCount == 0 {
		log.Printf("✅ 测试通过: 成功避免了主键冲突")
	} else {
		log.Printf("❌ 测试失败: 仍然存在问题")
	}

	// 清理测试数据
	database.DB.Where("uid = ? AND schedule = ?", testUID, testActivityID).Delete(&model.UserDraw{})
}

// getUserDrawSafely 使用反射调用私有方法进行测试
func getUserDrawSafely(luckyService *service.LuckyService, uid uint64, activityID uint) (*model.UserDraw, error) {
	// 由于getUserDraw是私有方法，我们直接模拟其逻辑
	var userDraw model.UserDraw

	// 先尝试查询现有记录
	result := database.DB.Where("uid = ? AND schedule = ?", uid, activityID).First(&userDraw)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			// 记录不存在，尝试创建新记录
			userDraw = model.UserDraw{
				UID:      uid,
				Schedule: activityID,
				Extra:    0,
				Day:      1,
				Point:    1,
				Number:   0,
				Today:    0,
				Time:     time.Now(),
			}

			// 使用事务确保原子性
			err := database.DB.Transaction(func(tx *gorm.DB) error {
				// 再次检查记录是否存在（防止并发创建）
				var existingRecord model.UserDraw
				if err := tx.Where("uid = ? AND schedule = ?", uid, activityID).First(&existingRecord).Error; err == nil {
					// 记录已存在，使用现有记录
					userDraw = existingRecord
					return nil
				}

				// 创建新记录
				if err := tx.Create(&userDraw).Error; err != nil {
					// 如果是主键冲突错误，再次查询现有记录
					if err.Error() == "UNIQUE constraint failed: user_draw.uid, user_draw.schedule" ||
						err.Error() == "Error 1062: Duplicate entry" {
						log.Printf("[WARN] 主键冲突，查询现有记录: UID=%d, ActivityID=%d", uid, activityID)
						return tx.Where("uid = ? AND schedule = ?", uid, activityID).First(&userDraw).Error
					}
					return err
				}
				return nil
			})

			if err != nil {
				return nil, fmt.Errorf("创建用户签到记录失败: %v", err)
			}
		} else {
			return nil, fmt.Errorf("查询用户签到记录失败: %v", result.Error)
		}
	}

	return &userDraw, nil
}
